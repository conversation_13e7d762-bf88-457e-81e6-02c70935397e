---
kind: Template
apiVersion: v1
metadata:
  name: "${APP_NAME}-service"
  namespace: ${GROUP_NAME}-${STAGE}
labels:
  template: ${APP_NAME}-service
  app: ${APP_NAME}
objects:
- apiVersion: v1
  kind: Service
  metadata:
    name: ${APP_NAME}
    namespace: ${GROUP_NAME}-${STAGE}
  spec:
    selector:
      app: ${APP_NAME}
    ports:
      - name: tomcat
        protocol: TCP
        port: 8080
        targetPort: 8080
      - name: mgmnt
        protocol: TCP
        port: 8081
        targetPort: 8081
      - name: app
        protocol: TCP
        port: 9090
        targetPort: 9090
parameters:
  - name: APP_NAME
  - name: GROUP_NAME 
  - name: STAGE