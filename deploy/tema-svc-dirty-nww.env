STAGE=dirty

ELASTIC_APM_SERVICE_NAME=tema-svc
ELASTIC_APM_ENVIRONMENT=dirty
ELASTIC_APM_LOG_LEVEL=ERROR
ELASTIC_APM_SERVER_URLS=https://twprisma-apm-http.nww-monitoring.svc.cluster.local:8200
ELASTIC_APM_VERIFY_SERVER_CERT=false
ELASTIC_APM_APPLICATION_PACKAGES=com.btpns.fin
ELASTIC_APM_TOKEN=

REPLICAS_PODS=1
LIMIT_CPU=350m
LIMIT_MEMORY=356M

HEAP_MIN=32m
HEAP_MAX=256m
HEAP_STACK=228k

LIQUIBASE_ENABLE=false

MINIO_URI=https://storage-dirty.apps.nww.syariahbtpn.com
BUCKET_NAME=bucket01
IMAGE_BASE_URI=https://storage-dirty.apps.nww.syariahbtpn.com

UPM_TEMA_NOTIFICATION_TOPIC=upm.tema.notification

KAFKA_SERVER=kafka-dirty.svc.south.syariahbtpn.com:443
CONSUMER_AUTO_OFFSET_RESET=latest
CONSUMER_HEARTBEAT_INTERVAL=60000
CONSUMER_SESSION_TIMEOUT=180000
CONSUMER_AUTO_COMMIT=false
CONSUMER_CLIENT_ID=tema-svc-dirty
CONSUMER_GROUP_ID=tema-svc-dirty-group
PRODUCER_CLIENT_ID=tema-svc-dirty
PRODUCER_RETRIES=3

URL_FUID_DETAIL=https://tema-web-upm-dirty.apps.south.syariahbtpn.com/form/fuid/
URL_PARAM_DETAIL=https://tema-web-upm-dirty.apps.south.syariahbtpn.com/form/fsp/
URL_UAR_DETAIL=https://tema-web-upm-dirty.apps.south.syariahbtpn.com/rincian-pemantauan-user-id/

MAIL_URL=https://api-dirty-v2.apps.south.syariahbtpn.com/int/notification/3.0.0/
MAIL_API_VERSION=1.3.0
MAIL_SENDER=<<EMAIL>>
SEND_DATA_CORE_MAIL_TARGET=<EMAIL>,<EMAIL>
SEND_DATA_CORE_MAIL_CC=<EMAIL>

PROSPERA_ROUTER_URL=https://router-prospera2-beta.apps.south.syariahbtpn.com/prosperarest/services/

USER_WHITELIST_MENU_PROSPERA_BY_NIK=all
USER_WHITELIST_MENU_PROSPERA_BY_OFFICECODE=all

MAX_UPM_LIMIT_FOR_BWMP_NOMINAL_INPUT=100000000

HEADER_API_KEY=8131112bbf28291361447889aa1b9fac
