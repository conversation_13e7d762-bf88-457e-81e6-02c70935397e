FROM btpns/openjdk11:latest AS builder

COPY target/application.jar /tmp/app/target/

RUN curl  https://nexus.twprisma.com/repository/btpns-raw/elastic-apm/elastic-apm-agent.jar --output /tmp/elastic-apm-agent.jar

RUN mkdir -p /tmp/app/target/dependency && \
    cd /tmp/app/target/dependency && \
    jar -xf ../application.jar

FROM btpns/openjdk11:latest

COPY --from=builder /tmp/app/target/dependency/BOOT-INF/classes /opt/app/
COPY --from=builder /tmp/app/target/dependency/BOOT-INF/lib /opt/app/lib
COPY --from=builder /tmp/app/target/dependency/META-INF /opt/app/META-INF
COPY --from=builder /tmp/elastic-apm-agent.jar /opt/app/elastic-apm-agent.jar


RUN chmod 777 /opt/app && \
    chmod 666 /opt/app/lib/*.jar

ENV PATH "$PATH:$JAVA_HOME/bin"

EXPOSE 8080 8081 9090

WORKDIR /opt/app
ENTRYPOINT [ "sh", "-c", "java -javaagent:/opt/app/elastic-apm-agent.jar -Dspring.jmx.enabled=false -noverify -Djava.awt.headless=true -Djava.security.egd=file:/dev/./urandom -Xss$HEAP_STACK -Xms$HEAP_MIN -Xmx$HEAP_MAX -XX:+OptimizeStringConcat -XX:+UseG1GC -XX:+TieredCompilation -XX:TieredStopAtLevel=1 -XX:CICompilerCount=2 -XX:MinMetaspaceFreeRatio=5 -XX:MaxMetaspaceFreeRatio=10 -XX:MinHeapFreeRatio=5 -XX:MaxHeapFreeRatio=10 -XX:ParallelGCThreads=2 -XX:ConcGCThreads=2 -Djava.util.concurrent.ForkJoinPool.common.parallelism=10 -XX:+ExitOnOutOfMemoryError -XX:+UnlockDiagnosticVMOptions -XX:NativeMemoryTracking=summary --illegal-access=permit -cp /opt/app:/opt/app/lib/* com.btpns.fin.Application" ]
