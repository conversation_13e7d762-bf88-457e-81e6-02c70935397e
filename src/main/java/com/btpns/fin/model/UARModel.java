package com.btpns.fin.model;

public class UARModel {
    private String ticketId;
    private String aplikasi;
    private Integer periodeTahun;
    private String periodeTriwulan;
    private String nik;
    private String namaUser;
    private String kewenangan;
    private String jabatan;
    private String unitKerja;
    private String konfirmasiAkses;
    private String keterangan;
    private String reminder;
    private String picProcess;
    private String picApprove;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public Integer getPeriodeTahun() {
        return periodeTahun;
    }

    public void setPeriodeTahun(Integer periodeTahun) {
        this.periodeTahun = periodeTahun;
    }

    public String getPeriodeTriwulan() {
        return periodeTriwulan;
    }

    public void setPeriodeTriwulan(String periodeTriwulan) {
        this.periodeTriwulan = periodeTriwulan;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNamaUser() {
        return namaUser;
    }

    public void setNamaUser(String namaUser) {
        this.namaUser = namaUser;
    }

    public String getKewenangan() {
        return kewenangan;
    }

    public void setKewenangan(String kewenangan) {
        this.kewenangan = kewenangan;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getUnitKerja() {
        return unitKerja;
    }

    public void setUnitKerja(String unitKerja) {
        this.unitKerja = unitKerja;
    }

    public String getKonfirmasiAkses() {
        return konfirmasiAkses;
    }

    public void setKonfirmasiAkses(String konfirmasiAkses) {
        this.konfirmasiAkses = konfirmasiAkses;
    }

    public String getKeterangan() {
        return keterangan;
    }

    public void setKeterangan(String keterangan) {
        this.keterangan = keterangan;
    }

    public String getReminder() {
        return reminder;
    }

    public void setReminder(String reminder) {
        this.reminder = reminder;
    }

    public String getPicProcess() {
        return picProcess;
    }

    public void setPicProcess(String picProcess) {
        this.picProcess = picProcess;
    }

    public String getPicApprove() {
        return picApprove;
    }

    public void setPicApprove(String picApprove) {
        this.picApprove = picApprove;
    }
}
