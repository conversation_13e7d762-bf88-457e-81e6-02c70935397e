package com.btpns.fin.model;

import java.util.List;

public class Top5Activity {
    private List<TopActivity> data;
    private Integer totalNonMMS;
    private Integer totalMMS;
    private Integer grandTotal;

    public Top5Activity(List<TopActivity> data, Integer totalNonMMS, Integer totalMMS, Integer grandTotal) {
        this.data = data;
        this.totalNonMMS = totalNonMMS;
        this.totalMMS = totalMMS;
        this.grandTotal = grandTotal;
    }

    public List<TopActivity> getData() {
        return data;
    }

    public void setData(List<TopActivity> data) {
        this.data = data;
    }

    public Integer getTotalNonMMS() {
        return totalNonMMS;
    }

    public void setTotalNonMMS(Integer totalNonMMS) {
        this.totalNonMMS = totalNonMMS;
    }

    public Integer getTotalMMS() {
        return totalMMS;
    }

    public void setTotalMMS(Integer totalMMS) {
        this.totalMMS = totalMMS;
    }

    public Integer getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(Integer grandTotal) {
        this.grandTotal = grandTotal;
    }
}
