package com.btpns.fin.model;

import java.math.BigInteger;

public class ReportProductivityModel {
    private String nikProcessor;
    private String nameProcessor;
    private BigInteger countPerTiket;
    private BigInteger countPerAplikasi;

    public String getNikProcessor() {
        return nikProcessor;
    }

    public void setNikProcessor(String nikProcessor) {
        this.nikProcessor = nikProcessor;
    }

    public String getNameProcessor() {
        return nameProcessor;
    }

    public void setNameProcessor(String nameProcessor) {
        this.nameProcessor = nameProcessor;
    }

    public BigInteger getCountPerTiket() {
        return countPerTiket;
    }

    public void setCountPerTiket(BigInteger countPerTiket) {
        this.countPerTiket = countPerTiket;
    }

    public BigInteger getCountPerAplikasi() {
        return countPerAplikasi;
    }

    public void setCountPerAplikasi(BigInteger countPerAplikasi) {
        this.countPerAplikasi = countPerAplikasi;
    }
}
