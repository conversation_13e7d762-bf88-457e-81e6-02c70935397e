package com.btpns.fin.model;

import java.math.BigDecimal;
import java.util.List;

public class DetailUserProspera {
    private String centerCodeOfficer;
    private String governmentIdNumber;
    private String emailId;
    private List<String> dob;
    private Integer gender;
    private String localeName;
    private List<String> dateOfJoiningMFI;
    private List<String> dateOfJoiningBranch;
    private Address address;
    private String officeName;
    private Integer title;
    private String personnelLevels;
    private String roleName;
    private BigDecimal limit;
    private String loginName;
    private String nik;
    private Short personnelId;
    private Integer versionNo;
    private Integer maritalStatus;
    private String displayName;
    private Short status;
    private List<PersonnelNotes> personnelNotes;
    private Short entityType;
    private List<Schedule> schedule;
    private HierarchyDetailPersonnel hierarchyDetailPersonnel;
    private PersonnelParent personnelParent;
    private List<Short> personnelRoles;
    private Integer levelId;
    private Integer statusId;
    private NameUserProspera name;

    public String getCenterCodeOfficer() {
        return centerCodeOfficer;
    }

    public void setCenterCodeOfficer(String centerCodeOfficer) {
        this.centerCodeOfficer = centerCodeOfficer;
    }

    public String getGovernmentIdNumber() {
        return governmentIdNumber;
    }

    public void setGovernmentIdNumber(String governmentIdNumber) {
        this.governmentIdNumber = governmentIdNumber;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public List<String> getDob() {
        return dob;
    }

    public void setDob(List<String> dob) {
        this.dob = dob;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getLocaleName() {
        return localeName;
    }

    public void setLocaleName(String localeName) {
        this.localeName = localeName;
    }

    public List<String> getDateOfJoiningMFI() {
        return dateOfJoiningMFI;
    }

    public void setDateOfJoiningMFI(List<String> dateOfJoiningMFI) {
        this.dateOfJoiningMFI = dateOfJoiningMFI;
    }

    public List<String> getDateOfJoiningBranch() {
        return dateOfJoiningBranch;
    }

    public void setDateOfJoiningBranch(List<String> dateOfJoiningBranch) {
        this.dateOfJoiningBranch = dateOfJoiningBranch;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public Integer getTitle() {
        return title;
    }

    public void setTitle(Integer title) {
        this.title = title;
    }

    public String getPersonnelLevels() {
        return personnelLevels;
    }

    public void setPersonnelLevels(String personnelLevels) {
        this.personnelLevels = personnelLevels;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public BigDecimal getLimit() {
        return limit;
    }

    public void setLimit(BigDecimal limit) {
        this.limit = limit;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public Short getPersonnelId() {
        return personnelId;
    }

    public void setPersonnelId(Short personnelId) {
        this.personnelId = personnelId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(Integer maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public List<PersonnelNotes> getPersonnelNotes() {
        return personnelNotes;
    }

    public void setPersonnelNotes(List<PersonnelNotes> personnelNotes) {
        this.personnelNotes = personnelNotes;
    }

    public Short getEntityType() {
        return entityType;
    }

    public void setEntityType(Short entityType) {
        this.entityType = entityType;
    }

    public List<Schedule> getSchedule() {
        return schedule;
    }

    public void setSchedule(List<Schedule> schedule) {
        this.schedule = schedule;
    }

    public HierarchyDetailPersonnel getHierarchyDetailPersonnel() {
        return hierarchyDetailPersonnel;
    }

    public void setHierarchyDetailPersonnel(HierarchyDetailPersonnel hierarchyDetailPersonnel) {
        this.hierarchyDetailPersonnel = hierarchyDetailPersonnel;
    }

    public PersonnelParent getPersonnelParent() {
        return personnelParent;
    }

    public void setPersonnelParent(PersonnelParent personnelParent) {
        this.personnelParent = personnelParent;
    }

    public List<Short> getPersonnelRoles() {
        return personnelRoles;
    }

    public void setPersonnelRoles(List<Short> personnelRoles) {
        this.personnelRoles = personnelRoles;
    }

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public Integer getStatusId() {
        return statusId;
    }

    public void setStatusId(Integer statusId) {
        this.statusId = statusId;
    }

    public NameUserProspera getName() {
        return name;
    }

    public void setName(NameUserProspera name) {
        this.name = name;
    }
}
