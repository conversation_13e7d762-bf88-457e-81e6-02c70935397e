package com.btpns.fin.model;

import java.util.List;

public class SetupParamModel {
    private String tanggalEfektif;
    private DataSetupParamModel data;
    private List<String> aplikasi;
    private String parameterLama;
    private String parameterBaru;
    private List<AttachmentModel> attachment;
    private String fileName;
    private String kategoriParam;
    private String alasanPengajuan;
    private ApprovalPukModel approval;
    private String upmInputNIK;
    private String upmCheckerNIK;
    private String nikRequester;

    public String getTanggalEfektif() {
        return tanggalEfektif;
    }

    public List<String> getAplikasi() {
        return aplikasi;
    }

    public String getParameterLama() { return parameterLama; }

    public String getParameterBaru() {
        return parameterBaru;
    }

    public List<AttachmentModel> getAttachment() {
        return attachment;
    }

    public DataSetupParamModel getData() { return data; }

    public void setTanggalEfektif(String tanggalEfektif) { this.tanggalEfektif = tanggalEfektif; }

    public void setAplikasi(List<String> aplikasi) {
        this.aplikasi = aplikasi;
    }

    public void setParameterLama(String parameterLama) { this.parameterLama = parameterLama; }

    public void setParameterBaru(String parameterBaru) {
        this.parameterBaru = parameterBaru;
    }

    public void setAttachment(List<AttachmentModel> attachment) {
        this.attachment = attachment;
    }

    public void setData(DataSetupParamModel data) { this.data = data; }

    public String getKategoriParam() {
        return kategoriParam;
    }

    public void setKategoriParam(String kategoriParam) {
        this.kategoriParam = kategoriParam;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public ApprovalPukModel getApproval() {
        return approval;
    }

    public void setApproval(ApprovalPukModel approval) {
        this.approval = approval;
    }

    public String getUpmInputNIK() {
        return upmInputNIK;
    }

    public void setUpmInputNIK(String upmInputNIK) {
        this.upmInputNIK = upmInputNIK;
    }

    public String getUpmCheckerNIK() {
        return upmCheckerNIK;
    }

    public void setUpmCheckerNIK(String upmCheckerNIK) {
        this.upmCheckerNIK = upmCheckerNIK;
    }

    public String getNikRequester() {
        return nikRequester;
    }

    public void setNikRequester(String nikRequester) {
        this.nikRequester = nikRequester;
    }
}
