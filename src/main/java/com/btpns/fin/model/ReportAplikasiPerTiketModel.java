package com.btpns.fin.model;

public class ReportAplikasiPerTiketModel {
    private String ticketId;
    private String createDate;
    private String aplikasi;
    private String tanggalEfektif;
    private String tingkatanUser;
    private String attachment;
    private String dataNIK;
    private String dataNama;
    private String dataJabatan;
    private String dataUserId;
    private String dataKodeCabang;
    private String dataNamaCabang;
    private String location;
    private String dataEmail;
    private String dataTelepon;
    private String jenisPengajuan;
    private String alasanPengajuan;
    private String deskripsi;
    private String kategori;
    private String status;
    private String statusDesc;
    private String doneDate;
    private String currentStateDate;
    private String picProcess;
    private String picApprove;
    private String infoTambahan;
    private String masaBerlaku;
    private String puk1NIK;
    private String puk1Nama;
    private String puk1Jabatan;
    private String puk1ApproveDate;
    private String puk2NIK;
    private String puk2Nama;
    private String puk2Jabatan;
    private String puk2ApproveDate;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(String tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getDataNIK() {
        return dataNIK;
    }

    public void setDataNIK(String dataNIK) {
        this.dataNIK = dataNIK;
    }

    public String getDataNama() {
        return dataNama;
    }

    public void setDataNama(String dataNama) {
        this.dataNama = dataNama;
    }

    public String getDataUserId() {
        return dataUserId;
    }

    public void setDataUserId(String dataUserId) {
        this.dataUserId = dataUserId;
    }

    public String getDataKodeCabang() {
        return dataKodeCabang;
    }

    public void setDataKodeCabang(String dataKodeCabang) {
        this.dataKodeCabang = dataKodeCabang;
    }

    public String getDataNamaCabang() {
        return dataNamaCabang;
    }

    public void setDataNamaCabang(String dataNamaCabang) {
        this.dataNamaCabang = dataNamaCabang;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getDeskripsi() {
        return deskripsi;
    }

    public void setDeskripsi(String deskripsi) {
        this.deskripsi = deskripsi;
    }

    public String getKategori() {
        return kategori;
    }

    public void setKategori(String kategori) {
        this.kategori = kategori;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getDoneDate() {
        return doneDate;
    }

    public void setDoneDate(String doneDate) {
        this.doneDate = doneDate;
    }

    public String getPicProcess() {
        return picProcess;
    }

    public void setPicProcess(String picProcess) {
        this.picProcess = picProcess;
    }

    public String getPicApprove() {
        return picApprove;
    }

    public void setPicApprove(String picApprove) {
        this.picApprove = picApprove;
    }

    public String getInfoTambahan() {
        return infoTambahan;
    }

    public void setInfoTambahan(String infoTambahan) {
        this.infoTambahan = infoTambahan;
    }

    public String getMasaBerlaku() {
        return masaBerlaku;
    }

    public void setMasaBerlaku(String masaBerlaku) {
        this.masaBerlaku = masaBerlaku;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getTingkatanUser() {
        return tingkatanUser;
    }

    public void setTingkatanUser(String tingkatanUser) {
        this.tingkatanUser = tingkatanUser;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    public String getDataJabatan() {
        return dataJabatan;
    }

    public void setDataJabatan(String dataJabatan) {
        this.dataJabatan = dataJabatan;
    }

    public String getDataEmail() {
        return dataEmail;
    }

    public void setDataEmail(String dataEmail) {
        this.dataEmail = dataEmail;
    }

    public String getDataTelepon() {
        return dataTelepon;
    }

    public void setDataTelepon(String dataTelepon) {
        this.dataTelepon = dataTelepon;
    }

    public String getCurrentStateDate() {
        return currentStateDate;
    }

    public void setCurrentStateDate(String currentStateDate) {
        this.currentStateDate = currentStateDate;
    }

    public String getPuk1NIK() {
        return puk1NIK;
    }

    public void setPuk1NIK(String puk1NIK) {
        this.puk1NIK = puk1NIK;
    }

    public String getPuk1Nama() {
        return puk1Nama;
    }

    public void setPuk1Nama(String puk1Nama) {
        this.puk1Nama = puk1Nama;
    }

    public String getPuk1Jabatan() {
        return puk1Jabatan;
    }

    public void setPuk1Jabatan(String puk1Jabatan) {
        this.puk1Jabatan = puk1Jabatan;
    }

    public String getPuk1ApproveDate() {
        return puk1ApproveDate;
    }

    public void setPuk1ApproveDate(String puk1ApproveDate) {
        this.puk1ApproveDate = puk1ApproveDate;
    }

    public String getPuk2NIK() {
        return puk2NIK;
    }

    public void setPuk2NIK(String puk2NIK) {
        this.puk2NIK = puk2NIK;
    }

    public String getPuk2Nama() {
        return puk2Nama;
    }

    public void setPuk2Nama(String puk2Nama) {
        this.puk2Nama = puk2Nama;
    }

    public String getPuk2Jabatan() {
        return puk2Jabatan;
    }

    public void setPuk2Jabatan(String puk2Jabatan) {
        this.puk2Jabatan = puk2Jabatan;
    }

    public String getPuk2ApproveDate() {
        return puk2ApproveDate;
    }

    public void setPuk2ApproveDate(String puk2ApproveDate) {
        this.puk2ApproveDate = puk2ApproveDate;
    }
}
