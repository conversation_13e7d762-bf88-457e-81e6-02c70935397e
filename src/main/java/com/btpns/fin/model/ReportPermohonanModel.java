package com.btpns.fin.model;

import com.btpns.fin.model.entity.PermohonanDetail;

import java.util.List;

public class ReportPermohonanModel {
    private String startDate;
    private String endDate;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;
    private List<PermohonanDetail> data;

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() { return totalPages; }

    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

    public long getTotalItems() { return totalItems; }

    public void setTotalItems(long totalItems) { this.totalItems = totalItems; }

    public List<PermohonanDetail> getData() {
        return data;
    }

    public void setData(List<PermohonanDetail> data) {
        this.data = data;
    }
}
