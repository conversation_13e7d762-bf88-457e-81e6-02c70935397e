package com.btpns.fin.model;

import java.util.List;

public class UpmRoleModel {
    private String nik;
    private String nama;
    private boolean upmGroup;
    private Boolean isUserMMS;
    private Boolean isUserWhitelistMenuProspera;
    private String role;
    private List<UserIDAppModel> aplikasiUserID;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public boolean isUpmGroup() {
        return upmGroup;
    }

    public void setUpmGroup(boolean upmGroup) {
        this.upmGroup = upmGroup;
    }

    public Boolean getIsUserMMS() {
        return isUserMMS;
    }

    public void setIsUserMMS(Boolean userMMS) {
        isUserMMS = userMMS;
    }

    public Boolean getIsUserWhitelistMenuProspera() {
        return isUserWhitelistMenuProspera;
    }

    public void setIsUserWhitelistMenuProspera(Boolean userWhitelistMenuProspera) {
        isUserWhitelistMenuProspera = userWhitelistMenuProspera;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public List<UserIDAppModel> getAplikasiUserID() {
        return aplikasiUserID;
    }

    public void setAplikasiUserID(List<UserIDAppModel> aplikasiUserID) {
        this.aplikasiUserID = aplikasiUserID;
    }
}
