package com.btpns.fin.model;

import java.util.List;

public class UserInquiryModel {
    private UserInfoModel userInfo;
    private UserRoleModel userRole;
    private NotificationModel notifications;
    private List<UserIDAppModel> aplikasiUserID;
    private List<String> ownedActiveUserID;

    public UserInfoModel getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfoModel userInfo) {
        this.userInfo = userInfo;
    }

    public UserRoleModel getUserRole() {
        return userRole;
    }

    public void setUserRole(UserRoleModel userRole) {
        this.userRole = userRole;
    }

    public NotificationModel getNotifications() {
        return notifications;
    }

    public void setNotifications(NotificationModel notifications) {
        this.notifications = notifications;
    }

    public List<UserIDAppModel> getAplikasiUserID() {
        return aplikasiUserID;
    }

    public void setAplikasiUserID(List<UserIDAppModel> aplikasiUserID) {
        this.aplikasiUserID = aplikasiUserID;
    }

    public List<String> getOwnedActiveUserID() {
        return ownedActiveUserID;
    }

    public void setOwnedActiveUserID(List<String> ownedActiveUserID) {
        this.ownedActiveUserID = ownedActiveUserID;
    }
}
