package com.btpns.fin.model;

public class ProsperaUserIDModel {
    private String nik;
    private String officerName;
    private String roleName;
    private String loginName;
    private String mmsCode;
    private String officeName;
    private Double amtApprovalLimit;
    private String officerCode;
    private String officerStatusDesc;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getOfficerName() {
        return officerName;
    }

    public void setOfficerName(String officerName) {
        this.officerName = officerName;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getMmsCode() {
        return mmsCode;
    }

    public void setMmsCode(String mmsCode) {
        this.mmsCode = mmsCode;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public Double getAmtApprovalLimit() {
        return amtApprovalLimit;
    }

    public void setAmtApprovalLimit(Double amtApprovalLimit) {
        this.amtApprovalLimit = amtApprovalLimit;
    }

    public String getOfficerCode() {
        return officerCode;
    }

    public void setOfficerCode(String officerCode) {
        this.officerCode = officerCode;
    }

    public String getOfficerStatusDesc() {
        return officerStatusDesc;
    }

    public void setOfficerStatusDesc(String officerStatusDesc) {
        this.officerStatusDesc = officerStatusDesc;
    }
}
