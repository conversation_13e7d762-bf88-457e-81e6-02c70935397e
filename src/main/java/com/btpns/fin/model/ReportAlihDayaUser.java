package com.btpns.fin.model;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.util.Date;

@Entity
public class ReportAlihDayaUser {
    @Id
    private String nikVendor;
    private String nameVendor;
    private String occupationVendor;
    private String nikPUK;
    private String namePUK;
    private Date masaBerlakuSampai;

    public String getNikVendor() {
        return nikVendor;
    }

    public void setNikVendor(String nikVendor) {
        this.nikVendor = nikVendor;
    }

    public String getNameVendor() {
        return nameVendor;
    }

    public void setNameVendor(String nameVendor) {
        this.nameVendor = nameVendor;
    }

    public String getOccupationVendor() {
        return occupationVendor;
    }

    public void setOccupationVendor(String occupationVendor) {
        this.occupationVendor = occupationVendor;
    }

    public String getNikPUK() {
        return nikPUK;
    }

    public void setNikPUK(String nikPUK) {
        this.nikPUK = nikPUK;
    }

    public String getNamePUK() {
        return namePUK;
    }

    public void setNamePUK(String namePUK) {
        this.namePUK = namePUK;
    }

    public Date getMasaBerlakuSampai() {
        return masaBerlakuSampai;
    }

    public void setMasaBerlakuSampai(Date masaBerlakuSampai) {
        this.masaBerlakuSampai = masaBerlakuSampai;
    }
}
