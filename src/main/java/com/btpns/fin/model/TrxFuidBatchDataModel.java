package com.btpns.fin.model;

import com.btpns.fin.model.entity.*;

import java.util.ArrayList;
import java.util.List;

public class TrxFuidBatchDataModel {
    private List<TrxFuidRequest> trxFuidRequestList = new ArrayList<>();
    private List<TrxFuidApproval> trxFuidApprovalList = new ArrayList<>();
    private List<TrxFuidRequestAplikasi> trxFuidRequestAplikasiList = new ArrayList<>();
    private List<TrxAudittrail> trxAudittrailList = new ArrayList<>();
    private List<TrxFuidRequest> emailList = new ArrayList<>();
    private TrxFuidBatch trxFuidBatch;

    public List<TrxFuidRequest> getTrxFuidRequestList() {
        return trxFuidRequestList;
    }

    public void setTrxFuidRequestList(List<TrxFuidRequest> trxFuidRequestList) {
        this.trxFuidRequestList = trxFuidRequestList;
    }

    public List<TrxFuidApproval> getTrxFuidApprovalList() {
        return trxFuidApprovalList;
    }

    public void setTrxFuidApprovalList(List<TrxFuidApproval> trxFuidApprovalList) {
        this.trxFuidApprovalList = trxFuidApprovalList;
    }

    public List<TrxFuidRequestAplikasi> getTrxFuidRequestAplikasiList() {
        return trxFuidRequestAplikasiList;
    }

    public void setTrxFuidRequestAplikasiList(List<TrxFuidRequestAplikasi> trxFuidRequestAplikasiList) {
        this.trxFuidRequestAplikasiList = trxFuidRequestAplikasiList;
    }

    public List<TrxAudittrail> getTrxAudittrailList() {
        return trxAudittrailList;
    }

    public void setTrxAudittrailList(List<TrxAudittrail> trxAudittrailList) {
        this.trxAudittrailList = trxAudittrailList;
    }

    public TrxFuidBatch getTrxFuidBatch() {
        return trxFuidBatch;
    }

    public void setTrxFuidBatch(TrxFuidBatch trxFuidBatch) {
        this.trxFuidBatch = trxFuidBatch;
    }

    public List<TrxFuidRequest> getEmailList() {
        return emailList;
    }

    public void setEmailList(List<TrxFuidRequest> emailList) {
        this.emailList = emailList;
    }
}
