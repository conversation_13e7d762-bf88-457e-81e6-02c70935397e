package com.btpns.fin.model;

public class UserTicketModel {
    private String ticketId;
    private String tanggalEfektif;
    private String nik;
    private String nama;
    private String jabatan;
    private String kodeNamaCabang;
    private String aplikasi;
    private String jenisPengajuan;
    private String deskripsiAlasan;
    private String status;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(String tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getKodeNamaCabang() {
        return kodeNamaCabang;
    }

    public void setKodeNamaCabang(String kodeNamaCabang) {
        this.kodeNamaCabang = kodeNamaCabang;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getDeskripsiAlasan() {
        return deskripsiAlasan;
    }

    public void setDeskripsiAlasan(String deskripsiAlasan) {
        this.deskripsiAlasan = deskripsiAlasan;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
