package com.btpns.fin.model;

public class MsEGLS {
    private String username;
    private String firstName;
    private String lastName;
    private Integer roleID;
    private String roleName;
    private String email;
    private String kodeCabang;
    private String namaCabang;
    private String accountStatus;
    private String lastLogon;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Integer getRoleID() {
        return roleID;
    }

    public void setRoleID(Integer roleID) {
        this.roleID = roleID;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getKodeCabang() {
        return kodeCabang;
    }

    public void setKodeCabang(String kodeCabang) {
        this.kodeCabang = kodeCabang;
    }

    public String getNamaCabang() {
        return namaCabang;
    }

    public void setNamaCabang(String namaCabang) {
        this.namaCabang = namaCabang;
    }

    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getLastLogon() {
        return lastLogon;
    }

    public void setLastLogon(String lastLogon) {
        this.lastLogon = lastLogon;
    }

    private MsEGLS() {
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String username;
        private String firstName;
        private String lastName;
        private Integer roleID;
        private String roleName;
        private String email;
        private String kodeCabang;
        private String namaCabang;
        private String accountStatus;
        private String lastLogon;

        private Builder() {
        }

        public Builder username(String username) {
            this.username = username;
            return this;
        }

        public Builder firstName(String firstName) {
            this.firstName = firstName;
            return this;
        }

        public Builder lastName(String lastName) {
            this.lastName = lastName;
            return this;
        }

        public Builder roleID(Integer roleID) {
            this.roleID = roleID;
            return this;
        }

        public Builder roleName(String roleName) {
            this.roleName = roleName;
            return this;
        }

        public Builder email(String email) {
            this.email = email;
            return this;
        }

        public Builder kodeCabang(String kodeCabang) {
            this.kodeCabang = kodeCabang;
            return this;
        }

        public Builder namaCabang(String namaCabang) {
            this.namaCabang = namaCabang;
            return this;
        }

        public Builder accountEnabled(String accountEnabled) {
            this.accountStatus = accountEnabled;
            return this;
        }

        public Builder lastLogon(String lastLogon) {
            this.lastLogon = lastLogon;
            return this;
        }

        public MsEGLS build() {
            MsEGLS msEGLS = new MsEGLS();
            msEGLS.username = this.username;
            msEGLS.firstName = this.firstName;
            msEGLS.lastName = this.lastName;
            msEGLS.roleID = this.roleID;
            msEGLS.roleName = this.roleName;
            msEGLS.email = this.email;
            msEGLS.kodeCabang = this.kodeCabang;
            msEGLS.namaCabang = this.namaCabang;
            msEGLS.accountStatus = this.accountStatus;
            msEGLS.lastLogon = this.lastLogon;
            return msEGLS;
        }
    }
}
