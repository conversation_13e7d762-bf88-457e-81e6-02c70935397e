package com.btpns.fin.model;

import java.util.List;

public class MsUserIDOwnershipModel {
    private String nik;
    private String nama;
    private String jabatan;
    private List<String> userID;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public List<String> getUserID() {
        return userID;
    }

    public void setUserID(List<String> userID) {
        this.userID = userID;
    }

    public MsUserIDOwnershipModel() {
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder{
        private String nik;
        private String nama;
        private String jabatan;
        private List<String> userID;

        public String getNik() {
            return nik;
        }

        public void setNik(String nik) {
            this.nik = nik;
        }

        public String getNama() {
            return nama;
        }

        public void setNama(String nama) {
            this.nama = nama;
        }

        public String getJabatan() {
            return jabatan;
        }

        public void setJabatan(String jabatan) {
            this.jabatan = jabatan;
        }

        public List<String> getUserID() {
            return userID;
        }

        public void setUserID(List<String> userID) {
            this.userID = userID;
        }

        private Builder() {
        }

        public Builder nik(String nik) {
            this.nik = nik;
            return this;
        }

        public Builder nama(String nama) {
            this.nama = nama;
            return this;
        }

        public Builder jabatan(String jabatan) {
            this.jabatan = jabatan;
            return this;
        }

        public Builder userID (List<String> userID) {
            this.userID = userID;
            return this;
        }

        public MsUserIDOwnershipModel build(){
            MsUserIDOwnershipModel msUserIDOwnershipModel = new MsUserIDOwnershipModel();
            msUserIDOwnershipModel.nik = this.nik;
            msUserIDOwnershipModel.nama = this.nama;
            msUserIDOwnershipModel.jabatan = this.jabatan;
            msUserIDOwnershipModel.userID = this.userID;
            return msUserIDOwnershipModel;
        }
    }
}
