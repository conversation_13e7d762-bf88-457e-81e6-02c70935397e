package com.btpns.fin.model;

import java.util.List;

public class ListMsEmployeeDirectorModel {
    private List<MsEmployeeDirectorModel> msEmployeeDirectorModelList;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<MsEmployeeDirectorModel> getMsEmployeeDirectorModelList() {
        return msEmployeeDirectorModelList;
    }

    public void setMsEmployeeDirectorModelList(List<MsEmployeeDirectorModel> msEmployeeDirectorModelList) {
        this.msEmployeeDirectorModelList = msEmployeeDirectorModelList;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }
}
