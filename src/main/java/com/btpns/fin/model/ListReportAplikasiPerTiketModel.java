package com.btpns.fin.model;

import java.util.List;

public class ListReportAplikasiPerTiketModel {
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;
    private List<ReportAplikasiPerTiketModel> aplikasiPerTiket;
    private String status;
    private String type;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() { return totalPages; }

    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

    public long getTotalItems() { return totalItems; }

    public void setTotalItems(long totalItems) { this.totalItems = totalItems; }

    public List<ReportAplikasiPerTiketModel> getAplikasiPerTiket() {
        return aplikasiPerTiket;
    }

    public void setAplikasiPerTiket(List<ReportAplikasiPerTiketModel> aplikasiPerTiket) {
        this.aplikasiPerTiket = aplikasiPerTiket;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
