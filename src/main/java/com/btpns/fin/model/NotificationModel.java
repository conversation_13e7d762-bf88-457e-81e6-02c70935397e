package com.btpns.fin.model;

public class NotificationModel {
    private boolean commentUnread;
    private boolean unprocessedFuid;
    private boolean unconfirmedUAR;
    private NotificationMessage quarterlyReminder;
    private NotificationMessage monthlyReminder;

    public boolean getCommentUnread() {
        return commentUnread;
    }

    public void setCommentUnread(boolean commentUnread) {
        this.commentUnread = commentUnread;
    }

    public boolean isCommentUnread() {
        return commentUnread;
    }

    public boolean isUnprocessedFuid() {
        return unprocessedFuid;
    }

    public void setUnprocessedFuid(boolean unprocessedFuid) {
        this.unprocessedFuid = unprocessedFuid;
    }

    public boolean isUnconfirmedUAR() {
        return unconfirmedUAR;
    }

    public void setUnconfirmedUAR(boolean unconfirmedUAR) {
        this.unconfirmedUAR = unconfirmedUAR;
    }

    public NotificationMessage getQuarterlyReminder() {
        return quarterlyReminder;
    }

    public void setQuarterlyReminder(NotificationMessage quarterlyReminder) {
        this.quarterlyReminder = quarterlyReminder;
    }

    public NotificationMessage getMonthlyReminder() {
        return monthlyReminder;
    }

    public void setMonthlyReminder(NotificationMessage monthlyReminder) {
        this.monthlyReminder = monthlyReminder;
    }
}
