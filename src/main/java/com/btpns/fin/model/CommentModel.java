package com.btpns.fin.model;

import java.util.List;

public class CommentModel {
    private String commentId;
    private String ticketId;
    private String nikComment;
    private String namaComment;
    private String createDateTime;
    private String status;
    private String readDateTime;
    private String content;
    private String picUpmMakerNik;
    private String picUpmMakerNama;
    private List<AttachmentModel> attachment;

    public String getCommentId() {
        return commentId;
    }

    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getNikComment() {
        return nikComment;
    }

    public void setNikComment(String nikComment) {
        this.nikComment = nikComment;
    }

    public String getNamaComment() {
        return namaComment;
    }

    public void setNamaComment(String namaComment) {
        this.namaComment = namaComment;
    }

    public String getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(String createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReadDateTime() {
        return readDateTime;
    }

    public void setReadDateTime(String readDateTime) {
        this.readDateTime = readDateTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPicUpmMakerNik() {
        return picUpmMakerNik;
    }

    public void setPicUpmMakerNik(String picUpmMakerNik) {
        this.picUpmMakerNik = picUpmMakerNik;
    }

    public String getPicUpmMakerNama() {
        return picUpmMakerNama;
    }

    public void setPicUpmMakerNama(String picUpmMakerNama) {
        this.picUpmMakerNama = picUpmMakerNama;
    }

    public List<AttachmentModel> getAttachment() {
        return attachment;
    }

    public void setAttachment(List<AttachmentModel> attachment) {
        this.attachment = attachment;
    }
}
