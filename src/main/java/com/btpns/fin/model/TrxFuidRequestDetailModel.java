package com.btpns.fin.model;

import java.util.LinkedHashMap;
import java.util.List;

public class TrxFuidRequestDetailModel {
    private String ticketId;
    private String tujuan;
    private String alasan;
    private String tipeKaryawanBaru;
    private String tanggalEfektif;
    private DataFuidModel data;
    private String nikRequester;
    private List<DataAplikasiModel> aplikasi;
    private String tingkatan;
    private DataAplikasiModel role;
    private String statusMasaBerlaku;
    private String masaBerlaku;
    private String alasanPengajuan;
    private String infoTambahan;
    private List<AttachmentModel> attachment;
    private List<AttachmentModel> attachmentUPMInput;
    private String fileName;
    private StatusModel status;
    private String createDate;
    private String tipeLimitTransaksi;
    private Double nominalTransaksi;
    private Double nominalTransaksiUPM;
    private String unitKerjaLama;
    private String unitKerjaBaru;
    private String tipeKewenanganLimit;
    //private ProgressModel progress;
    private LinkedHashMap<String,Object> progress;
    private PUK1Model pukVendor;
    private List<CommentModel> comment;
    private String occupationDesc;
    private String organization;
    private String location;
    private List<UserProspera> personnelProspera;
    private Integer isInActivePersonnelProspera;
    private Integer isHitProspera;
    private PICEmailGroupModel picEmailGroup;
    private PICEmailGroupModel altPicEmailGroup;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getTujuan() {
        return tujuan;
    }

    public void setTujuan(String tujuan) {
        this.tujuan = tujuan;
    }

    public String getAlasan() {
        return alasan;
    }

    public void setAlasan(String alasan) {
        this.alasan = alasan;
    }

    public String getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(String tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public DataFuidModel getData() {
        return data;
    }

    public void setData(DataFuidModel data) {
        this.data = data;
    }

    public String getNikRequester() {
        return nikRequester;
    }

    public void setNikRequester(String nikRequester) {
        this.nikRequester = nikRequester;
    }

    public List<DataAplikasiModel> getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(List<DataAplikasiModel> aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getTingkatan() {
        return tingkatan;
    }

    public void setTingkatan(String tingkatan) {
        this.tingkatan = tingkatan;
    }

    public DataAplikasiModel getRole() {
        return role;
    }

    public void setRole(DataAplikasiModel role) {
        this.role = role;
    }

    public String getStatusMasaBerlaku() {
        return statusMasaBerlaku;
    }

    public void setStatusMasaBerlaku(String statusMasaBerlaku) {
        this.statusMasaBerlaku = statusMasaBerlaku;
    }

    public String getMasaBerlaku() {
        return masaBerlaku;
    }

    public void setMasaBerlaku(String masaBerlaku) {
        this.masaBerlaku = masaBerlaku;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getInfoTambahan() {
        return infoTambahan;
    }

    public void setInfoTambahan(String infoTambahan) {
        this.infoTambahan = infoTambahan;
    }

    public List<AttachmentModel> getAttachment() {
        return attachment;
    }

    public void setAttachment(List<AttachmentModel> attachment) {
        this.attachment = attachment;
    }

    public StatusModel getStatus() {
        return status;
    }

    public void setStatus(StatusModel status) {
        this.status = status;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    /*public ProgressModel getProgress() {
        return progress;
    }

    public void setProgress(ProgressModel progress) {
        this.progress = progress;
    }*/

    public LinkedHashMap<String, Object> getProgress() {
        return progress;
    }

    public void setProgress(LinkedHashMap<String, Object> progress) {
        this.progress = progress;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getTipeLimitTransaksi() {
        return tipeLimitTransaksi;
    }

    public void setTipeLimitTransaksi(String tipeLimitTransaksi) {
        this.tipeLimitTransaksi = tipeLimitTransaksi;
    }

    public Double getNominalTransaksi() {
        return nominalTransaksi;
    }

    public void setNominalTransaksi(Double nominalTransaksi) {
        this.nominalTransaksi = nominalTransaksi;
    }

    public Double getNominalTransaksiUPM() {
        return nominalTransaksiUPM;
    }

    public void setNominalTransaksiUPM(Double nominalTransaksiUPM) {
        this.nominalTransaksiUPM = nominalTransaksiUPM;
    }

    public String getUnitKerjaLama() {
        return unitKerjaLama;
    }

    public void setUnitKerjaLama(String unitKerjaLama) {
        this.unitKerjaLama = unitKerjaLama;
    }

    public String getUnitKerjaBaru() {
        return unitKerjaBaru;
    }

    public void setUnitKerjaBaru(String unitKerjaBaru) {
        this.unitKerjaBaru = unitKerjaBaru;
    }

    public String getTipeKewenanganLimit() {
        return tipeKewenanganLimit;
    }

    public void setTipeKewenanganLimit(String tipeKewenanganLimit) {
        this.tipeKewenanganLimit = tipeKewenanganLimit;
    }

    public String getTipeKaryawanBaru() {
        return tipeKaryawanBaru;
    }

    public void setTipeKaryawanBaru(String tipeKaryawanBaru) {
        this.tipeKaryawanBaru = tipeKaryawanBaru;
    }

    public PUK1Model getPukVendor() {
        return pukVendor;
    }

    public void setPukVendor(PUK1Model pukVendor) {
        this.pukVendor = pukVendor;
    }

    public List<AttachmentModel> getAttachmentUPMInput() {
        return attachmentUPMInput;
    }

    public void setAttachmentUPMInput(List<AttachmentModel> attachmentUPMInput) {
        this.attachmentUPMInput = attachmentUPMInput;
    }

    public List<CommentModel> getComment() {
        return comment;
    }

    public void setComment(List<CommentModel> comment) {
        this.comment = comment;
    }

    public String getOccupationDesc() { return occupationDesc; }

    public void setOccupationDesc(String occupationDesc) { this.occupationDesc = occupationDesc; }

    public String getOrganization() { return organization; }

    public void setOrganization(String organization) { this.organization = organization; }

    public String getLocation() { return location; }

    public void setLocation(String location) { this.location = location; }

    public List<UserProspera> getPersonnelProspera() {
        return personnelProspera;
    }

    public void setPersonnelProspera(List<UserProspera> personnelProspera) {
        this.personnelProspera = personnelProspera;
    }

    public Integer getIsInActivePersonnelProspera() {
        return isInActivePersonnelProspera;
    }

    public void setIsInActivePersonnelProspera(Integer isInActivePersonnelProspera) {
        this.isInActivePersonnelProspera = isInActivePersonnelProspera;
    }

    public Integer getIsHitProspera() {
        return isHitProspera;
    }

    public void setIsHitProspera(Integer isHitProspera) {
        this.isHitProspera = isHitProspera;
    }

    public PICEmailGroupModel getPicEmailGroup() {
        return picEmailGroup;
    }

    public void setPicEmailGroup(PICEmailGroupModel picEmailGroup) {
        this.picEmailGroup = picEmailGroup;
    }

    public PICEmailGroupModel getAltPicEmailGroup() {
        return altPicEmailGroup;
    }

    public void setAltPicEmailGroup(PICEmailGroupModel altPicEmailGroup) {
        this.altPicEmailGroup = altPicEmailGroup;
    }
}
