package com.btpns.fin.model;

import java.util.List;

public class ListWaitingTicketFuidModel {
    private List<WaitingTicketFuidModel> waitingTicket;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<WaitingTicketFuidModel> getWaitingTicket() {
        return waitingTicket;
    }

    public int getPage() { return page; }

    public int getLimit() { return limit; }

    public void setWaitingTicket(List<WaitingTicketFuidModel> waitingTicket) {
        this.waitingTicket = waitingTicket;
    }

    public void setPage(int page) { this.page = page; }

    public void setLimit(int limit) { this.limit = limit; }

    public int getTotalPages() { return totalPages; }

    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

    public long getTotalItems() { return totalItems; }

    public void setTotalItems(long totalItems) { this.totalItems = totalItems; }
}
