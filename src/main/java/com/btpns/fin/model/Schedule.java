package com.btpns.fin.model;

import java.util.List;

public class Schedule {
    private Short scheduleId;
    private List<String> effectiveDate;
    private String scheduleType;
    private String scheduleEntityType;
    private String scheduleStatus;
    private Integer retryCount;
    private List<String> createdAt;
    private List<String> updatedAt;
    private Short entityId;
    private Short parentScheduleId;
    private String scheduleDateType;

    public short getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(short scheduleId) {
        this.scheduleId = scheduleId;
    }

    public List<String> getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(List<String> effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getScheduleType() {
        return scheduleType;
    }

    public void setScheduleType(String scheduleType) {
        this.scheduleType = scheduleType;
    }

    public String getScheduleEntityType() {
        return scheduleEntityType;
    }

    public void setScheduleEntityType(String scheduleEntityType) {
        this.scheduleEntityType = scheduleEntityType;
    }

    public String getScheduleStatus() {
        return scheduleStatus;
    }

    public void setScheduleStatus(String scheduleStatus) {
        this.scheduleStatus = scheduleStatus;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public List<String> getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(List<String> createdAt) {
        this.createdAt = createdAt;
    }

    public List<String> getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(List<String> updatedAt) {
        this.updatedAt = updatedAt;
    }

    public short getEntityId() {
        return entityId;
    }

    public void setEntityId(short entityId) {
        this.entityId = entityId;
    }

    public short getParentScheduleId() {
        return parentScheduleId;
    }

    public void setParentScheduleId(short parentScheduleId) {
        this.parentScheduleId = parentScheduleId;
    }

    public String getScheduleDateType() {
        return scheduleDateType;
    }

    public void setScheduleDateType(String scheduleDateType) {
        this.scheduleDateType = scheduleDateType;
    }
}
