package com.btpns.fin.model;

import com.btpns.fin.model.entity.MsCustomUserID;

public class UserIDModel {
    private String nik;
    private String namaUser;
    private String kewenangan;
    private String jabatan;
    private String unitKerja;

    public UserIDModel() {
    }

    public UserIDModel(String nik, String namaUser, String kewenangan, String jabatan, String unitKerja) {
        this.nik = nik;
        this.namaUser = namaUser;
        this.kewenangan = kewenangan;
        this.jabatan = jabatan;
        this.unitKerja = unitKerja;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNamaUser() {
        return namaUser;
    }

    public void setNamaUser(String namaUser) {
        this.namaUser = namaUser;
    }

    public String getKewenangan() {
        return kewenangan;
    }

    public void setKewenangan(String kewenangan) {
        this.kewenangan = kewenangan;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getUnitKerja() {
        return unitKerja;
    }

    public void setUnitKerja(String unitKerja) {
        this.unitKerja = unitKerja;
    }

    public MsCustomUserID toMsCustomUserID(String paramDetailId) {
        return new MsCustomUserID(paramDetailId, this.nik, this.namaUser, this.kewenangan, this.jabatan, this.unitKerja);
    }
}
