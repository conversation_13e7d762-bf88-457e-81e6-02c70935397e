package com.btpns.fin.model;

import com.btpns.fin.model.entity.TotalTujuanPerBranchType;

import java.util.List;

public class TotalTujuanPerKategori {
    private List<TotalTujuanPerBranchType> userIDMaintenance;
    private List<TotalTujuanPerBranchType> parameterMaintenance;
    private GrandTotalPerBranchType grandTotal;

    public List<TotalTujuanPerBranchType> getUserIDMaintenance() {
        return userIDMaintenance;
    }

    public void setUserIDMaintenance(List<TotalTujuanPerBranchType> userIDMaintenance) {
        this.userIDMaintenance = userIDMaintenance;
    }

    public List<TotalTujuanPerBranchType> getParameterMaintenance() {
        return parameterMaintenance;
    }

    public void setParameterMaintenance(List<TotalTujuanPerBranchType> parameterMaintenance) {
        this.parameterMaintenance = parameterMaintenance;
    }

    public GrandTotalPerBranchType getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(GrandTotalPerBranchType grandTotal) {
        this.grandTotal = grandTotal;
    }
}
