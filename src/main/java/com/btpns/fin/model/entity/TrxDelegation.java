package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
public class TrxDelegation {
    @Id
    private String delegationId;
    private String nikRequester;
    private String namaRequester;
    private String jabatanRequester;
    private Date startDate;
    private Date endDate;
    private String info;
    private String nikDelegation;
    private String namaDelegation;
    private String jabatanDelegation;
    private String status;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDateTime;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime updateDateTime;

    public String getNikRequester() {
        return nikRequester;
    }

    public void setNikRequester(String nikRequester) {
        this.nikRequester = nikRequester;
    }

    public String getNamaRequester() {
        return namaRequester;
    }

    public void setNamaRequester(String namaRequester) {
        this.namaRequester = namaRequester;
    }

    public String getJabatanRequester() {
        return jabatanRequester;
    }

    public void setJabatanRequester(String jabatanRequester) {
        this.jabatanRequester = jabatanRequester;
    }

    public String getDelegationId() {
        return delegationId;
    }

    public void setDelegationId(String delegationId) {
        this.delegationId = delegationId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getNikDelegation() {
        return nikDelegation;
    }

    public void setNikDelegation(String nikDelegation) {
        this.nikDelegation = nikDelegation;
    }

    public String getNamaDelegation() {
        return namaDelegation;
    }

    public void setNamaDelegation(String namaDelegation) {
        this.namaDelegation = namaDelegation;
    }

    public String getJabatanDelegation() {
        return jabatanDelegation;
    }

    public void setJabatanDelegation(String jabatanDelegation) {
        this.jabatanDelegation = jabatanDelegation;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public LocalDateTime getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(LocalDateTime updateDateTime) {
        this.updateDateTime = updateDateTime;
    }
}
