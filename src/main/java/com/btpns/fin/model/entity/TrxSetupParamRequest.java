package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.btpns.fin.helper.VarbinaryConverter;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
public class TrxSetupParamRequest {
    @Id
    private String ticketId;

    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDateTime;

    private Date tanggalEfektif;

    @Size(max=40)
    private String dataNik;

    @Size(max=100)
    private String dataNamaLengkap;

    @Size(max=100)
    private String dataJabatan;

    @Size(max=10)
    private String dataKodeCabang;

    @Size(max=200)
    private String dataNamaCabang;

    @Size(max=25)
    private String dataTelepon;

    @Size(max=40)
    private String nikRequester;

    private String aplikasi;

    private String parameterLama;

    private String parameterBaru;

    @OneToOne(mappedBy = "trxSetupParamRequest")
    private TrxSetupParamApproval trxSetupParamApproval;

    //@Convert(converter = VarbinaryConverter.class)
    private String attachment;

    private String fileName;

    private String kategoriParamId;

    private String kategoriParamName;

    private String alasanPengajuan;

    private String dataEmail;

    private String requestId;

    private String inputType;

    public String getTicketId() {
        return ticketId;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public Date getTanggalEfektif() {
        return tanggalEfektif;
    }

    public String getDataNik() {
        return dataNik;
    }

    public String getDataNamaLengkap() {
        return dataNamaLengkap;
    }

    public String getDataJabatan() {
        return dataJabatan;
    }

    public String getDataKodeCabang() {
        return dataKodeCabang;
    }

    public String getDataNamaCabang() {
        return dataNamaCabang;
    }

    public String getDataTelepon() {
        return dataTelepon;
    }

    public String getNikRequester() {
        return nikRequester;
    }

    public String getAplikasi() { return aplikasi; }

    public String getParameterLama() {
        return parameterLama;
    }

    public String getParameterBaru() {
        return parameterBaru;
    }

    public String getAttachment() {
        return attachment;
    }

    public TrxSetupParamApproval getTrxSetupParamApproval() { return trxSetupParamApproval; }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public void setTanggalEfektif(Date tanggalEfektif) { this.tanggalEfektif = tanggalEfektif; }

    public void setDataNik(String dataNik) { this.dataNik = dataNik; }

    public void setDataNamaLengkap(String dataNamaLengkap) {
        this.dataNamaLengkap = dataNamaLengkap;
    }

    public void setDataJabatan(String dataJabatan) {
        this.dataJabatan = dataJabatan;
    }

    public void setDataKodeCabang(String dataKodeCabang) {
        this.dataKodeCabang = dataKodeCabang;
    }

    public void setDataNamaCabang(String dataNamaCabang) {
        this.dataNamaCabang = dataNamaCabang;
    }

    public void setDataTelepon(String dataTelepon) {
        this.dataTelepon = dataTelepon;
    }

    public void setNikRequester(String nikRequester) {
        this.nikRequester = nikRequester;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public void setParameterLama(String parameterLama) { this.parameterLama = parameterLama; }

    public void setParameterBaru(String parameterBaru) { this.parameterBaru = parameterBaru; }

    public void setAttachment(String attachment) { this.attachment = attachment; }

    public void setTrxSetupParamApproval(TrxSetupParamApproval trxSetupParamApproval) { this.trxSetupParamApproval = trxSetupParamApproval; }

    public String getKategoriParamId() {
        return kategoriParamId;
    }

    public void setKategoriParamId(String kategoriParamId) {
        this.kategoriParamId = kategoriParamId;
    }

    public String getKategoriParamName() {
        return kategoriParamName;
    }

    public void setKategoriParamName(String kategoriParamName) {
        this.kategoriParamName = kategoriParamName;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getDataEmail() {
        return dataEmail;
    }

    public void setDataEmail(String dataEmail) {
        this.dataEmail = dataEmail;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getInputType() {
        return inputType;
    }

    public void setInputType(String inputType) {
        this.inputType = inputType;
    }
}
