package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.btpns.fin.model.MsEmployeeDirectorModel;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Entity
public class MsEmployeeDirector {
    @Id
    private String nikOptima;
    private String nikLdap;
    private String namaLengkap;
    private String email;
    private String keterangan;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDatetime;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime updateDatetime;

    public String getNikOptima() {
        return nikOptima;
    }

    public void setNikOptima(String nikOptima) {
        this.nikOptima = nikOptima;
    }

    public String getNikLdap() {
        return nikLdap;
    }

    public void setNikLdap(String nikLdap) {
        this.nikLdap = nikLdap;
    }

    public String getNamaLengkap() {
        return namaLengkap;
    }

    public void setNamaLengkap(String namaLengkap) {
        this.namaLengkap = namaLengkap;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getKeterangan() {
        return keterangan;
    }

    public void setKeterangan(String keterangan) {
        this.keterangan = keterangan;
    }

    public LocalDateTime getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(LocalDateTime createDatetime) {
        this.createDatetime = createDatetime;
    }

    public LocalDateTime getUpdateDatetime() {
        return updateDatetime;
    }

    public void setUpdateDatetime(LocalDateTime updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    public MsEmployeeDirectorModel toMsEmployeeDirectorModel (){
        MsEmployeeDirectorModel msEmployeeDirectorModel = new MsEmployeeDirectorModel();
        msEmployeeDirectorModel.setNikOptima(this.nikOptima);
        msEmployeeDirectorModel.setNikLdap(this.nikLdap);
        msEmployeeDirectorModel.setNamaLengkap(this.namaLengkap);
        msEmployeeDirectorModel.setEmail(this.email);
        msEmployeeDirectorModel.setKeterangan(this.keterangan);
        return msEmployeeDirectorModel;
    }
}
