package com.btpns.fin.model.entity;

import com.btpns.fin.model.ProsperaRoleModel;

import javax.persistence.Entity;
import javax.persistence.Id;

import static com.btpns.fin.helper.Constants.*;

@Entity
public class MsProsperaRole {
    @Id
    private String temaRoleCode;
    private Integer prosperaRoleCode;
    private String roleDesc;
    private String systemParamId;
    private boolean isActive;

    public String getTemaRoleCode() {
        return temaRoleCode;
    }

    public void setTemaRoleCode(String temaRoleCode) {
        this.temaRoleCode = temaRoleCode;
    }

    public Integer getProsperaRoleCode() {
        return prosperaRoleCode;
    }

    public void setProsperaRoleCode(Integer prosperaRoleCode) {
        this.prosperaRoleCode = prosperaRoleCode;
    }

    public String getRoleDesc() {
        return roleDesc;
    }

    public void setRoleDesc(String roleDesc) {
        this.roleDesc = roleDesc;
    }

    public String getSystemParamId() {
        return systemParamId;
    }

    public void setSystemParamId(String systemParamId) {
        this.systemParamId = systemParamId;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public ProsperaRoleModel toProsperaRoleModel() {
        ProsperaRoleModel prosperaRoleModel = new ProsperaRoleModel();
        prosperaRoleModel.setTemaRoleCode(this.temaRoleCode);
        prosperaRoleModel.setProsperaRoleCode(this.prosperaRoleCode);
        prosperaRoleModel.setRoleName(this.roleDesc);
        prosperaRoleModel.setOfficeLevel(getOfficeLevel());
        prosperaRoleModel.setActive(this.isActive);
        return prosperaRoleModel;
    }

    private String getOfficeLevel() {
        switch (this.systemParamId) {
            case KODE_ROLE_MMS:
                return MMS;
            case KODE_ROLE_CABANG:
                return KC + "/" + KFO;
            case KODE_ROLE_HO:
                return BTPN_HO;
            default:
                return ALASAN_LAINNYA;
        }
    }
}
