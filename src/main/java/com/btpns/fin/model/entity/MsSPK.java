package com.btpns.fin.model.entity;

import com.btpns.fin.model.UserIDModel;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigInteger;

@Entity
public class MsSPK {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private BigInteger id;

    private String nik;
    private String namaUser;
    private String kewenangan;
    private String jabatan;
    private String unitKerja;

    public MsSPK() {
    }

    public MsSPK(String nik, String namaUser, String kewenangan, String jabatan, String unitKerja) {
        this.nik = nik;
        this.namaUser = namaUser;
        this.kewenangan = kewenangan;
        this.jabatan = jabatan;
        this.unitKerja = unitKerja;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNamaUser() {
        return namaUser;
    }

    public void setNamaUser(String namaUser) {
        this.namaUser = namaUser;
    }

    public String getKewenangan() {
        return kewenangan;
    }

    public void setKewenangan(String kewenangan) {
        this.kewenangan = kewenangan;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getUnitKerja() {
        return unitKerja;
    }

    public void setUnitKerja(String unitKerja) {
        this.unitKerja = unitKerja;
    }

    public UserIDModel toUserIDModel() {
        return new UserIDModel(this.getNik(), this.getNamaUser(), this.getKewenangan(), this.getJabatan(), this.getUnitKerja());
    }
}
