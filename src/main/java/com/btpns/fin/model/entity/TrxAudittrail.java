package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
public class TrxAudittrail {
    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private BigInteger id;

    @Size(max=40)
    private String nik;

    @Size(max=60)
    private String action;

    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDateTime;

    private String ticketId;

    private String additionalInfo;

    public BigInteger getId() {
        return id;
    }

    public String getNik() {
        return nik;
    }

    public String getAction() {
        return action;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }
}
