package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class MsMMS {
    @Id
    private String mmsId;
    private String address;
    private String costCenter;
    private String districtDesc;
    private String kfoCode;
    private String kfoName;
    private String mmsCode;
    private String mmsName;
    private String mmsStatusCode;
    private String mmsStatusDesc;
    private String provinceDesc;
    private String rtRW;
    private String stateDesc;
    private String subDistrictDesc;
    private String zipCode;

    public String getMmsId() {
        return mmsId;
    }

    public void setMmsId(String mmsId) {
        this.mmsId = mmsId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCostCenter() {
        return costCenter;
    }

    public void setCostCenter(String costCenter) {
        this.costCenter = costCenter;
    }

    public String getDistrictDesc() {
        return districtDesc;
    }

    public void setDistrictDesc(String districtDesc) {
        this.districtDesc = districtDesc;
    }

    public String getKfoCode() {
        return kfoCode;
    }

    public void setKfoCode(String kfoCode) {
        this.kfoCode = kfoCode;
    }

    public String getKfoName() {
        return kfoName;
    }

    public void setKfoName(String kfoName) {
        this.kfoName = kfoName;
    }

    public String getMmsCode() {
        return mmsCode;
    }

    public void setMmsCode(String mmsCode) {
        this.mmsCode = mmsCode;
    }

    public String getMmsName() {
        return mmsName;
    }

    public void setMmsName(String mmsName) {
        this.mmsName = mmsName;
    }

    public String getMmsStatusCode() {
        return mmsStatusCode;
    }

    public void setMmsStatusCode(String mmsStatusCode) {
        this.mmsStatusCode = mmsStatusCode;
    }

    public String getMmsStatusDesc() {
        return mmsStatusDesc;
    }

    public void setMmsStatusDesc(String mmsStatusDesc) {
        this.mmsStatusDesc = mmsStatusDesc;
    }

    public String getProvinceDesc() {
        return provinceDesc;
    }

    public void setProvinceDesc(String provinceDesc) {
        this.provinceDesc = provinceDesc;
    }

    public String getRtRW() {
        return rtRW;
    }

    public void setRtRW(String rtRW) {
        this.rtRW = rtRW;
    }

    public String getStateDesc() {
        return stateDesc;
    }

    public void setStateDesc(String stateDesc) {
        this.stateDesc = stateDesc;
    }

    public String getSubDistrictDesc() {
        return subDistrictDesc;
    }

    public void setSubDistrictDesc(String subDistrictDesc) {
        this.subDistrictDesc = subDistrictDesc;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }
}
