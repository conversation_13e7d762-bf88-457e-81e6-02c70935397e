package com.btpns.fin.model.entity;

import com.btpns.fin.model.MsUserIDOwnershipModel;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Entity
public class MsUserIDOwnership {
    @Id
    private String nik;
    private String nama;
    private String jabatan;
    private String userID;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getUserID() {
        return userID;
    }

    public void setUserID(String userID) {
        this.userID = userID;
    }

    public MsUserIDOwnershipModel toMsUserIDOwnershipModel(){
        List<String> listUserId = this.userID.isEmpty() ? Collections.emptyList() : Arrays.asList(this.userID.replaceAll("\\s", "").split(","));

        return MsUserIDOwnershipModel.builder()
                .nik(this.nik)
                .nama(this.nama)
                .jabatan(this.jabatan)
                .userID(listUserId)
                .build();
    }
}
