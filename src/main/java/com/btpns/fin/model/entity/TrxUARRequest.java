package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Entity
public class TrxUARRequest {
    @Id
    private String ticketId;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    @CreationTimestamp
    private LocalDateTime createdAt;
    private String aplikasi;
    private Integer periodYear;
    private String periodQuarter;
    private String nik;
    private String namaUser;
    private String kewenangan;
    private String jabatan;
    private String unitKerja;
    private String email;
    private Boolean isManualConfirmation;
    private Integer reminder;
    @OneToOne(mappedBy = "trxUARRequest", cascade = CascadeType.ALL)
    private TrxUARApproval trxUARApproval;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public Integer getPeriodYear() {
        return periodYear;
    }

    public void setPeriodYear(Integer periodYear) {
        this.periodYear = periodYear;
    }

    public String getPeriodQuarter() {
        return periodQuarter;
    }

    public void setPeriodQuarter(String periodQuarter) {
        this.periodQuarter = periodQuarter;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNamaUser() {
        return namaUser;
    }

    public void setNamaUser(String namaUser) {
        this.namaUser = namaUser;
    }

    public String getKewenangan() {
        return kewenangan;
    }

    public void setKewenangan(String kewenangan) {
        this.kewenangan = kewenangan;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getUnitKerja() {
        return unitKerja;
    }

    public void setUnitKerja(String unitKerja) {
        this.unitKerja = unitKerja;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean isManualConfirmation() {
        return isManualConfirmation;
    }

    public void setManualConfirmation(Boolean manualConfirmation) {
        isManualConfirmation = manualConfirmation;
    }

    public Integer getReminder() {
        return reminder;
    }

    public void setReminder(Integer reminder) {
        this.reminder = reminder;
    }

    public TrxUARApproval getTrxUARApproval() {
        return trxUARApproval;
    }

    public void setTrxUARApproval(TrxUARApproval trxUARApproval) {
        this.trxUARApproval = trxUARApproval;
    }
}
