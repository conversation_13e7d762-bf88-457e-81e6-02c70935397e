package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Entity
public class MsEmployee {
    @Id
    private String nik;
    private String identityNum;
    private String fullName;
    private String occupation;
    private String occupationDesc;
    private String statusEmployeeDesc;
    private String branchCode;
    private String costCenterCode;
    private String directSupervisorNIK;
    private String directSupervisorName;
    private String fieldChecksum;
    private String srcSystem;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime DTPopulate;
    private String sysPopulate;

    public String getNik() {
        return nik.toLowerCase();
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getIdentityNum() {
        return identityNum;
    }

    public void setIdentityNum(String identityNum) {
        this.identityNum = identityNum;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getOccupationDesc() {
        return occupationDesc;
    }

    public void setOccupationDesc(String occupationDesc) {
        this.occupationDesc = occupationDesc;
    }

    public String getStatusEmployeeDesc() {
        return statusEmployeeDesc;
    }

    public void setStatusEmployeeDesc(String statusEmployeeDesc) {
        this.statusEmployeeDesc = statusEmployeeDesc;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public String getDirectSupervisorNIK() {
        return directSupervisorNIK;
    }

    public void setDirectSupervisorNIK(String directSupervisorNIK) {
        this.directSupervisorNIK = directSupervisorNIK;
    }

    public String getDirectSupervisorName() {
        return directSupervisorName;
    }

    public void setDirectSupervisorName(String directSupervisorName) {
        this.directSupervisorName = directSupervisorName;
    }

    public String getFieldChecksum() {
        return fieldChecksum;
    }

    public void setFieldChecksum(String fieldChecksum) {
        this.fieldChecksum = fieldChecksum;
    }

    public String getSrcSystem() {
        return srcSystem;
    }

    public void setSrcSystem(String srcSystem) {
        this.srcSystem = srcSystem;
    }

    public LocalDateTime getDTPopulate() {
        return DTPopulate;
    }

    public void setDTPopulate(LocalDateTime DTPopulate) {
        this.DTPopulate = DTPopulate;
    }

    public String getSysPopulate() {
        return sysPopulate;
    }

    public void setSysPopulate(String sysPopulate) {
        this.sysPopulate = sysPopulate;
    }
}
