package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import java.util.List;

@Entity
public class MsSystemParam {
    @Id
    private String paramId;
    private String paramDesc;
    @OneToMany(mappedBy = "msSystemParam")
    private List<MsSystemParamDetail> msSystemParamDetails;
    @OneToMany(mappedBy = "msSystemParam")
    private List<MsTemaApplication> msTemaApplications;

    public String getParamId() {
        return paramId;
    }

    public void setParamId(String paramId) {
        this.paramId = paramId;
    }

    public String getParamDesc() {
        return paramDesc;
    }

    public void setParamDesc(String paramDesc) {
        this.paramDesc = paramDesc;
    }

    public List<MsSystemParamDetail> getMsSystemParamDetails() {
        return msSystemParamDetails;
    }

    public void setMsSystemParamDetails(List<MsSystemParamDetail> msSystemParamDetails) {
        this.msSystemParamDetails = msSystemParamDetails;
    }

    public List<MsTemaApplication> getMsTemaApplications() {
        return msTemaApplications;
    }

    public void setMsTemaApplications(List<MsTemaApplication> msTemaApplications) {
        this.msTemaApplications = msTemaApplications;
    }
}
