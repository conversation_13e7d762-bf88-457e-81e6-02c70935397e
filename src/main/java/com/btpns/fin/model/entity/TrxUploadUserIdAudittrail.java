package com.btpns.fin.model.entity;

import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.btpns.fin.model.TrxUploadUserIdAudittrailModel;
import com.btpns.fin.model.response.ResUploadModel;
import com.google.gson.Gson;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Map;

@Entity
public class TrxUploadUserIdAudittrail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private BigInteger id;
    private String paramDetailId;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime UploadAt;
    private String dataUser;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getParamDetailId() {
        return paramDetailId;
    }

    public void setParamDetailId(String paramDetailId) {
        this.paramDetailId = paramDetailId;
    }

    public LocalDateTime getUploadAt() {
        return UploadAt;
    }

    public void setUploadAt(LocalDateTime uploadAt) {
        UploadAt = uploadAt;
    }

    public String getDataUser() {
        return dataUser;
    }

    public void setDataUser(String dataUser) {
        this.dataUser = dataUser;
    }

    public TrxUploadUserIdAudittrailModel toTrxUploadUserIdAudittrailModel(TrxUploadUserIdAudittrail trxUploadUserIdAudittrail, Map<String, MsUserIDApplication> applicationMap) {
        TrxUploadUserIdAudittrailModel trxUploadUserIdAudittrailModel = new TrxUploadUserIdAudittrailModel();
        trxUploadUserIdAudittrailModel.setId(trxUploadUserIdAudittrail.getId());
        trxUploadUserIdAudittrailModel.setAplikasi(applicationMap.get(this.paramDetailId).getParamDetailDesc());
        trxUploadUserIdAudittrailModel.setUploadAt(DateTimeHelper.getDate(this.UploadAt));
        return trxUploadUserIdAudittrailModel;
    }
}
