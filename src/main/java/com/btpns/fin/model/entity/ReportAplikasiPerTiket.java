package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
public class ReportAplikasiPerTiket {
    @Id
    private BigInteger id;
    private String ticketId;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime CreateDatetime;
    private String aplikasi;
    private Date tanggalEfektif;
    private String tingkatanUser;
    private String attachment;
    private String dataNIK;
    private String dataNamaLengkap;
    private String dataJabatan;
    private String dataUserId;
    private String dataKodeCabang;
    private String dataNamaCabang;
    private String dataEmail;
    private String dataTelepon;
    private String jenisPengajuan;
    private String alasanPengajuan;
    private String deskripsi;
    private String kategori;
    private String currentState;
    private String currentStateDesc;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime doneDt;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime currentStateDT;
    private String upmInputNIK;
    private String upmCheckerNIK;
    private String infoTambahan;
    private Date masaBerlakuSampai;
    private String puk1NIK;
    private String puk1Nama;
    private String puk1Jabatan;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime puk1Dt;
    private String puk2NIK;
    private String puk2Nama;
    private String puk2Jabatan;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime puk2Dt;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public Date getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(Date tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getDataNIK() {
        return dataNIK;
    }

    public void setDataNIK(String dataNIK) {
        this.dataNIK = dataNIK;
    }

    public String getDataNamaLengkap() {
        return dataNamaLengkap;
    }

    public void setDataNamaLengkap(String dataNamaLengkap) {
        this.dataNamaLengkap = dataNamaLengkap;
    }

    public String getDataUserId() {
        return dataUserId;
    }

    public void setDataUserId(String dataUserId) {
        this.dataUserId = dataUserId;
    }

    public String getDataKodeCabang() {
        return dataKodeCabang;
    }

    public void setDataKodeCabang(String dataKodeCabang) {
        this.dataKodeCabang = dataKodeCabang;
    }

    public String getDataNamaCabang() {
        return dataNamaCabang;
    }

    public void setDataNamaCabang(String dataNamaCabang) {
        this.dataNamaCabang = dataNamaCabang;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getDeskripsi() {
        return deskripsi;
    }

    public void setDeskripsi(String deskripsi) {
        this.deskripsi = deskripsi;
    }

    public String getKategori() {
        return kategori;
    }

    public void setKategori(String kategori) {
        this.kategori = kategori;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public String getCurrentStateDesc() {
        return currentStateDesc;
    }

    public void setCurrentStateDesc(String currentStateDesc) {
        this.currentStateDesc = currentStateDesc;
    }

    public LocalDateTime getDoneDt() {
        return doneDt;
    }

    public void setDoneDt(LocalDateTime doneDt) {
        this.doneDt = doneDt;
    }

    public LocalDateTime getCurrentStateDT() {
        return currentStateDT;
    }

    public void setCurrentStateDT(LocalDateTime currentStateDT) {
        this.currentStateDT = currentStateDT;
    }

    public String getUpmInputNIK() {
        return upmInputNIK;
    }

    public void setUpmInputNIK(String upmInputNIK) {
        this.upmInputNIK = upmInputNIK;
    }

    public String getUpmCheckerNIK() {
        return upmCheckerNIK;
    }

    public void setUpmCheckerNIK(String upmCheckerNIK) {
        this.upmCheckerNIK = upmCheckerNIK;
    }

    public String getInfoTambahan() {
        return infoTambahan;
    }

    public void setInfoTambahan(String infoTambahan) {
        this.infoTambahan = infoTambahan;
    }

    public Date getMasaBerlakuSampai() {
        return masaBerlakuSampai;
    }

    public void setMasaBerlakuSampai(Date masaBerlakuSampai) {
        this.masaBerlakuSampai = masaBerlakuSampai;
    }

    public LocalDateTime getCreateDatetime() {
        return CreateDatetime;
    }

    public void setCreateDatetime(LocalDateTime CreateDatetime) {
        this.CreateDatetime = CreateDatetime;
    }

    public String getTingkatanUser() {
        return tingkatanUser;
    }

    public void setTingkatanUser(String tingkatanUser) {
        this.tingkatanUser = tingkatanUser;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    public String getDataJabatan() {
        return dataJabatan;
    }

    public void setDataJabatan(String dataJabatan) {
        this.dataJabatan = dataJabatan;
    }

    public String getDataEmail() {
        return dataEmail;
    }

    public void setDataEmail(String dataEmail) {
        this.dataEmail = dataEmail;
    }

    public String getDataTelepon() {
        return dataTelepon;
    }

    public void setDataTelepon(String dataTelepon) {
        this.dataTelepon = dataTelepon;
    }

    public String getPuk1NIK() {
        return puk1NIK;
    }

    public void setPuk1NIK(String puk1NIK) {
        this.puk1NIK = puk1NIK;
    }

    public String getPuk1Nama() {
        return puk1Nama;
    }

    public void setPuk1Nama(String puk1Nama) {
        this.puk1Nama = puk1Nama;
    }

    public String getPuk1Jabatan() {
        return puk1Jabatan;
    }

    public void setPuk1Jabatan(String puk1Jabatan) {
        this.puk1Jabatan = puk1Jabatan;
    }

    public LocalDateTime getPuk1Dt() {
        return puk1Dt;
    }

    public void setPuk1Dt(LocalDateTime puk1Dt) {
        this.puk1Dt = puk1Dt;
    }

    public String getPuk2NIK() {
        return puk2NIK;
    }

    public void setPuk2NIK(String puk2NIK) {
        this.puk2NIK = puk2NIK;
    }

    public String getPuk2Nama() {
        return puk2Nama;
    }

    public void setPuk2Nama(String puk2Nama) {
        this.puk2Nama = puk2Nama;
    }

    public String getPuk2Jabatan() {
        return puk2Jabatan;
    }

    public void setPuk2Jabatan(String puk2Jabatan) {
        this.puk2Jabatan = puk2Jabatan;
    }

    public LocalDateTime getPuk2Dt() {
        return puk2Dt;
    }

    public void setPuk2Dt(LocalDateTime puk2Dt) {
        this.puk2Dt = puk2Dt;
    }
}
