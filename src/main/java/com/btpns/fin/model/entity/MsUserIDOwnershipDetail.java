package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class MsUserIDOwnershipDetail {
    private String nik;
    private String namaUser;
    private String kewenangan;
    private String jabatan;
    private String unitKerja;
    @Id
    private String aplikasi;

    public MsUserIDOwnershipDetail() {
    }

    public MsUserIDOwnershipDetail(String nik, String namaUser, String kewenangan, String jabatan, String unitKerja, String aplikasi) {
        this.nik = nik;
        this.namaUser = namaUser;
        this.kewenangan = kewenangan;
        this.jabatan = jabatan;
        this.unitKerja = unitKerja;
        this.aplikasi = aplikasi;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNamaUser() {
        return namaUser;
    }

    public void setNamaUser(String namaUser) {
        this.namaUser = namaUser;
    }

    public String getKewenangan() {
        return kewenangan;
    }

    public void setKewenangan(String kewenangan) {
        this.kewenangan = kewenangan;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getUnitKerja() {
        return unitKerja;
    }

    public void setUnitKerja(String unitKerja) {
        this.unitKerja = unitKerja;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }
}
