package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigInteger;

@Entity
public class ReportProductivity {
    @Id
    String nik;
    String nama;
    private BigInteger countPerTiket;
    private BigInteger countPerAplikasi;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public BigInteger getCountPerTiket() {
        return countPerTiket;
    }

    public void setCountPerTiket(BigInteger countPerTiket) {
        this.countPerTiket = countPerTiket;
    }

    public BigInteger getCountPerAplikasi() {
        return countPerAplikasi;
    }

    public void setCountPerAplikasi(BigInteger countPerAplikasi) {
        this.countPerAplikasi = countPerAplikasi;
    }
}
