package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
public class TrxPUKVendor {
    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private BigInteger id;
    private String nikVendor;
    private String nikPUK;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    @CreationTimestamp
    private LocalDateTime createDatetime;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    @UpdateTimestamp
    private LocalDateTime updateDateTime;
    private Date masaBerlakuSampai;
    private String nameVendor;
    private String occupationVendor;
    private String occupationDescVendor;
    private String namePUK;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getNikVendor() {
        return nikVendor;
    }

    public void setNikVendor(String nikVendor) {
        this.nikVendor = nikVendor;
    }

    public String getNikPUK() {
        return nikPUK;
    }

    public void setNikPUK(String nikPUK) {
        this.nikPUK = nikPUK;
    }

    public LocalDateTime getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(LocalDateTime createDatetime) {
        this.createDatetime = createDatetime;
    }

    public LocalDateTime getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(LocalDateTime updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public Date getMasaBerlakuSampai() {
        return masaBerlakuSampai;
    }

    public void setMasaBerlakuSampai(Date masaBerlakuSampai) {
        this.masaBerlakuSampai = masaBerlakuSampai;
    }

    public String getNameVendor() {
        return nameVendor;
    }

    public void setNameVendor(String nameVendor) {
        this.nameVendor = nameVendor;
    }

    public String getOccupationVendor() {
        return occupationVendor;
    }

    public void setOccupationVendor(String occupationVendor) {
        this.occupationVendor = occupationVendor;
    }

    public String getOccupationDescVendor() {
        return occupationDescVendor;
    }

    public void setOccupationDescVendor(String occupationDescVendor) {
        this.occupationDescVendor = occupationDescVendor;
    }

    public String getNamePUK() {
        return namePUK;
    }

    public void setNamePUK(String namePUK) {
        this.namePUK = namePUK;
    }
}
