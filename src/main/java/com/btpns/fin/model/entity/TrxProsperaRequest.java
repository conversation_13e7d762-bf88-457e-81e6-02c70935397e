package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
public class TrxProsperaRequest {
    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private BigInteger id;
    private String ticketId;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDateTime;
    private String tujuan;
    private String alasan;
    private String payloadRequest;
    private String payloadResponse;
    private String status;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getTujuan() {
        return tujuan;
    }

    public void setTujuan(String tujuan) {
        this.tujuan = tujuan;
    }

    public String getAlasan() {
        return alasan;
    }

    public void setAlasan(String alasan) {
        this.alasan = alasan;
    }

    public String getPayloadRequest() {
        return payloadRequest;
    }

    public void setPayloadRequest(String payloadRequest) {
        this.payloadRequest = payloadRequest;
    }

    public String getPayloadResponse() {
        return payloadResponse;
    }

    public void setPayloadResponse(String payloadResponse) {
        this.payloadResponse = payloadResponse;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
