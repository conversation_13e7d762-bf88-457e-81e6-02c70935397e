package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
public class PUKApprovalReminder {
    @Id
    private BigInteger id;
    private String ticketId;
    private LocalDateTime createDateTime;
    private String currentState;
    private String currentStateDesc;
    private LocalDateTime currentStateDT;
    private Integer approvalInterval;
    private String dataNik;
    private String dataNamaLengkap;
    private String dataJabatan;
    private String dataKodeCabang;
    private String dataNamaCabang;
    private String aplikasi;
    private String jenisPengajuan;
    private String deskripsi;
    private String alasanPengajuan;
    private String infoTambahan;
    private Integer puk1ApprovalReminder;
    private Integer puk2ApprovalReminder;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public String getCurrentStateDesc() {
        return currentStateDesc;
    }

    public void setCurrentStateDesc(String currentStateDesc) {
        this.currentStateDesc = currentStateDesc;
    }

    public LocalDateTime getCurrentStateDT() {
        return currentStateDT;
    }

    public void setCurrentStateDT(LocalDateTime currentStateDT) {
        this.currentStateDT = currentStateDT;
    }

    public Integer getApprovalInterval() {
        return approvalInterval;
    }

    public void setApprovalInterval(Integer approvalInterval) {
        this.approvalInterval = approvalInterval;
    }

    public String getDataNik() {
        return dataNik;
    }

    public void setDataNik(String dataNik) {
        this.dataNik = dataNik;
    }

    public String getDataNamaLengkap() {
        return dataNamaLengkap;
    }

    public void setDataNamaLengkap(String dataNamaLengkap) {
        this.dataNamaLengkap = dataNamaLengkap;
    }

    public String getDataJabatan() {
        return dataJabatan;
    }

    public void setDataJabatan(String dataJabatan) {
        this.dataJabatan = dataJabatan;
    }

    public String getDataKodeCabang() {
        return dataKodeCabang;
    }

    public void setDataKodeCabang(String dataKodeCabang) {
        this.dataKodeCabang = dataKodeCabang;
    }

    public String getDataNamaCabang() {
        return dataNamaCabang;
    }

    public void setDataNamaCabang(String dataNamaCabang) {
        this.dataNamaCabang = dataNamaCabang;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getDeskripsi() {
        return deskripsi;
    }

    public void setDeskripsi(String deskripsi) {
        this.deskripsi = deskripsi;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getInfoTambahan() {
        return infoTambahan;
    }

    public void setInfoTambahan(String infoTambahan) {
        this.infoTambahan = infoTambahan;
    }

    public Integer getPuk1ApprovalReminder() {
        return puk1ApprovalReminder;
    }

    public void setPuk1ApprovalReminder(Integer puk1ApprovalReminder) {
        this.puk1ApprovalReminder = puk1ApprovalReminder;
    }

    public Integer getPuk2ApprovalReminder() {
        return puk2ApprovalReminder;
    }

    public void setPuk2ApprovalReminder(Integer puk2ApprovalReminder) {
        this.puk2ApprovalReminder = puk2ApprovalReminder;
    }
}
