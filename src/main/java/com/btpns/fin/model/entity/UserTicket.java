package com.btpns.fin.model.entity;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.btpns.fin.model.UserTicketModel;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
public class UserTicket {
    @Id
    private String ticketId;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDateTime;
    private String currentState;
    private String currentStateDesc;
    private Date tanggalEfektif;
    private String dataNik;
    private String dataNamaLengkap;
    private String dataJabatan;
    private String dataKodeCabang;
    private String dataNamaCabang;
    private String nikRequester;
    private String puk1NIK;
    private String puk2NIK;
    private String aplikasi;
    private String jenisPengajuan;
    private String deskripsiAlasan;
    private String kategori;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public String getCurrentStateDesc() {
        return currentStateDesc;
    }

    public void setCurrentStateDesc(String currentStateDesc) {
        this.currentStateDesc = currentStateDesc;
    }

    public Date getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(Date tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getDataNik() {
        return dataNik;
    }

    public void setDataNik(String dataNik) {
        this.dataNik = dataNik;
    }

    public String getDataNamaLengkap() {
        return dataNamaLengkap;
    }

    public void setDataNamaLengkap(String dataNamaLengkap) {
        this.dataNamaLengkap = dataNamaLengkap;
    }

    public String getDataJabatan() {
        return dataJabatan;
    }

    public void setDataJabatan(String dataJabatan) {
        this.dataJabatan = dataJabatan;
    }

    public String getDataKodeCabang() {
        return dataKodeCabang;
    }

    public void setDataKodeCabang(String dataKodeCabang) {
        this.dataKodeCabang = dataKodeCabang;
    }

    public String getDataNamaCabang() {
        return dataNamaCabang;
    }

    public void setDataNamaCabang(String dataNamaCabang) {
        this.dataNamaCabang = dataNamaCabang;
    }

    public String getNikRequester() {
        return nikRequester;
    }

    public void setNikRequester(String nikRequester) {
        this.nikRequester = nikRequester;
    }

    public String getPuk1NIK() {
        return puk1NIK;
    }

    public void setPuk1NIK(String puk1NIK) {
        this.puk1NIK = puk1NIK;
    }

    public String getPuk2NIK() {
        return puk2NIK;
    }

    public void setPuk2NIK(String puk2NIK) {
        this.puk2NIK = puk2NIK;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getDeskripsiAlasan() {
        return deskripsiAlasan;
    }

    public void setDeskripsiAlasan(String deskripsiAlasan) {
        this.deskripsiAlasan = deskripsiAlasan;
    }

    public String getKategori() {
        return kategori;
    }

    public void setKategori(String kategori) {
        this.kategori = kategori;
    }
}
