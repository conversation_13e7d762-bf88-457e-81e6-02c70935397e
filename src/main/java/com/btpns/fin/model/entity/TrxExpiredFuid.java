package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.util.Date;

@Entity
public class TrxExpiredFuid {
    @Id
    private String ticketId;
    private Date createdDate;
    private Date tanggalEfektif;
    private String nik;
    private String nama;
    private String kodeCabang;
    private String namaCabang;
    private String aplikasi;
    private Date tanggalMasaBerlaku;
    private String jenisPengajuan;
    private String alasanPengajuan;
    private String picProcess;
    private String picApprove;
    private Integer isProcessedByUpm;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getKodeCabang() {
        return kodeCabang;
    }

    public void setKodeCabang(String kodeCabang) {
        this.kodeCabang = kodeCabang;
    }

    public Date getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(Date tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getKodeCabamg() {
        return kodeCabang;
    }

    public void setKodeCabamg(String kodeCabamg) {
        this.kodeCabang = kodeCabamg;
    }

    public String getNamaCabang() {
        return namaCabang;
    }

    public void setNamaCabang(String namaCabang) {
        this.namaCabang = namaCabang;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public Date getTanggalMasaBerlaku() {
        return tanggalMasaBerlaku;
    }

    public void setTanggalMasaBerlaku(Date tanggalMasaBerlaku) {
        this.tanggalMasaBerlaku = tanggalMasaBerlaku;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getPicProcess() {
        return picProcess;
    }

    public void setPicProcess(String picProcess) {
        this.picProcess = picProcess;
    }

    public String getPicApprove() {
        return picApprove;
    }

    public void setPicApprove(String picApprove) {
        this.picApprove = picApprove;
    }

    public Integer getIsProcessedByUpm() {
        return isProcessedByUpm;
    }

    public void setIsProcessedByUpm(Integer isProcessedByUpm) {
        this.isProcessedByUpm = isProcessedByUpm;
    }
}
