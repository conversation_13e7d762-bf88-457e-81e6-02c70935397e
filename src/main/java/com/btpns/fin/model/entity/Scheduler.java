package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
public class Scheduler {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private BigInteger id;
    private String schedulerKey;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime executeDateTime;
    private String status;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getSchedulerKey() {
        return schedulerKey;
    }

    public void setSchedulerKey(String schedulerKey) {
        this.schedulerKey = schedulerKey;
    }

    public LocalDateTime getExecuteDateTime() {
        return executeDateTime;
    }

    public void setExecuteDateTime(LocalDateTime executeDateTime) {
        this.executeDateTime = executeDateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}