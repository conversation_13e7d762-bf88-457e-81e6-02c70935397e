package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
public class TrxFuidRequestAplikasi {
    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private BigInteger id;
    private String ticketId;
    private String aplikasi;
    private String aplikasiName;
    private String periodDate;
    private String periodMonth;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDateTime;
    private String periodDateDone;
    private String periodMonthDone;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getPeriodDate() {
        return periodDate;
    }

    public void setPeriodDate(String periodDate) {
        this.periodDate = periodDate;
    }

    public String getPeriodMonth() {
        return periodMonth;
    }

    public void setPeriodMonth(String periodMonth) {
        this.periodMonth = periodMonth;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getAplikasiName() {
        return aplikasiName;
    }

    public void setAplikasiName(String aplikasiName) {
        this.aplikasiName = aplikasiName;
    }

    public String getPeriodDateDone() {
        return periodDateDone;
    }

    public void setPeriodDateDone(String periodDateDone) {
        this.periodDateDone = periodDateDone;
    }

    public String getPeriodMonthDone() {
        return periodMonthDone;
    }

    public void setPeriodMonthDone(String periodMonthDone) {
        this.periodMonthDone = periodMonthDone;
    }
}
