package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
public class TrxUARApproval {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private BigInteger id;
    private String ticketId;
    private String currentState;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    @UpdateTimestamp
    private LocalDateTime currentStateDT;

    @OneToOne
    @JoinColumn(name = "ticketId", insertable = false, updatable = false)
    @JsonIgnore
    private TrxUARRequest trxUARRequest;

    private String userNik;
    private String userNikStatus;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime userResponseDT;
    private Integer userConfirmation;
    private String userNikNotes;
    private String pukNik;
    private String pukName;
    private String pukOccupation;
    private String pukStatus;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime pukApprovalDT;
    private String pukNotes;
    private String pukDelegationId;
    private String upmMakerNik;
    private String upmMakerStatus;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime upmMakerProcessDT;
    private String upmMakerNotes;
    private String upmMakerAttachment;
    private String upmCheckerNik;
    private String upmCheckerStatus;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime upmCheckerApprovalDT;
    private String upmCheckerNotes;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public LocalDateTime getCurrentStateDT() {
        return currentStateDT;
    }

    public void setCurrentStateDT(LocalDateTime currentStateDT) {
        this.currentStateDT = currentStateDT;
    }

    public TrxUARRequest getTrxUARRequest() {
        return trxUARRequest;
    }

    public void setTrxUARRequest(TrxUARRequest trxUARRequest) {
        this.trxUARRequest = trxUARRequest;
    }

    public String getUserNik() {
        return userNik;
    }

    public void setUserNik(String userNik) {
        this.userNik = userNik;
    }

    public String getUserNikStatus() {
        return userNikStatus;
    }

    public void setUserNikStatus(String userNikStatus) {
        this.userNikStatus = userNikStatus;
    }

    public LocalDateTime getUserResponseDT() {
        return userResponseDT;
    }

    public void setUserResponseDT(LocalDateTime userResponseDT) {
        this.userResponseDT = userResponseDT;
    }

    public Integer getUserConfirmation() {
        return userConfirmation;
    }

    public void setUserConfirmation(Integer userConfirmation) {
        this.userConfirmation = userConfirmation;
    }

    public String getUserNikNotes() {
        return userNikNotes;
    }

    public void setUserNikNotes(String userNikNotes) {
        this.userNikNotes = userNikNotes;
    }

    public String getPukNik() {
        return pukNik;
    }

    public void setPukNik(String pukNik) {
        this.pukNik = pukNik;
    }

    public String getPukName() {
        return pukName;
    }

    public void setPukName(String pukName) {
        this.pukName = pukName;
    }

    public String getPukOccupation() {
        return pukOccupation;
    }

    public void setPukOccupation(String pukOccupation) {
        this.pukOccupation = pukOccupation;
    }

    public String getPukStatus() {
        return pukStatus;
    }

    public void setPukStatus(String pukStatus) {
        this.pukStatus = pukStatus;
    }

    public LocalDateTime getPukApprovalDT() {
        return pukApprovalDT;
    }

    public void setPukApprovalDT(LocalDateTime pukApprovalDT) {
        this.pukApprovalDT = pukApprovalDT;
    }

    public String getPukNotes() {
        return pukNotes;
    }

    public void setPukNotes(String pukNotes) {
        this.pukNotes = pukNotes;
    }

    public String getPukDelegationId() {
        return pukDelegationId;
    }

    public void setPukDelegationId(String pukDelegationId) {
        this.pukDelegationId = pukDelegationId;
    }

    public String getUpmMakerNik() {
        return upmMakerNik;
    }

    public void setUpmMakerNik(String upmMakerNik) {
        this.upmMakerNik = upmMakerNik;
    }

    public String getUpmMakerStatus() {
        return upmMakerStatus;
    }

    public void setUpmMakerStatus(String upmMakerStatus) {
        this.upmMakerStatus = upmMakerStatus;
    }

    public LocalDateTime getUpmMakerProcessDT() {
        return upmMakerProcessDT;
    }

    public void setUpmMakerProcessDT(LocalDateTime upmMakerProcessDT) {
        this.upmMakerProcessDT = upmMakerProcessDT;
    }

    public String getUpmMakerNotes() {
        return upmMakerNotes;
    }

    public void setUpmMakerNotes(String upmMakerNotes) {
        this.upmMakerNotes = upmMakerNotes;
    }

    public String getUpmMakerAttachment() {
        return upmMakerAttachment;
    }

    public void setUpmMakerAttachment(String upmMakerAttachment) {
        this.upmMakerAttachment = upmMakerAttachment;
    }

    public String getUpmCheckerNik() {
        return upmCheckerNik;
    }

    public void setUpmCheckerNik(String upmCheckerNik) {
        this.upmCheckerNik = upmCheckerNik;
    }

    public String getUpmCheckerStatus() {
        return upmCheckerStatus;
    }

    public void setUpmCheckerStatus(String upmCheckerStatus) {
        this.upmCheckerStatus = upmCheckerStatus;
    }

    public LocalDateTime getUpmCheckerApprovalDT() {
        return upmCheckerApprovalDT;
    }

    public void setUpmCheckerApprovalDT(LocalDateTime upmCheckerApprovalDT) {
        this.upmCheckerApprovalDT = upmCheckerApprovalDT;
    }

    public String getUpmCheckerNotes() {
        return upmCheckerNotes;
    }

    public void setUpmCheckerNotes(String upmCheckerNotes) {
        this.upmCheckerNotes = upmCheckerNotes;
    }
}
