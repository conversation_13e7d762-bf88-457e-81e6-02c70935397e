package com.btpns.fin.model.entity;

import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.helper.Mapper;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.util.Date;
import java.util.Map;

@Entity
public class InquiryTicket {
    @Id
    private String ticketId;
    private Date tanggalEfektif;
    private String dataNIK;
    private String dataNamaLengkap;
    private String dataJabatan;
    private String dataKodeCabang;
    private String dataNamaCabang;
    private String aplikasi;
    private String jenisPengajuan;
    private String deskripsiAlasan;
    private String kategori;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public Date getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(Date tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getDataNIK() {
        return dataNIK;
    }

    public void setDataNIK(String dataNIK) {
        this.dataNIK = dataNIK;
    }

    public String getDataNamaLengkap() {
        return dataNamaLengkap;
    }

    public void setDataNamaLengkap(String dataNamaLengkap) {
        this.dataNamaLengkap = dataNamaLengkap;
    }

    public String getDataJabatan() {
        return dataJabatan;
    }

    public void setDataJabatan(String dataJabatan) {
        this.dataJabatan = dataJabatan;
    }

    public String getDataKodeCabang() {
        return dataKodeCabang;
    }

    public void setDataKodeCabang(String dataKodeCabang) {
        this.dataKodeCabang = dataKodeCabang;
    }

    public String getDataNamaCabang() {
        return dataNamaCabang;
    }

    public void setDataNamaCabang(String dataNamaCabang) {
        this.dataNamaCabang = dataNamaCabang;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getDeskripsiAlasan() {
        return deskripsiAlasan;
    }

    public void setDeskripsiAlasan(String deskripsiAlasan) {
        this.deskripsiAlasan = deskripsiAlasan;
    }

    public String getKategori() {
        return kategori;
    }

    public void setKategori(String kategori) {
        this.kategori = kategori;
    }
}
