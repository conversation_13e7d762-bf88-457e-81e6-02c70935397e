package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.btpns.fin.model.MsFAQTemaModel;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
public class MsFAQTema {
    @Id
    private String faqId;
    private String contentTitle;
    private String contentDesc;
    private Integer contentOrder;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createdAt;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime updatedAt;
    private Boolean visible;
    private String createdBy;

    public String getFaqId() {
        return faqId;
    }

    public void setFaqId(String faqId) {
        this.faqId = faqId;
    }

    public String getContentTitle() {
        return contentTitle;
    }

    public void setContentTitle(String contentTitle) {
        this.contentTitle = contentTitle;
    }

    public String getContentDesc() {
        return contentDesc;
    }

    public void setContentDesc(String contentDesc) {
        this.contentDesc = contentDesc;
    }

    public Integer getContentOrder() {
        return contentOrder;
    }

    public void setContentOrder(Integer contentOrder) {
        this.contentOrder = contentOrder;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public MsFAQTemaModel toMsFAQTemaModel(){
        MsFAQTemaModel msFAQTemaModel = new MsFAQTemaModel();
        msFAQTemaModel.setFaqId(this.faqId);
        msFAQTemaModel.setContentTitle(this.contentTitle);
        msFAQTemaModel.setContentDesc(this.contentDesc);
        msFAQTemaModel.setContentOrder(this.contentOrder);
        msFAQTemaModel.setVisible(this.visible);
        return msFAQTemaModel;
    }
}
