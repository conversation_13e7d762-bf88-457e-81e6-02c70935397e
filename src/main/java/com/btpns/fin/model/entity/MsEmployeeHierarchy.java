package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Entity
public class MsEmployeeHierarchy {
    @Id
    private String nik;
    private String fullName;
    private String dtJoin;
    private String dtPermanent;
    private String occupationDesc;
    private String organization;
    private String location;
    private String directSupervisorNik;
    private String directSupervisorName;
    private String directSupervisorOccupation;
    private String directSupervisorOrganization;
    private String directSupervisor2Nik;
    private String directSupervisor2Name;
    private String directSupervisor2Occupation;
    private String directSupervisor2Organization;
    private String dtTermination;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime dtPopulateSource;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime dtPopulate;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getDtJoin() {
        return dtJoin;
    }

    public void setDtJoin(String dtJoin) {
        this.dtJoin = dtJoin;
    }

    public String getDtPermanent() {
        return dtPermanent;
    }

    public void setDtPermanent(String dtPermanent) {
        this.dtPermanent = dtPermanent;
    }

    public String getOccupationDesc() {
        return occupationDesc;
    }

    public void setOccupationDesc(String occupationDesc) {
        this.occupationDesc = occupationDesc;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDirectSupervisorNik() {
        return directSupervisorNik;
    }

    public void setDirectSupervisorNik(String directSupervisorNik) {
        this.directSupervisorNik = directSupervisorNik;
    }

    public String getDirectSupervisorName() {
        return directSupervisorName;
    }

    public void setDirectSupervisorName(String directSupervisorName) {
        this.directSupervisorName = directSupervisorName;
    }

    public String getDirectSupervisorOccupation() {
        return directSupervisorOccupation;
    }

    public void setDirectSupervisorOccupation(String directSupervisorOccupation) {
        this.directSupervisorOccupation = directSupervisorOccupation;
    }

    public String getDirectSupervisorOrganization() {
        return directSupervisorOrganization;
    }

    public void setDirectSupervisorOrganization(String directSupervisorOrganization) {
        this.directSupervisorOrganization = directSupervisorOrganization;
    }

    public String getDirectSupervisor2Nik() {
        return directSupervisor2Nik;
    }

    public void setDirectSupervisor2Nik(String directSupervisor2Nik) {
        this.directSupervisor2Nik = directSupervisor2Nik;
    }

    public String getDirectSupervisor2Name() {
        return directSupervisor2Name;
    }

    public void setDirectSupervisor2Name(String directSupervisor2Name) {
        this.directSupervisor2Name = directSupervisor2Name;
    }

    public String getDirectSupervisor2Occupation() {
        return directSupervisor2Occupation;
    }

    public void setDirectSupervisor2Occupation(String directSupervisor2Occupation) {
        this.directSupervisor2Occupation = directSupervisor2Occupation;
    }

    public String getDirectSupervisor2Organization() {
        return directSupervisor2Organization;
    }

    public void setDirectSupervisor2Organization(String directSupervisor2Organization) {
        this.directSupervisor2Organization = directSupervisor2Organization;
    }

    public String getDtTermination() {
        return dtTermination;
    }

    public void setDtTermination(String dtTermination) {
        this.dtTermination = dtTermination;
    }

    public LocalDateTime getDtPopulateSource() {
        return dtPopulateSource;
    }

    public void setDtPopulateSource(LocalDateTime dtPopulateSource) {
        this.dtPopulateSource = dtPopulateSource;
    }

    public LocalDateTime getDtPopulate() {
        return dtPopulate;
    }

    public void setDtPopulate(LocalDateTime dtPopulate) {
        this.dtPopulate = dtPopulate;
    }
}
