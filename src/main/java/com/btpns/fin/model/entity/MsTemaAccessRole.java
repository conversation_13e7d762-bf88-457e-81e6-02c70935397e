package com.btpns.fin.model.entity;

import com.btpns.fin.model.TemaAccessRoleModel;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
public class MsTemaAccessRole {
    private String systemParamId;
    @Id
    private String accessRoleCode;
    private String accessRoleDesc;
    @ManyToOne
    @JoinColumn(name = "systemParamId", insertable = false, updatable = false)
    private MsSystemParam msSystemParam;

    public String getSystemParamId() {
        return systemParamId;
    }

    public void setSystemParamId(String systemParamId) {
        this.systemParamId = systemParamId;
    }

    public String getAccessRoleCode() {
        return accessRoleCode;
    }

    public void setAccessRoleCode(String accessRoleCode) {
        this.accessRoleCode = accessRoleCode;
    }

    public String getAccessRoleDesc() {
        return accessRoleDesc;
    }

    public void setAccessRoleDesc(String accessRoleDesc) {
        this.accessRoleDesc = accessRoleDesc;
    }

    public MsSystemParam getMsSystemParam() {
        return msSystemParam;
    }

    public void setMsSystemParam(MsSystemParam msSystemParam) {
        this.msSystemParam = msSystemParam;
    }

    public TemaAccessRoleModel toTemaAccessRoleModel(){
        TemaAccessRoleModel temaAccessRoleModel = new TemaAccessRoleModel();
        temaAccessRoleModel.setAccessRoleCode(this.accessRoleCode);
        temaAccessRoleModel.setAccessRoleDesc(this.accessRoleDesc);
        return temaAccessRoleModel;
    }
}
