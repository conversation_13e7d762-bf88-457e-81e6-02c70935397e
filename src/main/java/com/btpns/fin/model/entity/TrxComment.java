package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.btpns.fin.helper.VarbinaryConverter;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Entity
public class TrxComment {
    @Id
    private String commentId;

    private String ticketId;

    private String nikComment;

    private String namaComment;

    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDateTime;

    private String status;

    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime readDateTime;

    private String content;

    private String picUpmMakerNik;

    private String picUpmMakerNama;

    private String attachment;

    public String getCommentId() {
        return commentId;
    }

    public void setCommentId(String commentId) {
        this.commentId = commentId;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getNikComment() {
        return nikComment;
    }

    public void setNikComment(String nikComment) {
        this.nikComment = nikComment;
    }

    public String getNamaComment() {
        return namaComment;
    }

    public void setNamaComment(String namaComment) {
        this.namaComment = namaComment;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getReadDateTime() {
        return readDateTime;
    }

    public void setReadDateTime(LocalDateTime readDateTime) {
        this.readDateTime = readDateTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPicUpmMakerNik() {
        return picUpmMakerNik;
    }

    public void setPicUpmMakerNik(String picUpmMakerNik) {
        this.picUpmMakerNik = picUpmMakerNik;
    }

    public String getPicUpmMakerNama() {
        return picUpmMakerNama;
    }

    public void setPicUpmMakerNama(String picUpmMakerNama) {
        this.picUpmMakerNama = picUpmMakerNama;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }
}
