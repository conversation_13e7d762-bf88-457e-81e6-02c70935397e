package com.btpns.fin.model.entity;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.Constants;
import com.btpns.fin.model.DataAplikasiModel;
import com.btpns.fin.model.UserIDAppDetailModel;
import com.btpns.fin.model.UserIDAppModel;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class MsUserIDApplication {
    private String paramId;
    @Id
    private String paramDetailId;
    private String paramDetailDesc;
    private String type;
    private Boolean visible;
    private Boolean uar;

    public MsUserIDApplication() {
    }

    public MsUserIDApplication(String paramId, String paramDetailId, String paramDetailDesc, String type, Boolean visible, Boolean uar) {
        this.paramId = paramId;
        this.paramDetailId = paramDetailId;
        this.paramDetailDesc = paramDetailDesc;
        this.type = type;
        this.visible = visible;
        this.uar = uar;
    }

    public String getParamId() {
        return paramId;
    }

    public void setParamId(String paramId) {
        this.paramId = paramId;
    }

    public String getParamDetailId() {
        return paramDetailId;
    }

    public void setParamDetailId(String paramDetailId) {
        this.paramDetailId = paramDetailId;
    }

    public String getParamDetailDesc() {
        return paramDetailDesc;
    }

    public void setParamDetailDesc(String paramDetailDesc) {
        this.paramDetailDesc = paramDetailDesc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean isVisible() {
        return visible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.visible = isVisible;
    }

    public Boolean isUar() {
        return uar;
    }

    public void setIsUar(Boolean uar) {
        this.uar = uar;
    }

    public UserIDAppModel toUserIDAppModel() {
        String desc = CommonHelper.resolveParamDetailDescS4(this.paramDetailDesc);
        return new UserIDAppModel(this.paramDetailId, desc, this.type, this.visible, this.uar);
    }

    public UserIDAppDetailModel toUserIDAppDetailModel() {
        String status = Boolean.TRUE.equals(this.isVisible()) ? Constants.AKTIF : Constants.TIDAK_AKTIF;
        String isUar = Boolean.TRUE.equals(this.isUar()) ? Constants.IYA : Constants.TIDAK;
        String desc = CommonHelper.resolveParamDetailDescS4(this.paramDetailDesc);

        return new UserIDAppDetailModel(this.type, this.paramDetailId, desc, status, isUar);
    }

    public DataAplikasiModel toDataAplikasiModel() {
        DataAplikasiModel dataAplikasiModel = new DataAplikasiModel();
        dataAplikasiModel.setCode(this.paramDetailId);
        dataAplikasiModel.setDesc(CommonHelper.resolveParamDetailDescS4(this.paramDetailDesc));

        return dataAplikasiModel;
    }
}
