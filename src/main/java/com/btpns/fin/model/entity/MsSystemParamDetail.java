package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Entity
public class MsSystemParamDetail {
    private String paramId;
    @Id
    private String paramDetailId;
    private String paramDetailDesc;
    private String status;

    @ManyToOne
    @JoinColumn(name = "paramId", insertable = false, updatable = false)
    private MsSystemParam msSystemParam;

    public String getParamId() {
        return paramId;
    }

    public void setParamId(String paramId) {
        this.paramId = paramId;
    }

    public String getParamDetailId() {
        return paramDetailId;
    }

    public void setParamDetailId(String paramDetailId) {
        this.paramDetailId = paramDetailId;
    }

    public String getParamDetailDesc() {
        return paramDetailDesc;
    }

    public void setParamDetailDesc(String paramDetailDesc) {
        this.paramDetailDesc = paramDetailDesc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public MsSystemParam getMsSystemParam() {
        return msSystemParam;
    }

    public void setMsSystemParam(MsSystemParam msSystemParam) {
        this.msSystemParam = msSystemParam;
    }
}
