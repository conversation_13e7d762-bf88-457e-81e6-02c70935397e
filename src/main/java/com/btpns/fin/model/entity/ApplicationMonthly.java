package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class ApplicationMonthly {
    @Id
    private String aplikasi;
    private String aplikasiDesc;
    private int total;

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getAplikasiDesc() {
        return aplikasiDesc;
    }

    public void setAplikasiDesc(String aplikasiDesc) {
        this.aplikasiDesc = aplikasiDesc;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
