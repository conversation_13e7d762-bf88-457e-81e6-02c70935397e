package com.btpns.fin.model.entity;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.btpns.fin.model.ApprovalKewenanganLimitModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Entity
public class MsApprovalKewenanganLimit {
    @Id
    private String id;
    private String approvalOccupation;
    @JsonProperty("IsKcKFO")
    private boolean isKcKFO;
    @JsonProperty("IsHO")
    private boolean isHO;
    private Double minTunai;
    private Double maxTunai;
    private Double minNonTunai;
    private Double maxNonTunai;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime updatedAt;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApprovalOccupation() {
        return approvalOccupation;
    }

    public void setApprovalOccupation(String approvalOccupation) {
        this.approvalOccupation = approvalOccupation;
    }

    public boolean isKcKFO() {
        return isKcKFO;
    }

    public void setKcKFO(boolean kcKFO) {
        isKcKFO = kcKFO;
    }

    public boolean isHO() {
        return isHO;
    }

    public void setHO(boolean HO) {
        isHO = HO;
    }

    public Double getMinTunai() {
        return minTunai;
    }

    public void setMinTunai(Double minTunai) {
        this.minTunai = minTunai;
    }

    public Double getMaxTunai() {
        return maxTunai;
    }

    public void setMaxTunai(Double maxTunai) {
        this.maxTunai = maxTunai;
    }

    public Double getMinNonTunai() {
        return minNonTunai;
    }

    public void setMinNonTunai(Double minNonTunai) {
        this.minNonTunai = minNonTunai;
    }

    public Double getMaxNonTunai() {
        return maxNonTunai;
    }

    public void setMaxNonTunai(Double maxNonTunai) {
        this.maxNonTunai = maxNonTunai;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public ApprovalKewenanganLimitModel toApprovalKewenanganLimitModel() {
        ApprovalKewenanganLimitModel approvalKewenanganLimitModel = new ApprovalKewenanganLimitModel();
        approvalKewenanganLimitModel.setId(this.id);
        approvalKewenanganLimitModel.setApprovalOccupation(this.approvalOccupation);
        approvalKewenanganLimitModel.setOfficeApprovalLevel(CommonHelper.generateOfficeApprovalLevel(this.isHO, this.isKcKFO));
        approvalKewenanganLimitModel.setMinTunai(this.minTunai);
        approvalKewenanganLimitModel.setMaxTunai(this.maxTunai);
        approvalKewenanganLimitModel.setMinNonTunai(this.minNonTunai);
        approvalKewenanganLimitModel.setMaxNonTunai(this.maxNonTunai);
        return approvalKewenanganLimitModel;
    }
}
