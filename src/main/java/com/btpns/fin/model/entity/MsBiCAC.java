package com.btpns.fin.model.entity;

import com.btpns.fin.model.UserIDModel;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigInteger;
import java.util.Date;

@Entity
public class MsBiCAC {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private BigInteger id;
    private String idUser;
    private String nik;
    private String namaUser;
    private String email;
    private String institusi;
    private String unitKerja;
    private String grupUser;
    private Date tanggalAktif;
    private Date tanggalNonAktif;
    private String statusUser;
    private String statusLogin;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getIdUser() {
        return idUser;
    }

    public void setIdUser(String idUser) {
        this.idUser = idUser;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNamaUser() {
        return namaUser;
    }

    public void setNamaUser(String namaUser) {
        this.namaUser = namaUser;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getInstitusi() {
        return institusi;
    }

    public void setInstitusi(String institusi) {
        this.institusi = institusi;
    }

    public String getUnitKerja() {
        return unitKerja;
    }

    public void setUnitKerja(String unitKerja) {
        this.unitKerja = unitKerja;
    }

    public String getGrupUser() {
        return grupUser;
    }

    public void setGrupUser(String grupUser) {
        this.grupUser = grupUser;
    }

    public Date getTanggalAktif() {
        return tanggalAktif;
    }

    public void setTanggalAktif(Date tanggalAktif) {
        this.tanggalAktif = tanggalAktif;
    }

    public Date getTanggalNonAktif() {
        return tanggalNonAktif;
    }

    public void setTanggalNonAktif(Date tanggalNonAktif) {
        this.tanggalNonAktif = tanggalNonAktif;
    }

    public String getStatusUser() {
        return statusUser;
    }

    public void setStatusUser(String statusUser) {
        this.statusUser = statusUser;
    }

    public String getStatusLogin() {
        return statusLogin;
    }

    public void setStatusLogin(String statusLogin) {
        this.statusLogin = statusLogin;
    }

    public UserIDModel toUserIDModel() {
        return new UserIDModel(this.getNik(), this.getNamaUser(), "", "", this.unitKerja);
    }
}
