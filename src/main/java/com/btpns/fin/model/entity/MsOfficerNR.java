package com.btpns.fin.model.entity;

import com.btpns.fin.helper.Constants;
import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.model.MsEGLS;
import com.btpns.fin.model.MsT24;
import com.btpns.fin.model.ProsperaUserIDModel;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
public class MsOfficerNR {
    @Id
    private BigInteger msOfficerNRID;
    private String officerSource;
    private BigInteger officerID;
    private String officerCode;
    private String srcSystem;
    private LocalDateTime dtPopulate;
    private String sysPopulate;
    private Double mprosperaOfficerID;
    private Integer mmsID;
    private Integer roleID;
    private String mmsCode;
    private String fieldChecksum;
    private String officerName;
    private String nik;
    private String loginName;
    private String emailName;
    private String roleName;
    private Integer officerStatusCode;
    private String officerStatusDesc;
    private Double amtApprovalLimit;
    private Timestamp dtKafka;
    private String mmsName;
    private LocalDate dtCreated;
    private String departmentCode;
    private String firstName;
    private String lastName;
    private LocalDateTime dtLastLogon;
    private Boolean accountEnabled;
    private String menuID;
    private LocalDateTime dtStartProfile;
    private LocalDateTime dtEndProfile;
    private LocalDateTime dtValidityPassword;
    private Integer kfoID;
    private String kfoCode;
    private String kfoName;
    private String kcsID;
    private String kcsCode;
    private String kcsName;
    private String departmentName;

    public BigInteger getMsOfficerNRID() {
        return msOfficerNRID;
    }

    public void setMsOfficerNRID(BigInteger msOfficerNRID) {
        this.msOfficerNRID = msOfficerNRID;
    }

    public String getOfficerSource() {
        return officerSource;
    }

    public void setOfficerSource(String officerSource) {
        this.officerSource = officerSource;
    }

    public BigInteger getOfficerID() {
        return officerID;
    }

    public void setOfficerID(BigInteger officerID) {
        this.officerID = officerID;
    }

    public String getOfficerCode() {
        return officerCode;
    }

    public void setOfficerCode(String officerCode) {
        this.officerCode = officerCode;
    }

    public String getSrcSystem() {
        return srcSystem;
    }

    public void setSrcSystem(String srcSystem) {
        this.srcSystem = srcSystem;
    }

    public LocalDateTime getDtPopulate() {
        return dtPopulate;
    }

    public void setDtPopulate(LocalDateTime dtPopulate) {
        this.dtPopulate = dtPopulate;
    }

    public String getSysPopulate() {
        return sysPopulate;
    }

    public void setSysPopulate(String sysPopulate) {
        this.sysPopulate = sysPopulate;
    }

    public Double getMprosperaOfficerID() {
        return mprosperaOfficerID;
    }

    public void setMprosperaOfficerID(Double mprosperaOfficerID) {
        this.mprosperaOfficerID = mprosperaOfficerID;
    }

    public Integer getMmsID() {
        return mmsID;
    }

    public void setMmsID(Integer mmsID) {
        this.mmsID = mmsID;
    }

    public Integer getRoleID() {
        return roleID;
    }

    public void setRoleID(Integer roleID) {
        this.roleID = roleID;
    }

    public String getMmsCode() {
        return mmsCode;
    }

    public void setMmsCode(String mmsCode) {
        this.mmsCode = mmsCode;
    }

    public String getFieldChecksum() {
        return fieldChecksum;
    }

    public void setFieldChecksum(String fieldChecksum) {
        this.fieldChecksum = fieldChecksum;
    }

    public String getOfficerName() {
        return officerName;
    }

    public void setOfficerName(String officerName) {
        this.officerName = officerName;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getEmailName() {
        return emailName;
    }

    public void setEmailName(String emailName) {
        this.emailName = emailName;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getOfficerStatusCode() {
        return officerStatusCode;
    }

    public void setOfficerStatusCode(Integer officerStatusCode) {
        this.officerStatusCode = officerStatusCode;
    }

    public String getOfficerStatusDesc() {
        return officerStatusDesc;
    }

    public void setOfficerStatusDesc(String officerStatusDesc) {
        this.officerStatusDesc = officerStatusDesc;
    }

    public Double getAmtApprovalLimit() {
        return amtApprovalLimit;
    }

    public void setAmtApprovalLimit(Double amtApprovalLimit) {
        this.amtApprovalLimit = amtApprovalLimit;
    }

    public Timestamp getDtKafka() {
        return dtKafka;
    }

    public void setDtKafka(Timestamp dtKafka) {
        this.dtKafka = dtKafka;
    }

    public String getMmsName() {
        return mmsName;
    }

    public void setMmsName(String mmsName) {
        this.mmsName = mmsName;
    }

    public LocalDate getDtCreated() {
        return dtCreated;
    }

    public void setDtCreated(LocalDate dtCreated) {
        this.dtCreated = dtCreated;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public LocalDateTime getDtLastLogon() {
        return dtLastLogon;
    }

    public void setDtLastLogon(LocalDateTime dtLastLogon) {
        this.dtLastLogon = dtLastLogon;
    }

    public Boolean getAccountEnabled() {
        return accountEnabled;
    }

    public void setAccountEnabled(Boolean accountEnabled) {
        this.accountEnabled = accountEnabled;
    }

    public String getMenuID() {
        return menuID;
    }

    public void setMenuID(String menuID) {
        this.menuID = menuID;
    }

    public LocalDateTime getDtStartProfile() {
        return dtStartProfile;
    }

    public void setDtStartProfile(LocalDateTime dtStartProfile) {
        this.dtStartProfile = dtStartProfile;
    }

    public LocalDateTime getDtEndProfile() {
        return dtEndProfile;
    }

    public void setDtEndProfile(LocalDateTime dtEndProfile) {
        this.dtEndProfile = dtEndProfile;
    }

    public LocalDateTime getDtValidityPassword() {
        return dtValidityPassword;
    }

    public void setDtValidityPassword(LocalDateTime dtValidityPassword) {
        this.dtValidityPassword = dtValidityPassword;
    }

    public Integer getKfoID() {
        return kfoID;
    }

    public void setKfoID(Integer kfoID) {
        this.kfoID = kfoID;
    }

    public String getKfoCode() {
        return kfoCode;
    }

    public void setKfoCode(String kfoCode) {
        this.kfoCode = kfoCode;
    }

    public String getKfoName() {
        return kfoName;
    }

    public void setKfoName(String kfoName) {
        this.kfoName = kfoName;
    }

    public String getKcsID() {
        return kcsID;
    }

    public void setKcsID(String kcsID) {
        this.kcsID = kcsID;
    }

    public String getKcsCode() {
        return kcsCode;
    }

    public void setKcsCode(String kcsCode) {
        this.kcsCode = kcsCode;
    }

    public String getKcsName() {
        return kcsName;
    }

    public void setKcsName(String kcsName) {
        this.kcsName = kcsName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    private String getKodeCabang() {
        if (this.mmsCode != null) {
            return this.mmsCode;
        } else if (this.kfoCode != null) {
            return this.kfoCode;
        } else {
            return this.kcsCode;
        }
    }

    private String getNamaCabang() {
        if (this.mmsName != null) {
            return this.mmsName;
        } else if (this.kfoName != null) {
            return this.kfoName;
        } else {
            return this.kcsName;
        }
    }

    public MsEGLS toEGLS() {
        String accountEnabledString = Boolean.TRUE.equals(this.accountEnabled) ? Constants.AKTIF : Constants.TIDAK_AKTIF;

        return MsEGLS.builder()
                .username(this.officerName)
                .firstName(this.firstName)
                .lastName(this.lastName)
                .roleID(this.roleID)
                .roleName(this.roleName)
                .email(this.emailName)
                .kodeCabang(this.getKodeCabang())
                .namaCabang(this.getNamaCabang())
                .accountEnabled(accountEnabledString)
                .lastLogon(DateTimeHelper.getFullDate(this.dtLastLogon))
                .build();
    }

    public MsT24 toT24() {
        return MsT24.builder()
                .signOnID(this.loginName)
                .userID(this.officerCode)
                .namaUser(this.officerName)
                .kodeCabang(this.getKodeCabang())
                .namaCabang(this.getNamaCabang())
                .deptID(this.departmentCode)
                .namaDepartemen(this.departmentName)
                .menu(this.menuID)
                .tanggalMulaiProfil(DateTimeHelper.getFullDate(this.dtStartProfile))
                .tanggalBerakhirProfil(DateTimeHelper.getFullDate(this.dtEndProfile))
                .validitasPwd(DateTimeHelper.getFullDate(this.dtValidityPassword))
                .tanggalLoginTerakhir(DateTimeHelper.getFullDate(this.dtLastLogon))
                .build();
    }

    public ProsperaUserIDModel toProsperaUserID() {
        ProsperaUserIDModel prospera = new ProsperaUserIDModel();
        prospera.setNik(this.nik);
        prospera.setOfficerName(this.officerName);
        prospera.setRoleName(this.roleName);
        prospera.setLoginName(this.loginName);
        prospera.setMmsCode(this.getKodeCabang());
        prospera.setAmtApprovalLimit(this.amtApprovalLimit);
        prospera.setOfficerCode(this.officerCode);
        prospera.setOfficerStatusDesc(this.officerStatusDesc);
        prospera.setOfficeName(this.getNamaCabang());

        return prospera;
    }
}
