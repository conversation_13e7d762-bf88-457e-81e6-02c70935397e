package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
public class UpmTicket {
    @Id
    private String ticketId;
    private Date tanggalEfektif;
    private String dataNIK;
    private String dataNamaLengkap;
    private String dataUserId;
    private String dataKodeCabang;
    private String dataNamaCabang;
    private String aplikasi;
    private String jenisPengajuan;
    private String deskripsi;
    private String keterangan;
    private String currentState;
    private String upmInputNIK;
    private String upmCheckerNIK;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime currentStateDT;

    public UpmTicket() {
    }

    public UpmTicket(String ticketId, Date tanggalEfektif, String dataNIK, String dataNamaLengkap, String dataUserId, String dataKodeCabang, String dataNamaCabang, String aplikasi, String jenisPengajuan, String deskripsi, String keterangan, String currentState, String upmInputNIK, String upmCheckerNIK, LocalDateTime currentStateDT) {
        this.ticketId = ticketId;
        this.tanggalEfektif = tanggalEfektif;
        this.dataNIK = dataNIK;
        this.dataNamaLengkap = dataNamaLengkap;
        this.dataUserId = dataUserId;
        this.dataKodeCabang = dataKodeCabang;
        this.dataNamaCabang = dataNamaCabang;
        this.aplikasi = aplikasi;
        this.jenisPengajuan = jenisPengajuan;
        this.deskripsi = deskripsi;
        this.keterangan = keterangan;
        this.currentState = currentState;
        this.upmInputNIK = upmInputNIK;
        this.upmCheckerNIK = upmCheckerNIK;
        this.currentStateDT = currentStateDT;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public Date getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(Date tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getDataNIK() {
        return dataNIK;
    }

    public void setDataNIK(String dataNIK) {
        this.dataNIK = dataNIK;
    }

    public String getDataNamaLengkap() {
        return dataNamaLengkap;
    }

    public void setDataNamaLengkap(String dataNamaLengkap) {
        this.dataNamaLengkap = dataNamaLengkap;
    }

    public String getDataUserId() {
        return dataUserId;
    }

    public void setDataUserId(String dataUserId) {
        this.dataUserId = dataUserId;
    }

    public String getDataKodeCabang() {
        return dataKodeCabang;
    }

    public void setDataKodeCabang(String dataKodeCabang) {
        this.dataKodeCabang = dataKodeCabang;
    }

    public String getDataNamaCabang() {
        return dataNamaCabang;
    }

    public void setDataNamaCabang(String dataNamaCabang) {
        this.dataNamaCabang = dataNamaCabang;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getDeskripsi() {
        return deskripsi;
    }

    public void setDeskripsi(String deskripsi) {
        this.deskripsi = deskripsi;
    }

    public String getKeterangan() {
        return keterangan;
    }

    public void setKeterangan(String keterangan) {
        this.keterangan = keterangan;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public String getUpmInputNIK() {
        return upmInputNIK;
    }

    public void setUpmInputNIK(String upmInputNIK) {
        this.upmInputNIK = upmInputNIK;
    }

    public String getUpmCheckerNIK() {
        return upmCheckerNIK;
    }

    public void setUpmCheckerNIK(String upmCheckerNIK) {
        this.upmCheckerNIK = upmCheckerNIK;
    }

    public LocalDateTime getCurrentStateDT() { return currentStateDT; }

    public void setCurrentStateDT(LocalDateTime currentStateDT) { this.currentStateDT = currentStateDT; }
}
