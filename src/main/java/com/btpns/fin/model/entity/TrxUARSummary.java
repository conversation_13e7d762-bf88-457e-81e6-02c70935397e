package com.btpns.fin.model.entity;

import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.model.DataAplikasiModel;
import com.btpns.fin.model.UARSummaryModel;
import com.btpns.fin.model.response.ResUploadModel;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Map;

@Entity
public class TrxUARSummary {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private BigInteger id;
    private String aplikasi;
    private Integer periodYear;
    private String periodQuarter;
    private String refNumber;

    @CreationTimestamp
    private Timestamp createdAt;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public Integer getPeriodYear() {
        return periodYear;
    }

    public void setPeriodYear(Integer periodYear) {
        this.periodYear = periodYear;
    }

    public String getPeriodQuarter() {
        return periodQuarter;
    }

    public void setPeriodQuarter(String periodQuarter) {
        this.periodQuarter = periodQuarter;
    }

    public String getRefNumber() {
        return refNumber;
    }

    public void setRefNumber(String refNumber) {
        this.refNumber = refNumber;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public UARSummaryModel toUARSummaryModel(Map<String, MsUserIDApplication> applicationMap) {
        UARSummaryModel uarSummaryModel = new UARSummaryModel();
        uarSummaryModel.setRefNumber(this.refNumber);
        uarSummaryModel.setTahun(String.valueOf(this.periodYear));
        uarSummaryModel.setTriwulan(this.periodQuarter);
        uarSummaryModel.setAplikasi(new DataAplikasiModel(this.aplikasi, applicationMap.get(this.aplikasi).getParamDetailDesc()));
        uarSummaryModel.setCreatedAt(DateTimeHelper.getFullDate(this.createdAt.toLocalDateTime()));
        return uarSummaryModel;
    }
}
