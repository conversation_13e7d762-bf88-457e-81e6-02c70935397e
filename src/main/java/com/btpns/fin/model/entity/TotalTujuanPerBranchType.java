package com.btpns.fin.model.entity;

import com.btpns.fin.model.TopActivity;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class TotalTujuanPerBranchType {
    @Id
    private String jenisPengajuan;
    private int ho;
    private int mms;
    private int kcKfo;
    private int grandTotal;

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public int getHo() {
        return ho;
    }

    public void setHo(int ho) {
        this.ho = ho;
    }

    public int getMms() {
        return mms;
    }

    public void setMms(int mms) {
        this.mms = mms;
    }

    public int getKcKfo() {
        return kcKfo;
    }

    public void setKcKfo(int kcKfo) {
        this.kcKfo = kcKfo;
    }

    public int getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(int grandTotal) {
        this.grandTotal = grandTotal;
    }

    public TopActivity toTopActivity() {
        TopActivity topActivity = new TopActivity();
        topActivity.setActivity(this.jenisPengajuan);
        topActivity.setNonMMS(this.ho + this.kcKfo);
        topActivity.setMms(this.mms);
        topActivity.setGrandTotal(this.grandTotal);
        return topActivity;
    }
}
