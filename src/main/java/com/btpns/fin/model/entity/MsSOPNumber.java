package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;
import com.btpns.fin.model.SOPNumberModel;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
public class MsSOPNumber {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private BigInteger id;
    private String sopNumber;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime UpdatedAt;

    public MsSOPNumber() {
    }

    public MsSOPNumber(String sopNumber, LocalDateTime updatedAt) {
        this.sopNumber = sopNumber;
        UpdatedAt = updatedAt;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getSopNumber() {
        return sopNumber;
    }

    public void setSopNumber(String sopNumber) {
        this.sopNumber = sopNumber;
    }

    public LocalDateTime getUpdatedAt() {
        return UpdatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        UpdatedAt = updatedAt;
    }

    public SOPNumberModel toMsSOPNumberModel() {
        return new SOPNumberModel(this.getSopNumber());
    }
}
