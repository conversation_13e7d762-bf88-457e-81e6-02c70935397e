package com.btpns.fin.model.entity;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.*;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
public class MsCenterCodeOfficerProspera {
    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private BigInteger id;
    private String cabangDesc;
    private String cabangId;
    private String generatedCenterCodeOfficer = null;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDateTime;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getCabangDesc() {
        return cabangDesc;
    }

    public void setCabangDesc(String cabangDesc) {
        this.cabangDesc = cabangDesc;
    }

    public String getCabangId() {
        return cabangId;
    }

    public void setCabangId(String cabangId) {
        this.cabangId = cabangId;
    }

    public String getGeneratedCenterCodeOfficer() {
        return generatedCenterCodeOfficer;
    }

    public void setGeneratedCenterCodeOfficer(String generatedCenterCodeOfficer) {
        this.generatedCenterCodeOfficer = generatedCenterCodeOfficer;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }
}
