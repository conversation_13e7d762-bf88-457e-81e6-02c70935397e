package com.btpns.fin.model;

import java.util.List;

public class ContentEmail {
    private String nomorTiket;
    private String nik;
    private String nama;
    private String jabatan;
    private String kodeDanNamaCabang;
    private String jenisPengajuan;
    private String aplikasiPengajuan;
    private String deskripsi;
    private String alasanPengajuan;
    private String infoTambahan;
    private String tanggalTiket;
    private String statusTiket;
    private List<AttachmentModel> attachmentUPM;

    public String getNomorTiket() {
        return nomorTiket;
    }

    public void setNomorTiket(String nomorTiket) {
        this.nomorTiket = nomorTiket;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getKodeDanNamaCabang() {
        return kodeDanNamaCabang;
    }

    public void setKodeDanNamaCabang(String kodeDanNamaCabang) {
        this.kodeDanNamaCabang = kodeDanNamaCabang;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getAplikasiPengajuan() {
        return aplikasiPengajuan;
    }

    public void setAplikasiPengajuan(String aplikasiPengajuan) {
        this.aplikasiPengajuan = aplikasiPengajuan;
    }

    public String getDeskripsi() {
        return deskripsi;
    }

    public void setDeskripsi(String deskripsi) {
        this.deskripsi = deskripsi;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getInfoTambahan() {
        return infoTambahan;
    }

    public void setInfoTambahan(String infoTambahan) {
        this.infoTambahan = infoTambahan;
    }

    public String getTanggalTiket() {
        return tanggalTiket;
    }

    public void setTanggalTiket(String tanggalTiket) {
        this.tanggalTiket = tanggalTiket;
    }

    public String getStatusTiket() {
        return statusTiket;
    }

    public void setStatusTiket(String statusTiket) {
        this.statusTiket = statusTiket;
    }

    public List<AttachmentModel> getAttachmentUPM() {
        return attachmentUPM;
    }

    public void setAttachmentUPM(List<AttachmentModel> attachmentUPM) {
        this.attachmentUPM = attachmentUPM;
    }
}
