package com.btpns.fin.model;

import java.util.LinkedHashMap;
import java.util.List;

public class UARDetailModel {
    private String ticketId;
    private String aplikasi;
    private Integer periodeTahun;
    private String periodeTriwulan;
    private String konfirmasiAkses;
    private DataUserIDModel data;
    private StatusModel status;
    private LinkedHashMap<String,Object> progress;
    private Boolean isManualConfirmation;
    private Boolean isReminded;
    private List<AttachmentModel> attachment;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public Integer getPeriodeTahun() {
        return periodeTahun;
    }

    public void setPeriodeTahun(Integer periodeTahun) {
        this.periodeTahun = periodeTahun;
    }

    public String getPeriodeTriwulan() {
        return periodeTriwulan;
    }

    public void setPeriodeTriwulan(String periodeTriwulan) {
        this.periodeTriwulan = periodeTriwulan;
    }

    public String getKonfirmasiAkses() {
        return konfirmasiAkses;
    }

    public void setKonfirmasiAkses(String konfirmasiAkses) {
        this.konfirmasiAkses = konfirmasiAkses;
    }

    public DataUserIDModel getData() {
        return data;
    }

    public void setData(DataUserIDModel data) {
        this.data = data;
    }

    public StatusModel getStatus() {
        return status;
    }

    public void setStatus(StatusModel status) {
        this.status = status;
    }

    public LinkedHashMap<String, Object> getProgress() {
        return progress;
    }

    public void setProgress(LinkedHashMap<String, Object> progress) {
        this.progress = progress;
    }

    public Boolean getManualConfirmation() {
        return isManualConfirmation;
    }

    public void setManualConfirmation(Boolean manualConfirmation) {
        isManualConfirmation = manualConfirmation;
    }

    public Boolean getIsReminded() {
        return isReminded;
    }

    public void setIsReminded(Boolean reminded) {
        isReminded = reminded;
    }

    public List<AttachmentModel> getAttachment() {
        return attachment;
    }

    public void setAttachment(List<AttachmentModel> attachment) {
        this.attachment = attachment;
    }
}
