package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigInteger;

public class NotificationMessage {
    private BigInteger id;
    private String type;
    private String message;
    private boolean notified;

    public NotificationMessage() {
    }

    public NotificationMessage(BigInteger id, String type, String message, boolean notified) {
        this.id = id;
        this.type = type;
        this.message = message;
        this.notified = notified;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @JsonProperty("isNotified")
    public boolean isNotified() {
        return notified;
    }

    public void setNotified(boolean notified) {
        this.notified = notified;
    }
}
