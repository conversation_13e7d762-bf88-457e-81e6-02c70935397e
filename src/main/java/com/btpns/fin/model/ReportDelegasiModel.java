package com.btpns.fin.model;

import java.util.List;

public class ReportDelegasiModel {
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;
    private String startDate;
    private String endDate;
    private List<DelegationDetailModel> data;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() { return totalPages; }

    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

    public long getTotalItems() { return totalItems; }

    public void setTotalItems(long totalItems) { this.totalItems = totalItems; }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public List<DelegationDetailModel> getData() {
        return data;
    }

    public void setData(List<DelegationDetailModel> data) {
        this.data = data;
    }
}
