package com.btpns.fin.model;

import com.btpns.fin.model.response.ResDetailErrorProspera;
import com.btpns.fin.model.response.ResDetailPagesProspera;
import com.btpns.fin.model.response.ResStatusProspera;

import java.util.List;

public class MessageValueProspera<T> {
    private ResStatusProspera responseStatus;
    private T data;
    private List<ResDetailErrorProspera> detailErrors;
    private ResDetailPagesProspera detailPages;

    public ResStatusProspera getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(ResStatusProspera responseStatus) {
        this.responseStatus = responseStatus;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public List<ResDetailErrorProspera> getDetailErrors() {
        return detailErrors;
    }

    public void setDetailErrors(List<ResDetailErrorProspera> detailErrors) {
        this.detailErrors = detailErrors;
    }

    public ResDetailPagesProspera getDetailPages() {
        return detailPages;
    }

    public void setDetailPages(ResDetailPagesProspera detailPages) {
        this.detailPages = detailPages;
    }
}
