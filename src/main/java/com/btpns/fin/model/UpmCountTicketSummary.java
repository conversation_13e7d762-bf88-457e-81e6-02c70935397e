package com.btpns.fin.model;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class UpmCountTicketSummary {
    @Id
    private String ticketId;
    private String status;
    private String currentStateDT;
    private String tanggalEfektif;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCurrentStateDT() {
        return currentStateDT;
    }

    public void setCurrentStateDT(String currentStateDT) {
        this.currentStateDT = currentStateDT;
    }

    public String getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(String tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }
}
