package com.btpns.fin.model;

import com.btpns.fin.model.entity.TrxPUKVendor;

public class AlihDayaUserModel {
    private String nik;
    private String nama;
    private String jabatan;
    private String masaBerlakuSampai;
    private DetailPUKModel pukVendor;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getMasaBerlakuSampai() {
        return masaBerlakuSampai;
    }

    public void setMasaBerlakuSampai(String masaBerlakuSampai) {
        this.masaBerlakuSampai = masaBerlakuSampai;
    }

    public DetailPUKModel getPukVendor() {
        return pukVendor;
    }

    public void setPukVendor(DetailPUKModel pukVendor) {
        this.pukVendor = pukVendor;
    }

    public TrxPUKVendor toTrxPUKVendor() {
        TrxPUKVendor trxPUKVendor = new TrxPUKVendor();
        trxPUKVendor.setNikVendor(this.nik.toUpperCase());
        trxPUKVendor.setNameVendor(this.nama);
        trxPUKVendor.setOccupationVendor(this.jabatan);
        trxPUKVendor.setOccupationDescVendor(this.jabatan);
        trxPUKVendor.setNikPUK(this.pukVendor.getNikPUK());
        trxPUKVendor.setNamePUK(this.pukVendor.getNamaPUK());
        return trxPUKVendor;
    }
}
