package com.btpns.fin.model;

public class PersonnelParent {
    private Integer hierarchyMappingId;
    private String hierarchyName;
    private String shortName;
    private Integer lookupId;
    private Integer hierarchyLevelId;
    private String hierarchyLevelName;
    private Integer ordinal;
    private Integer parentHierarchyLevelId;
    private Integer personnelId;
    private String globalPersonnelNum;
    private String personnelName;

    public Integer getHierarchyMappingId() {
        return hierarchyMappingId;
    }

    public void setHierarchyMappingId(Integer hierarchyMappingId) {
        this.hierarchyMappingId = hierarchyMappingId;
    }

    public String getHierarchyName() {
        return hierarchyName;
    }

    public void setHierarchyName(String hierarchyName) {
        this.hierarchyName = hierarchyName;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public Integer getLookupId() {
        return lookupId;
    }

    public void setLookupId(Integer lookupId) {
        this.lookupId = lookupId;
    }

    public Integer getHierarchyLevelId() {
        return hierarchyLevelId;
    }

    public void setHierarchyLevelId(Integer hierarchyLevelId) {
        this.hierarchyLevelId = hierarchyLevelId;
    }

    public String getHierarchyLevelName() {
        return hierarchyLevelName;
    }

    public void setHierarchyLevelName(String hierarchyLevelName) {
        this.hierarchyLevelName = hierarchyLevelName;
    }

    public Integer getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Integer ordinal) {
        this.ordinal = ordinal;
    }

    public Integer getParentHierarchyLevelId() {
        return parentHierarchyLevelId;
    }

    public void setParentHierarchyLevelId(Integer parentHierarchyLevelId) {
        this.parentHierarchyLevelId = parentHierarchyLevelId;
    }

    public Integer getPersonnelId() {
        return personnelId;
    }

    public void setPersonnelId(Integer personnelId) {
        this.personnelId = personnelId;
    }

    public String getGlobalPersonnelNum() {
        return globalPersonnelNum;
    }

    public void setGlobalPersonnelNum(String globalPersonnelNum) {
        this.globalPersonnelNum = globalPersonnelNum;
    }

    public String getPersonnelName() {
        return personnelName;
    }

    public void setPersonnelName(String personnelName) {
        this.personnelName = personnelName;
    }
}
