package com.btpns.fin.model;

import java.util.List;

public class UserDelegationModel {
    private String nik;
    private String nama;
    private String jabatan;
    private List<UserDelegationModelDetail> possibleUsersForDelegation;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public List<UserDelegationModelDetail> getPossibleUsersForDelegation() {
        return possibleUsersForDelegation;
    }

    public void setPossibleUsersForDelegation(List<UserDelegationModelDetail> possibleUsersForDelegation) {
        this.possibleUsersForDelegation = possibleUsersForDelegation;
    }
}
