package com.btpns.fin.model;

import com.btpns.fin.model.entity.MsEmployeeDirector;

import java.time.LocalDateTime;
import java.util.List;

public class MsEmployeeDirectorLocalMemory {
    private LocalDateTime insertDateTime;
    private List<MsEmployeeDirector> msEmployeeDirector;

    public LocalDateTime getInsertDateTime() {
        return insertDateTime;
    }

    public void setInsertDateTime(LocalDateTime insertDateTime) {
        this.insertDateTime = insertDateTime;
    }

    public List<MsEmployeeDirector> getMsEmployeeDirector() {
        return msEmployeeDirector;
    }

    public void setMsEmployeeDirector(List<MsEmployeeDirector> msEmployeeDirector) {
        this.msEmployeeDirector = msEmployeeDirector;
    }
}
