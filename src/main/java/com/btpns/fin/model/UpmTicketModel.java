package com.btpns.fin.model;

public class UpmTicketModel {
    private String ticketId;
    private String tanggalEfektif;
    private String dataNIK;
    private String dataNama;
    private String dataUserId;
    private String dataKodeCabang;
    private String dataNamaCabang;
    private String aplikasi;
    private String jenisPengajuan;
    private String alasanPengajuan;
    private String keterangan;
    private String status;
    private String statusDesc;
    private String picProcess;
    private String picApprove;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(String tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getDataNIK() {
        return dataNIK;
    }

    public void setDataNIK(String dataNIK) {
        this.dataNIK = dataNIK;
    }

    public String getDataNama() {
        return dataNama;
    }

    public void setDataNama(String dataNama) {
        this.dataNama = dataNama;
    }

    public String getDataUserId() {
        return dataUserId;
    }

    public void setDataUserId(String dataUserId) {
        this.dataUserId = dataUserId;
    }

    public String getDataKodeCabang() {
        return dataKodeCabang;
    }

    public void setDataKodeCabang(String dataKodeCabang) {
        this.dataKodeCabang = dataKodeCabang;
    }

    public String getDataNamaCabang() {
        return dataNamaCabang;
    }

    public void setDataNamaCabang(String dataNamaCabang) {
        this.dataNamaCabang = dataNamaCabang;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getKeterangan() {
        return keterangan;
    }

    public void setKeterangan(String keterangan) {
        this.keterangan = keterangan;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getPicProcess() {
        return picProcess;
    }

    public void setPicProcess(String picProcess) {
        this.picProcess = picProcess;
    }

    public String getPicApprove() {
        return picApprove;
    }

    public void setPicApprove(String picApprove) {
        this.picApprove = picApprove;
    }
}
