package com.btpns.fin.model;

import java.util.Objects;

public class PukModel {
    private String role;
    private String nik;
    private String nama;
    private String jabatan;

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PukModel pukModel = (PukModel) o;
        return Objects.equals(nik, pukModel.nik) && Objects.equals(nama, pukModel.nama) && Objects.equals(jabatan, pukModel.jabatan);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nik, nama, jabatan);
    }
}
