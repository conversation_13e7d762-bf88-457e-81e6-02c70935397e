package com.btpns.fin.model;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

public class TrxSetupParamRequestDetailModel {
    private String ticketId;
    private String tanggalEfektif;
    private DataSetupParamModel data;
    private String nikRequester;
    private DataAplikasiModel kategoriParam;
    private List<DataAplikasiModel> aplikasi;
    private String parameterLama;
    private String parameterBaru;
    private String alasanPengajuan;
    private List<AttachmentModel> attachment;
    private List<AttachmentModel> attachmentUPMInput;
    private String fileName;
    private StatusModel status;
    private String createDate;
    //private ProgressModel progress;
    private LinkedHashMap<String,Object> progress;
    private List<CommentModel> comment;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(String tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public DataSetupParamModel getData() {
        return data;
    }

    public void setData(DataSetupParamModel data) {
        this.data = data;
    }

    public String getNikRequester() {
        return nikRequester;
    }

    public void setNikRequester(String nikRequester) {
        this.nikRequester = nikRequester;
    }

    public DataAplikasiModel getKategoriParam() {
        return kategoriParam;
    }

    public void setKategoriParam(DataAplikasiModel kategoriParam) {
        this.kategoriParam = kategoriParam;
    }

    public List<DataAplikasiModel> getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(List<DataAplikasiModel> aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getParameterLama() {
        return parameterLama;
    }

    public void setParameterLama(String parameterLama) {
        this.parameterLama = parameterLama;
    }

    public String getParameterBaru() {
        return parameterBaru;
    }

    public void setParameterBaru(String parameterBaru) {
        this.parameterBaru = parameterBaru;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public List<AttachmentModel> getAttachment() {
        return attachment;
    }

    public void setAttachment(List<AttachmentModel> attachment) {
        this.attachment = attachment;
    }

    public StatusModel getStatus() {
        return status;
    }

    public void setStatus(StatusModel status) {
        this.status = status;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    /*public ProgressModel getProgress() {
        return progress;
    }

    public void setProgress(ProgressModel progress) {
        this.progress = progress;
    }*/

    public LinkedHashMap<String, Object> getProgress() {
        return progress;
    }

    public void setProgress(LinkedHashMap<String, Object> progress) {
        this.progress = progress;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public List<AttachmentModel> getAttachmentUPMInput() {
        return attachmentUPMInput;
    }

    public void setAttachmentUPMInput(List<AttachmentModel> attachmentUPMInput) {
        this.attachmentUPMInput = attachmentUPMInput;
    }

    public List<CommentModel> getComment() {
        return comment;
    }

    public void setComment(List<CommentModel> comment) {
        this.comment = comment;
    }
}
