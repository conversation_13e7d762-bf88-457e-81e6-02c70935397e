package com.btpns.fin.model;

import com.btpns.fin.model.entity.MsProsperaRole;

public class ProsperaRoleModel {
    private String temaRoleCode;
    private Integer prosperaRoleCode;
    private String roleName;
    private String officeLevel;
    private boolean isActive;

    public ProsperaRoleModel() {
    }

    public ProsperaRoleModel(String temaRoleCode, Integer prosperaRoleCode, String roleName, String officeLevel) {
        this.temaRoleCode = temaRoleCode;
        this.prosperaRoleCode = prosperaRoleCode;
        this.roleName = roleName;
        this.officeLevel = officeLevel;
    }

    public String getTemaRoleCode() {
        return temaRoleCode;
    }

    public void setTemaRoleCode(String temaRoleCode) {
        this.temaRoleCode = temaRoleCode;
    }

    public Integer getProsperaRoleCode() {
        return prosperaRoleCode;
    }

    public void setProsperaRoleCode(Integer prosperaRoleCode) {
        this.prosperaRoleCode = prosperaRoleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getOfficeLevel() {
        return officeLevel;
    }

    public void setOfficeLevel(String officeLevel) {
        this.officeLevel = officeLevel;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public MsProsperaRole toMsProsperaRole() {
        MsProsperaRole msProsperaRole = new MsProsperaRole();
        msProsperaRole.setTemaRoleCode(this.temaRoleCode);
        msProsperaRole.setProsperaRoleCode(this.prosperaRoleCode);
        msProsperaRole.setRoleDesc(this.roleName);
        msProsperaRole.setSystemParamId(this.officeLevel);
        msProsperaRole.setActive(this.isActive);
        return msProsperaRole;
    }
}
