package com.btpns.fin.model;

import com.btpns.fin.model.entity.TrxFuidApproval;
import com.btpns.fin.model.entity.TrxSetupParamApproval;

public class ValidationPUKApprovalModel {
    private TrxFuidApproval tfra;
    private TrxSetupParamApproval tspa;
    private boolean isPuk1;
    private boolean isPuk2;

    public TrxFuidApproval getTfra() {
        return tfra;
    }

    public void setTfra(TrxFuidApproval tfra) {
        this.tfra = tfra;
    }

    public TrxSetupParamApproval getTspa() {
        return tspa;
    }

    public void setTspa(TrxSetupParamApproval tspa) {
        this.tspa = tspa;
    }

    public boolean isPuk1() {
        return isPuk1;
    }

    public void setPuk1(boolean puk1) {
        isPuk1 = puk1;
    }

    public boolean isPuk2() {
        return isPuk2;
    }

    public void setPuk2(boolean puk2) {
        isPuk2 = puk2;
    }
}
