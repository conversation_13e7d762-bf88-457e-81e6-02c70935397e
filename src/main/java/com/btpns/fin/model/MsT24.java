package com.btpns.fin.model;

public class MsT24 {
    private String signOnID;
    private String userID;
    private String namaUser;
    private String kodeCabang;
    private String namaCabang;
    private String deptID;
    private String namaDepartemen;
    private String menu;
    private String tanggalMulaiProfil;
    private String tanggalBerakhirProfil;
    private String validitasPwd;
    private String tanggalLoginTerakhir;

    public String getSignOnID() {
        return signOnID;
    }

    public void setSignOnID(String signOnID) {
        this.signOnID = signOnID;
    }

    public String getUserID() {
        return userID;
    }

    public void setUserID(String userID) {
        this.userID = userID;
    }

    public String getNamaUser() {
        return namaUser;
    }

    public void setNamaUser(String namaUser) {
        this.namaUser = namaUser;
    }

    public String getKodeCabang() {
        return kodeCabang;
    }

    public void setKodeCabang(String kodeCabang) {
        this.kodeCabang = kodeCabang;
    }

    public String getNamaCabang() {
        return namaCabang;
    }

    public void setNamaCabang(String namaCabang) {
        this.namaCabang = namaCabang;
    }

    public String getDeptID() {
        return deptID;
    }

    public void setDeptID(String deptID) {
        this.deptID = deptID;
    }

    public String getNamaDepartemen() {
        return namaDepartemen;
    }

    public void setNamaDepartemen(String namaDepartemen) {
        this.namaDepartemen = namaDepartemen;
    }

    public String getMenu() {
        return menu;
    }

    public void setMenu(String menu) {
        this.menu = menu;
    }

    public String getTanggalMulaiProfil() {
        return tanggalMulaiProfil;
    }

    public void setTanggalMulaiProfil(String tanggalMulaiProfil) {
        this.tanggalMulaiProfil = tanggalMulaiProfil;
    }

    public String getTanggalBerakhirProfil() {
        return tanggalBerakhirProfil;
    }

    public void setTanggalBerakhirProfil(String tanggalBerakhirProfil) {
        this.tanggalBerakhirProfil = tanggalBerakhirProfil;
    }

    public String getValiditasPwd() {
        return validitasPwd;
    }

    public void setValiditasPwd(String validitasPwd) {
        this.validitasPwd = validitasPwd;
    }

    public String getTanggalLoginTerakhir() {
        return tanggalLoginTerakhir;
    }

    public void setTanggalLoginTerakhir(String tanggalLoginTerakhir) {
        this.tanggalLoginTerakhir = tanggalLoginTerakhir;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String signOnID;
        private String userID;
        private String namaUser;
        private String kodeCabang;
        private String namaCabang;
        private String deptID;
        private String namaDepartemen;
        private String menu;
        private String tanggalMulaiProfil;
        private String tanggalBerakhirProfil;
        private String validitasPwd;
        private String tanggalLoginTerakhir;

        private Builder() {
        }

        public Builder signOnID(String signOnID) {
            this.signOnID = signOnID;
            return this;
        }

        public Builder userID(String userID) {
            this.userID = userID;
            return this;
        }

        public Builder namaUser(String namaUser) {
            this.namaUser = namaUser;
            return this;
        }

        public Builder kodeCabang(String kodeCabang) {
            this.kodeCabang = kodeCabang;
            return this;
        }

        public Builder namaCabang(String namaCabang) {
            this.namaCabang = namaCabang;
            return this;
        }

        public Builder deptID(String deptID) {
            this.deptID = deptID;
            return this;
        }

        public Builder namaDepartemen(String namaDepartemen) {
            this.namaDepartemen = namaDepartemen;
            return this;
        }

        public Builder menu(String menu) {
            this.menu = menu;
            return this;
        }

        public Builder tanggalMulaiProfil(String tanggalMulaiProfil) {
            this.tanggalMulaiProfil = tanggalMulaiProfil;
            return this;
        }

        public Builder tanggalBerakhirProfil(String tanggalBerakhirProfil) {
            this.tanggalBerakhirProfil = tanggalBerakhirProfil;
            return this;
        }

        public Builder validitasPwd(String validitasPwd) {
            this.validitasPwd = validitasPwd;
            return this;
        }

        public Builder tanggalLoginTerakhir(String tanggalLoginTerakhir) {
            this.tanggalLoginTerakhir = tanggalLoginTerakhir;
            return this;
        }

        public MsT24 build() {
            MsT24 msT24 = new MsT24();
            msT24.signOnID = this.signOnID;
            msT24.userID = this.userID;
            msT24.namaUser = this.namaUser;
            msT24.kodeCabang = this.kodeCabang;
            msT24.namaCabang = this.namaCabang;
            msT24.deptID = this.deptID;
            msT24.namaDepartemen = this.namaDepartemen;
            msT24.menu = this.menu;
            msT24.tanggalMulaiProfil = this.tanggalMulaiProfil;
            msT24.tanggalBerakhirProfil = this.tanggalBerakhirProfil;
            msT24.validitasPwd = this.validitasPwd;
            msT24.tanggalLoginTerakhir = this.tanggalLoginTerakhir;
            return msT24;
        }
    }
}
