package com.btpns.fin.model;

import com.btpns.fin.model.entity.TotalActivity;
import com.btpns.fin.model.response.ResAnalyticVolumeTrxUser;
import com.btpns.fin.model.response.ResAnalyticVolumeTrxUserDetail;

import java.util.List;

public class SummaryReportUPM {
    private ResAnalyticVolumeTrxUser volumeTrxUPMLast3Months;
    private List<TotalActivity> totalActivity;
    private TotalTujuanPerKategori totalJenisPengajuanPerKategori;
    private Top5Activity top5Activity;

    public ResAnalyticVolumeTrxUser getVolumeTrxUPMLast3Months() {
        return volumeTrxUPMLast3Months;
    }

    public void setVolumeTrxUPMLast3Months(ResAnalyticVolumeTrxUser volumeTrxUPMLast3Months) {
        this.volumeTrxUPMLast3Months = volumeTrxUPMLast3Months;
    }

    public List<TotalActivity> getTotalActivity() {
        return totalActivity;
    }

    public void setTotalActivity(List<TotalActivity> totalActivity) {
        this.totalActivity = totalActivity;
    }

    public TotalTujuanPerKategori getTotalJenisPengajuanPerKategori() {
        return totalJenisPengajuanPerKategori;
    }

    public void setTotalJenisPengajuanPerKategori(TotalTujuanPerKategori totalJenisPengajuanPerKategori) {
        this.totalJenisPengajuanPerKategori = totalJenisPengajuanPerKategori;
    }

    public Top5Activity getTop5Activity() {
        return top5Activity;
    }

    public void setTop5Activity(Top5Activity top5Activity) {
        this.top5Activity = top5Activity;
    }
}
