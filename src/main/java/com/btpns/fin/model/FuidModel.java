package com.btpns.fin.model;

import java.util.List;

public class FuidModel {
    private String tujuan;
    private String alasan;
    private String tanggalEfektif;
    private DataFuidModel data;
    private List<String> aplikasi;
    private String tingkatan;
    private String role;
    private String statusMasaBerlaku;
    private String masaBerlaku;
    private String alasanPengajuan;
    private String infoTambahan;
    private List<AttachmentModel> attachment;
    private String fileName;
    private String tipeLimitTransaksi;
    private Double nominalTransaksi;
    private String unitKerjaLama;
    private String unitKerjaBaru;
    private String tipeKewenanganLimit;
    private ApprovalPukModel approval;
    private String tipeKaryawanBaru;
    private String occupationDesc;
    private String organization;
    private String location;
    private String puk;
    private String upmInputNIK;
    private String upmCheckerNIK;
    private String nikRequester;
    private MutasiModel mutasi;
    private Integer isInActivePersonnelProspera;
    private String picEmailGroup;
    private String picEmailGroupName;
    private String picEmailGroupOccupation;
    private String altPicEmailGroup;
    private String altPicEmailGroupName;
    private String altPicEmailGroupOccupation;

    public String getTujuan() {return tujuan;}

    public String getAlasan() {return alasan;}

    public String getTanggalEfektif() {return tanggalEfektif;}

    public DataFuidModel getData() {return data;}

    public List<String> getAplikasi() {return aplikasi;}

    public String getTingkatan() {return tingkatan;}

    public String getStatusMasaBerlaku() {return statusMasaBerlaku;}

    public String getMasaBerlaku() {return masaBerlaku;}

    public String getAlasanPengajuan() {return alasanPengajuan;}

    public String getInfoTambahan() {return infoTambahan;}

    public List<AttachmentModel> getAttachment() {return attachment;}

    public void setTujuan(String tujuan) {this.tujuan = tujuan;}

    public void setAlasan(String alasan) {this.alasan = alasan;}

    public void setTanggalEfektif(String tanggalEfektif) {this.tanggalEfektif = tanggalEfektif;}

    public void setData(DataFuidModel data) {this.data = data;}

    public void setAplikasi(List<String> aplikasi) {this.aplikasi = aplikasi;}

    public void setTingkatan(String tingkatan) {this.tingkatan = tingkatan;}

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public void setStatusMasaBerlaku(String statusMasaBerlaku) {this.statusMasaBerlaku = statusMasaBerlaku;}

    public void setMasaBerlaku(String masaBerlaku) {this.masaBerlaku = masaBerlaku;}

    public void setAlasanPengajuan(String alasanPengajuan) {this.alasanPengajuan = alasanPengajuan;}

    public void setInfoTambahan(String infoTambahan) {this.infoTambahan = infoTambahan;}

    public void setAttachment(List<AttachmentModel> attachment) {this.attachment = attachment;}

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getTipeLimitTransaksi() {
        return tipeLimitTransaksi;
    }

    public void setTipeLimitTransaksi(String tipeLimitTransaksi) {
        this.tipeLimitTransaksi = tipeLimitTransaksi;
    }

    public Double getNominalTransaksi() {
        return nominalTransaksi;
    }

    public void setNominalTransaksi(Double nominalTransaksi) {
        this.nominalTransaksi = nominalTransaksi;
    }

    public String getUnitKerjaLama() {
        return unitKerjaLama;
    }

    public void setUnitKerjaLama(String unitKerjaLama) {
        this.unitKerjaLama = unitKerjaLama;
    }

    public String getUnitKerjaBaru() {
        return unitKerjaBaru;
    }

    public void setUnitKerjaBaru(String unitKerjaBaru) {
        this.unitKerjaBaru = unitKerjaBaru;
    }

    public String getTipeKewenanganLimit() {
        return tipeKewenanganLimit;
    }

    public void setTipeKewenanganLimit(String tipeKewenanganLimit) {
        this.tipeKewenanganLimit = tipeKewenanganLimit;
    }

    public ApprovalPukModel getApproval() {
        return approval;
    }

    public void setApproval(ApprovalPukModel approval) {
        this.approval = approval;
    }

    public String getTipeKaryawanBaru() {
        return tipeKaryawanBaru;
    }

    public void setTipeKaryawanBaru(String tipeKaryawanBaru) {
        this.tipeKaryawanBaru = tipeKaryawanBaru;
    }

    public String getOccupationDesc() { return occupationDesc; }

    public void setOccupationDesc(String occupationDesc) { this.occupationDesc = occupationDesc; }

    public String getOrganization() { return organization; }

    public void setOrganization(String organization) { this.organization = organization; }

    public String getLocation() { return location; }

    public void setLocation(String location) { this.location = location; }

    public String getPuk() {
        return puk;
    }

    public void setPuk(String puk) {
        this.puk = puk;
    }

    public String getUpmInputNIK() {
        return upmInputNIK;
    }

    public void setUpmInputNIK(String upmInputNIK) {
        this.upmInputNIK = upmInputNIK;
    }

    public String getUpmCheckerNIK() {
        return upmCheckerNIK;
    }

    public void setUpmCheckerNIK(String upmCheckerNIK) {
        this.upmCheckerNIK = upmCheckerNIK;
    }

    public String getNikRequester() {
        return nikRequester;
    }

    public void setNikRequester(String nikRequester) {
        this.nikRequester = nikRequester;
    }

    public MutasiModel getMutasi() { return mutasi; }

    public void setMutasi(MutasiModel mutasi) { this.mutasi = mutasi; }

    public Integer getIsInActivePersonnelProspera() {
        return isInActivePersonnelProspera;
    }

    public void setIsInActivePersonnelProspera(Integer isInActivePersonnelProspera) {
        this.isInActivePersonnelProspera = isInActivePersonnelProspera;
    }

    public String getPicEmailGroup() {
        return picEmailGroup;
    }

    public void setPicEmailGroup(String picEmailGroup) {
        this.picEmailGroup = picEmailGroup;
    }

    public String getPicEmailGroupName() {
        return picEmailGroupName;
    }

    public void setPicEmailGroupName(String picEmailGroupName) {
        this.picEmailGroupName = picEmailGroupName;
    }

    public String getPicEmailGroupOccupation() {
        return picEmailGroupOccupation;
    }

    public void setPicEmailGroupOccupation(String picEmailGroupOccupation) {
        this.picEmailGroupOccupation = picEmailGroupOccupation;
    }

    public String getAltPicEmailGroup() {
        return altPicEmailGroup;
    }

    public void setAltPicEmailGroup(String altPicEmailGroup) {
        this.altPicEmailGroup = altPicEmailGroup;
    }

    public String getAltPicEmailGroupName() {
        return altPicEmailGroupName;
    }

    public void setAltPicEmailGroupName(String altPicEmailGroupName) {
        this.altPicEmailGroupName = altPicEmailGroupName;
    }

    public String getAltPicEmailGroupOccupation() {
        return altPicEmailGroupOccupation;
    }

    public void setAltPicEmailGroupOccupation(String altPicEmailGroupOccupation) {
        this.altPicEmailGroupOccupation = altPicEmailGroupOccupation;
    }
}
