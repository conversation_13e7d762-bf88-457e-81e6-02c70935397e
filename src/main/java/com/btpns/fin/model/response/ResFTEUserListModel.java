package com.btpns.fin.model.response;

import com.btpns.fin.model.FTEUserModel;

import java.util.List;

public class ResFTEUserListModel {
    private List<FTEUserModel> fteUser;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<FTEUserModel> getFteUser() {
        return fteUser;
    }

    public void setFteUser(List<FTEUserModel> fteUser) {
        this.fteUser = fteUser;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }
}
