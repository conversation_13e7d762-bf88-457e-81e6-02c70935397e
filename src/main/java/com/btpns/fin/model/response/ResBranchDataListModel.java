package com.btpns.fin.model.response;

import com.btpns.fin.model.BranchDataModel;

import java.util.List;

public class ResBranchDataListModel {
    private List<BranchDataModel> branch;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<BranchDataModel> getBranch() {
        return branch;
    }

    public void setBranch(List<BranchDataModel> branch) {
        this.branch = branch;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }
}
