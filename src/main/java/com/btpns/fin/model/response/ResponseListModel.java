package com.btpns.fin.model.response;

import java.util.List;

public class ResponseListModel<T> {
    private List<T> data;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }

    public static class Builder<T> {
        private List<T> data;
        private int page;
        private int limit;
        private int totalPages;
        private long totalItems;

        public ResponseListModel.Builder<T> data(List<T> data) {
            this.data = data;
            return this;
        }

        public ResponseListModel.Builder<T> page(int page) {
            this.page = page;
            return this;
        }

        public ResponseListModel.Builder<T> limit(int limit) {
            this.limit = limit;
            return this;
        }

        public ResponseListModel.Builder<T> totalPages(int totalPages) {
            this.totalPages = totalPages;
            return this;
        }

        public ResponseListModel.Builder<T> totalItems(long totalItems) {
            this.totalItems = totalItems;
            return this;
        }

        public ResponseListModel<T> build() {
            ResponseListModel<T> responseListModel = new ResponseListModel<>();
            responseListModel.data = this.data;
            responseListModel.page = this.page;
            responseListModel.limit = this.limit;
            responseListModel.totalPages = this.totalPages;
            responseListModel.totalItems = this.totalItems;
            return responseListModel;
        }
    }

    public static <T> ResponseListModel.Builder<T> builder() {
        return new ResponseListModel.Builder<>();
    }
}
