package com.btpns.fin.model.response;

import com.btpns.fin.model.OfficeAddress;

public class ResOfficeDetailProspera {
    private int officeId;
    private String globalOfficeNum;
    private String officeName;
    private String officeCode;
    private String officeShortName;
    private int officeLevelId;
    private String officeLevelName;
    private int officeStatusId;
    private int officeStatusLookupId;
    private String officeStatusName;
    private Integer parentOfficeId;
    private String parentOfficeCode;
    private String parentOfficeName;
    private String glConfigType;
    private String umkMapping;
    private String syariahMapping;
    private String onlineState;
    private String onlineDate;
    private OfficeAddress address;
    private int costCenter;
    private String openOfficeDate;
    private String manajerSentra;
    private String wakilManajerSentra;
    private int clientPerGroup;
    private int clientPerCenter;

    public int getOfficeId() {
        return officeId;
    }

    public void setOfficeId(int officeId) {
        this.officeId = officeId;
    }

    public String getGlobalOfficeNum() {
        return globalOfficeNum;
    }

    public void setGlobalOfficeNum(String globalOfficeNum) {
        this.globalOfficeNum = globalOfficeNum;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    public String getOfficeShortName() {
        return officeShortName;
    }

    public void setOfficeShortName(String officeShortName) {
        this.officeShortName = officeShortName;
    }

    public int getOfficeLevelId() {
        return officeLevelId;
    }

    public void setOfficeLevelId(int officeLevelId) {
        this.officeLevelId = officeLevelId;
    }

    public String getOfficeLevelName() {
        return officeLevelName;
    }

    public void setOfficeLevelName(String officeLevelName) {
        this.officeLevelName = officeLevelName;
    }

    public int getOfficeStatusId() {
        return officeStatusId;
    }

    public void setOfficeStatusId(int officeStatusId) {
        this.officeStatusId = officeStatusId;
    }

    public int getOfficeStatusLookupId() {
        return officeStatusLookupId;
    }

    public void setOfficeStatusLookupId(int officeStatusLookupId) {
        this.officeStatusLookupId = officeStatusLookupId;
    }

    public String getOfficeStatusName() {
        return officeStatusName;
    }

    public void setOfficeStatusName(String officeStatusName) {
        this.officeStatusName = officeStatusName;
    }

    public Integer getParentOfficeId() {
        return parentOfficeId;
    }

    public void setParentOfficeId(Integer parentOfficeId) {
        this.parentOfficeId = parentOfficeId;
    }

    public String getParentOfficeCode() {
        return parentOfficeCode;
    }

    public void setParentOfficeCode(String parentOfficeCode) {
        this.parentOfficeCode = parentOfficeCode;
    }

    public String getParentOfficeName() {
        return parentOfficeName;
    }

    public void setParentOfficeName(String parentOfficeName) {
        this.parentOfficeName = parentOfficeName;
    }

    public String getGlConfigType() {
        return glConfigType;
    }

    public void setGlConfigType(String glConfigType) {
        this.glConfigType = glConfigType;
    }

    public String getUmkMapping() {
        return umkMapping;
    }

    public void setUmkMapping(String umkMapping) {
        this.umkMapping = umkMapping;
    }

    public String getSyariahMapping() {
        return syariahMapping;
    }

    public void setSyariahMapping(String syariahMapping) {
        this.syariahMapping = syariahMapping;
    }

    public String getOnlineState() {
        return onlineState;
    }

    public void setOnlineState(String onlineState) {
        this.onlineState = onlineState;
    }

    public String getOnlineDate() {
        return onlineDate;
    }

    public void setOnlineDate(String onlineDate) {
        this.onlineDate = onlineDate;
    }

    public OfficeAddress getAddress() {
        return address;
    }

    public void setAddress(OfficeAddress address) {
        this.address = address;
    }

    public int getCostCenter() {
        return costCenter;
    }

    public void setCostCenter(int costCenter) {
        this.costCenter = costCenter;
    }

    public String getOpenOfficeDate() {
        return openOfficeDate;
    }

    public void setOpenOfficeDate(String openOfficeDate) {
        this.openOfficeDate = openOfficeDate;
    }

    public String getManajerSentra() {
        return manajerSentra;
    }

    public void setManajerSentra(String manajerSentra) {
        this.manajerSentra = manajerSentra;
    }

    public String getWakilManajerSentra() {
        return wakilManajerSentra;
    }

    public void setWakilManajerSentra(String wakilManajerSentra) {
        this.wakilManajerSentra = wakilManajerSentra;
    }

    public int getClientPerGroup() {
        return clientPerGroup;
    }

    public void setClientPerGroup(int clientPerGroup) {
        this.clientPerGroup = clientPerGroup;
    }

    public int getClientPerCenter() {
        return clientPerCenter;
    }

    public void setClientPerCenter(int clientPerCenter) {
        this.clientPerCenter = clientPerCenter;
    }
}
