package com.btpns.fin.model.response;

import com.btpns.fin.model.DataAplikasiModel;
import com.btpns.fin.model.UserIDModel;

import java.util.List;

public class ResponseListCustomUserID {
    private DataAplikasiModel aplikasi;
    private List<UserIDModel> data;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public ResponseListCustomUserID() {
    }

    public DataAplikasiModel getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(DataAplikasiModel aplikasi) {
        this.aplikasi = aplikasi;
    }

    public List<UserIDModel> getData() {
        return data;
    }

    public void setData(List<UserIDModel> data) {
        this.data = data;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }

    public static class Builder {
        private DataAplikasiModel aplikasi;
        private List<UserIDModel> data;
        private int page;
        private int limit;
        private int totalPages;
        private long totalItems;

        public Builder() {
            // Initialize default values here if needed
        }

        public Builder aplikasi(DataAplikasiModel aplikasi) {
            this.aplikasi = aplikasi;
            return this;
        }

        public Builder data(List<UserIDModel> data) {
            this.data = data;
            return this;
        }

        public Builder page(int page) {
            this.page = page;
            return this;
        }

        public Builder limit(int limit) {
            this.limit = limit;
            return this;
        }

        public Builder totalPages(int totalPages) {
            this.totalPages = totalPages;
            return this;
        }

        public Builder totalItems(long totalItems) {
            this.totalItems = totalItems;
            return this;
        }

        public ResponseListCustomUserID build() {
            ResponseListCustomUserID response = new ResponseListCustomUserID();
            response.aplikasi = this.aplikasi;
            response.data = this.data;
            response.page = this.page;
            response.limit = this.limit;
            response.totalPages = this.totalPages;
            response.totalItems = this.totalItems;
            return response;
        }
    }
}
