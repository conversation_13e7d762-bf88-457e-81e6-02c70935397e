package com.btpns.fin.model.response;


import com.btpns.fin.model.AlihDayaUserModel;

import java.util.List;

public class ResAlihDayaUserListModel {
    private List<AlihDayaUserModel> alihDayaUser;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<AlihDayaUserModel> getAlihDayaUser() {
        return alihDayaUser;
    }

    public void setAlihDayaUser(List<AlihDayaUserModel> alihDayaUser) {
        this.alihDayaUser = alihDayaUser;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() { return totalPages; }

    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

    public long getTotalItems() { return totalItems; }

    public void setTotalItems(long totalItems) { this.totalItems = totalItems; }
}
