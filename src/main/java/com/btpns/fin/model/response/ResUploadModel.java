package com.btpns.fin.model.response;

import java.util.Map;

import static com.btpns.fin.helper.Constants.*;

public class ResUploadModel {
    private String contentUrl;
    private String contentFilePath;
    private String contentFileName;
    private String contentType;

    public ResUploadModel(Map<String, String> map) {
        this.contentUrl = map.get(CONTENT_URL);
        this.contentFilePath = map.get(CONTENT_FILEPATH);
        this.contentFileName = map.get(CONTENT_FILENAME);
        this.contentType = map.get(CONTENT_TYPE);
    }

    public String getContentUrl() {
        return contentUrl;
    }

    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }

    public String getContentFilePath() {
        return contentFilePath;
    }

    public void setContentFilePath(String contentFilePath) {
        this.contentFilePath = contentFilePath;
    }

    public String getContentFileName() {
        return contentFileName;
    }

    public void setContentFileName(String contentFileName) {
        this.contentFileName = contentFileName;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
}
