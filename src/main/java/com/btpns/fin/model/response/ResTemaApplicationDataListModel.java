package com.btpns.fin.model.response;

import com.btpns.fin.model.MsTemaApplicationModel;
import com.btpns.fin.model.entity.MsTemaApplication;

import java.util.List;

public class ResTemaApplicationDataListModel {
    private List<MsTemaApplicationModel> applicationTemaDetail;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<MsTemaApplicationModel> getApplicationTemaDetail() {
        return applicationTemaDetail;
    }

    public void setApplicationTemaDetail(List<MsTemaApplicationModel> applicationTemaDetail) {
        this.applicationTemaDetail = applicationTemaDetail;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }
}
