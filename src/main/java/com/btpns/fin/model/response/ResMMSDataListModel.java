package com.btpns.fin.model.response;


import com.btpns.fin.model.entity.MMS_UPM;

import java.util.List;

public class ResMMSDataListModel {
    private List<MMS_UPM> mms;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<MMS_UPM> getMms() {
        return mms;
    }

    public void setMms(List<MMS_UPM> mms) {
        this.mms = mms;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }
}
