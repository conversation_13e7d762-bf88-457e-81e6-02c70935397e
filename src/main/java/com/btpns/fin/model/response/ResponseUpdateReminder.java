package com.btpns.fin.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ResponseUpdateReminder {
    private String type;
    private boolean notified;

    public ResponseUpdateReminder() {
    }

    public ResponseUpdateReminder(String type, boolean notified) {
        this.type = type;
        this.notified = notified;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @JsonProperty("isNotified")
    public boolean isNotified() {
        return notified;
    }

    public void setNotified(boolean notified) {
        this.notified = notified;
    }
}
