package com.btpns.fin.model.response;

public class ResponseModel<T> {
    private String type;
    private String status;
    private String statusDesc;
    private T details;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public T getDetails() {
        return details;
    }

    public void setDetails(T details) {
        this.details = details;
    }

    public static class Builder<T> {
        private String type;
        private String status;
        private String statusDesc;
        private T details;

        public Builder<T> type(String type) {
            this.type = type;
            return this;
        }

        public Builder<T> status(String status) {
            this.status = status;
            return this;
        }

        public Builder<T> statusDesc(String statusDesc) {
            this.statusDesc = statusDesc;
            return this;
        }

        public Builder<T> details(T details) {
            this.details = details;
            return this;
        }

        public ResponseModel<T> build() {
            ResponseModel<T> responseModel = new ResponseModel<>();
            responseModel.type = this.type;
            responseModel.status = this.status;
            responseModel.statusDesc = this.statusDesc;
            responseModel.details = this.details;
            return responseModel;
        }
    }

    public static <T> Builder<T> builder() {
        return new Builder<>();
    }
}
