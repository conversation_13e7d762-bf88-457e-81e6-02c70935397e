package com.btpns.fin.model;

public class ExpiredFuidModel {
    private String ticketId;
    private String tanggalEfektif;
    private String nik;
    private String nama;
    private String kodeCabang;
    private String namaCabang;
    private String aplikasi;
    private String jenisPengajuan;
    private String alasanPengajuan;
    private String picProcess;
    private String picApprove;
    private Integer isProcessedByUpm;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getTanggalEfektif() {
        return tanggalEfektif;
    }

    public void setTanggalEfektif(String tanggalEfektif) {
        this.tanggalEfektif = tanggalEfektif;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getKodeCabang() {
        return kodeCabang;
    }

    public void setKodeCabang(String kodeCabang) {
        this.kodeCabang = kodeCabang;
    }

    public String getNamaCabang() {
        return namaCabang;
    }

    public void setNamaCabang(String namaCabang) {
        this.namaCabang = namaCabang;
    }

    public String getAplikasi() {
        return aplikasi;
    }

    public void setAplikasi(String aplikasi) {
        this.aplikasi = aplikasi;
    }

    public String getJenisPengajuan() {
        return jenisPengajuan;
    }

    public void setJenisPengajuan(String jenisPengajuan) {
        this.jenisPengajuan = jenisPengajuan;
    }

    public String getAlasanPengajuan() {
        return alasanPengajuan;
    }

    public void setAlasanPengajuan(String alasanPengajuan) {
        this.alasanPengajuan = alasanPengajuan;
    }

    public String getPicProcess() {
        return picProcess;
    }

    public void setPicProcess(String picProcess) {
        this.picProcess = picProcess;
    }

    public String getPicApprove() {
        return picApprove;
    }

    public void setPicApprove(String picApprove) {
        this.picApprove = picApprove;
    }

    public Integer getIsProcessedByUpm() {
        return isProcessedByUpm;
    }

    public void setIsProcessedByUpm(Integer isProcessedByUpm) {
        this.isProcessedByUpm = isProcessedByUpm;
    }
}
