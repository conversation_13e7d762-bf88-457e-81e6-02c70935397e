package com.btpns.fin.model;

public class UserRoleModel {
    private boolean upmGroup;
    private String role;
    private Boolean isUserPUK;
    private Boolean isUserMMS;
    private Boolean isUserWhitelistMenuProspera;
    private Integer maxUpmLimitForBwmpNominalInput;

    public boolean isUpmGroup() {
        return upmGroup;
    }

    public void setUpmGroup(boolean upmGroup) {
        this.upmGroup = upmGroup;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Boolean getUserPUK() {
        return isUserPUK;
    }

    public void setUserPUK(Boolean userPUK) {
        isUserPUK = userPUK;
    }

    public Boolean getUserMMS() {
        return isUserMMS;
    }

    public void setUserMMS(Boolean userMMS) {
        isUserMMS = userMMS;
    }

    public Boolean getUserWhitelistMenuProspera() {
        return isUserWhitelistMenuProspera;
    }

    public void setUserWhitelistMenuProspera(Boolean userWhitelistMenuProspera) {
        isUserWhitelistMenuProspera = userWhitelistMenuProspera;
    }

    public Integer getMaxUpmLimitForBwmpNominalInput() {
        return maxUpmLimitForBwmpNominalInput;
    }

    public void setMaxUpmLimitForBwmpNominalInput(Integer maxUpmLimitForBwmpNominalInput) {
        this.maxUpmLimitForBwmpNominalInput = maxUpmLimitForBwmpNominalInput;
    }
}
