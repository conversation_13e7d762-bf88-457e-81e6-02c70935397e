package com.btpns.fin.model.request;

import com.btpns.fin.helper.LocalDateTimeAtributeConverter;

import javax.persistence.Convert;
import java.time.LocalDateTime;

public class ReqMsDataAlihDayaModel {
    private String type;
    private String nik;
    private String nama;
    private String nikPUK;
    private String jabatanVendor;
    private String masaBerlakuSampai;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime createDatetime;
    @Convert(converter = LocalDateTimeAtributeConverter.class)
    private LocalDateTime updateDateTime;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public String getNikPUK() {
        return nikPUK;
    }

    public void setNikPUK(String nikPUK) {
        this.nikPUK = nikPUK;
    }

    public String getJabatanVendor() {
        return jabatanVendor;
    }

    public void setJabatanVendor(String jabatanVendor) {
        this.jabatanVendor = jabatanVendor;
    }

    public String getMasaBerlakuSampai() {
        return masaBerlakuSampai;
    }

    public void setMasaBerlakuSampai(String masaBerlakuSampai) {
        this.masaBerlakuSampai = masaBerlakuSampai;
    }

    public LocalDateTime getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(LocalDateTime createDatetime) {
        this.createDatetime = createDatetime;
    }

    public LocalDateTime getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(LocalDateTime updateDateTime) {
        this.updateDateTime = updateDateTime;
    }
}
