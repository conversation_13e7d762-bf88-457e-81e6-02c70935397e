package com.btpns.fin.model.request;

import com.btpns.fin.model.entity.*;

public class ReqUserIDModel {
    private String type;
    private String nik;
    private String namaUser;
    private String kewenangan;
    private String jabatan;
    private String unitKerja;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNamaUser() {
        return namaUser;
    }

    public void setNamaUser(String namaUser) {
        this.namaUser = namaUser;
    }

    public String getKewenangan() {
        return kewenangan;
    }

    public void setKewenangan(String kewenangan) {
        this.kewenangan = kewenangan;
    }

    public String getJabatan() {
        return jabatan;
    }

    public void setJabatan(String jabatan) {
        this.jabatan = jabatan;
    }

    public String getUnitKerja() {
        return unitKerja;
    }

    public void setUnitKerja(String unitKerja) {
        this.unitKerja = unitKerja;
    }

    public MsSPK toSPK() {
        return new MsSPK(this.getNik(), this.getNamaUser(), this.getKewenangan(), this.getJabatan(), this.getUnitKerja());
    }

    public MsDboRTGS toDboRTGS() {
        return new MsDboRTGS(this.getNik(), this.getNamaUser(), this.getKewenangan(), this.getJabatan(), this.getUnitKerja());
    }

    public MsS4 toS4() {
        return new MsS4(this.getNik(), this.getNamaUser(), this.getKewenangan(), this.getJabatan(), this.getUnitKerja());
    }

    public MsSlik toSlik() {
        return new MsSlik(this.getNik(), this.getNamaUser(), this.getKewenangan(), this.getJabatan(), this.getUnitKerja());
    }

    public MsCMS toCMS() {
        return new MsCMS(this.getNik(), this.getNamaUser(), this.getKewenangan(), this.getJabatan(), this.getUnitKerja());
    }

    public MsTepatMBankingIndividu toTepatMBankingIndividu() {
        return new MsTepatMBankingIndividu(this.getNik(), this.getNamaUser(), this.getKewenangan(), this.getJabatan(), this.getUnitKerja());
    }

    public MsTepatMBankingCorporate toTepatMBankingCorporate() {
        return new MsTepatMBankingCorporate(this.getNik(), this.getNamaUser(), this.getKewenangan(), this.getJabatan(), this.getUnitKerja());
    }
}
