package com.btpns.fin.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigInteger;

public class ReqApprovalKewenanganLimit {
    String type;
    private String id;
    private String approvalOccupation;
    @JsonProperty("isKcKFO")
    private boolean isKcKFO;
    @JsonProperty("isHO")
    private boolean isHO;
    private Double minTunai;
    private Double maxTunai;
    private Double minNonTunai;
    private Double maxNonTunai;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApprovalOccupation() {
        return approvalOccupation;
    }

    public void setApprovalOccupation(String approvalOccupation) {
        this.approvalOccupation = approvalOccupation;
    }

    public boolean isKcKFO() {
        return isKcKFO;
    }

    public void setKcKFO(boolean kcKFO) {
        isKcKFO = kcKFO;
    }

    public boolean isHO() {
        return isHO;
    }

    public void setHO(boolean HO) {
        isHO = HO;
    }

    public Double getMinTunai() {
        return minTunai;
    }

    public void setMinTunai(Double minTunai) {
        this.minTunai = minTunai;
    }

    public Double getMaxTunai() {
        return maxTunai;
    }

    public void setMaxTunai(Double maxTunai) {
        this.maxTunai = maxTunai;
    }

    public Double getMinNonTunai() {
        return minNonTunai;
    }

    public void setMinNonTunai(Double minNonTunai) {
        this.minNonTunai = minNonTunai;
    }

    public Double getMaxNonTunai() {
        return maxNonTunai;
    }

    public void setMaxNonTunai(Double maxNonTunai) {
        this.maxNonTunai = maxNonTunai;
    }
}
