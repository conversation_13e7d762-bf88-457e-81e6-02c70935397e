package com.btpns.fin.model.request;

import com.btpns.fin.model.AttachmentModel;

import java.util.List;

public class ReqApprovalModel {
    private String ticketId;
    private String type;
    private String notes;
    private Integer confirmation;
    private List<AttachmentModel> attachment;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Integer getConfirmation() {
        return confirmation;
    }

    public void setConfirmation(Integer confirmation) {
        this.confirmation = confirmation;
    }

    public List<AttachmentModel> getAttachment() {
        return attachment;
    }

    public void setAttachment(List<AttachmentModel> attachment) {
        this.attachment = attachment;
    }
}
