package com.btpns.fin.model.request;

import com.btpns.fin.helper.Constants;
import com.btpns.fin.model.entity.MsUserIDApplication;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;

public class ReqUserIDApplication {
    private String code;
    private String desc;
    private String type;
    private boolean uar;
    private boolean visible;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @JsonProperty("isUAR")
    public boolean isUar() {
        return uar;
    }

    public void setUar(boolean uar) {
        this.uar = uar;
    }

    @JsonProperty("isVisible")
    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public MsUserIDApplication toMsUserIDApplication() {
        return new MsUserIDApplication(Constants.KODE_APLIKASI_USER_ID, this.code, this.desc, this.type, this.visible, this.uar);
    }
}
