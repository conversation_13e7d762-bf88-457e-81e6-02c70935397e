package com.btpns.fin.model.request;

import com.btpns.fin.model.Address;

import java.util.List;

public class ReqPersonnelProspera {
    private String centerCodeOfficer;
    private String firstName;
    private String middleName;
    private String secondLastName;
    private String lastName;
    private String governmentIdNumber;
    private String emailId;
    private String dob;
    private Integer maritalStatus;
    private Integer gender;
    private Integer preferredLocale;
    private String dateOfJoiningMFI;
    private String effectiveDate;
    private Integer officeId;
    private Address address;
    private String nik;
    private Integer title;
    private Integer levelId;
    private List<Integer> personnelRoles;
    private Integer status;
    private Double limit;
    private String loginName;
    private String userPassword;
    private String passwordRepeat;
    private Integer personnelParentId;
    private Integer versionNo;

    public String getCenterCodeOfficer() {
        return centerCodeOfficer;
    }

    public void setCenterCodeOfficer(String centerCodeOfficer) {
        this.centerCodeOfficer = centerCodeOfficer;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getSecondLastName() {
        return secondLastName;
    }

    public void setSecondLastName(String secondLastName) {
        this.secondLastName = secondLastName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getGovernmentIdNumber() {
        return governmentIdNumber;
    }

    public void setGovernmentIdNumber(String governmentIdNumber) {
        this.governmentIdNumber = governmentIdNumber;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    public String getDob() {
        return dob;
    }

    public void setDob(String dob) {
        this.dob = dob;
    }

    public Integer getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(Integer maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getPreferredLocale() {
        return preferredLocale;
    }

    public void setPreferredLocale(Integer preferredLocale) {
        this.preferredLocale = preferredLocale;
    }

    public String getDateOfJoiningMFI() {
        return dateOfJoiningMFI;
    }

    public void setDateOfJoiningMFI(String dateOfJoiningMFI) {
        this.dateOfJoiningMFI = dateOfJoiningMFI;
    }

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Integer getOfficeId() {
        return officeId;
    }

    public void setOfficeId(Integer officeId) {
        this.officeId = officeId;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public Integer getTitle() {
        return title;
    }

    public void setTitle(Integer title) {
        this.title = title;
    }

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public List<Integer> getPersonnelRoles() {
        return personnelRoles;
    }

    public void setPersonnelRoles(List<Integer> personnelRoles) {
        this.personnelRoles = personnelRoles;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Double getLimit() {
        return limit;
    }

    public void setLimit(Double limit) {
        this.limit = limit;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }

    public String getPasswordRepeat() {
        return passwordRepeat;
    }

    public void setPasswordRepeat(String passwordRepeat) {
        this.passwordRepeat = passwordRepeat;
    }

    public Integer getPersonnelParentId() {
        return personnelParentId;
    }

    public void setPersonnelParentId(Integer personnelParentId) {
        this.personnelParentId = personnelParentId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
