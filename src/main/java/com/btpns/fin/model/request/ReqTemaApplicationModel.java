package com.btpns.fin.model.request;

public class ReqTemaApplicationModel {
    private String type;
    private String status;
    private String paramId;
    private String paramDetailId;
    private String paramDetailDesc;
    private Integer isHo;
    private Integer isBranch;
    private Integer isMms;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getParamId() {
        return paramId;
    }

    public void setParamId(String paramId) {
        this.paramId = paramId;
    }

    public String getParamDetailId() {
        return paramDetailId;
    }

    public void setParamDetailId(String paramDetailId) {
        this.paramDetailId = paramDetailId;
    }

    public String getParamDetailDesc() {
        return paramDetailDesc;
    }

    public void setParamDetailDesc(String paramDetailDesc) {
        this.paramDetailDesc = paramDetailDesc;
    }

    public Integer getIsHo() {
        return isHo;
    }

    public void setIsHo(Integer isHo) {
        this.isHo = isHo;
    }

    public Integer getIsBranch() {
        return isBranch;
    }

    public void setIsBranch(Integer isBranch) {
        this.isBranch = isBranch;
    }

    public Integer getIsMms() {
        return isMms;
    }

    public void setIsMms(Integer isMms) {
        this.isMms = isMms;
    }
}
