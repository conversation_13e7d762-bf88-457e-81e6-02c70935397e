package com.btpns.fin.model;

public class GrandTotalPerBranchType {
    private int ho;
    private int kcKfo;
    private int mms;
    private int grandTotal;

    public GrandTotalPerBranchType(int ho, int kcKfo, int mms, int grandTotal) {
        this.ho = ho;
        this.kcKfo = kcKfo;
        this.mms = mms;
        this.grandTotal = grandTotal;
    }

    public int getHo() {
        return ho;
    }

    public void setHo(int ho) {
        this.ho = ho;
    }

    public int getKcKfo() {
        return kcKfo;
    }

    public void setKcKfo(int kcKfo) {
        this.kcKfo = kcKfo;
    }

    public int getMms() {
        return mms;
    }

    public void setMms(int mms) {
        this.mms = mms;
    }

    public int getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(int grandTotal) {
        this.grandTotal = grandTotal;
    }
}
