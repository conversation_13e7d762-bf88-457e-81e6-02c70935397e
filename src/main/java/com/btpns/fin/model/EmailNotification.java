package com.btpns.fin.model;

import java.util.List;

public class EmailNotification {
    private String ticketId;
    private String emailSubject;
    private String emailDestination;
    private String emailCc;
    private String emailBcc;
    private String emailMessage;
    private List<AttachmentModel> emailAttachments;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }

    public String getEmailDestination() {
        return emailDestination;
    }

    public void setEmailDestination(String emailDestination) {
        this.emailDestination = emailDestination;
    }

    public String getEmailCc() {
        return emailCc;
    }

    public void setEmailCc(String emailCc) {
        this.emailCc = emailCc;
    }

    public String getEmailBcc() {
        return emailBcc;
    }

    public void setEmailBcc(String emailBcc) {
        this.emailBcc = emailBcc;
    }

    public String getEmailMessage() {
        return emailMessage;
    }

    public void setEmailMessage(String emailMessage) {
        this.emailMessage = emailMessage;
    }

    public List<AttachmentModel> getEmailAttachments() {
        return emailAttachments;
    }

    public void setEmailAttachments(List<AttachmentModel> emailAttachments) {
        this.emailAttachments = emailAttachments;
    }
}
