package com.btpns.fin.model;

import java.util.List;

public class UserProspera {
    private Integer personnelId;
    private String officerLoanCode;
    private String username;
    private String wismaCode;
    private String wismaName;
    private Integer userHierarchyId;
    private String userHierarchy;
    private List<String> roles;
    private Integer statusId;
    private String status;
    private String globalPersonnelNum;
    private String nik;
    private String globalOfficeNum;
    private String loginName;

    public Integer getPersonnelId() {
        return personnelId;
    }

    public void setPersonnelId(Integer personnelId) {
        this.personnelId = personnelId;
    }

    public String getOfficerLoanCode() {
        return officerLoanCode;
    }

    public void setOfficerLoanCode(String officerLoanCode) {
        this.officerLoanCode = officerLoanCode;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getWismaCode() {
        return wismaCode;
    }

    public void setWismaCode(String wismaCode) {
        this.wismaCode = wismaCode;
    }

    public String getWismaName() {
        return wismaName;
    }

    public void setWismaName(String wismaName) {
        this.wismaName = wismaName;
    }

    public Integer getUserHierarchyId() {
        return userHierarchyId;
    }

    public void setUserHierarchyId(Integer userHierarchyId) {
        this.userHierarchyId = userHierarchyId;
    }

    public String getUserHierarchy() {
        return userHierarchy;
    }

    public void setUserHierarchy(String userHierarchy) {
        this.userHierarchy = userHierarchy;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public Integer getStatusId() {
        return statusId;
    }

    public void setStatusId(Integer statusId) {
        this.statusId = statusId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getGlobalPersonnelNum() {
        return globalPersonnelNum;
    }

    public void setGlobalPersonnelNum(String globalPersonnelNum) {
        this.globalPersonnelNum = globalPersonnelNum;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getGlobalOfficeNum() {
        return globalOfficeNum;
    }

    public void setGlobalOfficeNum(String globalOfficeNum) {
        this.globalOfficeNum = globalOfficeNum;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }
}
