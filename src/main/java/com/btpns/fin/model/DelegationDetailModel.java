package com.btpns.fin.model;

public class DelegationDetailModel {
    private String id;
    private DelegatedUserModel user;
    private String startDate;
    private String endDate;
    private String info;
    private DelegatedUserModel delegatedUser;
    private String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public DelegatedUserModel getUser() {
        return user;
    }

    public void setUser(DelegatedUserModel user) {
        this.user = user;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public DelegatedUserModel getDelegatedUser() {
        return delegatedUser;
    }

    public void setDelegatedUser(DelegatedUserModel delegatedUser) {
        this.delegatedUser = delegatedUser;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
