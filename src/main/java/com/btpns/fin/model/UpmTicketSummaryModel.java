package com.btpns.fin.model;

import java.math.BigInteger;

public class UpmTicketSummaryModel {
    private BigInteger newTicket;
    private BigInteger onHold;
    private BigInteger inprogress;
    private BigInteger waitingApproval;
    private BigInteger done;

    public BigInteger getNewTicket() {
        return newTicket;
    }

    public void setNewTicket(BigInteger newTicket) {
        this.newTicket = newTicket;
    }

    public BigInteger getOnHold() {
        return onHold;
    }

    public void setOnHold(BigInteger onHold) {
        this.onHold = onHold;
    }

    public BigInteger getInprogress() {
        return inprogress;
    }

    public void setInprogress(BigInteger inprogress) {
        this.inprogress = inprogress;
    }

    public BigInteger getWaitingApproval() {
        return waitingApproval;
    }

    public void setWaitingApproval(BigInteger waitingApproval) {
        this.waitingApproval = waitingApproval;
    }

    public BigInteger getDone() {
        return done;
    }

    public void setDone(BigInteger done) {
        this.done = done;
    }
}
