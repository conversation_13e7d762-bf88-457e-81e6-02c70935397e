package com.btpns.fin.model;

import com.btpns.fin.model.request.ReqProspera;

import java.util.List;

public class UpmManageTicketModel {
    private String ticketId;
    private String type;
    private String nikMaker;
    private String notes;
    private List<AttachmentModel> attachmentUPMInput;
    private Double nominalTransaksiUPM;
    private String role;
    private String kodeCabang;
    private ReqProspera reqProspera;
    private List<String> upmActionProspera;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNikMaker() {
        return nikMaker;
    }

    public void setNikMaker(String nikMaker) {
        this.nikMaker = nikMaker;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<AttachmentModel> getAttachmentUPMInput() {
        return attachmentUPMInput;
    }

    public void setAttachmentUPMInput(List<AttachmentModel> attachmentUPMInput) {
        this.attachmentUPMInput = attachmentUPMInput;
    }

    public Double getNominalTransaksiUPM() {
        return nominalTransaksiUPM;
    }

    public void setNominalTransaksiUPM(Double nominalTransaksiUPM) {
        this.nominalTransaksiUPM = nominalTransaksiUPM;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getKodeCabang() {
        return kodeCabang;
    }

    public void setKodeCabang(String kodeCabang) {
        this.kodeCabang = kodeCabang;
    }

    public ReqProspera getReqProspera() {
        return reqProspera;
    }

    public void setReqProspera(ReqProspera reqProspera) {
        this.reqProspera = reqProspera;
    }

    public List<String> getUpmActionProspera() {
        return upmActionProspera;
    }

    public void setUpmActionProspera(List<String> upmActionProspera) {
        this.upmActionProspera = upmActionProspera;
    }
}
