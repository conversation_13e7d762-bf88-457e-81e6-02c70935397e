package com.btpns.fin.model;

import java.util.List;

public class ListWaitingTicketSetupParamModel {
    private List<WaitingTicketSetupParamModel> waitingTicket;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<WaitingTicketSetupParamModel> getWaitingTicket() { return waitingTicket; }

    public void setWaitingTicket(List<WaitingTicketSetupParamModel> waitingTicket) { this.waitingTicket = waitingTicket; }

    public int getPage() { return page; }

    public void setPage(int page) { this.page = page; }

    public int getLimit() { return limit; }

    public void setLimit(int limit) { this.limit = limit; }

    public int getTotalPages() { return totalPages; }

    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

    public long getTotalItems() { return totalItems; }

    public void setTotalItems(long totalItems) { this.totalItems = totalItems; }
}
