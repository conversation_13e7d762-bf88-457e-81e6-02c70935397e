package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UserIDAppModel {
    private String code;
    private String desc;
    private String type;
    private boolean visible;
    private boolean uar;

    public UserIDAppModel() {
    }

    public UserIDAppModel(String code, String desc, String type, boolean visible, boolean uar) {
        this.code = code;
        this.desc = desc;
        this.type = type;
        this.visible = visible;
        this.uar = uar;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @JsonProperty("isVisible")
    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    @JsonProperty("isUAR")
    public boolean isUar() {
        return uar;
    }

    public void setUar(boolean uar) {
        this.uar = uar;
    }
}
