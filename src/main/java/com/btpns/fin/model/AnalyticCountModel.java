package com.btpns.fin.model;

import java.math.BigInteger;

public class AnalyticCountModel {
    private String nik;
    private String nama;
    private BigInteger countTicketInprogress;
    private BigInteger countTicketVerification;
    private BigInteger countTicketDone;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getNama() {
        return nama;
    }

    public void setNama(String nama) {
        this.nama = nama;
    }

    public BigInteger getCountTicketInprogress() {
        return countTicketInprogress;
    }

    public void setCountTicketInprogress(BigInteger countTicketInprogress) {
        this.countTicketInprogress = countTicketInprogress;
    }

    public BigInteger getCountTicketVerification() {
        return countTicketVerification;
    }

    public void setCountTicketVerification(BigInteger countTicketVerification) {
        this.countTicketVerification = countTicketVerification;
    }

    public BigInteger getCountTicketDone() {
        return countTicketDone;
    }

    public void setCountTicketDone(BigInteger countTicketDone) {
        this.countTicketDone = countTicketDone;
    }
}
