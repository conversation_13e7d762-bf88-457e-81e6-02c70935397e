package com.btpns.fin.model;

import java.util.List;

public class ListMsHolidayModel {
    private List<MsHolidayModel> msHolidayModelList;
    private int page;
    private int limit;
    private int totalPages;
    private long totalItems;

    public List<MsHolidayModel> getMsHolidayModelList() {
        return msHolidayModelList;
    }

    public void setMsHolidayModelList(List<MsHolidayModel> msHolidayModelList) {
        this.msHolidayModelList = msHolidayModelList;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(long totalItems) {
        this.totalItems = totalItems;
    }
}
