package com.btpns.fin.model;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
public class UpmCountTicketOnHoldSummary {
    @Id
    private String status;
    private Integer total;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
