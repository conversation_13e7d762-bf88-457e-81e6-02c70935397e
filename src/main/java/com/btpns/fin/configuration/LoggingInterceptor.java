package com.btpns.fin.configuration;

import okhttp3.*;
import okio.Buffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class LoggingInterceptor implements Interceptor {
    private boolean isLogBase64Enable;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    public LoggingInterceptor(boolean isLogBase64Enable) {
        this.isLogBase64Enable = isLogBase64Enable;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();

        Buffer requestBufferBody = new Buffer();
        if (request.body() != null) {
            request.body().writeTo(requestBufferBody);
        }
        log.info("OkHttp --> Sending request {} Header: {} Body: {}", request.url(), request.headers().toString().replaceAll("\n", " "), getBodyLoggerPattern(requestBufferBody));
        Response response = chain.proceed(request);

        MediaType contentType = response.body().contentType();
        String content = response.body().string();
        log.info("OkHttp <-- Received response for {} Headers: {} Body: {}", response.request().url(), response.headers().toString().replaceAll("\n", " "), content);

        ResponseBody wrappedBody = ResponseBody.create(contentType, content);
        return response.newBuilder().body(wrappedBody).build();
    }

    private String getBodyLoggerPattern(Buffer requestBufferBody) {
        String bodyContent = requestBufferBody.readUtf8();
        
        if(isLogBase64Enable) {
            return bodyContent;
        }
        
        bodyContent = bodyContent
                .replaceAll("\"userPassword\":\"(.*?)\"", "\"userPassword\":\"********\"")
                .replaceAll("\"passwordRepeat\":\"(.*?)\"", "\"passwordRepeat\":\"********\"")
                .replaceAll("\"password\":\"(.*?)\"", "\"Password\":\"********\"")
                .replaceAll("\"file_dokumen\":\"(.*?)\"", "\"file_dokumen\":\"truncated base64 image\"")
                .replaceAll("\"dokumen_kelengkapan\":\"(.*?)\"", "\"dokumen_kelengkapan\":\"truncated base64 file\"")
                .replaceAll("\"documenKelengkapan\":\"(.*?)\"", "\"documenKelengkapan\":\"truncated base64 file\"")
                .replaceAll("\"attachment\":\\[(.*?)\\]", "\"attachment\":\"truncated base64 attachment\"");
        
        return bodyContent;
    }
}
