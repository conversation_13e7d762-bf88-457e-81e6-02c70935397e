package com.btpns.fin.configuration;


import io.minio.MinioClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

@Configuration
public class MinioConfig {
    @Value("${minio.uri}")
    public String minioUrl;

    private static final Logger logger = LoggerFactory.getLogger(MinioConfig.class);

    @Bean
    public MinioClient generateMinioClient() {
        try {
            return  MinioClient
                .builder()
                .endpoint(minioUrl)
                .build();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Bean
    public MultipartResolver multipartResolver() {
        return new StandardServletMultipartResolver();
    }}
