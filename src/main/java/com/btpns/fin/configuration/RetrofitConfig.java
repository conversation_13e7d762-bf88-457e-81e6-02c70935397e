package com.btpns.fin.configuration;

import com.btpns.fin.repository.*;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import java.util.concurrent.TimeUnit;

@Configuration
public class RetrofitConfig {
    @Value("${mail.uri}")
    private String mailUri;
    @Value("${prospera.router.uri}")
    private String prosperaRouterUri;
    @Value("${enable.log.base64}")
    private boolean isLogBase64Enable;

    @Bean
    public OkHttpClient getOkHttpClient() {
        return new OkHttpClient.Builder()
                .readTimeout(30000, TimeUnit.MILLISECONDS)
                .writeTimeout(30000, TimeUnit.MILLISECONDS)
                .connectTimeout(30000, TimeUnit.MILLISECONDS)
                .addInterceptor(new LoggingInterceptor(isLogBase64Enable))
                .build();
    }

    @Bean
    public Retrofit getEmailSvcRetrofit() {
        return new Retrofit.Builder()
                .baseUrl(mailUri)
                .addConverterFactory(GsonConverterFactory.create())
                .client(getOkHttpClient())
                .build();
    }

    @Bean
    public Retrofit getProsperaRouterUriRetrofit() {
        return new Retrofit.Builder()
                .baseUrl(prosperaRouterUri)
                .addConverterFactory(GsonConverterFactory.create())
                .client(getOkHttpClient())
                .build();
    }

    @Bean(name = "mail")
    public IEmailServerRepository emailServerRepository() {
        return getEmailSvcRetrofit().create(IEmailServerRepository.class);
    }

    @Bean(name = "prospera-router")
    public IProsperaRepository prosperaUriRepository() {
        return getProsperaRouterUriRetrofit().create(IProsperaRepository.class);
    }
}
