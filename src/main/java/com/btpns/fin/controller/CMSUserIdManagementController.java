package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsCMS;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.CMSUserIdService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.ALREADY_EXIST;
import static com.btpns.fin.helper.ResponseStatus.NOT_FOUND;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000005")
@CrossOrigin("*")
public class CMSUserIdManagementController {
    private static final Logger logger = LoggerFactory.getLogger(CMSUserIdManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxUserIdBatchService trxUserIdBatchService;

    @Autowired
    CMSUserIdService cmsUserIdService;

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchMsCMSUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                        @RequestBody ReqUserIdBatchModel<MsCMS> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsCMSUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (!CommonHelper.isExistInInterval(trxUserIdBatchService.existInInterval(nikRequester, request.getType(), 30))) {
                    if(isValidRequest(request) && !CommonHelper.isDuplicateData(request.getData().stream().map(d -> d.getNik()).collect(Collectors.toList()))) {
                        ResponseModel<ResBatchUserId> savedBatchCmsUserId = cmsUserIdService.saveBatchMsCMSUserId(request, nikRequester);

                        logger.info("Response >>> saveBatchMsCMSUserIdManagement {}", gson.toJson(savedBatchCmsUserId));
                        return ResponseEntity.ok(savedBatchCmsUserId);
                    }
                    logger.warn("Response >>> saveBatchMsCMSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                    return responseBadRequest("saveBatchMsCMSUserIdManagement");
                }
                logger.warn("Response >>> saveBatchMsCMSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            logger.warn("Response >>> saveBatchMsCMSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("saveBatchMsCMSUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> saveBatchMsCMSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsCMSUserIdManagement", e);
        }
    }

    private boolean isValidRequest(ReqUserIdBatchModel<MsCMS> request) {
        return request != null
               && request.getType() != null
               && request.getBatchId() != null
               && request.getFileName() != null
               && request.getTotalData() > 0
               && request.getData() != null
               && request.getData().size() > 0
               && trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsCMSUserManagement(@RequestHeader("XToken") String authorization,
                                                                                   @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                   @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                   @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                   @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsCMSUserManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            int pageNumMin1 = pageNumber - 1;
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsCMSUserManagement");
            }
            ResponseModel<ResponseListModel> response = this.cmsUserIdService.getListCMSUsers(pageNumMin1, pageNumber, pageSize, searchFlag, searchData);
            logger.info("Response >>> getMsCMSUserManagement size {}", response.getDetails());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsCMSUserManagement", exception);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<UserIDModel>> getMsCMSUserManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                  @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsCMSUserManagementByNik nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsCMSUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsCMSUserManagementByNik");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> getMsCMSUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsCMSUserManagementByNik");
            }
            ResponseModel<UserIDModel> response = this.cmsUserIdService.getCMSUserByNik(nik);
            logger.info("Response >>> getMsCMSUserManagementByNik {}", gson.toJson(response.getDetails()));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsCMSUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsCMSUserManagementByNik", exception);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsCMSUserManagement(@RequestHeader("XToken") String authorization,
                                                                                       @RequestBody RequestModel<ReqUserIDModel> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsCMSUserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> addOrEditMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("addOrEditMsCMSUserManagement");
            }
            if (!isValidUserIDRequest(request.getDetails())) {
                logger.warn("Response >>> addOrEditMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsCMSUserManagement");
            }
            if (request.getDetails().getType().equalsIgnoreCase(ADD)
                    && this.cmsUserIdService.findByNik(request.getDetails().getNik()).isPresent()) {
                return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMsCMSUserManagement", gson);
            }
            if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                ResponseModel<ResCUDUserIdModel> updatedCMSUser = this.cmsUserIdService.updateCMSUser(request.getDetails());
                logger.info("Response >>> editMsCMSUserManagement {} ", gson.toJson(updatedCMSUser));

                return ResponseEntity.ok(updatedCMSUser);
            }
            ResponseModel<ResCUDUserIdModel> savedCMSUser = this.cmsUserIdService.saveCMSUser(request.getDetails());
            logger.info("Response >>> addMsCMSUserManagement {} ", gson.toJson(savedCMSUser));

            return ResponseEntity.ok(savedCMSUser);
        } catch (Exception exception) {
            logger.warn("Response >>> addOrEditMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsCMSUserManagement", exception);
        }
    }

    @DeleteMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsCMSUserManagement(@RequestHeader("XToken") String authorization,
                                                                                  @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsCMSUserManagement nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> deleteMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("deleteMsCMSUserManagement");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> deleteMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsCMSUserManagement");
            }
            if (this.cmsUserIdService.findByNik(nik).isEmpty()) {
                logger.warn("Response >>> deleteMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.NOT_FOUND));
                return responseFailed(TYPE_MS_CMS_MANAGEMENT_DELETE, NOT_FOUND, "deleteMsCMSUserManagement", gson);
            }

            ResponseModel<ResCUDUserIdModel> response = this.cmsUserIdService.deleteCMSUser(nik);
            logger.info("Response >>> deleteMsCMSUserManagement : {}", gson.toJson(response));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> deleteMsCMSUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsCMSUserManagement", exception);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateExcelMsCMSUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< generateExcelMsCMSUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = cmsUserIdService.generateExcelMsCMSUserId();
                logger.info("Response >>> generateExcelMsCMSUserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> generateExcelMsCMSUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("generateExcelMsCMSUserId");
        } catch (Exception e) {
            logger.warn("Response >>> generateExcelMsCMSUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("generateExcelMsCMSUserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsCMSUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsCMSUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = cmsUserIdService.directDownloadExcelMsCMSUserId();
                return responseDownloadFile("directDownloadExcelMsCMSUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsCMSUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsCMSUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsCMSUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsCMSUserId", e);
        }
    }
}
