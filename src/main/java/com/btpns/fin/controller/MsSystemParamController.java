package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.AplikasiModel;
import com.btpns.fin.service.MsSystemParamDetailService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;

@Controller
@RequestMapping(value = "tema/initdata")
@CrossOrigin("*")
public class MsSystemParamController {
    private static final Logger logger = LoggerFactory.getLogger(MsSystemParamController.class);

    @Autowired
    MsSystemParamDetailService msSystemParamDetailService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping()
    public ResponseEntity<AplikasiModel> getMsSystemParam(@RequestParam("data") String data,
                                                          @RequestParam(value = "isHo", required = false) Boolean isHo,
                                                          @RequestParam(value = "isBranch", required = false) Boolean isBranch,
                                                          @RequestParam(value = "isMms", required = false) Boolean isMms,
                                                          @RequestParam(value = "isKewenanganLimit", required = false) Boolean isKewenanganLimit,
                                                          @RequestParam(value = "status", required = false, defaultValue = "all") String status,
                                                          @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getMsSystemParam data {} from {} ", data, getProfile(gson, profile));

            AplikasiModel result = msSystemParamDetailService.getMsSystemParamData(data, isHo, isBranch, isMms, isKewenanganLimit, status);
            logger.info("Response >>> getMsSystemParam : ", gson.toJson(result));

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Fail to getMsSystemParam ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
