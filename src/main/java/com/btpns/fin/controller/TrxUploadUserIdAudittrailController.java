package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.TrxUploadUserIdAudittrailModel;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.TrxUploadUserIdAudittrailService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@RestController
@RequestMapping(value = "tema/monitoring/upload-userId")
@CrossOrigin("*")
public class TrxUploadUserIdAudittrailController {
    private static final Logger logger = LoggerFactory.getLogger(TrxUploadUserIdAudittrailController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel<TrxUploadUserIdAudittrailModel>>> getMonitoringUploadUserId(@RequestHeader("XToken") String authorization,
                                                                                                                      @RequestParam(value = "aplikasi", required = false, defaultValue = "all") String aplikasi,
                                                                                                                      @RequestParam(value = "startDate", required = false) String startDate,
                                                                                                                      @RequestParam(value = "endDate", required = false) String endDate,
                                                                                                                      @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                                                                      @RequestParam(value = "limit", defaultValue = "50") Integer limit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getMonitoringUploadUserId startDate {} endDate {} page {} limit {} from {}", startDate, endDate, page, limit, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if (validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel<TrxUploadUserIdAudittrailModel>> response = trxUploadUserIdAudittrailService.getMonitoringUploadUserId(aplikasi, startDate, endDate, page, limit);

                logger.info("Response >>> getMonitoringUploadUserId : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsS4UserManagement");
        } catch (Exception e) {
            logger.error("Fail to getMonitoringUploadUserId ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/direct-download")
    public ResponseEntity<Resource> directDownloadReportUploadUserIdPdf(@RequestHeader("XToken") String authorization,
                                                                        @RequestParam("id") Integer id) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadReportUploadUserIdPdf from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if (validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = trxUploadUserIdAudittrailService.directDownloadReportUploadUserIdPdf(id);

                logger.info("Response >>> directDownloadReportUploadUserIdPdf : {}", gson.toJson(response));
                return responseDownloadFile("directDownloadReportUploadUserIdPdf", response);
            }
            logger.warn("Response >>> directDownloadReportUploadUserIdPdf : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadReportUploadUserIdPdf");
        } catch (Exception e) {
            logger.error("Fail to directDownloadReportUploadUserIdPdf ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
