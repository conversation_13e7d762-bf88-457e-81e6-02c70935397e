package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.entity.MMS_UPM;
import com.btpns.fin.model.request.ReqMMSDataModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMmsUPMRepository;
import com.btpns.fin.service.MsCabangMmsService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "tema/upm/mms/management")
@CrossOrigin("*")
public class MMSDataManagementController {
    private static final Logger logger = LoggerFactory.getLogger(MMSDataManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsCabangMmsService msCabangMmsService;

    @Autowired
    IMmsUPMRepository iMmsUPMRepository;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResMMSDataListModel>> getMMSDataManagement(@RequestHeader("XToken") String authorization,
                                                                                   @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                   @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                   @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                   @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMMSDataManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResMMSDataListModel> response = msCabangMmsService.getListDataMMS(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);
                logger.info("Response >>> getMMSDataManagement size {}", response.getDetails().getMms().size());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getMMSDataManagement");
        } catch (Exception e){
            return responseInternalServerError("getMMSDataManagement", e);
        }
    }

    @GetMapping(value = "/{mmsCode}")
    public ResponseEntity<ResponseModel<MMS_UPM>> getMMSDataManagementByMmsCode(@RequestHeader("XToken") String authorization,
                                                                                @PathVariable("mmsCode") String mmsCode) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMMSDataManagementByMmsCode mmsCode {} from {}", mmsCode, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(mmsCode)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    ResponseModel<MMS_UPM> response = msCabangMmsService.getMmsDataByMmsCode(mmsCode);
                    logger.info("Response >>> getMMSDataManagementByMmsCode {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                return responseForbidden("getMMSDataManagementByMmsCode");
            }
            return responseBadRequest("getMMSDataManagementByMmsCode");
        } catch (Exception e){
            return responseInternalServerError("getMMSDataManagementByMmsCode", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResMMSDataModel>> addOrEditMMSDataManagement(@RequestHeader("XToken") String authorization,
                                                                                     @RequestBody RequestModel<ReqMMSDataModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMMSDataManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(isValidRequest(request)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (iMmsUPMRepository.findByMmsCode(request.getDetails().getMmsCode()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResMMSDataModel> updateMmsData = msCabangMmsService.updateMms(request.getDetails());
                            logger.info("Response >>> editMMSDataManagement {} ", gson.toJson(updateMmsData));

                            return ResponseEntity.ok(updateMmsData);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMMSDataManagement", gson);
                    }

                    ResponseModel<ResMMSDataModel> saveMmsData = msCabangMmsService.saveMms(request.getDetails());
                    logger.info("Response >>> addMMSDataManagement {} ", gson.toJson(saveMmsData));

                    return ResponseEntity.ok(saveMmsData);
                }
                return responseForbidden("addOrEditMMSDataManagement");
            }
            return responseBadRequest("addOrEditMMSDataManagement");
        } catch (Exception e){
            return responseInternalServerError("addOrEditMMSDataManagement", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqMMSDataModel> request) {
        return request != null
               && request.getType() != null
               && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
               && request.getDetails().getMmsCode() != null
               && request.getDetails().getMmsName() != null
               && request.getDetails().getAddress() != null
               && request.getDetails().getKcsCode() != null
               && request.getDetails().getKfoName() != null
               && request.getDetails().getKcsCode() != null
               && request.getDetails().getKcsName() != null;
    }

    @DeleteMapping(value = "/{mmsCode}")
    public ResponseEntity<ResponseModel<ResMMSDataModel>> deleteMMSDataManagement(@RequestHeader("XToken") String authorization,
                                                                                  @PathVariable("mmsCode") String mmsCode) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMMSDataManagement mmsCode {} from {}", mmsCode, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(mmsCode)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (iMmsUPMRepository.findByMmsCode(mmsCode) != null) {
                        ResponseModel<ResMMSDataModel> response = msCabangMmsService.deleteMms(mmsCode);
                        logger.info("Response >>> deleteMMSDataManagement : {}", gson.toJson(response));

                        return ResponseEntity.ok(response);
                    }
                    return responseFailed(TYPE_MMS_MANAGEMENT_DELETE, NOT_FOUND, "deleteMMSDataManagement", gson);
                }
                return responseForbidden("deleteMMSDataManagement");
            }
            return responseBadRequest("deleteMMSDataManagement");
        } catch (Exception e){
            return responseInternalServerError("deleteMMSDataManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getMMSDataCsv(@RequestHeader("XToken") String authorization) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMMSDataCsv from {}", getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = msCabangMmsService.generateMMSDataCsv();

                logger.info("Response >>> getMMSDataCsv {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getMMSDataCsv");
        } catch (Exception e){
            return responseInternalServerError("getMMSDataCsv", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadMMSDataCsv(@RequestHeader("XToken") String authorization) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< directDownloadMMSDataCsv from {}", getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = msCabangMmsService.directDownloadMMSDataCsv();
                return responseDownloadFile("directDownloadMMSDataCsv", response);
            }
            return responseForbidden("directDownloadMMSDataCsv");
        } catch (Exception e){
            return responseInternalServerError("directDownloadMMSDataCsv", e);
        }
    }
}
