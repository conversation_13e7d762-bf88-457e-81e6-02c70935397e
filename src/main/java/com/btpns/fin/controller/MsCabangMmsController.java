package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.CabangMmsModel;
import com.btpns.fin.service.MsCabangMmsService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;

@Controller
@RequestMapping(value = "/tema/cabangmms")
@CrossOrigin("*")
public class MsCabangMmsController {
    private static final Logger logger = LoggerFactory.getLogger(MsCabangMmsController.class);

    @Autowired
    MsCabangMmsService msCabangMmsService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping()
    public ResponseEntity<CabangMmsModel> getMsCabangMmsAll(@RequestHeader("XToken") String authorization,
                                                            @RequestParam("data") String data,
                                                            @RequestParam(value = "isProsperaSource", required = false, defaultValue = "0") Boolean isProsperaSource) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getMsCabangMmsAll data {} isProsperaSource {} from {} ", data, isProsperaSource, getProfile(gson, profile));
            CabangMmsModel cabangMmsModel = msCabangMmsService.getListCabang(data, isProsperaSource, authorization);
            logger.info("Response >>> getMsCabangMmsAll : with size {}", cabangMmsModel.getData().size());
            return ResponseEntity.ok(cabangMmsModel);
        } catch (Exception e) {
            logger.error("Fail to getMsSystemParam ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
