package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.BranchDataModel;
import com.btpns.fin.model.request.ReqBranchDataModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMsCabangRepository;
import com.btpns.fin.service.MsCabangMmsService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.resource.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "tema/upm/branch/management")
@CrossOrigin("*")
public class BranchDataManagementController {
    private static final Logger logger = LoggerFactory.getLogger(BranchDataManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsCabangMmsService msCabangMmsService;

    @Autowired
    IMsCabangRepository iMsCabangRepository;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResBranchDataListModel>> getBranchDataManagement(@RequestHeader("XToken") String authorization,
                                                                                         @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                         @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                         @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                         @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getBranchDataManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResBranchDataListModel> response = msCabangMmsService.getListDataBranch(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);
                logger.info("Response >>> getBranchDataManagement size {}", response.getDetails().getBranch().size());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getBranchDataManagement");
        } catch (Exception e){
            return responseInternalServerError("getBranchDataManagement", e);
        }
    }

    @GetMapping(value = "/{branchId}")
    public ResponseEntity<ResponseModel<BranchDataModel>> getBranchDataManagementByBranchId(@RequestHeader("XToken") String authorization,
                                                                                            @PathVariable("branchId") String branchId) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getBranchDataManagementByBranchId branchId {} from {}", branchId, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(branchId)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    ResponseModel<BranchDataModel> response = msCabangMmsService.getBranchDataByBranchId(branchId);
                    logger.info("Response >>> getBranchDataManagementByBranchId {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                return responseForbidden("getBranchDataManagementByBranchId");
            }
            return responseBadRequest("getBranchDataManagementByBranchId");
        } catch (Exception e){
            return responseInternalServerError("getBranchDataManagementByBranchId", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResBranchDataModel>> addOrEditBranchDataManagement(@RequestHeader("XToken") String authorization,
                                                                                           @RequestBody RequestModel<ReqBranchDataModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditBranchDataManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(isValidRequest(request)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (iMsCabangRepository.findByBranchId(request.getDetails().getBranchId()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResBranchDataModel> updateBranchData = msCabangMmsService.updateBranch(request.getDetails());
                            logger.info("Response >>> editBranchDataManagement {} ", gson.toJson(updateBranchData));

                            return ResponseEntity.ok(updateBranchData);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMMSDataManagement", gson);
                    }

                    ResponseModel<ResBranchDataModel> saveBranchData = msCabangMmsService.saveBranch(request.getDetails());
                    logger.info("Response >>> addBranchDataManagement {} ", gson.toJson(saveBranchData));

                    return ResponseEntity.ok(saveBranchData);
                }
                return responseForbidden("addOrEditMMSDataManagement");
            }
            return responseBadRequest("addOrEditMMSDataManagement");
        } catch (Exception e){
            return responseInternalServerError("addOrEditMMSDataManagement", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqBranchDataModel> request) {
        return request != null
               && request.getType() != null
               && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
               && request.getDetails().getBranchId() != null
               && request.getDetails().getBranchName() != null;
    }

    @DeleteMapping(value = "/{branchId}")
    public ResponseEntity<ResponseModel<ResBranchDataModel>> deleteBranchDataManagement(@RequestHeader("XToken") String authorization,
                                                                                        @PathVariable("branchId") String branchId) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteBranchDataManagement branchId {} from {}", branchId, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(branchId)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (iMsCabangRepository.findByBranchId(branchId) != null) {
                        ResponseModel<ResBranchDataModel> response = msCabangMmsService.deleteBranch(branchId);
                        logger.info("Response >>> deleteBranchDataManagement : {}", gson.toJson(response));

                        return ResponseEntity.ok(response);
                    }
                    return responseFailed(TYPE_BRANCH_MANAGEMENT_DELETE, NOT_FOUND, "deleteBranchDataManagement", gson);
                }
                return responseForbidden("deleteBranchDataManagement");
            }
            return responseBadRequest("deleteBranchDataManagement");
        } catch (Exception e){
            return responseInternalServerError("deleteBranchDataManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getBranchDataCsv(@RequestHeader("XToken") String authorization) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getBranchDataCsv from {}", getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = msCabangMmsService.generateBranchDataCsv();

                logger.info("Response >>> getBranchDataCsv {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getBranchDataCsv");
        } catch (Exception e){
            return responseInternalServerError("getBranchDataCsv", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadBranchDataCsv(@RequestHeader("XToken") String authorization) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< directDownloadBranchDataCsv from {}", getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = msCabangMmsService.directDownloadAlihDayaCSV();
                return responseDownloadFile("directDownloadAlihDayaCSV", response);
            }
            return responseForbidden("directDownloadBranchDataCsv");
        } catch (Exception e){
            return responseInternalServerError("directDownloadBranchDataCsv", e);
        }
    }
}
