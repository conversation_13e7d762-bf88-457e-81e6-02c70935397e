package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsSPK;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.MsSPKUserManagementService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.ALREADY_EXIST;
import static com.btpns.fin.helper.ResponseStatus.NOT_FOUND;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000003")
@CrossOrigin("*")
public class MsSPKUserManagementController {
    private static final Logger logger = LoggerFactory.getLogger(MsSPKUserManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private MsSPKUserManagementService msSPKUserManagementService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private TrxUserIdBatchService trxUserIdBatchService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsSPKUserManagement(@RequestHeader("XToken") String authorization,
                                                                                   @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                   @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                   @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                   @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsSPKUserManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            int pageNumMin1 = pageNumber - 1;
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsSPKUserManagement");
            }
            ResponseModel<ResponseListModel> response = this.msSPKUserManagementService.getListSPKUsers(pageNumMin1, pageNumber, pageSize, searchFlag, searchData);
            logger.info("Response >>> getMsSPKUserManagement size {}", response.getDetails());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsSPKUserManagement", exception);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<UserIDModel>> getMsSPKUserManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                  @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsSPKUserManagementByNik nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsSPKUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsSPKUserManagementByNik");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> getMsSPKUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsSPKUserManagementByNik");
            }
            ResponseModel<UserIDModel> response = this.msSPKUserManagementService.getSPKUserByNik(nik);
            logger.info("Response >>> getMsSPKUserManagementByNik {}", gson.toJson(response.getDetails()));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsSPKUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsSPKUserManagementByNik", exception);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsSPKUserManagement(@RequestHeader("XToken") String authorization,
                                                                                      @RequestBody RequestModel<ReqUserIDModel> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsSPKUserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> addOrEditMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("addOrEditMsSPKUserManagement");
            }
            if (!CommonHelper.isValidUserIDRequest(request.getDetails())) {
                logger.warn("Response >>> addOrEditMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsSPKUserManagement");
            }
            if (request.getDetails().getType().equalsIgnoreCase(ADD)
                    && this.msSPKUserManagementService.findByNik(request.getDetails().getNik()).isPresent()) {
                return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMsSPKUserManagement", gson);
            }
            if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                ResponseModel<ResCUDUserIdModel> updatedSPKUser = this.msSPKUserManagementService.updateSPKUser(request.getDetails());
                logger.info("Response >>> editMsSPKUserManagement {} ", gson.toJson(updatedSPKUser));

                return ResponseEntity.ok(updatedSPKUser);
            }
            ResponseModel<ResCUDUserIdModel> savedSPKUser = this.msSPKUserManagementService.saveSPKUser(request.getDetails());
            logger.info("Response >>> addMsSPKUserManagement {} ", gson.toJson(savedSPKUser));

            return ResponseEntity.ok(savedSPKUser);
        } catch (Exception exception) {
            logger.warn("Response >>> addOrEditMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsSPKUserManagement", exception);
        }
    }

    @DeleteMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsSPKUserManagement(@RequestHeader("XToken") String authorization,
                                                                                  @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsSPKUserManagement nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> deleteMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("deleteMsSPKUserManagement");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> deleteMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsSPKUserManagement");
            }
            if (this.msSPKUserManagementService.findByNik(nik).isEmpty()) {
                logger.warn("Response >>> deleteMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.NOT_FOUND));
                return responseFailed(TYPE_MS_SPK_MANAGEMENT_DELETE, NOT_FOUND, "deleteMsSPKUserManagement", gson);
            }

            ResponseModel<ResCUDUserIdModel> response = this.msSPKUserManagementService.deleteSPKUser(nik);
            logger.info("Response >>> deleteMsSPKUserManagement : {}", gson.toJson(response));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> deleteMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsSPKUserManagement", exception);
        }
    }

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchMsSPKUserManagement(@RequestHeader("XToken") String authorization,
                                                                                     @RequestBody ReqUserIdBatchModel<MsSPK> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsSPKUserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> saveBatchMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("saveBatchMsSPKUserManagement");
            }
            if (this.isExistInInterval(nikRequester, request.getType())) {
                logger.warn("Response >>> saveBatchMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            if(!this.isValidRequest(request) || CommonHelper.isDuplicateData(request.getData().stream().map(MsSPK::getNik).collect(Collectors.toList()))) {
                logger.warn("Response >>> saveBatchMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("saveBatchMsSPKUserManagement");
            }
            ResponseModel<ResBatchUserId> savedBatchSPKUserId = this.msSPKUserManagementService.saveBatchMsSPKUser(request, nikRequester);

            logger.info("Response >>> saveBatchMsSPKUserManagement {}", gson.toJson(savedBatchSPKUserId));
            return ResponseEntity.ok(savedBatchSPKUserId);

        } catch (Exception e){
            logger.warn("Response >>> saveBatchMsSPKUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsSPKUserManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateExcelMsSPKUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< generateExcelMsSPKUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = msSPKUserManagementService.generateExcelMsSPKUserId();
                logger.info("Response >>> generateExcelMsSPKUserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> generateExcelMsSPKUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("generateExcelMsSPKUserId");
        } catch (Exception e) {
            logger.warn("Response >>> generateExcelMsSPKUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("generateExcelMsSPKUserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsSPKUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsSPKUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester, trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = msSPKUserManagementService.directDownloadExcelMsSPKUserId();
                return responseDownloadFile("directDownloadExcelMsSPKUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsSPKUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsSPKUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsSPKUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsSPKUserId", e);
        }
    }

    private boolean isValidRequest(ReqUserIdBatchModel<MsSPK> request) {
        return request != null
                && request.getType() != null
                && request.getBatchId() != null
                && request.getFileName() != null
                && request.getTotalData() > 0
                && request.getData() != null
                && request.getData().size() > 0
                && this.trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    private boolean isExistInInterval(String nik, String type) {
        return this.trxUserIdBatchService.existInInterval(nik, type, 30);
    }
}
