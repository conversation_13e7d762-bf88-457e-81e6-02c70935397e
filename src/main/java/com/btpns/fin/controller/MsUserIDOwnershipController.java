package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.MsUserIDOwnershipModel;
import com.btpns.fin.model.entity.MsUserIDOwnershipDetail;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.UserIDOwnershipService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping(value = "tema")
@CrossOrigin("*")
public class MsUserIDOwnershipController {
    private static final Logger logger = LoggerFactory.getLogger(MsUserIDOwnershipController.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    TrxUpmRoleService trxUpmRoleService;
    @Autowired
    UserIDOwnershipService userIDOwnershipService;
    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    @GetMapping(value = "/upm/user-id/ownership")
    public ResponseEntity<ResponseModel<ResponseListModel<MsUserIDOwnershipModel>>> getMsUserIDOwnershipManagement(@RequestHeader("XToken") String authorization,
                                                                                                                   @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                                   @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                                                   @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                                                   @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsUserIDOwnershipManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel<MsUserIDOwnershipModel>> response = userIDOwnershipService.getListUserIDOwnership(pageSize, pageNumber, searchFlag, searchData);

                logger.info("Response >>> getMsUserIDOwnershipManagement {}", response.getDetails());
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getMsUserIDOwnershipManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsUserIDOwnershipManagement");
        } catch (Exception e){
            logger.warn("Response >>> getMsUserIDOwnershipManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsUserIDOwnershipManagement", e);
        }
    }

    @GetMapping(value = "/user-id/ownership/{nik:.+}")
    public ResponseEntity<ResponseModel<List<MsUserIDOwnershipDetail>>> getMsUserIDOwnershipDetails(@RequestHeader("XToken") String authorization,
                                                                                                    @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsUserIDOwnershipDetails from {}", getProfile(gson, token));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(token.getProfile().getPreferred_username());

            if (!nikRequester.equalsIgnoreCase(nik)) {
                logger.warn("Response >>> getMsUserIDOwnershipDetails : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsUserIDOwnershipDetails");
            }
            ResponseModel<List<MsUserIDOwnershipDetail>> response = userIDOwnershipService.getDetailUserIDOwnership(nik);

            logger.info("Response >>> getMsUserIDOwnershipDetails {}", response.getDetails());
            return ResponseEntity.ok(response);
        } catch (Exception e){
            logger.warn("Response >>> getMsUserIDOwnershipDetails : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsUserIDOwnershipDetails", e);
        }
    }
}
