package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.SOPNumberModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsSOPNumberService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping(value = "tema/upm/management/sop")
@CrossOrigin("*")
public class MsSOPNumberController {
    private static final Logger logger = LoggerFactory.getLogger(MsSOPNumberController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsSOPNumberService msSOPNumberService;

    @GetMapping()
    public ResponseEntity<ResponseModel<SOPNumberModel>> getListMsSOPNumber(@RequestHeader("XToken") String authorization) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getListMsSOPNumber from {}", getProfile(gson, token));

            if (CommonHelper.isNonViewerUPMRole(trxUpmRoleService.getTrxUpmRole(token.getProfile().getPreferred_username()))) {
                ResponseModel<SOPNumberModel> response = msSOPNumberService.getMsSOPNumberData();

                logger.info("Response >>> getListMsSOPNumber {}", gson.toJson(response.getDetails()));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getListMsSOPNumber : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getListMsSOPNumber");
        } catch (Exception e){
            logger.warn("Response >>> getListMsSOPNumber : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListMsSOPNumber", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<SOPNumberModel>> editMsSOPNumber(@RequestHeader("XToken") String authorization,
                                                                         @RequestBody RequestModel<SOPNumberModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< editMsSOPNumber {} from {}", gson.toJson(request), getProfile(gson, token));

            if (CommonHelper.isNonViewerUPMRole(trxUpmRoleService.getTrxUpmRole(token.getProfile().getPreferred_username()))) {
                ResponseModel<SOPNumberModel> response = msSOPNumberService.editMsSOPNumberData(request.getDetails());

                logger.info("Response >>> editMsSOPNumber {}", gson.toJson(response.getDetails()));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> editMsSOPNumber : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("editMsSOPNumber");
        } catch (Exception e){
            logger.warn("Response >>> editMsSOPNumber : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("editMsSOPNumber", e);
        }
    }
}
