package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDAppModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsUserIDApplicationService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.responseInternalServerError;

@RestController
@RequestMapping(value = "tema/user-id/applications")
@CrossOrigin("*")
public class MsUserIDApplicationController {
    private static final Logger logger = LoggerFactory.getLogger(MsUserIDApplicationController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private MsUserIDApplicationService msUserIDApplicationService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @GetMapping
    public ResponseEntity<ResponseModel<List<UserIDAppModel>>> getListUserIDApplications(@RequestHeader("XToken") String authorization,
                                                                                         @RequestParam(value = "nik", required = false) String nik,
                                                                                         @RequestParam(value = "isUAR", required = false) Boolean isUAR,
                                                                                         @RequestParam(value = "isVisible", required = false) Boolean isVisible) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getListUserIDApplications from {}", getProfile(gson, token));

            ResponseModel<List<UserIDAppModel>> response = msUserIDApplicationService.getAllAvaiableUserIDApplications(nik, isVisible, isUAR);

            logger.info("Response >>> getListUserIDApplications {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getListUserIDApplications : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListUserIDApplications", exception);
        }
    }
}
