package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.TemaAccessRoleModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsTemaAccessRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping("tema/upm/management/access-role")
@CrossOrigin("*")
public class MsTemaAccessRoleController {
    private static final Logger logger = LoggerFactory.getLogger(MsProsperaRoleController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    MsTemaAccessRoleService msTemaAccessRoleService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel<TemaAccessRoleModel>>> getListTemaAccessRole(@RequestHeader("XToken") String authorization,
                                                                                                       @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                       @RequestParam(value = "limit", defaultValue = "10") Integer pageSize) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getListTemaAccessRole page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, token));

            ResponseModel<ResponseListModel<TemaAccessRoleModel>> response = msTemaAccessRoleService.getListTemaAccessRole(pageNumber, pageSize);
            logger.info("Response >>> getListTemaAccessRole size {}", response.getDetails());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getListTemaAccessRole : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListTemaAccessRole", exception);
        }
    }
}
