package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.ProsperaRoleModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsProsperaRoleService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.ADD;
import static com.btpns.fin.helper.Constants.EDIT;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping("tema/upm/management/prospera-role")
@CrossOrigin("*")
public class MsProsperaRoleController {
    private static final Logger logger = LoggerFactory.getLogger(MsProsperaRoleController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    private MsProsperaRoleService prosperaRoleService;

    @Autowired
    private TrxUpmRoleService upmRoleService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel<ProsperaRoleModel>>> getListRoleProspera(@RequestHeader("XToken") String authorization,
                                                                                                   @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                   @RequestParam(value = "limit", defaultValue = "50") Integer pageSize) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getListRoleProspera page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.isNonViewerUPMRole(upmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getListRoleProspera : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getListRoleProspera");
            }

            ResponseModel<ResponseListModel<ProsperaRoleModel>> response = prosperaRoleService.getListProsperaRole(pageNumber, pageSize);
            logger.info("Response >>> getListRoleProspera size {}", response.getDetails());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getListRoleProspera : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListRoleProspera", exception);
        }
    }

    @GetMapping(value = "/{temaRoleCode}")
    public ResponseEntity<ResponseModel<ProsperaRoleModel>> getMsProsperaRole(@RequestHeader("XToken") String authorization,
                                                                              @PathVariable("temaRoleCode") String temaRoleCode) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsProsperaRole temaRoleCode {} from {}", temaRoleCode, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!isNonViewerUPMRole(upmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsProsperaRole : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsProsperaRole");
            }
            if (StringUtils.isBlank(temaRoleCode)) {
                logger.warn("Response >>> getMsProsperaRole : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsProsperaRole");
            }
            ResponseModel<ProsperaRoleModel> response = this.prosperaRoleService.getDetailProsperaRole(temaRoleCode);
            logger.info("Response >>> getMsProsperaRole {}", gson.toJson(response.getDetails()));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsProsperaRole : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsProsperaRole", exception);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ProsperaRoleModel>> addOrEditProsperaRole(@RequestHeader("XToken") String authorization,
                                                                                  @RequestBody RequestModel<ProsperaRoleModel> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditProsperaRole {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!isNonViewerUPMRole(upmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> addOrEditProsperaRole : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("addOrEditProsperaRole");
            }
            if (!isValidRequest(request)) {
                logger.warn("Response >>> addOrEditProsperaRole : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditProsperaRole");
            }

            ResponseModel<ProsperaRoleModel> savedProsperaRole = this.prosperaRoleService.addOrEditProsperaRole(authorization, request.getType(), request.getDetails());
            logger.info("Response >>> addOrEditProsperaRole {} ", gson.toJson(savedProsperaRole));

            return ResponseEntity.ok(savedProsperaRole);
        } catch (Exception exception) {
            logger.warn("Response >>> addOrEditProsperaRole : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditProsperaRole", exception);
        }
    }

    private boolean isValidRequest(RequestModel<ProsperaRoleModel> request) {
        return (request.getType().equalsIgnoreCase(ADD) || request.getType().equalsIgnoreCase(EDIT))
                && request.getDetails().getProsperaRoleCode() != null
                && request.getDetails().getTemaRoleCode() != null
                && request.getDetails().getRoleName() != null
                && request.getDetails().getOfficeLevel() != null;
    }
}
