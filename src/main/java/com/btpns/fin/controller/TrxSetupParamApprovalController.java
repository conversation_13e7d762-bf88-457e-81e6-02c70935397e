package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.TrxSetupParamRequestDTO;
import com.btpns.fin.model.response.ResTrxSetupParamRequestModel;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "/tema/setup-parameter/approval")
@CrossOrigin("*")
public class TrxSetupParamApprovalController {
    private static final Logger logger = LoggerFactory.getLogger(TrxSetupParamApprovalController.class);

    @Autowired
    TrxSetupParamApprovalService trxSetupParamApprovalService;

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    TrxSetupParamRequestService trxSetupParamRequestService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    EmailNotificationService emailNotificationService;

    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping()
    public ResponseEntity<ResTrxSetupParamRequestModel> updateSetupParamApproval(@RequestBody ApprovalModel approvalModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< updateSetupParamApproval with data {} from {}", gson.toJson(approvalModel), getProfile(gson, profile));
            String nikPUKSSO = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            boolean isPuk1 = false; boolean isPuk2 = false;

            DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
            TrxSetupParamApproval tspa = trxSetupParamApprovalService.getTrxSetupParamApprovalByTicketId(approvalModel.getTicketId());
            LocalDateTime currStateDt = LocalDateTime.now();
            String sCurrStateDt = DateTimeHelper.getFullDate(currStateDt);
            tspa.setCurrentStateDT(DateTimeHelper.getFullDateAsDate(sCurrStateDt));
            //validation puk approval
            ValidationPUKApprovalModel vpam = validationPUKApproval(tspa, nikPUKSSO, approvalModel, sCurrStateDt);
            tspa = vpam.getTspa();
            isPuk1 = vpam.isPuk1();
            isPuk2 = vpam.isPuk2();

            ResTrxSetupParamRequestModel resTSPRM = new ResTrxSetupParamRequestModel();
            resTSPRM.setTicketId(approvalModel.getTicketId());
            if(isPuk1 || isPuk2) {
                if (tspa.getPuk2NIK() != null && tspa.getCurrentState().equals(CURR_STATUS_WAITING_PUK1) && approvalModel.getStatus().equals(CURR_STATUS_APPROVED)) {
                    tspa.setCurrentState(CURR_STATUS_WAITING_PUK2);
                } else {
                    tspa.setCurrentState(approvalModel.getStatus());
                }
                TrxSetupParamApproval savedTsa = trxSetupParamApprovalService.updateTrxSetupParamApproval(tspa);

                TrxAudittrail ta = new TrxAudittrail();
                String pukNik = tspa.getPuk1NIK();
                if (isPuk2) {
                    pukNik = tspa.getPuk2NIK();
                }
                ta.setNik(pukNik);
                ta.setTicketId(approvalModel.getTicketId());
                if (tspa.getPuk2NIK() != null && tspa.getCurrentState().equals(CURR_STATUS_WAITING_PUK2) && approvalModel.getStatus().equals(CURR_STATUS_APPROVED)) {
                    ta.setAction(CURR_STATUS_WAITING_PUK2);
                } else {
                    ta.setAction(approvalModel.getStatus());
                }
                ta.setCreateDateTime(DateTimeHelper.getFullDateAsDate(sCurrStateDt));
                //json additional info
                TimelineStatusModel tsm = new TimelineStatusModel();
                if (approvalModel.getStatus().equals(CURR_STATUS_REJECTED)) {
                    tsm.setStatus(TIMELINE_STATUS_REJECT_TICKET);
                    tsm.setNote(approvalModel.getNotes());
                } else {
                    tsm.setStatus(TIMELINE_STATUS_APPROVE_TICKET);
                }
                String pukName = msEmployeeService.getMsEmployeeByNik(pukNik).getFullName();
                //check delegation
                String delegationId = savedTsa.getPuk1DelegationId();
                if (isPuk2) {
                    delegationId = savedTsa.getPuk2DelegationId();
                }
                if (trxDelegationService.getTrxDelegationByDelegationId(delegationId) != null) {
                    tsm.setPic(TIMELINE_PIC_PUK_DELEGATION + pukNik + " - " + pukName);
                } else {
                    tsm.setPic(TIMELINE_PIC_PUK + pukNik + " - " + pukName);
                }
                tsm.setTimestamp(dateFormater3.format(ta.getCreateDateTime()));
                ta.setAdditionalInfo(new Gson().toJson(tsm));
                TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(ta);

                //send email approved or rejected to user
                TrxSetupParamRequestDTO savedTspr = trxSetupParamRequestService.getTrxSetupParamRequestByTicketIdData(approvalModel.getTicketId());

                //set current state
                savedTspr.setTrxSetupParamApproval(trxSetupParamRequestService.mappingTrxSetupParamApprovalDTO(savedTsa));
                //set nama aplikasi
                String[] splitAplikasi = savedTspr.getAplikasi().split(",");
                List<String> listAplikasi = Arrays.asList(splitAplikasi);
                List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
                savedTspr.setAplikasi(msta.toString().replaceAll("\\[|\\]", ""));
                //set status current
                String ecurrState = msSystemParamService.getMsSystemParamDetail(savedTsa.getCurrentState()).getParamDetailDesc();
                savedTspr.getTrxSetupParamApproval().setCurrentState(ecurrState);

                sendEmailApproval(tspa, approvalModel, savedTspr, savedTspr.getNikRequester(), validateCCNikDirectPuk(savedTspr, isPuk1, isPuk2), msEmployeeService.getListSkippedNikPukSp(tspa));

                if(savedTsa != null && savedTa != null) {
                    resTSPRM.setStatus(SUCCESS.getCode());
                    resTSPRM.setStatusDesc(SUCCESS.getValue());
                } else {
                    resTSPRM.setStatus(FAILED.getCode());
                    resTSPRM.setStatusDesc(FAILED.getValue());
                }
            } else {
                resTSPRM.setStatus(FAILED.getCode());
                resTSPRM.setStatusDesc(FAILED.getValue());
            }

            logger.info("Response >>> updateSetupParamApproval : {}", gson.toJson(resTSPRM));
            return ResponseEntity.ok(resTSPRM);
        } catch (Exception e) {
            logger.error("Fail to post updateSetupParamApproval ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    public ValidationPUKApprovalModel validationPUKApproval(TrxSetupParamApproval tspa, String nikPUKSSO, ApprovalModel approvalModel, String sCurrStateDt){
        ValidationPUKApprovalModel vpam = new ValidationPUKApprovalModel();
        boolean isPuk1 = false; boolean isPuk2 = false;
        if(tspa.getPuk1NIK().toLowerCase().equals(nikPUKSSO.toLowerCase())
                && (tspa.getPuk1Status().equals(PUK1_STATUS_WAITING) || tspa.getPuk1Status().equals(CURR_STATUS_REJECTED))){
            tspa.setPuk1Status(approvalModel.getStatus());
            tspa.setPuk1Notes(approvalModel.getNotes());
            tspa.setPuk1Dt(DateTimeHelper.getFullDateAsDate(sCurrStateDt));
            isPuk1 = true;
        } else {
            if(tspa.getPuk2NIK() != null) {
                if (tspa.getPuk2NIK().toLowerCase().equals(nikPUKSSO.toLowerCase())
                        && tspa.getPuk1Status().equals(CURR_STATUS_APPROVED)
                        && (tspa.getPuk2Status().equals(PUK2_STATUS_WAITING) || tspa.getPuk2Status().equals(CURR_STATUS_REJECTED))) {
                    tspa.setPuk2Status(approvalModel.getStatus());
                    tspa.setPuk2Notes(approvalModel.getNotes());
                    tspa.setPuk2Dt(DateTimeHelper.getFullDateAsDate(sCurrStateDt));
                    isPuk2 = true;
                }
            }
        }
        vpam.setTspa(tspa);
        vpam.setPuk1(isPuk1);
        vpam.setPuk2(isPuk2);
        return vpam;
    }

    private String validateCCNikDirectPuk(TrxSetupParamRequestDTO savedTSPR, boolean isPuk1, boolean isPuk2){
        String nikDirectPuk = "";
        TrxSetupParamApprovalDTO savedTSPA = savedTSPR.getTrxSetupParamApproval();
        MsEmployee directPUK = msEmployeeService.getDirectPUK(savedTSPR.getNikRequester());
        if(directPUK != null) {
            boolean isCCDirectPuk = false;
            if (isPuk1 && !savedTSPA.getPuk1NIK().equals(directPUK.getNik())) {
                isCCDirectPuk = true;
            }
            if (isPuk2 && !savedTSPA.getPuk2NIK().equals(directPUK.getNik())) {
                isCCDirectPuk = true;
            }
            if(isCCDirectPuk){
                nikDirectPuk = directPUK.getNik();
            }
        }
        return nikDirectPuk;
    }

    private void sendEmailApproval(TrxSetupParamApproval tspa, ApprovalModel approvalModel, TrxSetupParamRequestDTO savedTspr, String nikRequester, String ccNikDirectPuk, List<String> skippedNikPUKs) {
        MsEmployee waitingPUK = new MsEmployee();
        if (tspa.getPuk2NIK() != null && tspa.getCurrentState().equals(CURR_STATUS_WAITING_PUK2) && approvalModel.getStatus().equals(CURR_STATUS_APPROVED)) {
            waitingPUK = msEmployeeService.getMsEmployeeByNik(tspa.getPuk2NIK());
            //send email to puk2
            createApproval2Async(savedTspr, skippedNikPUKs, waitingPUK);
            //send email to user
            createApprovedAsync(nikRequester, savedTspr, EMPTY, waitingPUK);
        } else if (approvalModel.getStatus().equals(CURR_STATUS_APPROVED)) {
            //send email approved.
            if(tspa.getPuk2NIK() == null && tspa.getCurrentState().equals(CURR_STATUS_APPROVED)){
                waitingPUK = msEmployeeService.getMsEmployeeByNik(tspa.getPuk1NIK());
            } else {
                waitingPUK = msEmployeeService.getMsEmployeeByNik(tspa.getPuk2NIK());
            }
            createApprovedAsync(nikRequester, savedTspr, ccNikDirectPuk, waitingPUK);
        }
        //send email rejected.
        if (approvalModel.getStatus().equals(CURR_STATUS_REJECTED)) {
            String rejectedBy = "";
            if (CURR_STATUS_REJECTED.equalsIgnoreCase(savedTspr.getTrxSetupParamApproval().getPuk1Status())){
                rejectedBy = savedTspr.getTrxSetupParamApproval().getPuk1NIK();
            }else {
                rejectedBy = savedTspr.getTrxSetupParamApproval().getPuk2NIK();
            }
            MsEmployee rejectedPUK = msEmployeeService.getMsEmployeeByNik(rejectedBy);
            createRejectedAsync(nikRequester, savedTspr, rejectedPUK);
        }
    }

    public void createApproval2Async(TrxSetupParamRequestDTO trxSetupParamRequest, List<String> skippedNikPUKs, MsEmployee waitingPUK) {
        //check director
        HashMap<String, String> mapPUK2Director = msEmployeeDirectorService.isPukDirectorByNikOptima(trxSetupParamRequest.getTrxSetupParamApproval().getPuk2NIK());
        emailNotificationService.sendApprovalSPToUpmTemaNotification(trxSetupParamRequest, skippedNikPUKs, waitingPUK, mapPUK2Director);
    }

    public void createApprovedAsync(String userNik, TrxSetupParamRequestDTO trxSetupParamRequest, String ccNikDirectPuk, MsEmployee waitingPUK) {
        emailNotificationService.sendApprovedSPToUpmTemaNotification(userNik, trxSetupParamRequest, ccNikDirectPuk, waitingPUK);
    }

    public void createRejectedAsync(String userNik, TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee rejectedPUK) {
        emailNotificationService.sendCreateRejectedSPToUpmTemaNotification(userNik, trxSetupParamRequest, rejectedPUK);
    }
}
