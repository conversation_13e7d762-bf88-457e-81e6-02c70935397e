package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.ResDelegationModel;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "/tema/delegation")
@CrossOrigin("*")
public class DelegationController {
    private static final Logger logger = LoggerFactory.getLogger(DelegationController.class);

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    TrxFuidApprovalService trxFuidApprovalService;

    @Autowired
    TrxSetupParamRequestService trxSetupParamRequestService;

    @Autowired
    TrxSetupParamApprovalService trxSetupParamApprovalService;

    @Autowired
    EmailNotificationService emailNotificationService;

    @Autowired
    DelegationService delegationService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping()
    public ResponseEntity<ResDelegationModel> saveDelegation(@RequestBody DelegationModel delegationModel, @RequestHeader("XToken") String authorization) {
        try {
            LocalDateTime dateNow = LocalDateTime.now();
            //delete delegation if nik requester already register before
            Token profile = new Token(authorization);
            logger.info("Received <<< saveDelegation with data {} from {}", gson.toJson(delegationModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            if (!CommonHelper.isValidDateDuration(delegationModel.getStartDate(), delegationModel.getEndDate())) {
                logger.info("Response >>> saveDelegation : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }

            encodeRequestDel(delegationModel);
            if (isExistInInterval(nikRequester)) {
                logger.info("Response >>> saveDelegation : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            String delegationId = "DL9912280099";
            String currDtDelegation = new SimpleDateFormat("yyMMdd").format(new Date());
            //cek last delegation di db ada ngga
            logger.info("getLastDelegationId: " + trxDelegationService.getLastDelegationId());
            if (trxDelegationService.getLastDelegationId() == null) {
                delegationId = "DL" + currDtDelegation + "0001";
            } else {
                String lastDelegationId = trxDelegationService.getLastDelegationId();
                String strlastDate = lastDelegationId.substring(2, 8);
                logger.info("strlastDate: " + strlastDate);
                //cek tanggalnya sama ngga sama currentdate
                if (strlastDate.equals(currDtDelegation)) {
                    String strDelegationNum = lastDelegationId.substring(8, 12);
                    logger.info("strDelegationNum: " + strDelegationNum);
                    Integer delegationNum = Integer.parseInt(strDelegationNum) + 1;
                    delegationId = "DL" + currDtDelegation + String.format("%04d", delegationNum);
                } else {
                    delegationId = "DL" + currDtDelegation + "0001";
                }
            }
            TrxDelegation td = new TrxDelegation();
            td.setDelegationId(delegationId);
            td.setNikRequester(nikRequester);
            MsEmployee empRequester = msEmployeeService.getMsEmployeeByNik(nikRequester);
            td.setNamaRequester(empRequester.getFullName());
            td.setJabatanRequester(empRequester.getOccupationDesc());
            td.setNikDelegation(delegationModel.getNikDelegation());
            MsEmployee empDelegation = msEmployeeService.getMsEmployeeByNik(delegationModel.getNikDelegation());
            td.setNamaDelegation(empDelegation.getFullName());
            td.setJabatanDelegation(empDelegation.getOccupationDesc());
            td.setStartDate(DateTimeHelper.getDateDelegation(delegationModel.getStartDate()));
            td.setEndDate(DateTimeHelper.getDateDelegation(delegationModel.getEndDate()));
            td.setInfo(delegationModel.getInfo());
            td.setStatus(Constants.DELEGATION_STATUS_ACTIVE);
            td.setCreateDateTime(dateNow);
            td.setUpdateDateTime(dateNow);

            //check jika ada nikRequester sama + active, maka update status jadi inactive
            if(trxDelegationService.getDelegationActiveByNikRequester(nikRequester) != null){
                TrxDelegation lastActiveTD = trxDelegationService.getDelegationActiveByNikRequester(nikRequester);
                trxDelegationService.updateTrxDelegationToInactive(lastActiveTD);
            }
            TrxDelegation saveTD = trxDelegationService.saveTrxDelegation(td);

            //insert auditrail
            // TODO : register to trxAudittrail server.
            TrxAudittrail ta = new TrxAudittrail();
            ta.setNik(nikRequester);
            ta.setAction(DELEGATION_ACTION_DELEGATED_ACTIVE);
            ta.setCreateDateTime(dateNow);
            ta.setTicketId(delegationId);
            //json additional info
            TimelineStatusModel tsm = new TimelineStatusModel();
            tsm.setStatus(TIMELINE_STATUS_DELEGATION_ACTIVE);
            tsm.setPic(TIMELINE_PIC_USER + nikRequester + " - " + profile.getProfile().getName());
            tsm.setTimestamp(DateTimeHelper.getDateFormater3(dateNow));
            ta.setAdditionalInfo(new Gson().toJson(tsm));
            TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(ta);

            ResDelegationModel resDelegationModel = new ResDelegationModel();
            resDelegationModel.setDelegationId(delegationId);
            if(saveTD != null && savedTa != null) {
                //send email delegation to delegated PUK.
                sendEmailRequest(saveTD);
                //update puk delegated and send email
                updatePUKDelegatedAndSendEmail(
                        saveTD.getNikDelegation(),
                        trxFuidRequestService.getTrxFuidReqJoinApprovalWaitPuk1(saveTD.getNikRequester()),
                        trxSetupParamRequestService.getTrxSetupParamReqJoinApprovalPuk1(saveTD.getNikRequester()),
                        saveTD.getDelegationId());
                updatePUKDelegatedAndSendEmail(
                        saveTD.getNikDelegation(),
                        trxFuidRequestService.getTrxFuidReqJoinApprovalWaitPuk2(saveTD.getNikRequester()),
                        trxSetupParamRequestService.getTrxSetupParamReqJoinApprovalPuk2(saveTD.getNikRequester()),
                        saveTD.getDelegationId());

                resDelegationModel.setStatus(SUCCESS.getCode());
                resDelegationModel.setStatusDesc(SUCCESS.getValue());
            } else {
                resDelegationModel.setStatus(FAILED.getCode());
                resDelegationModel.setStatusDesc(FAILED.getValue());
            }
            logger.info("Response >>> saveDelegation : {}", gson.toJson(resDelegationModel));
            return ResponseEntity.ok(resDelegationModel);
        } catch (Exception e) {
            logger.error("Fail to post saveDelegation ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping()
    public ResponseEntity<DelegationPaginationModel> getDelegationList(@RequestParam("page") int pageNumber, @RequestParam("limit") int pageSize, @RequestHeader("XToken") String authorization){
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getDelegationList page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            List<DelegationListModel> delegationListModels = new ArrayList<DelegationListModel>();

            //offset start from 0
            int pageNumMin1 = pageNumber - 1;
            Page<TrxDelegation> pageTD = trxDelegationService.getListTrxDelegationByNikRequester(nikRequester, pageNumMin1, pageSize);
            List<TrxDelegation> listTD = pageTD.getContent();
            for(TrxDelegation td : listTD){
                DelegationListModel delegationListModel = Mapper.toDelegationListModel(td);
                delegationListModels.add(delegationListModel);
            }

            DelegationPaginationModel dpm = new DelegationPaginationModel();
            dpm.setPage(pageNumber);
            dpm.setLimit(pageSize);
            dpm.setDelegationListModel(delegationListModels);
            dpm.setTotalPages(pageTD.getTotalPages());
            dpm.setTotalItems(pageTD.getTotalElements());
            logger.info("Response >>> getDelegationList : with size {}", dpm.getDelegationListModel().size());
            return ResponseEntity.ok(dpm);
        } catch (Exception e) {
            logger.error("Fail to get getDelegationList ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/{delegationId}")
    public ResponseEntity<DelegationDetailModel> getDelegationDetailByDelegationId(@PathVariable("delegationId") String delegationId, @RequestHeader("XToken") String authorization){
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< getDelegationDetailByDelegationId for {} from {} ", delegationId, getProfile(gson, profile));
            TrxDelegation trxDelegation = trxDelegationService.getTrxDelegationByDelegationId(delegationId);
            //validation access based on token
            boolean passValidation = false;
            if(nikRequester.toLowerCase().equals(trxDelegation.getNikRequester().toLowerCase())){
                passValidation = true;
            }
            if(passValidation) {
                DelegationDetailModel delegationDetailModel = Mapper.toDelegationDetailModel(trxDelegation);
                logger.info("Response >>> getDelegationDetailByDelegationId : {} ", gson.toJson(delegationDetailModel));
                return ResponseEntity.ok(delegationDetailModel);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
        } catch (Exception e){
            logger.error("Fail to get getDelegationDetailByDelegationId ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/cancel")
    public ResponseEntity<ResDelegationModel> cancelDelegation(@RequestBody DelegationCancelModel delegationCancelModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< cancelDelegation for {} from {} ", gson.toJson(delegationCancelModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            boolean isSuccessCancel = false;
            TrxDelegation trxDelegation = trxDelegationService.getTrxDelegationByDelegationId(delegationCancelModel.getDelegationId());
            if(trxDelegation != null){
                if(trxDelegation.getNikRequester().toLowerCase().equals(nikRequester.toLowerCase())){
                    //cancel
                    trxDelegationService.updateTrxDelegationToInactive(trxDelegation);

                    //insert auditrail
                    // TODO : register to trxAudittrail server.
                    TrxAudittrail ta = new TrxAudittrail();
                    ta.setNik(nikRequester);
                    ta.setAction(DELEGATION_ACTION_DELEGATED_INACTIVE);
                    LocalDateTime dateNow = LocalDateTime.now();
                    ta.setCreateDateTime(dateNow);
                    ta.setTicketId(delegationCancelModel.getDelegationId());
                    //json additional info
                    TimelineStatusModel tsm = new TimelineStatusModel();
                    tsm.setStatus(TIMELINE_STATUS_DELEGATION_INACTIVE);
                    tsm.setPic(TIMELINE_PIC_USER + nikRequester + " - " + profile.getProfile().getName());
                    tsm.setTimestamp(DateTimeHelper.getDateFormater3(dateNow));
                    ta.setAdditionalInfo(new Gson().toJson(tsm));
                    TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(ta);

                    if(savedTa != null) {
                        isSuccessCancel = true;
                    }
                }
            }

            ResDelegationModel resDelegationModel = new ResDelegationModel();
            resDelegationModel.setDelegationId(delegationCancelModel.getDelegationId());
            if(isSuccessCancel) {
                resDelegationModel.setStatus("200");
                resDelegationModel.setStatusDesc("success");
            } else {
                resDelegationModel.setStatus("201");
                resDelegationModel.setStatusDesc("failed");
            }
            logger.info("Response >>> cancelDelegation : {} ", gson.toJson(resDelegationModel));
            return ResponseEntity.ok(resDelegationModel);
        } catch (Exception e){
            logger.error("Fail to post cancelDelegation ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    public boolean isExistInInterval(String nik) {
        return trxDelegationService.existInInterval(nik, 180);
    }

    public void sendEmailRequest(TrxDelegation trxDelegation) {
        emailNotificationService.sendCreateDelegation(trxDelegation);
    }

    public void updatePUKDelegatedAndSendEmail(String pukNik, List<TrxFuidRequest> listTFR, List<TrxSetupParamRequest> listTSPR, String delegationId){
        //FU
        if(listTFR.size() > 0){
            for(TrxFuidRequest tfr : listTFR){
                if(tfr.getTrxFuidApproval().getCurrentState().equals(CURR_STATUS_WAITING_PUK1)){
                    trxFuidApprovalService.updateTrxFuidApprovalPUK1ByTicketId(tfr.getTicketId(), pukNik, delegationId);
                }
                if(tfr.getTrxFuidApproval().getCurrentState().equals(CURR_STATUS_WAITING_PUK2)){
                    trxFuidApprovalService.updateTrxFuidApprovalPUK2ByTicketId(tfr.getTicketId(), pukNik, delegationId);
                }
                delegationService.sendEmailtoDelegatedFU(pukNik, tfr);
            }
        }
        //SP
        if(listTSPR.size() > 0){
            for(TrxSetupParamRequest tspr : listTSPR){
                if(tspr.getTrxSetupParamApproval().getCurrentState().equals(CURR_STATUS_WAITING_PUK1)){
                    trxSetupParamApprovalService.updateTrxSetupParamApprovalPUK1ByTicketId(tspr.getTicketId(), pukNik, delegationId);
                }
                if(tspr.getTrxSetupParamApproval().getCurrentState().equals(CURR_STATUS_WAITING_PUK2)){
                    trxSetupParamApprovalService.updateTrxSetupParamApprovalPUK2ByTicketId(tspr.getTicketId(), pukNik, delegationId);
                }
                delegationService.sendEmailtoDelegatedSP(pukNik, tspr);
            }
        }
    }
}
