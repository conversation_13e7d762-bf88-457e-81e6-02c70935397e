package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.ListMsHolidayModel;
import com.btpns.fin.model.MsHolidayModel;
import com.btpns.fin.model.entity.MsHolidayList;
import com.btpns.fin.model.request.ReqMsHolidayModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.ResMsHolidayModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsHolidayListService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseHelper.responseBadRequest;
import static com.btpns.fin.helper.ResponseStatus.*;

@Controller
@RequestMapping(value = "/tema/upm/holiday/management")
@CrossOrigin("*")
public class MsHolidayListController {
    private static final Logger logger = LoggerFactory.getLogger(MsHolidayListController.class);

    @Autowired
    MsHolidayListService msHolidayListService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    //detail
    @GetMapping(value = "/{id}")
    public ResponseEntity<ResponseModel<MsHolidayModel>> getMsHolidayById(@RequestHeader("XToken") String authorization,
                                                                          @PathVariable("id") String id){
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsHolidayById id {} from {}", id, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(id)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (trxUpmRoleService.getTrxUpmRole(nikRequester).getRole().equals(UPM_ROLE_ADMIN)) {
                        MsHolidayList mhl = msHolidayListService.getMsHolidayById(id);
                        ResponseModel<MsHolidayModel> response = new ResponseModel<>();
                        if(mhl != null){
                            response = buildResponse(TYPE_MS_HOLIDAY_GET, SUCCESS, buildResMsHolidayDetailModel(mhl));
                        }
                        logger.info("Response >>> getMsHolidayById {} ", gson.toJson(response));

                        return ResponseEntity.ok(response);
                    }
                    return responseForbidden("getMsHolidayById");
                }
                return responseForbidden("getMsHolidayById");
            }
            return responseBadRequest("getMsHolidayById");
        } catch (Exception e){
            return responseInternalServerError("getMsHolidayById", e);
        }
    }

    //list
    @GetMapping
    ResponseEntity<ResponseModel<ListMsHolidayModel>> getMsHolidayListByPeriod(@RequestParam(value = "period", required = false, defaultValue = "") String period,
                                                                               @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                               @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                               @RequestHeader("XToken") String authorization){
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsHolidayListByPeriod page {} limit {} period {} from {}", pageNumber, pageSize, period, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (trxUpmRoleService.getTrxUpmRole(nikRequester).getRole().equals(UPM_ROLE_ADMIN)) {
                    int pageNumMin1 = pageNumber - 1;
                    Page<MsHolidayList> pageMsHoliday = msHolidayListService.getHolidayListAll(pageNumMin1, pageSize, period);
                    List<MsHolidayList> listMsHoliday = pageMsHoliday.getContent();
                    List<MsHolidayModel> listMsHolidayModel = new ArrayList<MsHolidayModel>();
                    for (MsHolidayList mhl : listMsHoliday) {
                        MsHolidayModel mhm = Mapper.toMsHolidayModel(mhl);
                        listMsHolidayModel.add(mhm);
                    }
                    ResponseModel<ListMsHolidayModel> response = buildResponse(SUCCESS, buildResMsHolidayListModel(pageSize, pageNumber, listMsHolidayModel, pageMsHoliday.getTotalPages(), pageMsHoliday.getTotalElements()));
                    logger.info("Response >>> getMsHolidayListByPeriod size {}", response.getDetails().getMsHolidayModelList().size());
                    return ResponseEntity.ok(response);
                }
                return responseForbidden("getmsHolidayListByPeriod");
            }
            return responseForbidden("getmsHolidayListByPeriod");
        } catch (Exception e) {
            return responseInternalServerError("getmsHolidayListByPeriod", e);
        }
    }

    //add or edit
    @PostMapping
    public ResponseEntity<ResponseModel<ResMsHolidayModel>> addOrEditMsHoliday(@RequestHeader("XToken") String authorization,
                                                                               @RequestBody RequestModel<ReqMsHolidayModel> request){
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsHoliday {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(isValidRequest(request)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if(trxUpmRoleService.getTrxUpmRole(nikRequester).getRole().equals(UPM_ROLE_ADMIN)) {
                        ResponseModel<ResMsHolidayModel> response = new ResponseModel<>();
                        if (msHolidayListService.getMsHolidayById(request.getDetails().getId()) != null) {
                            if (EDIT.equalsIgnoreCase(request.getDetails().getType())) {
                                MsHolidayList updateMsHoliday = msHolidayListService.updateMsHolidayList(Mapper.toMsHolidayListEntity(request.getDetails()));
                                if(updateMsHoliday != null){
                                    response = buildResponse(TYPE_MS_HOLIDAY_ADD_EDIT, SUCCESS, buildResMsHolidayModel(request.getDetails().getId(), EDIT));
                                }
                                logger.info("Response >>> editMsHoliday {} ", gson.toJson(response));

                                return ResponseEntity.ok(response);
                            }
                            return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMsHoliday", gson);
                        }

                        MsHolidayList saveMsHoliday = msHolidayListService.saveMsHolidayList(Mapper.toMsHolidayListEntity(request.getDetails()));
                        if(saveMsHoliday != null){
                            response = buildResponse(TYPE_MS_HOLIDAY_ADD_EDIT, SUCCESS, buildResMsHolidayModel(saveMsHoliday.getId(), ADD));
                        }
                        logger.info("Response >>> addMsHoliday {} ", gson.toJson(response));

                        return ResponseEntity.ok(response);
                    }
                    return responseForbidden("addOrEditMsHoliday");
                }
                return responseForbidden("addOrEditMsHoliday");
            }
            return responseBadRequest("addOrEditMsHoliday");
        } catch (Exception e){
            return responseInternalServerError("addOrEditMsHoliday", e);
        }
    }

    //delete
    @DeleteMapping(value = "/{id}")
    public ResponseEntity<ResponseModel<ResMsHolidayModel>> deleteMsHoliday(@RequestHeader("XToken") String authorization,
                                                                                  @PathVariable("id") String id) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsHoliday id {} from {}", id, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(id)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (trxUpmRoleService.getTrxUpmRole(nikRequester).getRole().equals(UPM_ROLE_ADMIN)) {
                        if (msHolidayListService.getMsHolidayById(id) != null) {
                            ResponseStatus status = FAILED;
                            if(msHolidayListService.deleteMsHolidayById(id) > 0){
                                status = SUCCESS;
                            }
                            ResponseModel<ResMsHolidayModel> response =
                                    buildResponse(TYPE_MS_HOLIDAY_DELETE, status, buildResMsHolidayModel(id, DELETE));
                            logger.info("Response >>> deleteMsHoliday : {}", gson.toJson(response));

                            return ResponseEntity.ok(response);
                        }
                        return responseFailed(TYPE_MS_HOLIDAY_DELETE, NOT_FOUND, "deleteMsHoliday", gson);
                    }
                    return responseForbidden("deleteMsHoliday");
                }
                return responseForbidden("deleteMsHoliday");
            }
            return responseBadRequest("deleteMsHoliday");
        } catch (Exception e){
            return responseInternalServerError("deleteMsHoliday", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqMsHolidayModel> request) {
        return request != null
                && request.getType() != null
                && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
                && request.getDetails().getId() != null
                && request.getDetails().getHolidayDate() != null
                && request.getDetails().getHolidayDesc() != null;
    }

    private ResponseModel<ListMsHolidayModel> buildResponse(ResponseStatus status, ListMsHolidayModel listMsHolidayModel) {
        ResponseModel<ListMsHolidayModel> response = new ResponseModel<>();

        response.setType(TYPE_MS_HOLIDAY_GET_LIST);
        response.setDetails(listMsHolidayModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        return response;
    }

    private ResponseModel<ResMsHolidayModel> buildResponse(String type, ResponseStatus status, ResMsHolidayModel resMsHolidayModel) {
        ResponseModel<ResMsHolidayModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resMsHolidayModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    private ResponseModel<MsHolidayModel> buildResponse(String type, ResponseStatus status, MsHolidayModel msHolidayModel) {
        ResponseModel<MsHolidayModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(msHolidayModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    private ResMsHolidayModel buildResMsHolidayModel(String id, String type) {
        ResMsHolidayModel resMsHolidayModel = new ResMsHolidayModel();

        resMsHolidayModel.setType(type);
        resMsHolidayModel.setId(id);

        return resMsHolidayModel;
    }

    private MsHolidayModel buildResMsHolidayDetailModel(MsHolidayList mhl){
        MsHolidayModel mhm = Mapper.toMsHolidayModel(mhl);
        return mhm;
    }

    private ListMsHolidayModel buildResMsHolidayListModel(Integer pageSize, Integer pageNumber, List<MsHolidayModel> msHolidayModelList, int totalPages, long totalItems) {
        ListMsHolidayModel lmhm = new ListMsHolidayModel();
        lmhm.setMsHolidayModelList(msHolidayModelList);
        lmhm.setLimit(pageSize);
        lmhm.setPage(pageNumber);
        lmhm.setTotalPages(totalPages);
        lmhm.setTotalItems(totalItems);

        return lmhm;
    }
}
