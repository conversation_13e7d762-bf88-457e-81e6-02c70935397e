package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.ReportPermohonanModel;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.ReportPemohonService;
import com.btpns.fin.service.TrxUserUARService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.Constants.TYPE_REPORT_PERMOHONAN_PDF_DOWNLOAD;
import static com.btpns.fin.helper.ResponseHelper.responseForbidden;
import static com.btpns.fin.helper.ResponseHelper.responseInternalServerError;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@RestController
@RequestMapping(value = "/tema/report")
@CrossOrigin("*")
public class ReportUserController {
    private static final Logger logger = LoggerFactory.getLogger(ReportController.class);

    @Autowired
    ReportPemohonService reportPemohonService;

    @Autowired
    private TrxUserUARService trxUserUARService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "permohonan")
    public ResponseEntity<ReportPermohonanModel> getReportPermohonan(@RequestHeader("XToken") String authorization,
                                                                     @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                     @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                     @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                     @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                     @RequestParam(value = "isUser", defaultValue = "1") Integer isUser) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportPermohonan startDate {} endDate {} page {} limit {} isUser{} from {}", startDate, endDate, pageNumber, pageSize, isUser, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            Map.Entry<String, String> period = getPeriod(startDate, endDate);
            startDate = period.getKey();
            endDate = period.getValue();

            ReportPermohonanModel reportPermohonan = reportPemohonService.getListReportPermohonan(startDate, endDate, nikRequester, pageNumber, pageSize, isUser);

            logger.info("Response >>> getReportPermohonan : {}", gson.toJson(reportPermohonan));
            return ResponseEntity.ok(reportPermohonan);
        } catch (Exception e) {
            logger.error("Fail to getReportPermohonan ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "permohonan/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getReportSamplePdf(@RequestHeader("XToken") String authorization,
                                                             @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                             @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                             @RequestParam(value = "isUser", defaultValue = "1") Integer isUser) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportSamplePdf startDate {} endDate {} isUser {} ", startDate, endDate, isUser);
            String nikRequester = profile.getProfile().getPreferred_username();

            Map.Entry<String, String> period = getPeriod(startDate, endDate);
            startDate = period.getKey();
            endDate = period.getValue();

            ResUploadModel samplePdf = reportPemohonService.getReportPermohonanPdf(startDate, endDate, nikRequester, isUser);
            ResponseModel<ResUploadModel> response = buildResponse(SUCCESS, samplePdf);
            logger.info("Response >>> getReportSamplePdf : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return responseInternalServerError("getReportSamplePdf", e);
        }
    }

    private Map.Entry<String, String> getPeriod(String startDate, String endDate) {
        if(startDate.length() == 10 && endDate.length() == 10){
            startDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getFullDateAsDate(startDate + " 00:00:00"));
            endDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getFullDateAsDate(endDate + " 23:59:59"));
        } else if(startDate.length() == 7 && endDate.length() == 7) {
            startDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getBeginningofMonth(startDate));
            endDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getEndofMonth(endDate));
        }
        return Map.entry(startDate, endDate);
    }

    private ResponseModel<ResUploadModel> buildResponse(ResponseStatus status, ResUploadModel result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(TYPE_REPORT_PERMOHONAN_PDF_DOWNLOAD);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }

    @GetMapping(value = "uar")
    public ResponseEntity<ResponseModel<ResponseListModel>> getReportUARUser(@RequestHeader("XToken") String authorization,
                                                                            @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                            @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                            @RequestParam("triwulan") String triwulan,
                                                                            @RequestParam("tahun") String tahun,
                                                                            @RequestParam("aplikasi") String aplikasi) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportUARUser triwulan {} tahun {} aplikasi {} from {}", triwulan, tahun, aplikasi, getProfile(gson, profile));
            int pageNumMin1 = pageNumber - 1;
            ResponseModel<ResponseListModel> response = trxUserUARService.getReportUARUser(profile.getProfile().getPreferred_username(), triwulan, tahun, aplikasi, pageNumMin1, pageNumber, pageSize);

            logger.info("Response >>> getReportUARUser : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return responseInternalServerError("getReportUARUser", e);
        }
    }
}
