package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.TrxSetupParamRequestDTO;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResTrxSetupParamRequestModel;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import io.undertow.server.handlers.resource.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "tema/setup-parameter")
@CrossOrigin("*")
public class TrxSetupParamRequestController {
    private static final Logger logger = LoggerFactory.getLogger(TrxSetupParamRequestController.class);

    @Autowired
    TrxSetupParamRequestService trxSetupParamRequestService;

    @Autowired
    TrxSetupParamApprovalService trxSetupParamApprovalService;

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxSetupParamRequestAplikasiService trxSetupParamRequestAplikasiService;

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    EmailNotificationService emailNotificationService;

    @Autowired
    Mapper mapper;

    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "/{ticketId}")
    public ResponseEntity<ResponseModel<TrxSetupParamRequestDetailModel>> getTrxSetupParamRequestByTicketId(@PathVariable("ticketId") String ticketId,
                                                                                             @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            logger.info("Received <<< getTrxSetupParamRequestByTicketId ticketId {} from {}", ticketId, getProfile(gson, profile));
            TrxSetupParamRequest savedTrxSetupParamRequest = trxSetupParamRequestService.getTrxSetupParamRequestByTicketId(ticketId);
            //validation access based on token
            boolean passValidation = false;
            passValidation = CommonHelper.validateGetDetail(
                    nikRequester,
                    savedTrxSetupParamRequest.getNikRequester(),
                    savedTrxSetupParamRequest.getTrxSetupParamApproval().getPuk1NIK(),
                    savedTrxSetupParamRequest.getTrxSetupParamApproval().getPuk2NIK(),
                    trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidation) {
                TrxSetupParamRequestDetailModel tsprdm = trxSetupParamRequestService.getTrxSetupParamRequestDetail(ticketId, savedTrxSetupParamRequest);
                logger.info("Response >>> getTrxSetupParamRequestByTicketId : {}", gson.toJson(tsprdm));

                return ResponseEntity.ok(buildResponse(SUCCESS, tsprdm));
            } else {
                return responseHttpSuccessWithStatus(TYPE_DETAIL_SETUP_PARAMETER_GET, FORBIDDEN, "getTrxSetupParamRequestByTicketId", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("getTrxSetupParamRequestByTicketId", e);
        }
    }

    private ResponseModel<TrxSetupParamRequestDetailModel> buildResponse(ResponseStatus status, TrxSetupParamRequestDetailModel result) {
        ResponseModel<TrxSetupParamRequestDetailModel> response = new ResponseModel<>();

        response.setType(TYPE_DETAIL_SETUP_PARAMETER_GET);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }

    @GetMapping(value = "/{ticketId}/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getTrxSetupParamRequestByTicketIdPdf(@PathVariable("ticketId") String ticketId,
                                                                                              @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            logger.info("Received <<< getTrxSetupParamRequestByTicketIdPdf ticketId {} from {}", ticketId, getProfile(gson, profile));
            TrxSetupParamRequest savedTrxSetupParamRequest = trxSetupParamRequestService.getTrxSetupParamRequestByTicketId(ticketId);
            //validation access based on token
            boolean passValidation = false;
            passValidation = CommonHelper.validateGetDetail(
                    nikRequester,
                    savedTrxSetupParamRequest.getNikRequester(),
                    savedTrxSetupParamRequest.getTrxSetupParamApproval().getPuk1NIK(),
                    savedTrxSetupParamRequest.getTrxSetupParamApproval().getPuk2NIK(),
                    trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidation) {
                TrxSetupParamRequestDetailModel tsprdm = trxSetupParamRequestService.getTrxSetupParamRequestDetail(ticketId, savedTrxSetupParamRequest);
                logger.info("Response >>> getTrxSetupParamRequestByTicketIdPdf : {}", gson.toJson(tsprdm));

                ResUploadModel detailSetupParameterPdf = trxSetupParamRequestService.generateDetailSetupParameterPdf(tsprdm);
                return ResponseEntity.ok(buildResponse(SUCCESS, detailSetupParameterPdf));
            } else {
                return responseHttpSuccessWithStatus(TYPE_DETAIL_SETUP_PARAMETER_DOWNLOAD, FORBIDDEN, "getTrxSetupParamRequestByTicketIdPdf", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("getTrxSetupParamRequestByTicketIdPdf", e);
        }
    }

    @GetMapping(value = "/{ticketId}/direct-download")
    public ResponseEntity<Resource> directDownloadSPRequestByTicketIdPdf(@PathVariable("ticketId") String ticketId,
                                                                         @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            logger.info("Received <<< directDownloadSPRequestByTicketIdPdf ticketId {} from {}", ticketId, getProfile(gson, profile));
            TrxSetupParamRequest savedTrxSetupParamRequest = trxSetupParamRequestService.getTrxSetupParamRequestByTicketId(ticketId);
            //validation access based on token
            boolean passValidation = false;
            passValidation = CommonHelper.validateGetDetail(
                    nikRequester,
                    savedTrxSetupParamRequest.getNikRequester(),
                    savedTrxSetupParamRequest.getTrxSetupParamApproval().getPuk1NIK(),
                    savedTrxSetupParamRequest.getTrxSetupParamApproval().getPuk2NIK(),
                    trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidation) {
                TrxSetupParamRequestDetailModel tsprdm = trxSetupParamRequestService.getTrxSetupParamRequestDetail(ticketId, savedTrxSetupParamRequest);
                logger.info("Response >>> directDownloadSPRequestByTicketIdPdf : {}", gson.toJson(tsprdm));

                ResFileDownload response = trxSetupParamRequestService.directDownloadPdfDetailSP(tsprdm);
                return responseDownloadFile("directDownloadSPRequestByTicketIdPdf", response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_DETAIL_SETUP_PARAMETER_DOWNLOAD, FORBIDDEN, "directDownloadSPRequestByTicketIdPdf", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("directDownloadSPRequestByTicketIdPdf", e);
        }
    }


    private ResponseModel<ResUploadModel> buildResponse(ResponseStatus status, ResUploadModel result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(TYPE_DETAIL_SETUP_PARAMETER_DOWNLOAD);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }

    @GetMapping(value = "/own-ticket")
    public ResponseEntity<ListOwnTicketSetupParamModel> getTrxSetupParamReqJoinApproval(@RequestParam("page") int pageNumber, @RequestParam("limit") int pageSize, @RequestHeader("XToken") String authorization){
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getTrxSetupParamReqJoinApproval page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            //offset start from 0
            int pageNumMin1 = pageNumber - 1;
            Page<TrxSetupParamRequest> pageTSPR = trxSetupParamRequestService.getTrxSetupParamReqJoinApproval(nikRequester, pageNumMin1, pageSize);
            List<TrxSetupParamRequest> listTrxSetupParamRequest = pageTSPR.getContent();

            ListOwnTicketSetupParamModel listOwnTicketSetupParamModel = new ListOwnTicketSetupParamModel();
            List<OwnTicketSetupParamModel> tempList = new ArrayList<OwnTicketSetupParamModel>();
            Iterator<TrxSetupParamRequest> iterator = listTrxSetupParamRequest.iterator();
            while(iterator.hasNext()){
                TrxSetupParamRequest tspr = iterator.next();
                OwnTicketSetupParamModel otspm = Mapper.toOwnTicketSetupParamModel(tspr);
                //replace aplikasi code with desc
                String strAplikasi = otspm.getAplikasi();
                String[] splitAplikasi = strAplikasi.split(",");
                List<String> listAplikasi = Arrays.asList(splitAplikasi);
                List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
                otspm.setAplikasi(msta.toString().replaceAll("\\[|\\]", ""));
                //set status
                String ecurrState = msSystemParamService.getMsSystemParamDetail(otspm.getStatus().getKeyStatus()).getParamDetailDesc();
                otspm.getStatus().setValueStatus(ecurrState);
                tempList.add(otspm);
            }
            listOwnTicketSetupParamModel.setOwnTicket(tempList);
            listOwnTicketSetupParamModel.setPage(pageNumber);
            listOwnTicketSetupParamModel.setLimit(pageSize);
            listOwnTicketSetupParamModel.setTotalPages(pageTSPR.getTotalPages());
            listOwnTicketSetupParamModel.setTotalItems(pageTSPR.getTotalElements());

            logger.info("Response >>> getTrxSetupParamReqJoinApproval : with size {}", listOwnTicketSetupParamModel.getOwnTicket().size());
            return ResponseEntity.ok(listOwnTicketSetupParamModel);
        } catch (Exception e) {
            logger.error("Fail to getTrxSetupParamReqJoinApproval ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/waiting-ticket")
    public ResponseEntity<ListWaitingTicketSetupParamModel> getTrxSetupParamReqJoinApprovalWait(@RequestParam("page") int pageNumber, @RequestParam("limit") int pageSize, @RequestHeader("XToken") String authorization){
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getTrxSetupParamReqJoinApprovalWait page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            //offset start from 0
            int pageNumMin1 = pageNumber - 1;
            Page<TrxSetupParamRequest> pageTSPR = trxSetupParamRequestService.getTrxSetupParamReqJoinApprovalWait(nikRequester, pageNumMin1, pageSize);
            List<TrxSetupParamRequest> listTrxSetupParamRequest = pageTSPR.getContent();
            ListWaitingTicketSetupParamModel listWaitTicketSetupParamModel = new ListWaitingTicketSetupParamModel();
            List<WaitingTicketSetupParamModel> tempList = new ArrayList<WaitingTicketSetupParamModel>();
            Iterator<TrxSetupParamRequest> iterator = listTrxSetupParamRequest.iterator();
            while(iterator.hasNext()){
                TrxSetupParamRequest tspr = iterator.next();
                WaitingTicketSetupParamModel wtspm = Mapper.toWaitTicketSetupParamModel(tspr);
                //replace aplikasi code with desc
                String strAplikasi = wtspm.getAplikasi();
                String[] splitAplikasi = strAplikasi.split(",");
                List<String> listAplikasi = Arrays.asList(splitAplikasi);
                List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
                wtspm.setAplikasi(msta.toString().replaceAll("\\[|\\]", ""));
                //set status
                String ecurrState = msSystemParamService.getMsSystemParamDetail(wtspm.getStatus().getKeyStatus()).getParamDetailDesc();
                wtspm.getStatus().setValueStatus(ecurrState);
                tempList.add(wtspm);
            }
            listWaitTicketSetupParamModel.setWaitingTicket(tempList);
            listWaitTicketSetupParamModel.setPage(pageNumber);
            listWaitTicketSetupParamModel.setLimit(pageSize);
            listWaitTicketSetupParamModel.setTotalPages(pageTSPR.getTotalPages());
            listWaitTicketSetupParamModel.setTotalItems(pageTSPR.getTotalElements());

            logger.info("Response >>> getTrxSetupParamReqJoinApprovalWait : with size {}", listWaitTicketSetupParamModel.getWaitingTicket().size());
            return ResponseEntity.ok(listWaitTicketSetupParamModel);
        } catch (Exception e) {
            logger.error("Fail to getTrxSetupParamReqJoinApprovalWait ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping()
    public ResponseEntity<ResTrxSetupParamRequestModel> saveTrxSetupParamRequest(@RequestBody TrxSetupParamRequestModel trxSetupParamRequestModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxSetupParamRequest with data {} from {}", gson.toJson(trxSetupParamRequestModel), getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            encodeRequestSP(trxSetupParamRequestModel);
            if (isExistInInterval(nikRequester)) {
                logger.info("Response >>> saveTrxSetupParamRequest : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            if (!isValidPUK(nikRequester, trxSetupParamRequestModel)) {
                logger.info("Response >>> saveTrxSetupParamRequest : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
            boolean isUpdated = false;
            String ticketId = "DF9912280001";
            if (trxSetupParamRequestModel.getTicketId() == null || trxSetupParamRequestModel.getTicketId() == "") {
                String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());
                //cek last ticket di db ada ngga
                logger.info("getLastTicketId: " + trxSetupParamRequestService.getLastTicketId());
                if (trxSetupParamRequestService.getLastTicketId() == null) {
                    ticketId = "SP" + currDtTicket + "0001";
                } else {
                    String lastTicketId = trxSetupParamRequestService.getLastTicketId();
                    String strlastDate = lastTicketId.substring(2, 8);
                    logger.info("strlastDate: " + strlastDate);
                    //cek tanggalnya sama ngga sama currentdate
                    if (strlastDate.equals(currDtTicket)) {
                        String strTicketNum = lastTicketId.substring(8, 12);
                        logger.info("strTicketNum: " + strTicketNum);
                        Integer ticketNum = Integer.parseInt(strTicketNum) + 1;
                        ticketId = "SP" + currDtTicket + String.format("%04d", ticketNum);
                    } else {
                        ticketId = "SP" + currDtTicket + "0001";
                    }
                }
            } else {
                //update ticket if rejected
                isUpdated = true;
                ticketId = trxSetupParamRequestModel.getTicketId();
            }
            trxSetupParamRequestModel.setTicketId(ticketId);

            //check duplicate request
            boolean isSuccesRequest = false;
            if (!isDuplicateRequestId(trxSetupParamRequestModel.getRequestId())) {
                String currState = "";
                String puk1State = "";
                String puk2State = "";

                TrxSetupParamRequest tspr = Mapper.toTrxSetupParamRequestEntity(trxSetupParamRequestModel);
                tspr.setNikRequester(nikRequester);
                TrxSetupParamRequest savedTspr = null;
                if (isUpdated) {
                    savedTspr = trxSetupParamRequestService.updateTrxSetupParamRequest(tspr);
                } else {
                    savedTspr = trxSetupParamRequestService.saveTrxSetupParamRequest(tspr);
                }

                if (isUpdated) {
                    trxSetupParamRequestAplikasiService.deleteTrxSetupParamRequestAplikasiByTicketId(ticketId);
                }
                if (trxSetupParamRequestModel.getSetupParameter().getAplikasi().size() > 0) {
                    List<String> lAplikasi = trxSetupParamRequestModel.getSetupParameter().getAplikasi();
                    for (String aplikasi : lAplikasi) {
                        MsTemaApplication msta = msTemaApplicationService.getMsTemaApplication(aplikasi);
                        TrxSetupParamRequestAplikasi tsprap = Mapper.toTrxSetupParamRequestAplikasiEntity(ticketId, msta);
                        TrxSetupParamRequestAplikasi savedTsprap = trxSetupParamRequestAplikasiService.saveTrxSetupParamRequestAplikasi(tsprap);
                    }
                }

                TrxSetupParamApproval tspa = Mapper.toTrxSetupParamApprovalEntity(trxSetupParamRequestModel);
                String puk1Nik = trxSetupParamRequestModel.getSetupParameter().getApproval().getPuk1() == null ? "" : trxSetupParamRequestModel.getSetupParameter().getApproval().getPuk1();
                if (puk1Nik != "") {
                    //check delegation
                    if (trxDelegationService.getTrxDelegationByNikRequester(puk1Nik) != null) {
                        tspa.setPuk1DelegationId(trxDelegationService.getTrxDelegationByNikRequester(puk1Nik).getDelegationId());
                        puk1Nik = trxDelegationService.getTrxDelegationByNikRequester(puk1Nik).getNikDelegation();
                    }
                    MsEmployee puk1 = msEmployeeService.getEmployeeByNik(puk1Nik);
                    currState = CURR_STATUS_WAITING_PUK1;
                    puk1State = PUK1_STATUS_WAITING;
                    tspa.setPuk1NIK(puk1Nik);
                    tspa.setPuk1Name(puk1.getFullName());
                    tspa.setPuk1Occupation(puk1.getOccupationDesc());
                    tspa.setPuk1Dt(tspr.getCreateDateTime());
                    tspa.setPuk1Status(puk1State);
                    tspa.setPuk1Notes("");
                }
                String puk2Nik = trxSetupParamRequestModel.getSetupParameter().getApproval().getPuk2() == null ? "" : trxSetupParamRequestModel.getSetupParameter().getApproval().getPuk2();
                if (puk2Nik != "") {
                    //check delegation
                    if (trxDelegationService.getTrxDelegationByNikRequester(puk2Nik) != null) {
                        tspa.setPuk2DelegationId(trxDelegationService.getTrxDelegationByNikRequester(puk2Nik).getDelegationId());
                        puk2Nik = trxDelegationService.getTrxDelegationByNikRequester(puk2Nik).getNikDelegation();
                    }
                    MsEmployee puk2 = msEmployeeService.getEmployeeByNik(puk2Nik);
                    puk2State = PUK2_STATUS_WAITING;
                    tspa.setPuk2NIK(puk2Nik);
                    tspa.setPuk2Name(puk2.getFullName());
                    tspa.setPuk2Occupation(puk2.getOccupationDesc());
                    tspa.setPuk2Dt(tspr.getCreateDateTime());
                    tspa.setPuk2Status(puk2State);
                    tspa.setPuk2Notes("");
                }
                tspa.setCurrentState(currState);
                TrxSetupParamApproval savedTsa = null;
                if (isUpdated) {
                    savedTsa = trxSetupParamApprovalService.updateTrxSetupParamApproval(tspa);
                } else {
                    savedTsa = trxSetupParamApprovalService.saveTrxSetupParamApproval(tspa);
                }

                TrxAudittrail ta = Mapper.toTrxAudittrailEntity(trxSetupParamRequestModel);
                ta.setNik(nikRequester);
                ta.setAction(currState);
                //json additional info
                TimelineStatusModel tsm = new TimelineStatusModel();
                if (isUpdated) {
                    tsm.setStatus(TIMELINE_STATUS_RESUBMIT_TICKET);
                } else {
                    tsm.setStatus(TIMELINE_STATUS_CREATE_TICKET);
                }
                tsm.setPic(TIMELINE_PIC_USER + nikRequester + " - " + profile.getProfile().getName());
                tsm.setTimestamp(dateFormater3.format(ta.getCreateDateTime()));
                ta.setAdditionalInfo(new Gson().toJson(tsm));
                TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(ta);

                //send email
                if (puk1Nik != "") {
                    TrxSetupParamRequestDTO email = mapper.buildFormatedContentEmailTicketSP(savedTspr, savedTsa);
                    MsEmployee waitingPUK = msEmployeeService.getMsEmployeeByNik(savedTsa.getPuk1NIK());

                    sendEmailRequest(profile, email, puk1Nik, validateCCNikDirectPuk(email), waitingPUK);
                }

                if (savedTspr != null && savedTsa != null && savedTa != null) {
                    isSuccesRequest = true;
                }
            }

            ResTrxSetupParamRequestModel resTSPRM = new ResTrxSetupParamRequestModel();
            resTSPRM.setTicketId(trxSetupParamRequestModel.getTicketId());

            if(isSuccesRequest){
                resTSPRM.setStatus(SUCCESS.getCode());
                resTSPRM.setStatusDesc(SUCCESS.getValue());
            } else {
                resTSPRM.setStatus(FAILED.getCode());
                resTSPRM.setStatusDesc(FAILED.getValue());
            }
            logger.info("Response >>> saveTrxSetupParamRequest : {}", gson.toJson(resTSPRM));
            return ResponseEntity.ok(resTSPRM);
        } catch (Exception e) {
            logger.error("Fail to post TrxSetupParamRequest ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private void sendEmailRequest(Token profile, TrxSetupParamRequestDTO savedTspr, String puk1Nik, String ccNikDirectPuk, MsEmployee waitingPUK) {
        //send to created
        createRequestAsync(profile.getProfile().getPreferred_username(), savedTspr, waitingPUK);
        //send to approval
        createApprovalAsync(puk1Nik, savedTspr, ccNikDirectPuk, waitingPUK);
    }

    public void createRequestAsync(String userNik, TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee waitingPUK) {
        emailNotificationService.sendCreateRequestSPToUpmTemaNotification(userNik, trxSetupParamRequest, waitingPUK);
    }

    public void createApprovalAsync(String pukNik, TrxSetupParamRequestDTO trxSetupParamRequest, String ccNikDirectPuk, MsEmployee waitingPUK) {
        HashMap<String, String> mapPUKDirector = msEmployeeDirectorService.isPukDirectorByNikOptima(pukNik);
        emailNotificationService.sendCreateApprovalSPToUpmTemaNotification(pukNik, trxSetupParamRequest, ccNikDirectPuk, waitingPUK, mapPUKDirector);
    }

    public boolean isDuplicateRequestId(String requestId) throws ParseException {
        boolean isDuplicate = false;
        //jika requestId ada di db return true, lainnya false
        if(trxSetupParamRequestService.checkDuplicateRequestId(requestId) != null){
            isDuplicate = true;
        }
        return isDuplicate;
    }

    public boolean isExistInInterval(String nik) {
        return trxSetupParamRequestService.existInInterval(nik, 10);
    }

    private String validateCCNikDirectPuk(TrxSetupParamRequestDTO savedTSPR) {
        String nikDirectPuk = "";
        TrxSetupParamApprovalDTO savedTSPA = savedTSPR.getTrxSetupParamApproval();
        MsEmployee directPUK = msEmployeeService.getDirectPUK(savedTSPR.getNikRequester());
        if (directPUK != null) {
            boolean isCCDirectPuk = false;
            if (!savedTSPA.getPuk1NIK().equals(directPUK.getNik())) {
                isCCDirectPuk = true;
            }
            if (isCCDirectPuk) {
                nikDirectPuk = directPUK.getNik();
            }
        }
        return nikDirectPuk;
    }

    public boolean isValidPUK(String nikRequester, TrxSetupParamRequestModel trxSetupParamRequestModel) {
        if (nikRequester.equalsIgnoreCase(trxSetupParamRequestModel.getSetupParameter().getApproval().getPuk1())
                || (trxSetupParamRequestModel.getSetupParameter().getApproval().getPuk2() != null && nikRequester.equalsIgnoreCase(trxSetupParamRequestModel.getSetupParameter().getApproval().getPuk2()))) {
            return false;
        }
        return true;
    }
}
