package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserTicketModel;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import com.btpns.fin.service.UserTicketService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.responseDownloadFile;
import static com.btpns.fin.helper.ResponseHelper.responseInternalServerError;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@RestController
@RequestMapping(value = "/tema/user/ticket")
@CrossOrigin("*")
public class UserTicketController {
    private static final Logger logger = LoggerFactory.getLogger(UserTicketController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    UserTicketService userTicketService;

    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    @GetMapping("")
    public ResponseEntity<ResponseModel<ResponseListModel<UserTicketModel>>> getListTicketUser(@RequestHeader("XToken") String authorization,
                                                                                               @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                                               @RequestParam(value = "limit", defaultValue = "5") Integer limit,
                                                                                               @RequestParam(value = "status", required = false, defaultValue = "all") String status,
                                                                                               @RequestParam(value = "type", required = false, defaultValue = "all") String type,
                                                                                               @RequestParam(value = "isUser",required = false, defaultValue = "1") Integer isUser,
                                                                                               @RequestParam(value = "isWaitingApproval",required = false) Integer isWaitingApproval,
                                                                                               @RequestParam(value = "startDate", required = false) String startDate,
                                                                                               @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getListTicketUser from {}", getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            ResponseModel<ResponseListModel<UserTicketModel>> response = userTicketService.getListUserTicket(page, limit, status, type, nikRequester, isUser, isWaitingApproval, startDate, endDate);
            logger.info("Response >>> getListTicketUser {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail to getListTicketUser ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateReportTicketUserPdf(@RequestHeader("XToken") String authorization,
                                                                                     @RequestParam(value = "status", required = false, defaultValue = "all") String status,
                                                                                     @RequestParam(value = "type", required = false, defaultValue = "all") String type,
                                                                                     @RequestParam(value = "isUser",required = false, defaultValue = "1") Integer isUser,
                                                                                     @RequestParam(value = "isWaitingApproval",required = false) Integer isWaitingApproval,
                                                                                     @RequestParam(value = "startDate", required = false) String startDate,
                                                                                     @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< generateReportTicketUserPdf from {}", getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)){
                logger.info("Response >>> generateReportTicketUserPdf : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }

            ResponseModel<ResUploadModel> response = userTicketService.generateReportTicketUserPdf(status, type, nikRequester, isUser, isWaitingApproval, startDate, endDate);
            logger.info("Response >>> generateReportTicketUserPdf {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return responseInternalServerError("generateReportTicketUserPdf", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadReportTicketUserPdf(@RequestHeader("XToken") String authorization,
                                                                      @RequestParam(value = "status", required = false, defaultValue = "all") String status,
                                                                      @RequestParam(value = "type", required = false, defaultValue = "all") String type,
                                                                      @RequestParam(value = "isUser",required = false, defaultValue = "1") Integer isUser,
                                                                      @RequestParam(value = "isWaitingApproval",required = false) Integer isWaitingApproval,
                                                                      @RequestParam(value = "startDate", required = false) String startDate,
                                                                      @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadReportTicketUserPdf from {}", getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            ResFileDownload response = userTicketService.directDownloadReportTicketUserPdf(status, type, nikRequester, isUser, isWaitingApproval, startDate, endDate);
            return responseDownloadFile("directDownloadReportTicketUserPdf", response);
        } catch (Exception e) {
            return responseInternalServerError("directDownloadReportTicketUserPdf", e);
        }
    }
}
