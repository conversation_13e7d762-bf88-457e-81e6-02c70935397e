package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.MsEGLS;
import com.btpns.fin.model.entity.TrxUpmRole;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.EGLSUserIdService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping("tema/upm/user-id/AU00000011")
@CrossOrigin("*")
public class EGLSUserIDManagementController {
    private static final Logger logger = LoggerFactory.getLogger(EGLSUserIDManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private TrxUpmRoleService upmRoleService;

    @Autowired
    private EGLSUserIdService eglsUserIdService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel<MsEGLS>>> getEGLSUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                            @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                            @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                            @RequestParam(value = "status", defaultValue = "-1") String status,
                                                                                            @RequestParam(value = "searchFlag", required = false, defaultValue = "username") String searchFlag,
                                                                                            @RequestParam(value = "searchData", required = false) String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getEGLSUserIdManagement page {} limit {} searchFlag {} searchData {} status {} from {}", pageNumber, pageSize, searchFlag, searchData, status, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = this.upmRoleService.getTrxUpmRole(nikRequester);

            if (!CommonHelper.validateUPMTicket(nikRequester, trxUpmRole)) {
                logger.warn("Response >>> getEGLSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getEGLSUserIdManagement");
            }

            ResponseModel<ResponseListModel<MsEGLS>> response = this.eglsUserIdService.getEGLSUserIDs(pageSize, pageNumber, searchFlag, searchData, status);
            logger.info("Response >>> getEGLSUserIdManagement size {}", response.getDetails().getTotalItems());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.warn("Response >>> getEGLSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseInternalServerError("getEGLSUserIdManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getEGLSCsv(@RequestHeader("XToken") String authorization,
                                                                    @RequestParam(value = "status", defaultValue = "-1") String status) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = this.upmRoleService.getTrxUpmRole(nikRequester);
            logger.info("Received <<< getEGLSCsv from {}", getProfile(gson, token));

            if (!CommonHelper.validateUPMTicket(nikRequester, trxUpmRole)) {
                logger.warn("Response >>> getEGLSCsv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getEGLSCsv");
            }
            ResponseModel<ResUploadModel> response = eglsUserIdService.getEGLSCsv(status);

            logger.info("Response >>> getEGLSCsv : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.warn("Response >>> getEGLSCsv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseInternalServerError("getEGLSCsv", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadEGLSCsv(@RequestHeader("XToken") String authorization,
                                                          @RequestParam(value = "status", defaultValue = "-1") String status) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = this.upmRoleService.getTrxUpmRole(nikRequester);
            logger.info("Received <<< directDownloadEGLSCsv from {}", getProfile(gson, token));

            if (!CommonHelper.validateUPMTicket(nikRequester, trxUpmRole)) {
                logger.warn("Response >>> directDownloadEGLSCsv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("directDownloadEGLSCsv");
            }

            ResFileDownload response = eglsUserIdService.directDownloadAlihDayaCSV(status);
            return responseDownloadFile("directDownloadEGLSCsv", response);
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadEGLSCsv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseInternalServerError("directDownloadEGLSCsv", e);
        }
    }
}
