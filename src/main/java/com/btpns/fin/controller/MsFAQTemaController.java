package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.MsFAQTemaModel;
import com.btpns.fin.model.request.ReqMsFAQTemaModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.ResMsFAQTemaModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsFAQTemaService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping(value = "tema/upm/management/faq")
@CrossOrigin("*")
public class MsFAQTemaController {
    private static final Logger logger = LoggerFactory.getLogger(MsFAQTemaController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsFAQTemaService msFAQTemaService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel<MsFAQTemaModel>>> getListMsFAQTema(@RequestHeader("XToken") String authorization,
                                                                                             @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                             @RequestParam(value = "limit", defaultValue = "10") Integer pageSize) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getListMsFAQTema page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, token));

            ResponseModel<ResponseListModel<MsFAQTemaModel>> response = msFAQTemaService.getListMsFAQTema(pageSize, pageNumber);

            logger.info("Response >>> getListMsFAQTema {}", gson.toJson(response.getDetails()));
            return ResponseEntity.ok(response);
        } catch (Exception e){
            logger.warn("Response >>> getListMsFAQTema : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListMsFAQTema", e);
        }
    }

    @GetMapping(value = "/{faqId}")
    public ResponseEntity<ResponseModel<MsFAQTemaModel>> getMsFAQTemaByContentOrder(@RequestHeader("XToken") String authorization,
                                                                                    @PathVariable("faqId") String faqId) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsFAQTemaByContentOrder faqId {} from {}", faqId, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<MsFAQTemaModel> response = msFAQTemaService.getMsFAQTemaByFaqId(faqId);

                logger.info("Response >>> getMsFAQTemaByContentOrder {}", gson.toJson(response.getDetails()));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getMsFAQTemaByContentOrder : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsFAQTemaByContentOrder");
        } catch (Exception e){
            logger.warn("Response >>> getMsFAQTemaByContentOrder : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsFAQTemaByContentOrder", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResMsFAQTemaModel>> addOrEditMsFAQTema(@RequestHeader("XToken") String authorization,
                                                                               @RequestBody RequestModel<ReqMsFAQTemaModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsFAQTema {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester)) && isValidFAQTemaReq(request.getDetails())) {
                ResponseModel<ResMsFAQTemaModel> response = msFAQTemaService.addOrEditMsFAQTema(request.getDetails(), nikRequester);

                logger.info("Response >>> addOrEditMsFAQTema {} ", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> addOrEditMsFAQTema : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("addOrEditMsFAQTema");
        } catch (Exception e){
            logger.warn("Response >>> addOrEditMsFAQTema : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsFAQTema", e);
        }
    }

    private boolean isValidFAQTemaReq(ReqMsFAQTemaModel request) {
        return request.getType() != null && (ADD.equalsIgnoreCase(request.getType()) || EDIT.equalsIgnoreCase(request.getType()));
    }

    @DeleteMapping(value = "/{faqId}")
    public ResponseEntity<ResponseModel<ResMsFAQTemaModel>> deleteMsFAQTema(@RequestHeader("XToken") String authorization,
                                                                            @PathVariable("faqId") String faqId) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsFAQTema faqId {} from {}", faqId, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResMsFAQTemaModel> response = msFAQTemaService.deleteMsFAQTema(faqId);

                logger.info("Response >>> deleteMsFAQTema : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> deleteMsFAQTema : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("deleteMsFAQTema");
        } catch (Exception e){
            logger.warn("Response >>> deleteMsFAQTema : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsFAQTema", e);
        }
    }
}
