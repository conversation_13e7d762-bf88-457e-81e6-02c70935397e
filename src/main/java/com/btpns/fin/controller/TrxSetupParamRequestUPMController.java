package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.response.ResTrxFuidRequest;
import com.btpns.fin.model.TrxSetupParamRequestModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.TrxSetupParamRequestService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@RestController
@RequestMapping(value = "tema/upm/setup-parameter")
@CrossOrigin("*")
public class TrxSetupParamRequestUPMController {
    private static final Logger logger = LoggerFactory.getLogger(TrxSetupParamRequestUPMController.class);

    @Autowired
    TrxSetupParamRequestService trxSetupParamRequestService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping()
    public ResponseEntity<ResponseModel<ResTrxFuidRequest>> saveTrxSetupParamRequestUPM(@RequestHeader("XToken") String authorization,
                                                                                        @RequestBody TrxSetupParamRequestModel trxSetupParamRequestModel) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxSetupParamRequestUPM with data {} from {}", gson.toJson(trxSetupParamRequestModel), getProfile(gson, profile));
            String nikUPMRequester = profile.getProfile().getPreferred_username();
            encodeRequestSP(trxSetupParamRequestModel);

            if (!isExistInInterval(trxSetupParamRequestModel.getSetupParameter().getNikRequester())) {
                if (validateUPMTicket(nikUPMRequester,trxUpmRoleService.getTrxUpmRole(nikUPMRequester))){
                    ResponseModel<ResTrxFuidRequest> response = trxSetupParamRequestService.saveTrxSetupParamRequestUPM(trxSetupParamRequestModel);

                    logger.info("Response >>> saveTrxSetupParamRequestUPM {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                logger.warn("Invalid user for nik : {}", nikUPMRequester);
                return responseForbidden("saveTrxSetupParamRequestUPM");
            }
            return responseToManyRequest("saveTrxSetupParamRequestUPM");
        } catch (Exception e) {
            return responseInternalServerError("saveTrxSetupParamRequestUPM", e);
        }
    }

    public boolean isExistInInterval(String nik) {
        return trxSetupParamRequestService.existInInterval(nik, 10);
    }
}