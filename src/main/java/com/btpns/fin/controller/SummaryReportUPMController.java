package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.SummaryReportUPM;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.SummaryReportUPMService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping(value = "tema/upm/report/summary")
@CrossOrigin("*")
public class SummaryReportUPMController {
    private static final Logger logger = LoggerFactory.getLogger(SummaryReportUPMController.class);
    @Autowired
    private SummaryReportUPMService summaryReportUPMService;

    @Autowired
    private TrxUpmRoleService upmRoleService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping()
    public ResponseEntity<ResponseModel<SummaryReportUPM>> getSummaryReportUPM(@RequestHeader("XToken") String authorization,
                                                                               @RequestParam("month") Integer month,
                                                                               @RequestParam("year") Integer year) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getSummaryReportUPM month {} year {} from {}", month, year, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.validateUPMTicket(nikRequester, upmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getSummaryReportUPM : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getSummaryReportUPM");
            }
            ResponseModel<SummaryReportUPM> response = summaryReportUPMService.getSummaryReportUPM(month, year);

            logger.info("Response >>> getSummaryReportUPM {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getSummaryReportUPM : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getSummaryReportUPM", exception);
        }
    }

    @GetMapping("/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateExcelSummaryReportUPM(@RequestHeader("XToken") String authorization,
                                                                                       @RequestParam("month") Integer month,
                                                                                       @RequestParam("year") Integer year) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< generateExcelSummaryReportUPM month {} year {} from {}", month, year, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.validateUPMTicket(nikRequester, upmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> generateExcelSummaryReportUPM : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("generateExcelSummaryReportUPM");
            }
            ResponseModel<ResUploadModel> response = summaryReportUPMService.generateExcelSummaryReportUPM(month, year);

            logger.info("Response >>> generateExcelSummaryReportUPM {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> generateExcelSummaryReportUPM : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("generateExcelSummaryReportUPM", exception);
        }
    }

    @GetMapping("/direct-download")
    public ResponseEntity<Resource> directDownloadSummaryReportUPM(@RequestHeader("XToken") String authorization,
                                                                   @RequestParam("month") Integer month,
                                                                   @RequestParam("year") Integer year) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< directDownloadSummaryReportUPM month {} year {} from {}", month, year, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.validateUPMTicket(nikRequester, upmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> directDownloadSummaryReportUPM : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("directDownloadSummaryReportUPM");
            }
            ResFileDownload response = summaryReportUPMService.directDownloadSummaryReportUPM(month, year);
            return responseDownloadFile("directDownloadSummaryReportUPM", response);
        } catch (Exception exception) {
            logger.warn("Response >>> directDownloadSummaryReportUPM : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadSummaryReportUPM", exception);
        }
    }
}
