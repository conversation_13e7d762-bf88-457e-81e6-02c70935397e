package com.btpns.fin.controller;

import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.service.SchedulerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@RestController
@RequestMapping(value = "tema/scheduler")
@CrossOrigin("*")
public class SchedulerController {
    private static final Logger log = LoggerFactory.getLogger(SchedulerController.class);
    @Autowired
    SchedulerService schedulerService;

    @PostMapping("/execute/retryemail")
    public ResponseEntity<?> executeRetryEmail() {
        log.info("Trigger to execute retry email scheduler every 15 minutes on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));

        schedulerService.executeRetryEmail();

        return ResponseEntity.ok().build();
    }

    @PostMapping("/execute/uar_reminder")
    public ResponseEntity<?> executeSaveUARReminderFlag() {
        log.info("Trigger to execute UAR reminder scheduler quarterly on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));

        schedulerService.executeSaveUARReminderFlag();

        return ResponseEntity.ok().build();
    }

    @PostMapping("/execute/monthly_reminder")
    public ResponseEntity<?> executeSaveMonthlyReminderFlag() {
        log.info("Trigger to execute monthly reminder scheduler monthly on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));

        schedulerService.executeSaveMonthlyReminderFlag();

        return ResponseEntity.ok().build();
    }

    @PostMapping("/execute/monthly/data_core_system")
    public ResponseEntity<?> executeDataCoreSystemMonthly() {
        log.info("Trigger to execute data core system monthly to UPM on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));

        schedulerService.executeSendDataCoreSystemToUPM();

        return ResponseEntity.ok().build();
    }

    @PostMapping("/execute/reminder/ticket_waiting_approval")
    public ResponseEntity<?> executeReminderTicketWaitingApproval() {
        log.info("Trigger to execute reminder ticket waiting approval on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));

        schedulerService.executeSendReminderEmailTicketWaitingApprovalPUK();

        return ResponseEntity.ok().build();
    }
}
