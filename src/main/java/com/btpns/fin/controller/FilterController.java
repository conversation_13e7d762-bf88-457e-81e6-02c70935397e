package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.StatusModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.FilterService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value = "/tema/user/filter-status")
@CrossOrigin("*")
public class FilterController {
    private static final Logger logger = LoggerFactory.getLogger(FilterController.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    FilterService filterService;

    @GetMapping()
    public ResponseEntity<ResponseModel<List<StatusModel>>> getFilterStatus(@RequestHeader("XToken") String authorization,
                                                                            @RequestParam(value = "isUser") Integer isUser){
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getFilterStatus from {}", getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();

            ResponseModel<List<StatusModel>> response = filterService.getFilterStatus(nikRequester, isUser);
            logger.info("Response >>> getFilterStatus size {}", gson.toJson(response.getDetails()));
            return ResponseEntity.ok(response);
        } catch (Exception e){
            logger.error("Fail getFilterStatus ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
