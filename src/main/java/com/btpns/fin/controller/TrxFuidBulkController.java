package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.responseDownloadFile;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "/tema/fuid/bulk")
@CrossOrigin("*")
public class TrxFuidBulkController {
    private static final Logger logger = LoggerFactory.getLogger(TrxFuidBulkController.class);

    @Autowired
    TrxFuidBatchService trxFuidBatchService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxPUKVendorService trxPUKVendorService;

    @Autowired
    MsCabangMmsService msCabangMmsService;

    @Autowired
    EmailService emailService;

    @Autowired
    public RequestHelper requestHelper;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping(value = "/new-hire")
    public ResponseEntity<ResTrxFuidBatchModel> saveTrxFuidBulkNewHire(@RequestBody TrxFuidBatchModel trxFuidBatchModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxFuidBulkNewHire with data {} from {}", gson.toJson(trxFuidBatchModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            // VALIDATION REQUEST
            if (!isValidRequest(trxFuidBatchModel)
                    || !trxFuidBatchModel.getType().equalsIgnoreCase(BATCH_NEW_HIRE)
                    || trxFuidBatchService.getFuidBatchByBatchId(trxFuidBatchModel.getBatchId()) != null) {
                logger.info("Response >>> saveTrxFuidBulkNewHire : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            // VALIDATION INTERVAL
            if (isExistInInterval(nikRequester, trxFuidBatchModel.getType())) {
                logger.info("Response >>> saveTrxFuidBulkNewHire : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            // VALIDATION UPM
            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.info("Response >>> saveTrxFuidBulkNewHire : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            TrxFuidBatchDataModel trxFuidBatchDataModel = generateTrxFuidBatchDataNewHireOrResign(trxFuidBatchModel, getLastTicket(), nikRequester);
            // SAVE DATA
            boolean resultSuccess = trxFuidBatchService.saveFuidBatchData(trxFuidBatchDataModel);

            ResTrxFuidBatchModel response = resultSuccess ? buildResponse(trxFuidBatchModel, SUCCESS) : buildResponse(trxFuidBatchModel, FAILED);

            logger.info("Response >>> saveTrxFuidBulkNewHire : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail to post saveTrxFuidBulkNewHire ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private TrxFuidBatchDataModel generateTrxFuidBatchDataNewHireOrResign(TrxFuidBatchModel trxFuidBatchModel, String lastTicket, String nikRequester) throws Exception{
        TrxFuidBatchDataModel ret = new TrxFuidBatchDataModel();
        List<TrxFuidRequest> trxFuidRequestList = new ArrayList<>();
        List<TrxFuidApproval> trxFuidApprovalList = new ArrayList<>();
        List<TrxFuidRequestAplikasi> trxFuidRequestAplikasiList = new ArrayList<>();
        List<TrxFuidRequest> emailList = new ArrayList<>();
        List<TrxAudittrail> trxAudittrailList = new ArrayList<>();

        List<FuidModel> data = trxFuidBatchModel.getData();
        List<String> nikList = getAllNikPUK(data);
        Map<String, MsSystemParamDetail> msSystemParamDetailMap = msSystemParamService.getMsSystemParamDetailMap(Arrays.asList(KODE_ALASAN, KODE_TUJUAN, KODE_STATUS));
        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID));
        Map<String, MsEmployee> msEmployeeMap = getMsEmployeeMap(nikList);

        String ticketId = lastTicket;

        for (FuidModel fuid: data) {
            // BUILD DATA FUID REQUEST
            TrxFuidRequest tfr = new TrxFuidRequest();
            if (trxFuidBatchModel.getType().equalsIgnoreCase(BATCH_NEW_HIRE)) {
                tfr = Mapper.toTrxFuidRequestEntity(fuid, ticketId, fuid.getPuk(), trxFuidBatchModel.getBatchId(), TUJUAN_PENDAFTARAN_BARU, ALASAN_KARYAWAN_BARU, "Simplifikasi New Hire Tanggal ", Arrays.asList(KODE_APLIKASI_EMAIL, KODE_APLIKASI_PROSPERA));
            } else {
                tfr = Mapper.toTrxFuidRequestEntity(fuid, ticketId, fuid.getPuk(), trxFuidBatchModel.getBatchId(), TUJUAN_PENGHAPUSAN, ALASAN_LAINNYA, "Simplifikasi Batal Join Tanggal ", Arrays.asList(KODE_APLIKASI_EMAIL, KODE_APLIKASI_PROSPERA));
            }
            tfr.setDataNamaCabang(msCabangMmsService.getNameCabangOrMMS(tfr.getDataKodeCabang()));
            trxFuidRequestList.add(tfr);

            // BUILD DATA FUID REQUEST APLIKASI
            if (fuid.getAplikasi().size() > 0) {
                List<String> lAplikasi = fuid.getAplikasi();
                for (String aplikasi : lAplikasi) {
                    MsTemaApplication msta = msTemaApplicationMap.get(aplikasi);
                    TrxFuidRequestAplikasi tfrap = Mapper.toTrxFuidRequestAplikasiEntity(ticketId, msta);
                    trxFuidRequestAplikasiList.add(tfrap);
                }
            }

            // BUILD DATA FUID APPROVAL
            TrxFuidApproval tfra = Mapper.toTrxFuidApprovalEntity(ticketId);
            tfra.setCurrentState(UPM_STATUS_INPROGRESS);
            if (fuid.getPuk() != null){
                String nikPUK = CommonHelper.trimDashOnwards(fuid.getPuk()).toLowerCase();
                MsEmployee puk1 = msEmployeeMap.get(nikPUK);
                tfra.setPuk1NIK(nikPUK);
                tfra.setPuk1Name(puk1 != null ? puk1.getFullName() : EMPTY);
                tfra.setPuk1Occupation(puk1 != null ? puk1.getOccupationDesc() : EMPTY);
                tfra.setPuk1Status(CURR_STATUS_APPROVED);
                tfra.setPuk1Dt(LocalDateTime.now());
            }
            if (fuid.getUpmInputNIK() != null && !fuid.getUpmInputNIK().isEmpty()){
                tfra.setUpmInputNIK(fuid.getUpmInputNIK());
            }else{
                tfra.setUpmInputNIK(RPAUPMIN);
            }
            tfra.setUpmInputStatus(UPM_STATUS_INPROGRESS);
            tfra.setUpmInputDt(LocalDateTime.now());
            trxFuidApprovalList.add(tfra);

            // BUILD DATA AUDITTRAIL
            TrxAudittrail ta = Mapper.toTrxAudittrailEntity(ticketId);
            ta.setNik(fuid.getUpmInputNIK() != null && !fuid.getUpmInputNIK().isEmpty() ? fuid.getUpmInputNIK() : RPAUPMIN);
            ta.setAction(UPM_STATUS_INPROGRESS);
            //json additional info
            TimelineStatusModel tsm = mapToTsmModel(fuid, ta);
            ta.setAdditionalInfo(gson.toJson(tsm));
            trxAudittrailList.add(ta);

            ticketId = incrementTicketId(ticketId);
        }

        TrxFuidBatch trxFuidBatch = new TrxFuidBatch();
        trxFuidBatch.setBatchId(trxFuidBatchModel.getBatchId());
        trxFuidBatch.setBatchFileName(trxFuidBatchModel.getFileName());
        trxFuidBatch.setTotalData(trxFuidBatchModel.getTotalData());
        trxFuidBatch.setType(trxFuidBatchModel.getType());
        trxFuidBatch.setUploaderNIK(nikRequester);
        trxFuidBatch.setUploaderName(msEmployeeService.getEmployeeOrVendor(nikRequester));
        trxFuidBatch.setCreateDateTime(LocalDateTime.now());

        ret.setTrxFuidBatch(trxFuidBatch);
        ret.setTrxFuidRequestList(trxFuidRequestList);
        ret.setTrxFuidApprovalList(trxFuidApprovalList);
        ret.setTrxFuidRequestAplikasiList(trxFuidRequestAplikasiList);
        ret.setTrxAudittrailList(trxAudittrailList);
        ret.setEmailList(emailList);
        return ret;
    }

    private Map<String, MsEmployee> getMsEmployeeMap(List<String> nikList) {
        return msEmployeeService.getMsEmployeeMap(nikList);
    }

    private List<String> getAllNikPUK(List<FuidModel> fuidModelList) {
        return fuidModelList.stream().map(FuidModel::getPuk).filter(Objects::nonNull).map(nikPuk -> nikPuk.split("-")[0]).collect(Collectors.toList());
    }

    @PostMapping(value = "/resign")
    public ResponseEntity<ResTrxFuidBatchModel> saveTrxFuidBulkResign(@RequestBody TrxFuidBatchModel trxFuidBatchModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxFuidBulkResign with data {} from {}", gson.toJson(trxFuidBatchModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            // VALIDATION REQUEST
            if (!isValidRequest(trxFuidBatchModel)
                    || !trxFuidBatchModel.getType().equalsIgnoreCase(BATCH_RESIGN)
                    || trxFuidBatchService.getFuidBatchByBatchId(trxFuidBatchModel.getBatchId()) != null) {
                logger.info("Response >>> saveTrxFuidBulkResign : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            // VALIDATION INTERVAL
            if (isExistInInterval(nikRequester, trxFuidBatchModel.getType())) {
                logger.info("Response >>> saveTrxFuidBulkResign : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            // VALIDATION UPM
            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.info("Response >>> saveTrxFuidBulkResign : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            TrxFuidBatchDataModel trxFuidBatchDataModel = generateTrxFuidBatchDataNewHireOrResign(trxFuidBatchModel, getLastTicket(), nikRequester);
            // SAVE DATA
            boolean resultSuccess = trxFuidBatchService.saveFuidBatchData(trxFuidBatchDataModel);

            ResTrxFuidBatchModel response = resultSuccess ? buildResponse(trxFuidBatchModel, SUCCESS) : buildResponse(trxFuidBatchModel, FAILED);

            logger.info("Response >>> saveTrxFuidBulkResign : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail to post saveTrxFuidBulkResign ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/mutation")
    public ResponseEntity<ResTrxFuidBatchModel> saveTrxFuidBulkMutation(@RequestBody TrxFuidBatchModel trxFuidBatchModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxFuidBulkMutation with data {} from {}", gson.toJson(trxFuidBatchModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            // VALIDATION REQUEST
            if (!isValidRequest(trxFuidBatchModel)
                    || !trxFuidBatchModel.getType().equalsIgnoreCase(BATCH_MUTATION)
                    || trxFuidBatchService.getFuidBatchByBatchId(trxFuidBatchModel.getBatchId()) != null) {
                logger.info("Response >>> saveTrxFuidBulkMutation : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            // VALIDATION INTERVAL
            if (isExistInInterval(nikRequester, trxFuidBatchModel.getType())) {
                logger.info("Response >>> saveTrxFuidBulkMutation : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            // VALIDATION UPM
            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.info("Response >>> saveTrxFuidBulkMutation : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            TrxFuidBatchDataModel trxFuidBatchDataModel = generateTrxFuidBatchDataMutation(trxFuidBatchModel, getLastTicket(), nikRequester);
            // SAVE DATA
            boolean resultSuccess = trxFuidBatchService.saveFuidBatchData(trxFuidBatchDataModel);

            ResTrxFuidBatchModel response = resultSuccess ? buildResponse(trxFuidBatchModel, SUCCESS) : buildResponse(trxFuidBatchModel, FAILED);

            logger.info("Response >>> saveTrxFuidBulkMutation : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail to post saveTrxFuidBulkMutation ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private TrxFuidBatchDataModel generateTrxFuidBatchDataMutation(TrxFuidBatchModel trxFuidBatchModel, String lastTicket, String nikRequester) throws Exception{
        TrxFuidBatchDataModel ret = new TrxFuidBatchDataModel();
        List<TrxFuidRequest> trxFuidRequestList = new ArrayList<>();
        List<TrxFuidApproval> trxFuidApprovalList = new ArrayList<>();
        List<TrxFuidRequestAplikasi> trxFuidRequestAplikasiList = new ArrayList<>();
        List<TrxFuidRequest> emailList = new ArrayList<>();
        List<TrxAudittrail> trxAudittrailList = new ArrayList<>();

        List<FuidModel> data = trxFuidBatchModel.getData();
        List<String> nikPukList = getPreviousAndNextPUK(data);
        Map<String, MsSystemParamDetail> msSystemParamDetailMap = msSystemParamService.getMsSystemParamDetailMap(Arrays.asList(KODE_ALASAN, KODE_TUJUAN, KODE_STATUS));
        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID));
        Map<String, MsEmployee> msEmployeeMap = getMsEmployeeMap(nikPukList);

        String ticketId = lastTicket;

        for (FuidModel fuid: data) {
            // BUILD DATA FUID REQUEST
            MutasiModel mutasi = fuid.getMutasi();
            TrxFuidRequest fuidRequestDelete = new TrxFuidRequest();
            fuidRequestDelete = Mapper.toTrxFuidRequestEntity(fuid, ticketId, fuid.getData().getNIK(), trxFuidBatchModel.getBatchId(), TUJUAN_PENGHAPUSAN, ALASAN_MUTASI_ROTASI_PROMOSI, "Simplifikasi Mutasi Tanggal ", Arrays.asList(KODE_APLIKASI_PROSPERA));
            fuidRequestDelete.setDataJabatan(mutasi.getPosisiLama()!=null ? mutasi.getPosisiLama() : "");
            fuidRequestDelete.setDataKodeCabang(mutasi.getUnitKerjaLama()!=null ? mutasi.getUnitKerjaLama() : "");
            fuidRequestDelete.setDataNamaCabang(mutasi.getUnitKerjaLama()!=null ? msCabangMmsService.getNameCabangOrMMS(mutasi.getUnitKerjaLama()) : "");
            trxFuidRequestList.add(fuidRequestDelete);

            ticketId = incrementTicketId(ticketId);
            TrxFuidRequest fuidRequestAdd = new TrxFuidRequest();
            fuidRequestAdd = Mapper.toTrxFuidRequestEntity(fuid, ticketId, fuid.getData().getNIK(), trxFuidBatchModel.getBatchId(), TUJUAN_PENDAFTARAN_BARU, ALASAN_MUTASI_ROTASI_PROMOSI, "Simplifikasi Mutasi Tanggal ", Arrays.asList(KODE_APLIKASI_PROSPERA));
            fuidRequestAdd.setDataJabatan(mutasi.getPosisiBaru()!=null ? mutasi.getPosisiBaru() : "");
            fuidRequestAdd.setDataKodeCabang(mutasi.getUnitKerjaBaru()!=null ? mutasi.getUnitKerjaBaru() : "");
            fuidRequestAdd.setDataNamaCabang(mutasi.getUnitKerjaBaru()!=null ? msCabangMmsService.getNameCabangOrMMS(mutasi.getUnitKerjaBaru()) : "");
            trxFuidRequestList.add(fuidRequestAdd);

            // BUILD DATA FUID REQUEST APLIKASI
            MsTemaApplication msta = msTemaApplicationMap.get(KODE_APLIKASI_PROSPERA);
            TrxFuidRequestAplikasi fuidRequestAppDelete = Mapper.toTrxFuidRequestAplikasiEntity(fuidRequestDelete.getTicketId(), msta);
            trxFuidRequestAplikasiList.add(fuidRequestAppDelete);

            TrxFuidRequestAplikasi fuidRequestAppAdd = Mapper.toTrxFuidRequestAplikasiEntity(fuidRequestAdd.getTicketId(), msta);
            trxFuidRequestAplikasiList.add(fuidRequestAppAdd);

            // BUILD DATA FUID APPROVAL
            TrxFuidApproval fuidApprovalDelete = Mapper.toTrxFuidApprovalEntity(fuidRequestDelete.getTicketId());
            fuidApprovalDelete.setCurrentState(UPM_STATUS_INPROGRESS);
            if(mutasi.getPukLama() != null){
                String nikPUK = CommonHelper.trimDashOnwards(mutasi.getPukLama()).toLowerCase();
                MsEmployee pukLama = msEmployeeMap.get(nikPUK);
                fuidApprovalDelete.setPuk1NIK(nikPUK);
                fuidApprovalDelete.setPuk1Name(pukLama != null ? pukLama.getFullName() : EMPTY);
                fuidApprovalDelete.setPuk1Occupation(pukLama != null ? pukLama.getOccupationDesc() : EMPTY);
                fuidApprovalDelete.setPuk1Status(CURR_STATUS_APPROVED);
                fuidApprovalDelete.setPuk1Dt(LocalDateTime.now());
            }
            if (fuid.getUpmInputNIK() != null && !fuid.getUpmInputNIK().isEmpty()){
                fuidApprovalDelete.setUpmInputNIK(fuid.getUpmInputNIK());
            }else {
                fuidApprovalDelete.setUpmInputNIK(RPAUPMIN);
            }
            fuidApprovalDelete.setUpmInputStatus(UPM_STATUS_INPROGRESS);
            fuidApprovalDelete.setUpmInputDt(LocalDateTime.now());
            trxFuidApprovalList.add(fuidApprovalDelete);

            TrxFuidApproval fuidApprovalAdd = Mapper.toTrxFuidApprovalEntity(fuidRequestAdd.getTicketId());
            fuidApprovalAdd.setCurrentState(UPM_STATUS_INPROGRESS);
            if(mutasi.getPukBaru() != null){
                String nikPUK = CommonHelper.trimDashOnwards(mutasi.getPukBaru()).toLowerCase();
                MsEmployee pukBaru = msEmployeeMap.get(nikPUK);
                fuidApprovalAdd.setPuk1NIK(nikPUK);
                fuidApprovalAdd.setPuk1Name(pukBaru != null ? pukBaru.getFullName() : EMPTY);
                fuidApprovalAdd.setPuk1Occupation(pukBaru != null ? pukBaru.getOccupationDesc() : EMPTY);
                fuidApprovalAdd.setPuk1Status(CURR_STATUS_APPROVED);
                fuidApprovalAdd.setPuk1Dt(LocalDateTime.now());
            }
            if (fuid.getUpmInputNIK() != null && !fuid.getUpmInputNIK().isEmpty()){
                fuidApprovalAdd.setUpmInputNIK(fuid.getUpmInputNIK());
            }else {
                fuidApprovalAdd.setUpmInputNIK(RPAUPMIN);
            }
            fuidApprovalAdd.setUpmInputStatus(UPM_STATUS_INPROGRESS);
            fuidApprovalAdd.setUpmInputDt(LocalDateTime.now());
            trxFuidApprovalList.add(fuidApprovalAdd);

            // BUILD DATA AUDITTRAIL
            TrxAudittrail taDelete = Mapper.toTrxAudittrailEntity(fuidRequestDelete.getTicketId());
            taDelete.setNik(fuid.getUpmInputNIK() != null && !fuid.getUpmInputNIK().isEmpty() ? fuid.getUpmInputNIK() : RPAUPMIN);
            taDelete.setAction(UPM_STATUS_INPROGRESS);
            //json additional info
            TimelineStatusModel tsmDelete = mapToTsmModel(fuid, taDelete);
            taDelete.setAdditionalInfo(gson.toJson(tsmDelete));
            trxAudittrailList.add(taDelete);

            TrxAudittrail taAdd = Mapper.toTrxAudittrailEntity(fuidRequestAdd.getTicketId());
            taAdd.setNik(fuid.getUpmInputNIK() != null && !fuid.getUpmInputNIK().isEmpty() ? fuid.getUpmInputNIK() : RPAUPMIN);
            taAdd.setAction(UPM_STATUS_INPROGRESS);
            //json additional info
            TimelineStatusModel tsmAdd = mapToTsmModel(fuid, taAdd);
            taAdd.setAdditionalInfo(gson.toJson(tsmAdd));
            trxAudittrailList.add(taAdd);

            ticketId = incrementTicketId(ticketId);
        }

        TrxFuidBatch trxFuidBatch = new TrxFuidBatch();
        trxFuidBatch.setBatchId(trxFuidBatchModel.getBatchId());
        trxFuidBatch.setBatchFileName(trxFuidBatchModel.getFileName());
        trxFuidBatch.setTotalData(trxFuidBatchModel.getTotalData());
        trxFuidBatch.setType(trxFuidBatchModel.getType());
        trxFuidBatch.setUploaderNIK(nikRequester);
        trxFuidBatch.setUploaderName(msEmployeeService.getEmployeeOrVendor(nikRequester));
        trxFuidBatch.setCreateDateTime(LocalDateTime.now());

        ret.setTrxFuidBatch(trxFuidBatch);
        ret.setTrxFuidRequestList(trxFuidRequestList);
        ret.setTrxFuidApprovalList(trxFuidApprovalList);
        ret.setTrxFuidRequestAplikasiList(trxFuidRequestAplikasiList);
        ret.setTrxAudittrailList(trxAudittrailList);
        ret.setEmailList(emailList);
        return ret;
    }

    private List<String> getPreviousAndNextPUK(List<FuidModel> data) {
        return Stream.concat(
                data.stream().map(fuidModel -> fuidModel.getMutasi().getPukLama()).filter(Objects::nonNull),
                data.stream().map(fuidModel -> fuidModel.getMutasi().getPukBaru()).filter(Objects::nonNull))
                .map(nikPuk -> nikPuk.split("-")[0]).collect(Collectors.toList());
    }

    private String incrementTicketId(String ticketId) {
        String firstPart = ticketId.substring(0, 9);
        int incrementPart = Integer.parseInt(ticketId.substring(9)) + 1;
        return firstPart + String.format("%04d", incrementPart);
    }

    private ResTrxFuidBatchModel buildResponse(TrxFuidBatchModel trxFuidBatchModel, ResponseStatus status) {
        ResTrxFuidBatchModel response = new ResTrxFuidBatchModel();
        response.setType(trxFuidBatchModel.getType());
        response.setBatchId(trxFuidBatchModel.getBatchId());
        response.setTotalDatas(trxFuidBatchModel.getTotalData());
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    private String getLastTicket() {
        String lastTicketId = trxFuidRequestService.getLastTicketIdByInputType(INPUT_TYPE_BATCH);
        String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());

        logger.info("getLastTicketId: {} ", lastTicketId);
        if (lastTicketId == null) {
            return "FUS" + currDtTicket + "0001";
        } else {
            String strlastDate = lastTicketId.substring(3, 9);
            logger.info("strlastDate: " + strlastDate);
            //cek tanggalnya sama ngga sama currentdate
            if (strlastDate.equals(currDtTicket)) {
                String strTicketNum = lastTicketId.substring(9, 13);
                logger.info("strTicketNum: " + strTicketNum);
                Integer ticketNum = Integer.parseInt(strTicketNum) + 1;
                return "FUS" + currDtTicket + String.format("%04d", ticketNum);
            } else {
                return "FUS" + currDtTicket + "0001";
            }
        }
    }

    private boolean isValidRequest(TrxFuidBatchModel request) {
        return request != null
                && request.getType() != null
                && request.getBatchId() != null
                && request.getFileName() != null
                && request.getTotalData() > 0
                && request.getData() != null
                && request.getData().size() > 0
                && request.getData().size() <= 1000;
    }

    public boolean isExistInInterval(String nik, String type) {
        return trxFuidBatchService.existInInterval(nik, type, 30);
    }

    public boolean sentEmailBatch(TrxFuidBatchDataModel trxFuidBatchDataModel) {
        if (trxFuidBatchDataModel.getEmailList().size() > 0) {
            try {
                boolean flag = true;
                for (TrxFuidRequest trxFuidRequest: trxFuidBatchDataModel.getEmailList()) {
                    if (!createRequestAsync(trxFuidRequest.getNikRequester(), trxFuidRequest)) {
                        return false;
                    }
                }
                return flag;
            } catch (Exception e) {
                return false;
            }
        }
        return true;
    }

    public boolean createRequestAsync(String userNik, TrxFuidRequest trxFuidRequest) {
        return emailService.sendEmail(requestHelper.createEmailHeader(),requestHelper.createRequestFuidEmail(userNik, trxFuidRequest, null));
    }

    private TimelineStatusModel mapToTsmModel(FuidModel fuid, TrxAudittrail ta) {
        DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
        TimelineStatusModel tsm = new TimelineStatusModel();

        if (fuid.getUpmInputNIK() != null && !fuid.getUpmInputNIK().isEmpty()){
            tsm.setStatus(TIMELINE_STATUS_INPROGRESS_UPM);
            tsm.setPic(TIMELINE_PIC_UPM + fuid.getUpmInputNIK() + " - " + getUpmName(fuid.getUpmInputNIK()));
        }else {
            tsm.setStatus(TIMELINE_STATUS_INPROGRESS_UPM);
            tsm.setPic(TIMELINE_PIC_UPM + RPAUPMIN + " - " + RPAUPMIN);
        }
        tsm.setTimestamp(dateFormater3.format(ta.getCreateDateTime()));

        return tsm;
    }

    private String getUpmName(String upmInputNIK) {
        String upmInputName = "";
        MsEmployee msEmployee = msEmployeeService.getMsEmployeeByNik(upmInputNIK);

        if (msEmployee != null){
            upmInputName = msEmployee.getFullName();
        }else {
            upmInputName = trxPUKVendorService.findByNikVendor(upmInputNIK).getNameVendor();
        }

        return upmInputName;
    }

    @PostMapping(value = "/resign/optima")
    public ResponseEntity<ResponseModel<ResSimplifikasiBulkModel>> saveTrxFuidBulkResignOptimaUser(@RequestHeader("XToken") String authorization,
                                                                                                   @RequestBody TrxFuidBatchModel trxFuidBatchModel) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxFuidBulkResignOptimaUser with data {} from {}", gson.toJson(trxFuidBatchModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            // VALIDATION REQUEST
            if (isValidRequestBulkUserResignOptima(trxFuidBatchModel)
                    || trxFuidBatchModel.getType().equalsIgnoreCase(BATCH_RESIGN_OPTIMA)
                    || trxFuidBatchService.getFuidBatchByBatchId(trxFuidBatchModel.getBatchId()) != null) {
                // VALIDATION INTERVAL
                if (!isExistInInterval(nikRequester, trxFuidBatchModel.getType())) {
                    // VALIDATION UPM
                    if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                        ResponseModel<ResSimplifikasiBulkModel> response = trxFuidBatchService.saveTrxFuidBulkResignOptimaUser(trxFuidBatchModel, getLastTicketOptima(), nikRequester);
                        logger.info("Response >>> saveTrxFuidBulkResignOptimaUser : {}", gson.toJson(response));
                        return ResponseEntity.ok(response);
                    }
                    logger.info("Response >>> saveTrxFuidBulkResignOptimaUser : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                    return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
                }
                logger.info("Response >>> saveTrxFuidBulkResignOptimaUser : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            logger.info("Response >>> saveTrxFuidBulkResignOptimaUser : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        } catch (Exception e) {
            logger.error("Fail to post saveTrxFuidBulkResignOptimaUser ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private String getLastTicketOptima() {
        String lastTicketId = trxFuidRequestService.getLastTicketIdByInputType(INPUT_TYPE_RESIGN);
        String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());

        logger.info("getLastTicketId: {} ", lastTicketId);
        if (lastTicketId == null) {
            return "FUR" + currDtTicket + "0001";
        } else {
            String strlastDate = lastTicketId.substring(3, 9);
            logger.info("strlastDate: " + strlastDate);
            //cek tanggalnya sama ngga sama currentdate
            if (strlastDate.equals(currDtTicket)) {
                String strTicketNum = lastTicketId.substring(9, 13);
                logger.info("strTicketNum: " + strTicketNum);
                Integer ticketNum = Integer.parseInt(strTicketNum) + 1;
                return "FUR" + currDtTicket + String.format("%04d", ticketNum);
            } else {
                return "FUR" + currDtTicket + "0001";
            }
        }
    }

    private boolean isValidRequestBulkUserResignOptima(TrxFuidBatchModel request) {
        return request != null
                && request.getType() != null
                && request.getBatchId() != null
                && request.getFileName() != null
                && request.getTotalData() > 0
                && request.getData() != null
                && request.getData().size() > 0
                && request.getData().size() <= 1500;
    }

    @PostMapping(value = "/userId")
    public ResponseEntity<ResponseModel<ResSimplifikasiBulkModel>> saveTrxFuidBulkUserId(@RequestHeader("XToken") String authorization,
                                                                                         @RequestBody TrxFuidBatchModel trxFuidBatchModel) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxFuidBulkUserId with data {} from {}", gson.toJson(trxFuidBatchModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            ResponseModel<ResSimplifikasiBulkModel> response = trxFuidBatchService.saveTrxFuidBulkUserId(trxFuidBatchModel, nikRequester);

            logger.info("Response >>> saveTrxFuidBulkUserId : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail to post saveTrxFuidBulkUserId ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "userId/template-download")
    public ResponseEntity<ResponseModel<ResUploadModel>> downloadTemplateTrxFuidBulkUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< downloadTemplateTrxFuidBulkUserId from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            ResponseModel<ResUploadModel> response = trxFuidBatchService.downloadTemplateFuidBulkUserId(nikRequester);

            logger.info("Response >>> downloadTemplateTrxFuidBulkUserId : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail to post downloadTemplateTrxFuidBulkUserId ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "userId/template-direct-download")
    public ResponseEntity<Resource> directDownloadTemplateTrxFuidBulkUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadTemplateTrxFuidBulkUserId from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            ResFileDownload response = trxFuidBatchService.directDownloadTemplateTrxFuidBulkUserId(nikRequester);

            logger.info("Response >>> directDownloadTemplateTrxFuidBulkUserId : {}", gson.toJson(response));
            return responseDownloadFile("directDownloadTemplateTrxFuidBulkUserId", response);
        } catch (Exception e) {
            logger.error("Fail to post directDownloadTemplateTrxFuidBulkUserId ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
