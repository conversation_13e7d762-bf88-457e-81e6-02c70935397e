package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsTepatMBankingCorporate;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.ITepatMBankingCorporateUserIdRepository;
import com.btpns.fin.service.TepatMBankingCorporateUserIdService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000007")
@CrossOrigin("*")
public class TepatMBankingCorporateUserIdManagementController {
    private static final Logger logger = LoggerFactory.getLogger(TepatMBankingCorporateUserIdManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxUserIdBatchService trxUserIdBatchService;

    @Autowired
    TepatMBankingCorporateUserIdService tepatMBankingCorporateUserIdService;

    @Autowired
    ITepatMBankingCorporateUserIdRepository tepatMBankingCorporateUserIdRepository;

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchMsTepatMBankingCorporateUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                                           @RequestBody ReqUserIdBatchModel<MsTepatMBankingCorporate> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsTepatMBankingCorporateUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (!CommonHelper.isExistInInterval(trxUserIdBatchService.existInInterval(nikRequester, request.getType(), 30))) {
                    if(isValidRequest(request) && !CommonHelper.isDuplicateData(request.getData().stream().map(d -> d.getNik()).collect(Collectors.toList()))) {
                        ResponseModel<ResBatchUserId> saveBatchDboRtgsUserId = tepatMBankingCorporateUserIdService.saveBatchMsTepatMBankingCorporateUserId(request, nikRequester);

                        logger.info("Response >>> saveBatchMsTepatMBankingCorporateUserIdManagement {}", gson.toJson(saveBatchDboRtgsUserId));
                        return ResponseEntity.ok(saveBatchDboRtgsUserId);
                    }
                    logger.warn("Response >>> saveBatchMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                    return responseBadRequest("saveBatchMsTepatMBankingCorporateUserIdManagement");
                }
                logger.warn("Response >>> saveBatchMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            logger.warn("Response >>> saveBatchMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("saveBatchMsTepatMBankingCorporateUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> saveBatchTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsTepatMBankingCorporateUserIdManagement", e);
        }
    }

    private boolean isValidRequest(ReqUserIdBatchModel<MsTepatMBankingCorporate> request) {
        return request != null
                && request.getType() != null
                && request.getBatchId() != null
                && request.getFileName() != null
                && request.getTotalData() > 0
                && request.getData() != null
                && request.getData().size() > 0
                && trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsTepatMBankingCorporateManagement(@RequestHeader("XToken") String authorization,
                                                                                                  @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                  @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                                  @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                                  @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsTepatMBankingCorporateManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel> response = tepatMBankingCorporateUserIdService.getListMsTepatMBankingCorporate(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);

                logger.info("Response >>> getMsTepatMBankingCorporateManagement {}", response.getDetails());
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getMsTepatMBankingCorporateManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsTepatMBankingCorporateManagement");
        } catch (Exception e){
            logger.warn("Response >>> getMsTepatMBankingCorporateManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsTepatMBankingCorporateManagement", e);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<UserIDModel>> getMsTepatMBankingCorporateUserIdManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                                       @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsTepatMBankingCorporateUserIdManagementByNik nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(StringUtils.isNotBlank(nik)) {
                    ResponseModel<UserIDModel> response = tepatMBankingCorporateUserIdService.getMsTepatMBankingCorporateByNik(nik);

                    logger.info("Response >>> getMsTepatMBankingCorporateUserIdManagementByNik {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                logger.warn("Response >>> getMsTepatMBankingCorporateUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsTepatMBankingCorporateUserIdManagementByNik");
            }
            logger.warn("Response >>> getMsTepatMBankingCorporateUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsTepatMBankingCorporateUserIdManagementByNik");

        } catch (Exception e){
            logger.warn("Response >>> getMsTepatMBankingCorporateUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsTepatMBankingCorporateUserIdManagementByNik", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsTepatMBankingCorporateUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                                                              @RequestBody RequestModel<ReqUserIDModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsTepatMBankingCorporateUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(isValidUserIDRequest(request.getDetails())) {
                    if (tepatMBankingCorporateUserIdRepository.findByNikUser(request.getDetails().getNik()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResCUDUserIdModel> updateMsTepatMBankingCorporate = tepatMBankingCorporateUserIdService.updateMsTepatMBankingCorporateUserId(request.getDetails());

                            logger.info("Response >>> editMsTepatMBankingCorporateUserIdManagement {} ", gson.toJson(updateMsTepatMBankingCorporate));
                            return ResponseEntity.ok(updateMsTepatMBankingCorporate);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditAlihDayaUserManagement", gson);
                    }
                    ResponseModel<ResCUDUserIdModel> saveMsTepatMBankingCorporate = tepatMBankingCorporateUserIdService.saveMsTepatMBankingCorporateUserId(request.getDetails());

                    logger.info("Response >>> addditMsTepatMBankingCorporateUserIdManagement {} ", gson.toJson(saveMsTepatMBankingCorporate));
                    return ResponseEntity.ok(saveMsTepatMBankingCorporate);
                }
                logger.warn("Response >>> addOrEditMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsTepatMBankingCorporateUserIdManagement");
            }
            logger.warn("Response >>> addOrEditMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("addOrEditMsTepatMBankingCorporateUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> addOrEditMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsTepatMBankingCorporateUserIdManagement", e);
        }
    }

    @DeleteMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsTepatMBankingCorporateUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                                                           @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsTepatMBankingCorporateUserIdManagement nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(StringUtils.isNotBlank(nik)) {
                    if (tepatMBankingCorporateUserIdRepository.findByNikUser(nik) != null) {
                        ResponseModel<ResCUDUserIdModel> response = tepatMBankingCorporateUserIdService.deleteMsTepatMBankingCorporateUserId(nik);

                        logger.info("Response >>> deleteMsTepatMBankingCorporateUserIdManagement : {}", gson.toJson(response));
                        return ResponseEntity.ok(response);
                    }
                    return responseFailed(TYPE_MS_DBO_RTGS_MANAGEMENT_DELETE, NOT_FOUND, "deleteBranchDataManagement", gson);
                }
                logger.warn("Response >>> deleteMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsTepatMBankingCorporateUserIdManagement");
            }
            logger.warn("Response >>> deleteMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("deleteMsTepatMBankingCorporateUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> deleteMsTepatMBankingCorporateUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsTepatMBankingCorporateUserIdManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateExcelMsTepatMBankingCorporateUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< generateExcelMsTepatMBankingCorporateUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = tepatMBankingCorporateUserIdService.generateExcelMsTepatMBankingCorporateUserId();
                logger.info("Response >>> generateExcelMsTepatMBankingCorporateUserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> generateExcelMsTepatMBankingCorporateUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("generateExcelMsTepatMBankingCorporateUserId");
        } catch (Exception e) {
            logger.warn("Response >>> generateExcelMsTepatMBankingCorporateUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("generateExcelMsTepatMBankingCorporateUserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsTepatMBankingCorporateUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsTepatMBankingCorporateUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = tepatMBankingCorporateUserIdService.directDownloadExcelMsTepatMBankingCorporateUserId();
                return responseDownloadFile("directDownloadExcelMsTepatMBankingCorporateUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsTepatMBankingCorporateUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsTepatMBankingCorporateUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsTepatMBankingCorporateUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsTepatMBankingCorporateUserId", e);
        }
    }
}
