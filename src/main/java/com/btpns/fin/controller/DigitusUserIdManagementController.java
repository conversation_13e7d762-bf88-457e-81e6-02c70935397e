package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.MsDigitusModel;
import com.btpns.fin.model.request.*;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IDigitusUserIdRepository;
import com.btpns.fin.service.DigitusUserIdService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000010")
@CrossOrigin("*")
public class DigitusUserIdManagementController {
    private static final Logger logger = LoggerFactory.getLogger(DigitusUserIdManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxUserIdBatchService trxUserIdBatchService;

    @Autowired
    DigitusUserIdService digitusUserIdService;

    @Autowired
    IDigitusUserIdRepository digitusUserIdRepository;

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchMsDigitusUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                            @RequestBody ReqUserIdBatchModel<ReqMsDigitus> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsDigitusUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (!CommonHelper.isExistInInterval(trxUserIdBatchService.existInInterval(nikRequester, request.getType(), 30))) {
                    if(isValidRequest(request) && !CommonHelper.isDuplicateData(request.getData().stream().map(d -> d.getNik()).collect(Collectors.toList()))) {
                        ResponseModel<ResBatchUserId> saveBatchDboRtgsUserId = digitusUserIdService.saveBatchMsDigitusUserId(request, nikRequester);

                        logger.info("Response >>> saveBatchMsDigitusUserIdManagement {}", gson.toJson(saveBatchDboRtgsUserId));
                        return ResponseEntity.ok(saveBatchDboRtgsUserId);
                    }
                    logger.warn("Response >>> saveBatchMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                    return responseBadRequest("saveBatchMsDigitusUserIdManagement");
                }
                logger.warn("Response >>> saveBatchMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            logger.warn("Response >>> saveBatchMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("saveBatchMsDigitusUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> saveBatchMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsDigitusUserIdManagement", e);
        }
    }

    private boolean isValidRequest(ReqUserIdBatchModel<ReqMsDigitus> request) {
        return request != null
                && request.getType() != null
                && request.getBatchId() != null
                && request.getFileName() != null
                && request.getTotalData() > 0
                && request.getData() != null
                && request.getData().size() > 0
                && trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsDigitusUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                         @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                         @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                         @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                         @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsDigitusUserIdManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel> response = digitusUserIdService.getListMsDigitus(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);

                logger.info("Response >>> getMsDigitusUserIdManagement {}", response.getDetails());
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsDigitusUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> getMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsDigitusUserIdManagement", e);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<MsDigitusModel>> getMsDigitusUserIdManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                           @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsDigitusUserIdManagementByNik nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(StringUtils.isNotBlank(nik)) {
                    ResponseModel<MsDigitusModel> response = digitusUserIdService.getMsDigitusByNik(nik);

                    logger.info("Response >>> getMsDigitusUserIdManagementByNik {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                logger.warn("Response >>> getMsDigitusUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsDboRTGSUserIdManagementByNik");
            }
            logger.warn("Response >>> getMsDigitusUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsDigitusUserIdManagementByNik");

        } catch (Exception e){
            logger.warn("Response >>> getMsDigitusUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsDigitusUserIdManagementByNik", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsDigitusUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                               @RequestBody RequestModel<ReqMsDigitusModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsDigitusUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(isValidRequest(request)) {
                    if (digitusUserIdRepository.findByNikUser(request.getDetails().getNik()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResCUDUserIdModel> updateMsDigitus = digitusUserIdService.updateMsDigitusUserId(request.getDetails());

                            logger.info("Response >>> editMsDigitusUserIdManagement {} ", gson.toJson(updateMsDigitus));
                            return ResponseEntity.ok(updateMsDigitus);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditAlihDayaUserManagement", gson);
                    }
                    ResponseModel<ResCUDUserIdModel> saveMsDigitus = digitusUserIdService.saveMsDigitusUserId(request.getDetails());

                    logger.info("Response >>> addMsDigitusUserIdManagement {} ", gson.toJson(saveMsDigitus));
                    return ResponseEntity.ok(saveMsDigitus);
                }
                logger.warn("Response >>> addOrEditMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsDigitusUserIdManagement");
            }
            logger.warn("Response >>> addOrEditMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("addOrEditMsDigitusUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> addOrEditMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsDigitusUserIdManagement", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqMsDigitusModel> request) {
        return request != null
                && request.getType() != null
                && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
                && request.getDetails().getNik() != null
                && request.getDetails().getNamaUser() != null
                && request.getDetails().getKewenangan() != null
                && request.getDetails().getJabatan() != null;
    }

    @DeleteMapping(value = "/{nik:.+}")
        public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsDigitusUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                            @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsDigitusUserIdManagement nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(StringUtils.isNotBlank(nik)) {
                    if (digitusUserIdRepository.findByNikUser(nik) != null) {
                        ResponseModel<ResCUDUserIdModel> response = digitusUserIdService.deleteMsDigitusUserId(nik);

                        logger.info("Response >>> deleteMsDigitusUserIdManagement : {}", gson.toJson(response));
                        return ResponseEntity.ok(response);
                    }
                    return responseFailed(TYPE_MS_DBO_RTGS_MANAGEMENT_DELETE, NOT_FOUND, "deleteBranchDataManagement", gson);
                }
                logger.warn("Response >>> deleteMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsDigitusUserIdManagement");
            }
            logger.warn("Response >>> deleteMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("deleteMsDigitusUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> deleteMsDigitusUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsDigitusUserIdManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> genereteExcelMsDigitusUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< genereteExcelMsDigitusUserId {} from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = digitusUserIdService.genereteExcelMsDigitusUserId();
                logger.info("Response >>> genereteExcelMsDigitusUserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> genereteExcelMsDigitusUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("genereteExcelMsDigitusUserId");
        } catch (Exception e) {
            logger.warn("Response >>> genereteExcelMsDigitusUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("genereteExcelMsDigitusUserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsDigitusUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsDigitusUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = digitusUserIdService.directDownloadExcelMsDigitusUserId();
                return responseDownloadFile("directDownloadExcelMsDigitusUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsDigitusUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsDigitusUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsDigitusUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsDigitusUserId", e);
        }
    }
}
