package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.GenereteIDModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.GenereteIDService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.responseForbidden;
import static com.btpns.fin.helper.ResponseHelper.responseInternalServerError;

@RestController
@RequestMapping(value = "tema/upm/generate-id")
@CrossOrigin("*")
public class GenereteIDController {
    private static final Logger logger = LoggerFactory.getLogger(GenereteIDController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    GenereteIDService genereteIDService;

    @GetMapping(value = "/{paramId}")
    public ResponseEntity<ResponseModel<GenereteIDModel>> genereteID(@RequestHeader("XToken") String authorization,
                                                                     @PathVariable("paramId") String paramId) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< genereteID paramDetailId {} from {}", paramId, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<GenereteIDModel> response = genereteIDService.genereteID(paramId);

                logger.info("Response >>> genereteID size {}", response.getDetails());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("genereteID");
        } catch (Exception e){
            return responseInternalServerError("genereteID", e);
        }
    }
}
