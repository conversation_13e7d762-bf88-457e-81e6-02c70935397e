package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.ReqTicketBatchModel;
import com.btpns.fin.model.response.ResBatchProcess;
import com.btpns.fin.model.response.ResUpmManageTicketModel;
import com.btpns.fin.model.response.ResUpmReassignPukModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsOfficerNRRepository;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.time.*;
import java.util.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "/tema/upm")
@CrossOrigin("*")
public class UpmTicketController {
    private static final Logger logger = LoggerFactory.getLogger(UpmTicketController.class);

    @Value("${user.whitelist.menu.prospera.by.nik}")
    protected Set<String> userWhitelistMenuProsperaByNik;

    @Value("${user.whitelist.menu.prospera.by.officecode}")
    protected Set<String> userWhitelistMenuProsperaByOfficeCode;

    @Autowired
    UpmTicketService upmTicketService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxPUKVendorService trxPUKVendorService;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    MsUserIDApplicationService msUserIDApplicationService;

    @Autowired
    IMsOfficerNRRepository iMsOfficerNRRepository;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "/ticket")
    public ResponseEntity<ResponseModel<ListUpmTicketModel>> getUPMTicket(@RequestParam("status") String status,
                                                                          @RequestParam("type") String type,
                                                                          @RequestParam("page") Integer pageNumber,
                                                                          @RequestParam(value = "limit", required = false, defaultValue = "50") Integer pageSize,
                                                                          @RequestParam(value = "upmProcess", required = false, defaultValue = "") String upmProcess,
                                                                          @RequestParam(value = "searchFlag", required = false, defaultValue = SEARCH_FLAG_TICKET_ID) String searchFlag,
                                                                          @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData,
                                                                          @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getUPMTicket status {} type {} page {} limit {} upmProcess {} searchFlag {} searchData {} from {}", status, type, pageNumber, pageSize, upmProcess.toString(), searchFlag, searchData, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            boolean passValidation = false;
            passValidation = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidation) {
                String ticketId = "", name = "";
                List<String> nikList = Arrays.asList("");
                if(!searchFlag.equals(SEARCH_FLAG_TICKET_ID) && StringUtils.isNotBlank(searchData)){
                    nikList = Arrays.asList(searchData);
                    if (searchFlag.equals(SEARCH_FLAG_NAME)) {
                        name = searchData;
                    }
                }else {
                    searchFlag = SEARCH_FLAG_TICKET_ID;
                    ticketId = searchData;
                }

                if (ticketId.length() != 0 && ticketId.length() < 3) {
                    return responseFailed(TYPE_UPM_TICKET_GET_LIST, FAILED_MIN_INPUT_REQUIRED, "getUPMTicket", gson);
                }

                ListUpmTicketModel lutm = upmTicketService.getUPMTicket(ticketId, status, type, upmProcess, searchFlag, nikList, name, pageSize, pageNumber);

                logger.info("Response >>> getUPMTicket : with size {}", lutm.getTickets().size());
                return ResponseEntity.ok(buildResponse(SUCCESS,lutm));
            } else {
                return responseHttpSuccessWithStatus(TYPE_UPM_TICKET_GET_LIST, FORBIDDEN, "getUPMTicket", gson);
            }
        } catch (Exception e) {
            logger.error("Fail to getUPMTicket ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private ResponseModel<ListUpmTicketModel> buildResponse(ResponseStatus status, ListUpmTicketModel listUpmTicketModel) {
        ResponseModel<ListUpmTicketModel> response = new ResponseModel<>();

        response.setType(TYPE_UPM_TICKET_GET_LIST);
        response.setDetails(listUpmTicketModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        return response;
    }

    @GetMapping(value = "/role")
    public ResponseEntity<UpmRoleModel> getUPMRole(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getUPMRole from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            MsEmployee msEmployee = msEmployeeService.getMsEmployeeByNik(nikRequester);
            if(msEmployee == null){
                TrxPUKVendor trxPUKVendor = trxPUKVendorService.findByNikVendor(nikRequester);
                if(trxPUKVendor == null){
                    MsEmployeeDirector msEmployeeDirector = msEmployeeDirectorService.getMsEmployeeDirectorByNIKLdap(nikRequester);
                    if(msEmployeeDirector != null){
                        msEmployee = Mapper.toMsEmployee(msEmployeeDirector);
                    }
                }
                if(trxPUKVendor != null) {
                    msEmployee = Mapper.toMsEmployee(trxPUKVendor);
                }
            }
            if(msEmployee == null){
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }

            UpmRoleModel upmRoleModel = new UpmRoleModel();
            upmRoleModel.setNama(msEmployee.getFullName());
            boolean upmGroup = false;
            if(trxUpmRoleService.getTrxUpmRole(msEmployee.getNik()) != null){
                TrxUpmRole tur = trxUpmRoleService.getTrxUpmRole(msEmployee.getNik());
                upmGroup = true;
                upmRoleModel.setNama(tur.getNama());
                upmRoleModel.setRole(tur.getRole());
            }
            upmRoleModel.setIsUserMMS(CommonHelper.isUserMMS(msEmployee.getOccupation()));
            upmRoleModel.setIsUserWhitelistMenuProspera(validateUserWhitelistPropsera(nikRequester));
            upmRoleModel.setUpmGroup(upmGroup);
            upmRoleModel.setNik(msEmployee.getNik().toLowerCase());
            upmRoleModel.setAplikasiUserID(upmGroup ? msUserIDApplicationService.getAllActiveUserIDApplications() : Collections.emptyList());
            logger.info("Response >>> getUPMRole : {}", gson.toJson(upmRoleModel));
            return ResponseEntity.ok(upmRoleModel);
        } catch (Exception e) {
            logger.error("Fail to getUPMRole ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private Boolean validateUserWhitelistPropsera(String nik) {
        List<MsOfficerNR> msOfficerNR = iMsOfficerNRRepository.findProsperaByRoleByStatusByNik(OFFICER_STATUS_ACTIVE_CODE, nik);
        return CommonHelper.isUserWhitelistPropsera(userWhitelistMenuProsperaByNik, userWhitelistMenuProsperaByOfficeCode, nik, msOfficerNR);

    }

    @PostMapping(value = "/ticket")
    public ResponseEntity<ResponseModel<ResUpmManageTicketModel>> setUPMTicket(@RequestHeader("XToken") String authorization, @RequestBody UpmManageTicketModel umtModel){
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< setUPMTicket with data {} from {}", gson.toJson(umtModel), getProfile(gson, profile));
            String nik = profile.getProfile().getPreferred_username();

            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nik,trxUpmRoleService.getTrxUpmRole(nik));
            if(passValidationToken) {
                ResponseModel<ResUpmManageTicketModel> response = upmTicketService.setUPMTicket(umtModel, profile, authorization, nik);
                logger.info("Response >>> setUPMTicket : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_UPM_TICKET_SET, FORBIDDEN, "setUPMTicket", gson);
            }
        } catch (Exception e) {
            logger.error("Fail to setUPMTicket ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/ticket/reassign/puk")
    public ResponseEntity<ResponseModel<ResUpmReassignPukModel>> reassignPukUPMTicket(@RequestHeader("XToken") String authorization, @RequestBody UpmReassignPukModel urpModel){
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< reassignPukUPMTicket with data {} from {}", gson.toJson(urpModel), getProfile(gson, profile));
            String nik = profile.getProfile().getPreferred_username();

            //validation access based on token
            boolean passValidationToken = CommonHelper.validateUPMTicket(nik,trxUpmRoleService.getTrxUpmRole(nik));
            if(passValidationToken) {
                ResponseModel<ResUpmReassignPukModel> response = upmTicketService.reassignPUK(urpModel, profile.getProfile());

                logger.info("Response >>> reassignPukUPMTicket : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_UPM_TICKET_REASSIGN_PUK, FORBIDDEN, "reassignPukUPMTicket", gson);
            }
        } catch (Exception e) {
            logger.error("Fail to reassignPukUPMTicket ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/role/maker")
    public ResponseEntity<ListUpmRoleModel> getUPMRoleMaker(@RequestHeader("XToken") String authorization, @RequestParam(value = "role", defaultValue = "maker", required = false) String role) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getUPMRoleMaker role {} from {}", role, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                ListUpmRoleModel lURM = new ListUpmRoleModel();
                List<UpmRoleModel> listURM = new ArrayList<UpmRoleModel>();
                List<TrxUpmRole> listTUR = new ArrayList<TrxUpmRole>();
                if (role.equals("checker")) {
                    listTUR = trxUpmRoleService.getTrxUpmRoleCheckerAndAdminAll();
                } else {
                    listTUR = trxUpmRoleService.getTrxUpmRoleMakerAll();
                }
                Iterator<TrxUpmRole> iterator = listTUR.iterator();
                while (iterator.hasNext()) {
                    TrxUpmRole trxUpmRole = iterator.next();
                    ;
                    UpmRoleModel urm = new UpmRoleModel();
                    urm.setNik(trxUpmRole.getNik());
                    urm.setNama(trxUpmRole.getNama());
                    urm.setRole(trxUpmRole.getRole());
                    urm.setUpmGroup(true);
                    listURM.add(urm);
                }
                lURM.setUpmList(listURM);
                logger.info("Response >>> getUPMRoleMaker : with size {}", lURM.getUpmList().size());
                return ResponseEntity.ok(lURM);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
        } catch (Exception e) {
            logger.error("Fail to getUPMRoleMaker ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/ticket/summary")
    public ResponseEntity<UpmTicketSummaryModel> getUPMTicketSummary(@RequestHeader("XToken") String authorization, @RequestParam(value = "period", defaultValue = "daily") String period) {
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getUPMRoleMaker period {} from {}", period, getProfile(gson, profile));

            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                Map.Entry<String, String> mapPeriod = CommonHelper.getPeriod(period);
                String strStartDateTime = mapPeriod.getKey();
                String strEndDateTime = mapPeriod.getValue();

                Map.Entry<String, String> mapPeriodEffectiveDate = CommonHelper.getPeriodEffectiveDate(period);
                String strStartEffectiveDate = mapPeriodEffectiveDate.getKey();
                String strEndEffectiveDate = mapPeriodEffectiveDate.getValue();

                String startEffectiveDateOnHold = LocalDate.parse(CommonHelper.getStartDateTimeOnHold().split(" ")[0]).plusDays(1).toString();
                String endEffectiveDateOnHold = CommonHelper.getEndDateTimeOnHold().split(" ")[0];

                List<UpmCountTicketSummary> summaryUPMTicketList = upmTicketService.getListTicketSummaryUPM(strStartDateTime, strEndDateTime, strStartEffectiveDate, strEndEffectiveDate);
                List<UpmCountTicketOnHoldSummary> summaryUPMTicketOnHold = upmTicketService.getOnHoldTicketSummaryUPM(startEffectiveDateOnHold, endEffectiveDateOnHold);

                UpmTicketSummaryModel upmTicketSummaryModel = new UpmTicketSummaryModel();
                if (PARAM_PERIOD_DAILY.equalsIgnoreCase(period)){
                    upmTicketSummaryModel = mapToUpmTicketDailySummaryModel(summaryUPMTicketList, summaryUPMTicketOnHold, strEndDateTime);
                }else {
                    upmTicketSummaryModel = mapToUpmTicketSummaryModel(summaryUPMTicketList, summaryUPMTicketOnHold);
                }
                logger.info("Response >>> getUPMTicketSummary : {}", gson.toJson(upmTicketSummaryModel));
                return ResponseEntity.ok(upmTicketSummaryModel);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
        } catch (Exception e) {
            logger.error("Fail to getUPMTicketSummary ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private UpmTicketSummaryModel mapToUpmTicketDailySummaryModel(List<UpmCountTicketSummary> summaryUPMTicketList, List<UpmCountTicketOnHoldSummary> summaryUPMTicketOnHold, String strEndDateTime) {
        UpmTicketSummaryModel upmTicketSummaryModel = new UpmTicketSummaryModel();

        upmTicketSummaryModel.setNewTicket(getFilteredTicketTimeEndCutOff(summaryUPMTicketList, CURR_STATUS_APPROVED, strEndDateTime));
        upmTicketSummaryModel.setOnHold(getTotalTiketOnHold(summaryUPMTicketOnHold, CURR_STATUS_PENDING));
        upmTicketSummaryModel.setInprogress(getFilteredTicketTimeEndCutOff(summaryUPMTicketList, UPM_STATUS_INPROGRESS, strEndDateTime));
        upmTicketSummaryModel.setWaitingApproval(getFilteredTicketTimeEndCutOff(summaryUPMTicketList, UPM_STATUS_VERIFICATION, strEndDateTime));
        upmTicketSummaryModel.setDone(getFilteredTicketTimeEndCutOff(summaryUPMTicketList, UPM_STATUS_DONE, strEndDateTime));

        return upmTicketSummaryModel;
    }

    private BigInteger getFilteredTicketTimeEndCutOff(List<UpmCountTicketSummary> summaryUPMTicketList, String status, String strEndDateTime) {
        List<UpmCountTicketSummary> resultUnderSLA = new ArrayList<>();
        List<UpmCountTicketSummary> resultOverSLA = new ArrayList<>();

        String endDateTime = strEndDateTime.split(" ")[1];
        LocalTime timeEndCutOff = LocalTime.of(18, 30);

        summaryUPMTicketList.forEach(data -> {
            if (status.equalsIgnoreCase(data.getStatus())){
                if (data.getCurrentStateDT().split(" ")[1].compareTo(String.valueOf(timeEndCutOff)) > 0 &&
                    data.getCurrentStateDT().split(" ")[1].compareTo(endDateTime) < 0){
                    resultOverSLA.add(data);
                }else {
                    resultUnderSLA.add(data);
                }
            }
        });

        return getTotalFilteredTicketTimeEndCutOff(endDateTime, timeEndCutOff, resultUnderSLA.size(), resultOverSLA.size());
    }

    private BigInteger getTotalFilteredTicketTimeEndCutOff(String endDateTime, LocalTime timeEndCutOff, int totalUnderSLA, int totalOverSLA) {
        if (endDateTime.compareTo(String.valueOf(timeEndCutOff)) < 0){
            return BigInteger.valueOf(totalUnderSLA);
        }
        return BigInteger.valueOf(totalOverSLA);
    }

    private UpmTicketSummaryModel mapToUpmTicketSummaryModel(List<UpmCountTicketSummary> summaryUPMTicketList, List<UpmCountTicketOnHoldSummary> summaryUPMTicketOnHold) {
        UpmTicketSummaryModel upmTicketSummaryModel = new UpmTicketSummaryModel();

        upmTicketSummaryModel.setNewTicket(getTotalTiket(summaryUPMTicketList, CURR_STATUS_APPROVED));
        upmTicketSummaryModel.setOnHold(getTotalTiketOnHold(summaryUPMTicketOnHold, CURR_STATUS_PENDING));
        upmTicketSummaryModel.setInprogress(getTotalTiket(summaryUPMTicketList, UPM_STATUS_INPROGRESS));
        upmTicketSummaryModel.setWaitingApproval(getTotalTiket(summaryUPMTicketList, UPM_STATUS_VERIFICATION));
        upmTicketSummaryModel.setDone(getTotalTiket(summaryUPMTicketList, UPM_STATUS_DONE));

        return upmTicketSummaryModel;
    }

    private BigInteger getTotalTiket(List<UpmCountTicketSummary> summaryUPMTicketList, String status) {
        List<UpmCountTicketSummary> result = new ArrayList<>();
        summaryUPMTicketList.forEach(data -> {
            if (status.equalsIgnoreCase(data.getStatus())){
                result.add(data);
            }
        });
        return BigInteger.valueOf(result.size());
    }

    private BigInteger getTotalTiketOnHold(List<UpmCountTicketOnHoldSummary> summaryUPMTicketList, String status) {
        Integer totalTicket = 0;
        for (UpmCountTicketOnHoldSummary data : summaryUPMTicketList) {
            if (status.equalsIgnoreCase(data.getStatus())) {
                totalTicket = data.getTotal();
            }
        }
        return BigInteger.valueOf(totalTicket);
    }

    @GetMapping(value = "tickets/expired")
    public ResponseEntity<ResponseModel<ListExpiredFuidModel>> getListExpiredFuid(@RequestHeader("XToken") String authorization,
                                                                                  @RequestParam(value = "isProcessedByUpm", defaultValue = "0") Integer isProcessedByUpm,
                                                                                  @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                  @RequestParam(value = "limit", defaultValue = "50") Integer pageSize) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            logger.info("Received <<< getListUnprocessedExpiredFuid from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ListExpiredFuidModel> response = upmTicketService.getListExpiredFuid(pageNumMin1, pageNumber, pageSize, isProcessedByUpm);
                logger.info("Response >>> getListUnprocessedExpiredFuid : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }

            logger.warn("Response >>> getListUnprocessedExpiredFuid : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getListUnprocessedExpiredFuid");
        } catch (Exception exception) {
            logger.error("Response >>> getListUnprocessedExpiredFuid {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListUnprocessedExpiredFuid", exception);
        }
    }

    @PostMapping(value = "/tickets/expired/process")
    public ResponseEntity<ResponseModel<ResBatchProcess>> processExpiredFuidBatch(@RequestHeader("XToken") String authorization,
                                                                                  @RequestBody ReqTicketBatchModel requests) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< processExpiredFuidBatch from {}", getProfile(gson, profile));

            if (requests.getTicketIds().isEmpty()) {
                logger.warn("Response >>> processExpiredFuidBatch : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("processExpiredFuidBatch");
            }

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResBatchProcess> response = upmTicketService.processExpiredFuidBatch(requests);
                logger.info("Response >>> processExpiredFuidBatch : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }

            logger.warn("Response >>> processExpiredFuidBatch : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("processExpiredFuidBatch");
        } catch (Exception exception) {
            logger.error("Response >>> processExpiredFuidBatch {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("processExpiredFuidBatch", exception);
        }
    }
}
