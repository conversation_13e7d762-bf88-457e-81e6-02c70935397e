package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.ApprovalKewenanganLimitModel;
import com.btpns.fin.model.request.ReqApprovalKewenanganLimit;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.ResApprovalKewenanganLimit;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsApprovalKewenanganLimitService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping(value = "tema/upm/management/kewenangan-limit")
@CrossOrigin("*")
public class MsApprovalKewenanganLimitController {
    private static final Logger logger = LoggerFactory.getLogger(MsApprovalKewenanganLimitController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private MsApprovalKewenanganLimitService approvalKewenanganLimitService;

    @Autowired
    private TrxUpmRoleService upmRoleService;

    @GetMapping()
    public ResponseEntity<ResponseModel<List<ApprovalKewenanganLimitModel>>> getListApprovalKewenanganLimit(@RequestHeader("XToken") String authorization,
                                                                                                            @RequestParam(value = "officeLevel",required = false, defaultValue = "kckfo") String officeLevel) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getListApprovalKewenanganLimit from {}", getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.isNonViewerUPMRole(upmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<List<ApprovalKewenanganLimitModel>> response = approvalKewenanganLimitService.getListApprovalKewenanganLimit(officeLevel);

                logger.info("Response >>> getListApprovalKewenanganLimit {} ", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getListApprovalKewenanganLimit : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getListApprovalKewenanganLimit");
        } catch (Exception exception) {
            logger.warn("Response >>> getListApprovalKewenanganLimit : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListApprovalKewenanganLimit", exception);
        }
    }

    @GetMapping(value = "/{Id}")
    public ResponseEntity<ResponseModel<ApprovalKewenanganLimitModel>> getMsApprovalKewenanganLimitById(@RequestHeader("XToken") String authorization,
                                                                                                        @PathVariable("Id") String Id) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsApprovalKewenanganLimitById Id {} from {}", Id, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.isNonViewerUPMRole(upmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ApprovalKewenanganLimitModel> response = approvalKewenanganLimitService.getMsApprovalKewenanganLimitById(Id);

                logger.info("Response >>> getMsApprovalKewenanganLimitById {}", gson.toJson(response.getDetails()));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getMsApprovalKewenanganLimitById : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsApprovalKewenanganLimitById");
        } catch (Exception e){
            logger.warn("Response >>> getMsApprovalKewenanganLimitById : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsApprovalKewenanganLimitById", e);
        }
    }


    @PostMapping()
    public ResponseEntity<ResponseModel<ResApprovalKewenanganLimit>> addOrEditMsApprovalKewenanganLimit(@RequestHeader("XToken") String authorization,
                                                                                                        @RequestBody RequestModel<ReqApprovalKewenanganLimit> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsApprovalKewenanganLimit from {} {}", gson.toJson(request), getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.isNonViewerUPMRole(upmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResApprovalKewenanganLimit> response = approvalKewenanganLimitService.addOrEditMsApprovalKewenanganLimit(request.getDetails());

                logger.info("Response >>> addOrEditMsApprovalKewenanganLimit {} ", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> addOrEditMsApprovalKewenanganLimit : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("addOrEditMsApprovalKewenanganLimit");
        } catch (Exception exception) {
            logger.warn("Response >>> addOrEditMsApprovalKewenanganLimit : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsApprovalKewenanganLimit", exception);
        }
    }

    @DeleteMapping(value = "/{Id}")
    public ResponseEntity<ResponseModel<ResApprovalKewenanganLimit>> deleteMsApprovalKewenanganLimit(@RequestHeader("XToken") String authorization,
                                                                                                     @PathVariable("Id") String Id) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsApprovalKewenanganLimit Id {} from {}", Id, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.isNonViewerUPMRole(upmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResApprovalKewenanganLimit> response = approvalKewenanganLimitService.deleteMsApprovalKewenanganLimit(Id);

                logger.info("Response >>> deleteMsApprovalKewenanganLimit : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> deleteMsApprovalKewenanganLimit : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("deleteMsApprovalKewenanganLimit");
        } catch (Exception e){
            logger.warn("Response >>> deleteMsApprovalKewenanganLimit : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsApprovalKewenanganLimit", e);
        }
    }

}
