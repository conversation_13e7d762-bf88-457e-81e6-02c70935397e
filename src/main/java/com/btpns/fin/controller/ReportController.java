package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "/tema/upm")
@CrossOrigin("*")
public class ReportController {
    private static final Logger logger = LoggerFactory.getLogger(ReportController.class);

    @Autowired
    ReportProductivityService reportProductivityService;

    @Autowired
    MinioService minioService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    TrxUPMUARService trxUPMUARService;

    @Autowired
    ReportUPMService reportUPMService;

    @GetMapping(value = "report/ticket")
    public ResponseEntity<ListReportAplikasiPerTiketModel> getReportPermohonanPerTiket(@RequestHeader("XToken") String authorization, @RequestParam("status") String status, @RequestParam("type") String type, @RequestParam("page") Integer pageNumber, @RequestParam("limit") Integer pageSize,
                                                                                       @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess, @RequestParam(value = "upmChecker", required = false, defaultValue = "") List<String> upmChecker,
                                                                                       @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate, @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                                       @RequestParam(value = "dateFlag", required = false, defaultValue = "") String dateFlag,
                                                                                       @RequestParam(value = "tipeKewenanganLimit", required = false, defaultValue = "-1") String tipeKewenanganLimit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportPermohonanPerTiket status {} type {} page {} limit {} upmProcess {} upmChecker {} dateFlag {} startDate {} endDate {} from {}",
                    status, type, pageNumber, pageSize, upmProcess.toString(), upmChecker.toString(), dateFlag, startDate, endDate, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                Page<ReportAplikasiPerTiket> pageRAPT = reportUPMService.getListReportPermohonanPerTicket(status, type, pageNumber, pageSize, upmProcess, upmChecker, dateFlag, startDate, endDate, tipeKewenanganLimit);
                List<ReportAplikasiPerTiket> listRAPT = pageRAPT.getContent();

                ListReportAplikasiPerTiketModel lraptm = new ListReportAplikasiPerTiketModel();
                List<ReportAplikasiPerTiketModel> listRAPTM = new ArrayList<ReportAplikasiPerTiketModel>();
                Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM));
                for (int i = 0; i < listRAPT.size(); i++) {
                    ReportAplikasiPerTiket rapt = listRAPT.get(i);
                    ReportAplikasiPerTiketModel raptm = Mapper.toReportAplikasiPerTiketModel(rapt);
                    if (!StringUtils.isEmpty(raptm.getAplikasi())) {
                        String[] split = raptm.getAplikasi().split(",");
                        List<String> collect = Arrays.stream(split).map(data -> {
                            return msTemaApplicationMap.get(data).getParamDetailDesc();
                        }).collect(Collectors.toList());
                        raptm.setAplikasi(String.join(",", collect));
                    }
                    listRAPTM.add(raptm);
                }
                lraptm.setAplikasiPerTiket(listRAPTM);
                lraptm.setPage(pageNumber);
                lraptm.setLimit(pageSize);
                lraptm.setStatus(status);
                lraptm.setType(type);
                lraptm.setTotalPages(pageRAPT.getTotalPages());
                lraptm.setTotalItems(pageRAPT.getTotalElements());
                logger.info("Response >>> getReportPermohonanPerTiket : with size {}", lraptm.getAplikasiPerTiket().size());
                return ResponseEntity.ok(lraptm);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
        } catch (Exception e) {
            logger.error("Fail to getReportPermohonanPerTiket ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "report/ticket/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getReportPermohonanPerTiketCSV(@RequestHeader("XToken") String authorization,
                                                                                        @RequestParam("status") String status,
                                                                                        @RequestParam("type") String type,
                                                                                        @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess,
                                                                                        @RequestParam(value = "upmChecker", required = false, defaultValue = "") List<String> upmChecker,
                                                                                        @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                                        @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                                        @RequestParam(value = "dateFlag", required = false, defaultValue = "") String dateFlag,
                                                                                        @RequestParam(value = "tipeKewenanganLimit", required = false, defaultValue = "-1") String tipeKewenanganLimit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportPermohonanPerTiketCSV status {} type {} upmProcess {} upmChecker {} dateFlag {} startDate {} endDate {} tipeKewenangaLimit {} from {}",
                    status, type, upmProcess.toString(), upmChecker.toString(), dateFlag, startDate, endDate, tipeKewenanganLimit, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                ResponseModel<ResUploadModel> response = reportUPMService.generateReportPermohonanPerTiketCSV(status, type, upmProcess, upmChecker, dateFlag, startDate, endDate, tipeKewenanganLimit);

                logger.info("Response >>> getReportPermohonanPerTiketCSV : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            } else {
                return responseForbidden("getReportPermohonanPerTiketCSV");
            }
        } catch (Exception e) {
            return responseInternalServerError("getReportPermohonanPerTiketCSV", e);
        }
    }

    @GetMapping(value = "report/ticket/direct-download")
    public ResponseEntity<Resource> directDownloadReportPermohonanPerTiketCSV(@RequestHeader("XToken") String authorization,
                                                                              @RequestParam("status") String status,
                                                                              @RequestParam("type") String type,
                                                                              @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess,
                                                                              @RequestParam(value = "upmChecker", required = false, defaultValue = "") List<String> upmChecker,
                                                                              @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                              @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                              @RequestParam(value = "dateFlag", required = false, defaultValue = "") String dateFlag,
                                                                              @RequestParam(value = "tipeKewenanganLimit", required = false, defaultValue = "-1") String tipeKewenanganLimit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadReportPermohonanPerTiketCSV status {} type {} upmProcess {} upmChecker {} dateFlag {} startDate {} endDate {} tipeKewenangaLimit {} from {}",
                    status, type, upmProcess.toString(), upmChecker.toString(), dateFlag, startDate, endDate, tipeKewenanganLimit, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                ResFileDownload response = reportUPMService.directDownloadReportPermohonanPerTiketCSV(status, type, upmProcess, upmChecker, dateFlag, startDate, endDate, tipeKewenanganLimit);

                return responseDownloadFile("directDownloadReportPermohonanPerTiketCSV", response);
            } else {
                return responseForbidden("directDownloadReportPermohonanPerTiketCSV");
            }
        } catch (Exception e) {
            return responseInternalServerError("directDownloadReportPermohonanPerTiketCSV", e);
        }
    }

    @GetMapping(value = "report/ticket/send-email")
    public ResponseEntity sendEmailReportPermohonanPerTiketCSV(@RequestHeader("XToken") String authorization,
                                                               @RequestParam("status") String status,
                                                               @RequestParam("type") String type,
                                                               @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess,
                                                               @RequestParam(value = "upmChecker", required = false, defaultValue = "") List<String> upmChecker,
                                                               @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                               @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                               @RequestParam(value = "dateFlag", required = false, defaultValue = "") String dateFlag,
                                                               @RequestParam(value = "tipeKewenanganLimit", required = false, defaultValue = "-1") String tipeKewenanganLimit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< sendEmailReportPermohonanPerTiketCSV status {} type {} upmProcess {} upmChecker {} dateFlag {} startDate {} endDate {} tipeKewenangaLimit {} from {}",
                    status, type, upmProcess.toString(), upmChecker.toString(), dateFlag, startDate, endDate, tipeKewenanganLimit, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                reportUPMService.sendEmailReportPermohonanPerTiketCSV(nikRequester, status, type, upmProcess, upmChecker, dateFlag, startDate, endDate, tipeKewenanganLimit);

                logger.info("Response >>> sendEmailReportPermohonanPerTiketCSV : Finish Send Report");
                return ResponseEntity.ok(HttpStatus.OK);
            } else {
                return responseForbidden("sendEmailReportPermohonanPerTiketCSV");
            }
        } catch (Exception e) {
            return responseInternalServerError("sendEmailReportPermohonanPerTiketCSV", e);
        }
    }

    @GetMapping(value = "report/aplikasi")
    public ResponseEntity<ListReportAplikasiPerTiketModel> getReportPermohonanPerAplikasi(@RequestHeader("XToken") String authorization,
                                                                                          @RequestParam("status") String status, @RequestParam("type") String type,
                                                                                          @RequestParam("page") Integer pageNumber, @RequestParam("limit") Integer pageSize,
                                                                                          @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess,
                                                                                          @RequestParam(value = "upmChecker", required = false, defaultValue = "") List<String> upmChecker,
                                                                                          @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                                          @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                                          @RequestParam(value = "dateFlag", required = false, defaultValue = "") String dateFlag,
                                                                                          @RequestParam(value = "tipeKewenanganLimit", required = false, defaultValue = "-1") String tipeKewenanganLimit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportTiketPerAplikasi status {} type {} page {} limit {} upmProcess {} upmChecker {} dateFlag {} startDate {} endDate {} from {}",
                    status, type, pageNumber, pageSize, upmProcess.toString(), upmChecker.toString(), dateFlag, startDate, endDate, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                Page<ReportAplikasiPerTiket> pageRAPT = reportUPMService.getListReportPermohonanPerAplikasi(status, type, pageNumber, pageSize, upmProcess, upmChecker, dateFlag, startDate, endDate, tipeKewenanganLimit);
                List<ReportAplikasiPerTiket> listRAPT = pageRAPT.getContent();

                ListReportAplikasiPerTiketModel lraptm = new ListReportAplikasiPerTiketModel();
                List<ReportAplikasiPerTiketModel> listRAPTM = new ArrayList<ReportAplikasiPerTiketModel>();
                Iterator<ReportAplikasiPerTiket> iterator = listRAPT.iterator();
                while (iterator.hasNext()) {
                    ReportAplikasiPerTiket rapt = iterator.next();
                    ReportAplikasiPerTiketModel raptm = Mapper.toReportAplikasiPerTiketModel(rapt);
                    listRAPTM.add(raptm);
                }
                lraptm.setAplikasiPerTiket(listRAPTM);
                lraptm.setPage(pageNumber);
                lraptm.setLimit(pageSize);
                lraptm.setStatus(status);
                lraptm.setType(type);
                lraptm.setTotalPages(pageRAPT.getTotalPages());
                lraptm.setTotalItems(pageRAPT.getTotalElements());
                logger.info("Response >>> getReportTiketPerAplikasi : with size {}", lraptm.getAplikasiPerTiket().size());
                return ResponseEntity.ok(lraptm);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
        } catch (Exception e) {
            logger.error("Fail to getReportTiketPerAplikasi ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "report/aplikasi/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getReportPermohonanPerAplikasiCSV(@RequestHeader("XToken") String authorization,
                                                            @RequestParam("status") String status,
                                                            @RequestParam("type") String type,
                                                            @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess,
                                                            @RequestParam(value = "upmChecker", required = false, defaultValue = "") List<String> upmChecker,
                                                            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                            @RequestParam(value = "dateFlag", required = false, defaultValue = "") String dateFlag,
                                                            @RequestParam(value = "tipeKewenanganLimit", required = false, defaultValue = "-1") String tipeKewenanganLimit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportTiketPerAplikasiCSV status {} type {} upmProcess {} upmChecker {} dateFlag {} startDate {} endDate {} from {}",
                    status, type, upmProcess.toString(), upmChecker.toString(), dateFlag, startDate, endDate, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                ResponseModel<ResUploadModel> response = reportUPMService.generateReportPermohonanPerAplikasiCSV(status, type, upmProcess, upmChecker, dateFlag, startDate, endDate, tipeKewenanganLimit);
                logger.info("Response >>> getReportTiketPerAplikasiCSV : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            } else {
                return responseForbidden("getReportTiketPerAplikasiCSV");
            }
        } catch (Exception e) {
            return responseInternalServerError("getReportTiketPerAplikasiCSV", e);
        }
    }

    @GetMapping(value = "report/aplikasi/direct-download")
    public ResponseEntity<Resource> directDownloadReportPermohonanPerAplikasiCSV(@RequestHeader("XToken") String authorization,
                                                                                 @RequestParam("status") String status,
                                                                                 @RequestParam("type") String type,
                                                                                 @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess,
                                                                                 @RequestParam(value = "upmChecker", required = false, defaultValue = "") List<String> upmChecker,
                                                                                 @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                                 @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                                 @RequestParam(value = "dateFlag", required = false, defaultValue = "") String dateFlag,
                                                                                 @RequestParam(value = "tipeKewenanganLimit", required = false, defaultValue = "-1") String tipeKewenanganLimit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadReportPermohonanPerAplikasiCSV status {} type {} upmProcess {} upmChecker {} dateFlag {} startDate {} endDate {} from {}",
                    status, type, upmProcess.toString(), upmChecker.toString(), dateFlag, startDate, endDate, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                ResFileDownload response = reportUPMService.directDownloadReportPermohonanPerAplikasiCSV(status, type, upmProcess, upmChecker, dateFlag, startDate, endDate, tipeKewenanganLimit);

                return responseDownloadFile("directDownloadReportPermohonanPerAplikasiCSV", response);
            } else {
                return responseForbidden("directDownloadReportPermohonanPerAplikasiCSV");
            }
        } catch (Exception e) {
            return responseInternalServerError("directDownloadReportPermohonanPerAplikasiCSV", e);
        }
    }

    @GetMapping(value = "report/aplikasi/send-email")
    public ResponseEntity sendEmailReportPermohonanPerAplikasiCSV(@RequestHeader("XToken") String authorization,
                                                                  @RequestParam("status") String status,
                                                                  @RequestParam("type") String type,
                                                                  @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess,
                                                                  @RequestParam(value = "upmChecker", required = false, defaultValue = "") List<String> upmChecker,
                                                                  @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                  @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                  @RequestParam(value = "dateFlag", required = false, defaultValue = "") String dateFlag,
                                                                  @RequestParam(value = "tipeKewenanganLimit", required = false, defaultValue = "-1") String tipeKewenanganLimit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< sendEmailReportPermohonanPerAplikasiCSV status {} type {} upmProcess {} upmChecker {} dateFlag {} startDate {} endDate {} from {}",
                    status, type, upmProcess.toString(), upmChecker.toString(), dateFlag, startDate, endDate, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                reportUPMService.sendEmailReportPermohonanPerAplikasiCSV(nikRequester, status, type, upmProcess, upmChecker, dateFlag, startDate, endDate, tipeKewenanganLimit);

                logger.info("Response >>> sendEmailReportPermohonanPerAplikasiCSV : Finish Send Report");
                return ResponseEntity.ok(HttpStatus.OK);
            } else {
                return responseForbidden("sendEmailReportPermohonanPerAplikasiCSV");
            }
        } catch (Exception e) {
            return responseInternalServerError("sendEmailReportPermohonanPerAplikasiCSV", e);
        }
    }

    @GetMapping(value = "report/productivity")
    public ResponseEntity<ResponseModel<ListReportProductivityModel>> getReportProductivity(@RequestHeader("XToken") String authorization, @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate, @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate, @RequestParam(value = "upmProcess", required = false, defaultValue = "") List<String> upmProcess) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportProductivity startDate {} endDate {} upmProcess {} from {}", startDate, endDate, upmProcess, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                Date dateNow = new Date();
                String sDateNow = DateTimeHelper.getDateEfektif(dateNow);
                //check format period type
                if (startDate.length() == 10 && endDate.length() == 10) {
                    String[] arrSplitStartDt = startDate.split("-");
                    String[] arrSplitEndDt = endDate.split("-");
                    startDate = arrSplitStartDt[2] + arrSplitStartDt[1] + arrSplitStartDt[0];
                    endDate = arrSplitEndDt[2] + arrSplitEndDt[1] + arrSplitEndDt[0];
                } else if (startDate.length() == 7 && endDate.length() == 7) {
                    String[] arrSplitStartDt = startDate.split("-");
                    String[] arrSplitEndDt = endDate.split("-");
                    startDate = arrSplitStartDt[1] + arrSplitStartDt[0];
                    endDate = arrSplitEndDt[1] + arrSplitEndDt[0];
                } else {
                    String[] arrSplitDtNow = sDateNow.split("-");
                    startDate = arrSplitDtNow[0] + arrSplitDtNow[1] + arrSplitDtNow[2];
                    endDate = arrSplitDtNow[0] + arrSplitDtNow[1] + arrSplitDtNow[2];
                }

                if (CollectionUtils.isEmpty(upmProcess)) {
                    upmProcess = new ArrayList<String>(Arrays.asList("0"));
                }

                ListReportProductivityModel lRPM = new ListReportProductivityModel();
                List<ReportProductivityModel> listRPM = new ArrayList<ReportProductivityModel>();
                List<ReportProductivity> listRP = reportProductivityService.getListReportProductivity(startDate, endDate, upmProcess);
                Iterator<ReportProductivity> iterator = listRP.iterator();
                while (iterator.hasNext()) {
                    ReportProductivity rp = iterator.next();
                    ReportProductivityModel rpm = Mapper.toReportProductivityModel(rp);
                    listRPM.add(rpm);
                }
                lRPM.setReportProductivityModels(listRPM);

                ResponseModel<ListReportProductivityModel> response = buildResponse(SUCCESS, lRPM);
                logger.info("Response >>> getReportProductivity : with size {}", lRPM.getReportProductivityModels().size());
                return ResponseEntity.ok(response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_REPORT_PRODUCTIVITY_GET_LIST, FORBIDDEN, "getReportProductivity", gson);
            }
        } catch (Exception e) {
            logger.error("Fail to getReportProductivity ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "report/delegation")
    public ResponseEntity<ReportDelegasiModel> getReportDelegasi(@RequestHeader("XToken") String authorization,
                                                                         @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                         @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                         @RequestParam("status") String status,
                                                                         @RequestParam("page") Integer pageNumber,
                                                                         @RequestParam("limit") Integer pageSize) {

        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportDelegasi startDate {} endDate {} status {} page {} limit {} from {}", startDate, endDate, status, pageNumber, pageSize, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate) || status == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ReportDelegasiModel reportDelegasi = new ReportDelegasiModel();

                Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
                String startPeriod = period.getKey().split(" ")[0];
                String endPeriod = period.getValue().split(" ")[0];
                int pageNumMin1 = pageNumber - 1;

                Page<TrxDelegation> pageTD = trxDelegationService.getListTrxDelegationByDateAndStatus(startPeriod, endPeriod, status, pageNumMin1, pageSize);
                List<TrxDelegation> listTrxDelegationByDateAndStatus = pageTD.getContent();
                List<DelegationDetailModel> collect = listTrxDelegationByDateAndStatus.stream().map(a -> Mapper.toDelegationDetailModel(a)).collect(Collectors.toList());
                reportDelegasi.setData(collect);
                reportDelegasi.setStartDate(startDate);
                reportDelegasi.setEndDate(endDate);
                reportDelegasi.setPage(pageNumber);
                reportDelegasi.setLimit(pageSize);
                reportDelegasi.setTotalPages(pageTD.getTotalPages());
                reportDelegasi.setTotalItems(pageTD.getTotalElements());

                logger.info("Response >>> getReportDelegasi : with size {}", reportDelegasi.getData().size());
                return ResponseEntity.ok(reportDelegasi);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
        } catch (Exception e) {
            logger.error("Fail to getReportDelegasi ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "report/delegation/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getReportDelegasiCSV(@RequestHeader("XToken") String authorization,
                                                                              @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                              @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                              @RequestParam("status") String status) {

        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportDelegasiCSV startDate {} endDate {} status {} from {}", startDate, endDate, status, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if (!StringUtils.isEmpty(startDate) || !StringUtils.isEmpty(endDate) || status != null) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
                    String startPeriod = period.getKey().split(" ")[0];
                    String endPeriod = period.getValue().split(" ")[0];

                    ResponseModel<ResUploadModel> response = trxDelegationService.generateReportDelegasiCsv(startPeriod, endPeriod, status);
                    logger.info("Response >>> getReportDelegasiCSV : {}", gson.toJson(response));
                    return ResponseEntity.ok(response);
                }
                return responseForbidden("getReportDelegasiCSV");
            }
            return responseBadRequest("getReportDelegasiCSV");
        } catch (Exception e) {
            return responseInternalServerError("getReportDelegasiCSV", e);
        }
    }

    @GetMapping(value = "report/delegation/direct-download")
    public ResponseEntity<Resource> directDownloadReportDelegasiCSV(@RequestHeader("XToken") String authorization,
                                                                                         @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                                         @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
                                                                                         @RequestParam("status") String status) {

        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadReportDelegasiCSV startDate {} endDate {} status {} from {}", startDate, endDate, status, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if (!StringUtils.isEmpty(startDate) || !StringUtils.isEmpty(endDate) || status != null) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
                    String startPeriod = period.getKey().split(" ")[0];
                    String endPeriod = period.getValue().split(" ")[0];

                    ResFileDownload response = trxDelegationService.directDownloadReportDelegasiCSV(startPeriod, endPeriod, status);
                    return responseDownloadFile("directDownloadReportDelegasiCSV", response);
                }
                return responseForbidden("directDownloadReportDelegasiCSV");
            }
            return responseBadRequest("directDownloadReportDelegasiCSV");
        } catch (Exception e) {
            return responseInternalServerError("directDownloadReportDelegasiCSV", e);
        }
    }

    private ResponseModel<ListReportProductivityModel> buildResponse(ResponseStatus status, ListReportProductivityModel listReportProductivityModel) {
        ResponseModel<ListReportProductivityModel> response = new ResponseModel<>();

        response.setType(TYPE_REPORT_PRODUCTIVITY_GET_LIST);
        response.setDetails(listReportProductivityModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        return response;
    }

    @GetMapping(value = "report/uar")
    public ResponseEntity<ResponseModel<ResponseListModel>> getReportUARUPM(@RequestHeader("XToken") String authorization,
                                                                            @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                            @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                            @RequestParam("triwulan") String triwulan,
                                                                            @RequestParam("tahun") String tahun,
                                                                            @RequestParam("aplikasi") String aplikasi) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportUARUPM triwulan {} tahun {} aplikasi {} from {}", triwulan, tahun, aplikasi, getProfile(gson, profile));
            int pageNumMin1 = pageNumber - 1;
            String nikRequester = profile.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel> response = trxUPMUARService.getReportUARUPM(triwulan, tahun, aplikasi, pageNumMin1, pageNumber, pageSize);

                logger.info("Response >>> getReportUARUPM : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getReportUARUPM : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getReportUARUPM");
        } catch (Exception e) {
            return responseInternalServerError("getReportUARUPM", e);
        }
    }

    @GetMapping(value = "/report/uar/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getReportUARUPMAsPDF(@RequestHeader("XToken") String authorization,
                                                                           @RequestParam(value = "triwulan") String triwulan,
                                                                           @RequestParam(value = "tahun") String tahun,
                                                                           @RequestParam("aplikasi") String aplikasi) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getReportUARUPMAsPDF triwulan {} tahun {} aplikasi {} from {}", triwulan, tahun, aplikasi, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getReportUARUPMAsPDF : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getReportUARUPMAsPDF");
            }

            ResponseModel<ResUploadModel> response = trxUPMUARService.generateReportUARAsPDF(triwulan, tahun, aplikasi);
            logger.info("Response >>> getReportUARUPMAsPDF : {}", gson.toJson(response));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return responseInternalServerError("getReportUARUPMAsPDF", e);
        }
    }

    @GetMapping(value = "/report/uar/direct-download")
    public ResponseEntity<Resource> directDownloadReportUARUPMAsPDF(@RequestHeader("XToken") String authorization,
                                                                    @RequestParam(value = "triwulan") String triwulan,
                                                                    @RequestParam(value = "tahun") String tahun,
                                                                    @RequestParam("aplikasi") String aplikasi) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadReportUARUPMAsPDF triwulan {} tahun {} aplikasi {} from {}", triwulan, tahun, aplikasi, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> directDownloadReportUARUPMAsPDF : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("directDownloadReportUARUPMAsPDF");
            }

            ResFileDownload response = trxUPMUARService.directDownloadReportUARUPMAsPDF(triwulan, tahun, aplikasi);
            return responseDownloadFile("directDownloadReportUARUPMAsPDF", response);
        } catch (Exception e) {
            return responseInternalServerError("directDownloadReportUARUPMAsPDF", e);
        }
    }

    @GetMapping(value = "/report/uar/summary/direct-download")
    public ResponseEntity<Resource> directDownloadBeritaAcaraPDF(@RequestHeader("XToken") String authorization,
                                                                 @RequestParam(value = "nomorBA") String refNumber,
                                                                 @RequestParam(value = "triwulan") String triwulan,
                                                                 @RequestParam(value = "tahun") String tahun,
                                                                 @RequestParam(value = "aplikasi") String aplikasi,
                                                                 @RequestParam(value = "isNewBA") Integer isNewBA) {
        try {
            Token token = new Token(authorization);
            String profile = getProfile(gson, token);
            logger.info("Received <<< directDownloadBeritaAcaraPDF nomorBA {} triwulan {} tahun {} aplikasi {} from {}", refNumber, triwulan, tahun, aplikasi, profile);

            if (trxUpmRoleService.getTrxUpmRole(token.getProfile().getPreferred_username()) == null) {
                logger.warn("Response >>> directDownloadBeritaAcaraPDF : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("directDownloadBeritaAcaraPDF");
            }
            ResFileDownload response = trxUPMUARService.directDownloadBeritaAcaraPDF(refNumber, triwulan, tahun, aplikasi, isNewBA);

            logger.info("Response >>> directDownloadBeritaAcaraPDF {} ", gson.toJson(response));
            return responseDownloadFile("directDownloadBeritaAcaraPDF", response);
        } catch (Exception exception) {
            logger.warn("Response >>> directDownloadBeritaAcaraPDF : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadBeritaAcaraPDF", exception);
        }
    }

    @GetMapping(value = "/report/uar/summary")
    public ResponseEntity<ResponseModel<ResponseListModel<UARSummaryModel>>> getListUARSummary(@RequestHeader("XToken") String authorization,
                                                                                               @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                               @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                               @RequestParam(value = "nomorBA", required = false, defaultValue = UPM_FILTER_TYPE_ALL) String refNumber,
                                                                                               @RequestParam(value = "triwulan", required = false, defaultValue = UPM_FILTER_TYPE_ALL) String triwulan,
                                                                                               @RequestParam(value = "tahun", required = false, defaultValue = FILTER_CODE_ALL) String tahun,
                                                                                               @RequestParam(value = "aplikasi", required = false, defaultValue = UPM_FILTER_TYPE_ALL) String aplikasi) {
        try {
            Token token = new Token(authorization);
            String profile = getProfile(gson, token);
            logger.info("Received <<< getListUARSummary page {} limit {} nomorBA {} triwulan {} tahun {} aplikasi {} from {}", pageNumber, pageSize, refNumber, triwulan, tahun, aplikasi, profile);

            TrxUpmRole upmRole = trxUpmRoleService.getTrxUpmRole(token.getProfile().getPreferred_username());
            if (!CommonHelper.isNonViewerUPMRole(upmRole)) {
                logger.warn("Response >>> getListUARSummary : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getListUARSummary");
            }

            ResponseModel<ResponseListModel<UARSummaryModel>> response = trxUPMUARService.getListUARSummary(refNumber, triwulan, tahun, aplikasi, pageNumber, pageSize);
            logger.info("Response >>> getListUARSummary {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getListUARSummary : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListUARSummary", exception);
        }
    }
}
