package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsTepatMBankingIndividu;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.MsTepatMBankingIndividuService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.ALREADY_EXIST;
import static com.btpns.fin.helper.ResponseStatus.NOT_FOUND;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000006")
@CrossOrigin("*")
public class MsTepatMBankingIndividuManagementController {
    private static final Logger logger = LoggerFactory.getLogger(MsTepatMBankingIndividuManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private MsTepatMBankingIndividuService msTepatMBankingIndividuService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private TrxUserIdBatchService trxUserIdBatchService;

    @GetMapping
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsTepatMBankingIndividuManagement(@RequestHeader("Xtoken") String authorization,
                                                                                                 @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                 @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                                 @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                                 @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsTepatMBankingIndividuManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            int pageNumMin1 = pageNumber - 1;
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsTepatMBankingIndividuManagement");
            }
            ResponseModel<ResponseListModel> response = this.msTepatMBankingIndividuService.getListTepatMBankingIndividuUsers(pageNumMin1, pageNumber, pageSize, searchFlag, searchData);
            logger.info("Response >>> getMsTepatMBankingIndividuManagement size {}", response.getDetails());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsTepatMBankingIndividuManagement", exception);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<UserIDModel>> getMsTepatMBankingIndividuManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                                @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsTepatMBankingIndividuManagementByNik nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsTepatMBankingIndividuManagementByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsTepatMBankingIndividuManagementByNik");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> getMsTepatMBankingIndividuManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsTepatMBankingIndividuManagementByNik");
            }
            ResponseModel<UserIDModel> response = this.msTepatMBankingIndividuService.getTepatMBankingIndividuByNik(nik);
            logger.info("Response >>> getMsTepatMBankingIndividuManagementByNik {}", gson.toJson(response.getDetails()));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsTepatMBankingIndividuManagementByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsTepatMBankingIndividuManagementByNik", exception);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsTepatMBankingIndividuManagement(@RequestHeader("XToken") String authorization,
                                                                                                     @RequestBody RequestModel<ReqUserIDModel> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsTepatMBankingIndividuManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> addOrEditMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("addOrEditMsTepatMBankingIndividuManagement");
            }
            if (!isValidUserIDRequest(request.getDetails())) {
                logger.warn("Response >>> addOrEditMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsTepatMBankingIndividuManagement");
            }
            if (request.getDetails().getType().equalsIgnoreCase(ADD)
                    && this.msTepatMBankingIndividuService.findByNik(request.getDetails().getNik()).isPresent()) {
                return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMsTepatMBankingIndividuManagement", gson);
            }
            if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                ResponseModel<ResCUDUserIdModel> updatedTepatMBankingIndividu = this.msTepatMBankingIndividuService.updateTepatMBankingIndividu(request.getDetails());
                logger.info("Response >>> editMsTepatMBankingIndividuManagement {} ", gson.toJson(updatedTepatMBankingIndividu));

                return ResponseEntity.ok(updatedTepatMBankingIndividu);
            }
            ResponseModel<ResCUDUserIdModel> savedTepatMBankingIndividu = this.msTepatMBankingIndividuService.saveTepatMBankingIndividu(request.getDetails());
            logger.info("Response >>> addMsTepatMBankingIndividuManagement {} ", gson.toJson(savedTepatMBankingIndividu));

            return ResponseEntity.ok(savedTepatMBankingIndividu);
        } catch (Exception exception) {
            logger.warn("Response >>> addOrEditMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsTepatMBankingIndividuManagement", exception);
        }
    }

    @DeleteMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsTepatMBankingIndividuManagement(@RequestHeader("XToken") String authorization,
                                                                                    @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsTepatMBankingIndividuManagement nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> deleteMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("deleteMsTepatMBankingIndividuManagement");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> deleteMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsTepatMBankingIndividuManagement");
            }
            if (this.msTepatMBankingIndividuService.findByNik(nik).isEmpty()) {
                logger.warn("Response >>> deleteMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.NOT_FOUND));
                return responseFailed(TYPE_MS_TEPAT_MBANKING_INDIVIDU_DELETE, NOT_FOUND, "deleteMsTepatMBankingIndividuManagement", gson);
            }

            ResponseModel<ResCUDUserIdModel> response = this.msTepatMBankingIndividuService.deleteTepatMBankingIndividu(nik);
            logger.info("Response >>> deleteMsTepatMBankingIndividuManagement : {}", gson.toJson(response));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> deleteMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsTepatMBankingIndividuManagement", exception);
        }
    }

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchTepatMBankingIndividuManagement(@RequestHeader("XToken") String authorization,
                                                                                                  @RequestBody ReqUserIdBatchModel<MsTepatMBankingIndividu> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsTepatMBankingIndividuManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> saveBatchMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("saveBatchMsTepatMBankingIndividuManagement");
            }
            if (this.isExistInInterval(nikRequester, request.getType())) {
                logger.warn("Response >>> saveBatchMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            if(!this.isValidRequest(request) || this.msTepatMBankingIndividuService.hasDuplicate(request.getData())) {
                logger.warn("Response >>> saveBatchMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("saveBatchMsTepatMBankingIndividuManagement");
            }
            ResponseModel<ResBatchUserId> savedBatchTepatMBankingIndividuId = this.msTepatMBankingIndividuService.saveBatchMsTepatMBankingIndividu(request, nikRequester);

            logger.info("Response >>> saveBatchMsTepatMBankingIndividuManagement {}", gson.toJson(savedBatchTepatMBankingIndividuId));
            return ResponseEntity.ok(savedBatchTepatMBankingIndividuId);
        } catch (Exception exception) {
            logger.warn("Response >>> saveBatchMsTepatMBankingIndividuManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsTepatMBankingIndividuManagement", exception);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateExcelMsTepatMBankingIndividuUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< generateExcelMsTepatMBankingIndividuUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = msTepatMBankingIndividuService.generateExcelMsTepatMBankingIndividuUserId();
                logger.info("Response >>> generateExcelMsTepatMBankingIndividuUserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> generateExcelMsTepatMBankingIndividuUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("generateExcelMsTepatMBankingIndividuUserId");
        } catch (Exception e) {
            logger.warn("Response >>> generateExcelMsTepatMBankingIndividuUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("generateExcelMsTepatMBankingIndividuUserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsTepatMBankingIndividuUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsTepatMBankingIndividuUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = msTepatMBankingIndividuService.directDownloadExcelMsTepatMBankingIndividuUserId();
                return responseDownloadFile("directDownloadExcelMsTepatMBankingIndividuUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsTepatMBankingIndividuUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsTepatMBankingIndividuUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsTepatMBankingIndividuUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsTepatMBankingIndividuUserId", e);
        }
    }

    private boolean isExistInInterval(String nik, String type) {
        return this.trxUserIdBatchService.existInInterval(nik, type, 30);
    }

    private boolean isValidRequest(ReqUserIdBatchModel<MsTepatMBankingIndividu> request) {
        return request != null
                && request.getType() != null
                && request.getBatchId() != null
                && request.getFileName() != null
                && request.getTotalData() > 0
                && request.getData() != null
                && request.getData().size() > 0
                && this.trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }
}
