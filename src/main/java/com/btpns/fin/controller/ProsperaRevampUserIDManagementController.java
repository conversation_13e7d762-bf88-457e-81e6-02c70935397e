package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.ProsperaUserIDModel;
import com.btpns.fin.model.entity.TrxUpmRole;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.ProsperaRevampUserIdService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping("/tema/upm/user-id/AU00000008")
@CrossOrigin("*")
public class ProsperaRevampUserIDManagementController {
    private static final Logger logger = LoggerFactory.getLogger(ProsperaRevampUserIDManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private ProsperaRevampUserIdService prosperaRevampUserIdService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel<ProsperaUserIDModel>>> getProsperaRevampUserID(@RequestHeader("XToken") String authorization,
                                                                                                         @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                         @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                                         @RequestParam(value = "searchFlag", required = false, defaultValue = SEARCH_FLAG_NAME) String searchFlag,
                                                                                                         @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData,
                                                                                                         @RequestParam(value = "filterStatus", required = false, defaultValue = "-1") String filterStatus,
                                                                                                         @RequestParam(value = "filterRole", required = false, defaultValue = "-1") String filterRole,
                                                                                                         @RequestParam(value = "filterOffice", required = false, defaultValue = "-1") String filterOffice) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getProsperaRevampUserID page {} limit {} searchFlag {} searchData {} filterStatus {} filterRole {} filterOffice {} from {}", pageNumber, pageSize, searchFlag, searchData, filterStatus, filterRole, filterOffice, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = this.trxUpmRoleService.getTrxUpmRole(nikRequester);
            if (!isValidSearchFlag(searchFlag)) {
                return responseBadRequest("getProsperaRevampUserID");
            }

            if (!CommonHelper.validateUPMTicket(nikRequester, trxUpmRole) && !isValidUpmRole(trxUpmRole)) {
                return responseForbidden("getProsperaRevampUserID");
            }

            ResponseModel<ResponseListModel<ProsperaUserIDModel>> response = this.prosperaRevampUserIdService.getListProsperaRevampUserID(pageSize, pageNumber, searchFlag, searchData, filterStatus, filterRole, filterOffice);
            logger.info("Response >>> getProsperaRevampUserID size {}", response.getDetails().getData().size());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return responseInternalServerError("getProsperaRevampUserID", e);
        }
    }

    private static boolean isValidUpmRole(TrxUpmRole trxUpmRole) {
        return trxUpmRole.getRole().equals(UPM_ROLE_MAKER) || trxUpmRole.getRole().equals(UPM_ROLE_CHECKER) || trxUpmRole.getRole().equalsIgnoreCase(UPM_ROLE_ADMIN);
    }

    private static boolean isValidSearchFlag(String searchFlag) {
        return SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag) || SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag) || SEARCH_FLAG_LOGIN_NAME.equalsIgnoreCase(searchFlag);
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getProsperaRevampUserIDCsv(@RequestHeader("XToken") String authorization,
                                                                                    @RequestParam(value = "searchFlag", required = false, defaultValue = SEARCH_FLAG_NAME) String searchFlag,
                                                                                    @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData,
                                                                                    @RequestParam(value = "filterStatus", required = false, defaultValue = "-1") String filterStatus,
                                                                                    @RequestParam(value = "filterRole", required = false, defaultValue = "-1") String filterRole,
                                                                                    @RequestParam(value = "filterOffice", required = false, defaultValue = "-1") String filterOffice) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = trxUpmRoleService.getTrxUpmRole(nikRequester);
            logger.info("Received <<< getProsperaRevampUserIDCsv from {}", getProfile(gson, token));
            if (CommonHelper.validateUPMTicket(nikRequester, trxUpmRole) && isValidUpmRole(trxUpmRole)) {
                ResponseModel<ResUploadModel> response = prosperaRevampUserIdService.getProsperaUserIDCsv(searchFlag, searchData, filterStatus, filterRole, filterOffice);

                logger.info("Response >>> getProsperaRevampUserIDCsv : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getProsperaRevampUserIDCsv");
        } catch (Exception e) {
            return responseInternalServerError("getProsperaRevampUserIDCsv", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadProsperaRevampUserIDCsv(@RequestHeader("XToken") String authorization,
                                                                          @RequestParam(value = "searchFlag", required = false, defaultValue = SEARCH_FLAG_NAME) String searchFlag,
                                                                          @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData,
                                                                          @RequestParam(value = "filterStatus", required = false, defaultValue = "-1") String filterStatus,
                                                                          @RequestParam(value = "filterRole", required = false, defaultValue = "-1") String filterRole,
                                                                          @RequestParam(value = "filterOffice", required = false, defaultValue = "-1") String filterOffice) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = trxUpmRoleService.getTrxUpmRole(nikRequester);
            logger.info("Received <<< directDownloadProsperaRevampUserIDCsv from {}", getProfile(gson, token));
            if (CommonHelper.validateUPMTicket(nikRequester, trxUpmRole) && isValidUpmRole(trxUpmRole)) {
                ResFileDownload response = prosperaRevampUserIdService.directDownloadProsperaRevampUserIDCsv(searchFlag, searchData, filterStatus, filterRole, filterOffice);
                return responseDownloadFile("directDownloadProsperaRevampUserIDCsv", response);
            }
            return responseForbidden("directDownloadProsperaRevampUserIDCsv");
        } catch (Exception e) {
            return responseInternalServerError("directDownloadProsperaRevampUserIDCsv", e);
        }
    }
}
