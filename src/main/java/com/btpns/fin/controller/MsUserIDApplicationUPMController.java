package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDAppDetailModel;
import com.btpns.fin.model.entity.MsUserIDApplication;
import com.btpns.fin.model.request.ReqUserIDApplication;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsUserIDApplicationService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseHelper.responseBadRequest;
import static com.btpns.fin.helper.ResponseStatus.ALREADY_EXIST;

@RestController
@RequestMapping(value = "tema/upm/user-id/applications")
@CrossOrigin("*")
public class MsUserIDApplicationUPMController {
    private static final Logger logger = LoggerFactory.getLogger(MsUserIDApplicationUPMController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private MsUserIDApplicationService msUserIDApplicationService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @GetMapping
    public ResponseEntity<ResponseModel<ResponseListModel<UserIDAppDetailModel>>> getUserIDApplicationsUPM(@RequestHeader("XToken") String authorization,
                                                                                                           @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                           @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                                           @RequestParam(value = "searchFlag", required = false, defaultValue = "paramDetailId") String searchFlag,
                                                                                                           @RequestParam(value = "searchData", required = false, defaultValue = "all") String searchData) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            logger.info("Received <<< getUserIDApplicationsUPM from {}", getProfile(gson, token));

            if (!CommonHelper.isNonViewerUPMRole(this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getUserIDApplicationsUPM : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getUserIDApplicationsUPM");
            }

            ResponseModel<ResponseListModel<UserIDAppDetailModel>> response = msUserIDApplicationService.getUserIDApplications(searchFlag, searchData, pageNumber, pageSize);

            logger.info("Response >>> getUserIDApplicationsUPM {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getUserIDApplicationsUPM : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getUserIDApplicationsUPM", exception);
        }
    }

    @GetMapping(value = "/{paramDetailId}")
    public ResponseEntity<ResponseModel<UserIDAppDetailModel>> getDetailUserIDApplicationUPM(@RequestHeader("XToken") String authorization,
                                                                                             @PathVariable("paramDetailId") String paramDetailId) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            logger.info("Received <<< getDetailUserIDApplicationUPM from {}", getProfile(gson, token));

            if (!CommonHelper.isNonViewerUPMRole(this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getDetailUserIDApplicationUPM : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getDetailUserIDApplicationUPM");
            }

            ResponseModel<UserIDAppDetailModel> response = msUserIDApplicationService.getDetailUserIDApplication(paramDetailId);

            logger.info("Response >>> getDetailUserIDApplicationUPM {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getDetailUserIDApplicationUPM : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getDetailUserIDApplicationUPM", exception);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<UserIDAppDetailModel>> addOrEditMsUserIDApplication(@RequestHeader("XToken") String authorization,
                                                                                         @RequestBody RequestModel<ReqUserIDApplication> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsUserIDApplication {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(!isValidRequest(request)) {
                logger.warn("Response >>> addOrEditMsUserIDApplication : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsUserIDApplication");
            }
            if (!CommonHelper.isNonViewerUPMRole(trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> addOrEditMsUserIDApplication : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("addOrEditMsUserIDApplication");
            }

            Optional<MsUserIDApplication> optionalMsUserIDApplication = msUserIDApplicationService.findById(request.getDetails().getCode());
            if (ADD.equalsIgnoreCase(request.getType()) && optionalMsUserIDApplication.isPresent()){
                return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMsUserIDApplication", gson);
            }
            if (EDIT.equalsIgnoreCase(request.getType()) && optionalMsUserIDApplication.isPresent()){
                ResponseModel<UserIDAppDetailModel> updatedUserIDApplication = msUserIDApplicationService.updateUserIDApplication(optionalMsUserIDApplication.get(), request.getDetails());
                logger.info("Response >>> editUserIDApplication {} ", gson.toJson(updatedUserIDApplication));

                return ResponseEntity.ok(updatedUserIDApplication);
            }

            ResponseModel<UserIDAppDetailModel> saveApplicationTypeData = msUserIDApplicationService.saveUserIDApplication(request.getDetails());
            logger.info("Response >>> addUserIDApplication {} ", gson.toJson(saveApplicationTypeData));

            return ResponseEntity.ok(saveApplicationTypeData);

        } catch (Exception e){
            return responseInternalServerError("addOrEditMsUserIDApplication", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqUserIDApplication> request) {
        return (ADD.equals(request.getType()) || EDIT.equals(request.getType())) &&
                request.getDetails().getCode() != null &&
                request.getDetails().getDesc() != null;
    }
}
