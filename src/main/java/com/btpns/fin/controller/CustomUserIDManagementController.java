package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsCustomUserID;
import com.btpns.fin.model.CustomUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.MsCustomUserIDService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.ALREADY_EXIST;
import static com.btpns.fin.helper.ResponseStatus.NOT_FOUND;

@Controller
@RequestMapping(value = "tema/upm/user-id")
@CrossOrigin("*")
public class CustomUserIDManagementController {
    private static final Logger logger = LoggerFactory.getLogger(CustomUserIDManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private MsCustomUserIDService msCustomUserIDService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private TrxUserIdBatchService trxUserIdBatchService;

    @GetMapping("/{paramDetailId}")
    public ResponseEntity<ResponseModel<ResponseListCustomUserID>> getAllCustomUserIDByApplication(@RequestHeader("XToken") String authorization,
                                                                                                   @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                   @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                                   @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                                   @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData,
                                                                                                   @PathVariable("paramDetailId") String paramDetailId) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getAllCustomUserIDByApplication page {} limit {} searchFlag {} searchData {} paramDetailId {} from {}", pageNumber, pageSize, searchFlag, searchData, paramDetailId, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.isNonViewerUPMRole(this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getAllCustomUserIDByApplication : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getAllCustomUserIDByApplication");
            }
            ResponseModel<ResponseListCustomUserID> response = this.msCustomUserIDService.getAllByApplication(pageNumber, pageSize, searchFlag, searchData, paramDetailId);
            logger.info("Response >>> getAllCustomUserIDByApplication size {}", response.getDetails());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getAllCustomUserIDByApplication : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getAllCustomUserIDByApplication", exception);
        }
    }

    @GetMapping(value = "/{paramDetailId}/{nik:.+}")
    public ResponseEntity<ResponseModel<CustomUserIDModel>> getCustomUserIDByNik(@RequestHeader("XToken") String authorization,
                                                                                 @PathVariable("nik") String nik,
                                                                                 @PathVariable("paramDetailId") String paramDetailId) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getCustomUserIDByNik nik {} paramDetailId {} from {}", nik, paramDetailId, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();

            if (!CommonHelper.isNonViewerUPMRole(this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getCustomUserIDByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getCustomUserIDByNik");
            }

            ResponseModel<CustomUserIDModel> response = this.msCustomUserIDService.getCustomUserIDByNikAndParamDetailId(nik, paramDetailId);
            logger.info("Response >>> getCustomUserIDByNik {}", gson.toJson(response.getDetails()));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getCustomUserIDByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getCustomUserIDByNik", exception);
        }
    }

    @PostMapping("/{paramDetailId}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditCustomUserID(@RequestHeader("XToken") String authorization,
                                                                                  @PathVariable("paramDetailId") String paramDetailId,
                                                                                  @RequestBody RequestModel<UserIDModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditCustomUserID {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(!isValidRequest(request)) {
                logger.warn("Response >>> addOrEditCustomUserID : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditCustomUserID");
            }
            if (!CommonHelper.isNonViewerUPMRole(trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> addOrEditCustomUserID : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("addOrEditCustomUserID");
            }

            Optional<MsCustomUserID> optional = msCustomUserIDService.findByNikAndParamDetailId(request.getDetails().getNik(), paramDetailId);
            if (ADD.equalsIgnoreCase(request.getType()) && optional.isPresent()){
                return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditCustomUserID", gson);
            }
            if (EDIT.equalsIgnoreCase(request.getType()) && optional.isPresent()){
                ResponseModel<ResCUDUserIdModel> updatedCustomUserID = msCustomUserIDService.updateCustomUserID(optional.get(), request.getDetails());
                logger.info("Response >>> editCustomUserID {} ", gson.toJson(updatedCustomUserID));

                return ResponseEntity.ok(updatedCustomUserID);
            }

            ResponseModel<ResCUDUserIdModel> response = msCustomUserIDService.saveCustomUserID(request.getDetails(), paramDetailId);
            logger.info("Response >>> addCustomUserID {} ", gson.toJson(response));

            return ResponseEntity.ok(response);

        } catch (Exception e){
            return responseInternalServerError("addOrEditCustomUserID", e);
        }
    }

    private boolean isValidRequest(RequestModel<UserIDModel> request) {
        UserIDModel details = request.getDetails();
        return (ADD.equals(request.getType()) || EDIT.equals(request.getType()))
                && StringUtils.isNotBlank(details.getNik())
                && StringUtils.isNotBlank(details.getNamaUser())
                && StringUtils.isNotBlank(details.getKewenangan())
                && StringUtils.isNotBlank(details.getJabatan())
                && StringUtils.isNotBlank(details.getUnitKerja());
    }

    @DeleteMapping("/{paramDetailId}/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteCustomUserID(@RequestHeader("XToken") String authorization,
                                                                               @PathVariable("paramDetailId") String paramDetailId,
                                                                               @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteCustomUserID paramDetailId {} nik {} from {}", paramDetailId, nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();

            if (!CommonHelper.isNonViewerUPMRole(trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> deleteCustomUserID : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("deleteCustomUserID");
            }

            Optional<MsCustomUserID> optional = msCustomUserIDService.findByNikAndParamDetailId(nik, paramDetailId);
            if (optional.isEmpty()){
                return responseFailed(TYPE_MS_CUSTOM_USERID_MANAGEMENT_DELETE, NOT_FOUND, "deleteCustomUserID", gson);
            }

            ResponseModel<ResCUDUserIdModel> response = msCustomUserIDService.deleteCustomUserID(optional.get());
            logger.info("Response >>> deleteCustomUserID {} ", gson.toJson(response));

            return ResponseEntity.ok(response);

        } catch (Exception e){
            return responseInternalServerError("deleteCustomUserID", e);
        }
    }

    @PostMapping("/{paramDetailId}/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchCustomUserID(@RequestHeader("XToken") String authorization,
                                                                               @PathVariable("paramDetailId") String paramDetailId,
                                                                               @RequestBody ReqUserIdBatchModel<MsCustomUserID> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchCustomUserID {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.isNonViewerUPMRole(trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> saveBatchCustomUserID : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("saveBatchCustomUserID");
            }
            if(!isValidRequest(request)
                    || CommonHelper.isDuplicateData(request.getData().stream().map(MsCustomUserID::getNik).collect(Collectors.toList()))) {
                logger.warn("Response >>> saveBatchCustomUserID : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("saveBatchCustomUserID");
            }
            if (this.isExistInInterval(nikRequester, request.getType())) {
                logger.warn("Response >>> saveBatchCustomUserID : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }

            ResponseModel<ResBatchUserId> response = msCustomUserIDService.saveBatchCustomUserID(request, paramDetailId, nikRequester);
            logger.info("Response >>> saveBatchCustomUserID {} ", gson.toJson(response));

            return ResponseEntity.ok(response);

        } catch (Exception e){
            return responseInternalServerError("saveBatchCustomUserID", e);
        }
    }

    private boolean isExistInInterval(String nik, String type) {
        return this.trxUserIdBatchService.existInInterval(nik, type, 30);
    }

    private boolean isValidRequest(ReqUserIdBatchModel<MsCustomUserID> request) {
        return request != null
                && StringUtils.isNotBlank(request.getType())
                && StringUtils.isNotBlank(request.getBatchId())
                && StringUtils.isNotBlank(request.getFileName())
                && request.getTotalData() > 0
                && request.getData() != null
                && !request.getData().isEmpty()
                && this.trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    @GetMapping(value = "/{paramDetailId}/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateExcelMsCustomUserID(@RequestHeader("XToken") String authorization,
                                                                                     @PathVariable("paramDetailId") String paramDetailId) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< generateExcelMsCustomUserID from {}", getProfile(gson, profile));

            if (!CommonHelper.isNonViewerUPMRole(trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> generateExcelMsCustomUserID : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("generateExcelMsCustomUserID");
            }

            ResponseModel<ResUploadModel> response = msCustomUserIDService.generateExcelMsCustomUserID(paramDetailId);
            logger.info("Response >>> generateExcelMsCustomUserID : {}", gson.toJson(response));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.warn("Response >>> generateExcelMsCustomUserID : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("generateExcelMsCustomUserID", e);
        }
    }

    @GetMapping(value = "/{paramDetailId}/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsCustomSUserId(@RequestHeader("XToken") String authorization,
                                                                       @PathVariable("paramDetailId") String paramDetailId) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsCustomSUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = msCustomUserIDService.directDownloadExcelMsCustomSUserId(paramDetailId);
                return responseDownloadFile("directDownloadExcelMsCustomSUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsCustomSUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsCustomSUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsCustomSUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsCustomSUserId", e);
        }
    }
}
