package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsSlik;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.ISlikUserIdRepository;
import com.btpns.fin.service.SlikUserIdService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000004")
@CrossOrigin("*")
public class SlikUserIdManagementController {
    private static final Logger logger = LoggerFactory.getLogger(SlikUserIdManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxUserIdBatchService trxUserIdBatchService;

    @Autowired
    SlikUserIdService slikUserIdService;

    @Autowired
    ISlikUserIdRepository slikUserIdRepository;

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchMsSlikUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                         @RequestBody ReqUserIdBatchModel<MsSlik> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsSlikUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (!CommonHelper.isExistInInterval(trxUserIdBatchService.existInInterval(nikRequester, request.getType(), 30))) {
                    if(isValidRequest(request) && !CommonHelper.isDuplicateData(request.getData().stream().map(d -> d.getNik()).collect(Collectors.toList()))) {
                        ResponseModel<ResBatchUserId> saveBatchSlikUserId = slikUserIdService.saveBatchMsSlikUserId(request, nikRequester);

                        logger.info("Response >>> saveBatchMsSlikUserIdManagement {}", gson.toJson(saveBatchSlikUserId));
                        return ResponseEntity.ok(saveBatchSlikUserId);
                    }
                    logger.warn("Response >>> saveBatchMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                    return responseBadRequest("saveBatchMsSlikUserIdManagement");
                }
                logger.warn("Response >>> saveBatchMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            logger.warn("Response >>> saveBatchMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("saveBatchMsSlikUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> saveBatchMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsSlikUserIdManagement", e);
        }
    }

    private boolean isValidRequest(ReqUserIdBatchModel<MsSlik> request) {
        return request != null
               && request.getType() != null
               && request.getBatchId() != null
               && request.getFileName() != null
               && request.getTotalData() > 0
               && request.getData() != null
               && request.getData().size() > 0
               && trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsSlikUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                      @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                      @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                      @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                      @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsSlikUserIdManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel> response = slikUserIdService.getListMsSlik(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);

                logger.info("Response >>> getMsSlikUserIdManagement {}", response.getDetails());
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsSlikUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> getMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsSlikUserIdManagement", e);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<UserIDModel>> getMsSlikUserIdManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                     @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsSlikUserIdManagementByNik nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(StringUtils.isNotBlank(nik)) {
                    ResponseModel<UserIDModel> response = slikUserIdService.getMsSlikByNik(nik);

                    logger.info("Response >>> getMsSlikUserIdManagementByNik {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                logger.warn("Response >>> getMsSlikUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsSlikUserIdManagementByNik");
            }
            logger.warn("Response >>> getMsSlikUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsSlikUserIdManagementByNik");

        } catch (Exception e){
            logger.warn("Response >>> getMsSlikUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsSlikUserIdManagementByNik", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsSlikUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                         @RequestBody RequestModel<ReqUserIDModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsSlikUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(CommonHelper.isValidUserIDRequest(request.getDetails())) {
                    if (slikUserIdRepository.findByNikUser(request.getDetails().getNik()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResCUDUserIdModel> updateMsSlik = slikUserIdService.updateMsSlikUserId(request.getDetails());
                            logger.info("Response >>> editMsSlikUserIdManagement {} ", gson.toJson(updateMsSlik));

                            return ResponseEntity.ok(updateMsSlik);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditAlihDayaUserManagement", gson);
                    }
                    ResponseModel<ResCUDUserIdModel> saveMsSlik = slikUserIdService.saveMsSlikUserId(request.getDetails());

                    logger.info("Response >>> addMsSlikUserIdManagement {} ", gson.toJson(saveMsSlik));
                    return ResponseEntity.ok(saveMsSlik);
                }
                logger.warn("Response >>> addOrEditMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsSlikUserIdManagement");
            }
            logger.warn("Response >>> addOrEditMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("addOrEditMsSlikUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> addOrEditMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsSlikUserIdManagement", e);
        }
    }

    @DeleteMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsSlikUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                      @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsSlikUserIdManagement nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(StringUtils.isNotBlank(nik)) {
                    if (slikUserIdRepository.findByNikUser(nik) != null) {
                        ResponseModel<ResCUDUserIdModel> response = slikUserIdService.deleteMsSlikUserId(nik);

                        logger.info("Response >>> deleteMsSlikUserIdManagement : {}", gson.toJson(response));
                        return ResponseEntity.ok(response);
                    }
                    return responseFailed(TYPE_MS_SLIK_MANAGEMENT_DELETE, NOT_FOUND, "deleteMsSlikUserIdManagement", gson);
                }
                logger.warn("Response >>> deleteMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsSlikUserIdManagement");
            }
            logger.warn("Response >>> deleteMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("deleteMsSlikUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> deleteMsSlikUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsSlikUserIdManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateExcelMsSlikUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< generateExcelMsSlikUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = slikUserIdService.generateExcelMsSlikUserId();
                logger.info("Response >>> generateExcelMsSlikUserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> generateExcelMsSlikUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("generateExcelMsSlikUserId");
        } catch (Exception e) {
            logger.warn("Response >>> generateExcelMsSlikUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("generateExcelMsSlikUserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsSlikUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsSlikUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = slikUserIdService.directDownloadExcelMsSlikUserId();
                return responseDownloadFile("directDownloadExcelMsSlikUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsSlikUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsSlikUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsSlikUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsSlikUserId", e);
        }
    }
}
