package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsS4;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.MsS4UserManagementService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000002")
@CrossOrigin("*")
public class MsS4UserManagementController {
    private static final Logger logger = LoggerFactory.getLogger(MsS4UserManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private MsS4UserManagementService msS4UserManagementService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private TrxUserIdBatchService trxUserIdBatchService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsS4UserManagement(@RequestHeader("XToken") String authorization,
                                                                                  @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                  @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                  @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                  @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsS4UserManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            int pageNumMin1 = pageNumber - 1;
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsS4UserManagement");
            }
            ResponseModel<ResponseListModel> response = this.msS4UserManagementService.getListS4Users(pageNumMin1, pageNumber, pageSize, searchFlag, searchData);
            logger.info("Response >>> getMsS4UserManagement size {}", response.getDetails());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsS4UserManagement", exception);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<UserIDModel>> getMsS4UserManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                 @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsS4UserManagementByNik nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsS4UserManagementByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsS4UserManagementByNik");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> getMsS4UserManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsS4UserManagementByNik");
            }
            ResponseModel<UserIDModel> response = this.msS4UserManagementService.getS4UserByNik(nik);
            logger.info("Response >>> getMsS4UserManagementByNik {}", gson.toJson(response.getDetails()));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsS4UserManagementByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsS4UserManagementByNik", exception);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsS4UserManagement(@RequestHeader("XToken") String authorization,
                                                                                     @RequestBody RequestModel<ReqUserIDModel> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsS4UserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> addOrEditMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("addOrEditMsS4UserManagement");
            }
            if (!isValidUserIDRequest(request.getDetails())) {
                logger.warn("Response >>> addOrEditMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsS4UserManagement");
            }
            if (request.getDetails().getType().equalsIgnoreCase(ADD)
                    && this.msS4UserManagementService.findByNik(request.getDetails().getNik()).isPresent()) {
                return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMsS4UserManagement", gson);
            }
            if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                ResponseModel<ResCUDUserIdModel> updatedS4User = this.msS4UserManagementService.updateS4User(request.getDetails());
                logger.info("Response >>> editMsS4UserManagement {} ", gson.toJson(updatedS4User));

                return ResponseEntity.ok(updatedS4User);
            }
            ResponseModel<ResCUDUserIdModel> savedS4User = this.msS4UserManagementService.saveS4User(request.getDetails());
            logger.info("Response >>> addMsS4UserManagement {} ", gson.toJson(savedS4User));

            return ResponseEntity.ok(savedS4User);
        } catch (Exception exception) {
            logger.warn("Response >>> addOrEditMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsS4UserManagement", exception);
        }
    }

    @DeleteMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsS4UserManagement(@RequestHeader("XToken") String authorization,
                                                                                @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsS4UserManagement nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> deleteMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("deleteMsS4UserManagement");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> deleteMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsS4UserManagement");
            }
            if (this.msS4UserManagementService.findByNik(nik).isEmpty()) {
                logger.warn("Response >>> deleteMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.NOT_FOUND));
                return responseFailed(TYPE_MS_S4_MANAGEMENT_DELETE, NOT_FOUND, "deleteMsS4UserManagement", gson);
            }

            ResponseModel<ResCUDUserIdModel> response = this.msS4UserManagementService.deleteS4User(nik);
            logger.info("Response >>> deleteMsS4UserManagement : {}", gson.toJson(response));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> deleteMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsS4UserManagement", exception);
        }
    }

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchMsS4UserManagement(@RequestHeader("XToken") String authorization,
                                                                                     @RequestBody ReqUserIdBatchModel<MsS4> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsS4UserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> saveBatchMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("saveBatchMsS4UserManagement");
            }
            if (this.isExistInInterval(nikRequester, request.getType())) {
                logger.warn("Response >>> saveBatchMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            if(!this.isValidRequest(request) || CommonHelper.isDuplicateData(request.getData().stream().map(MsS4::getNik).collect(Collectors.toList()))) {
                logger.warn("Response >>> saveBatchMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("saveBatchMsS4UserManagement");
            }
            ResponseModel<ResBatchUserId> savedBatchS4UserId = this.msS4UserManagementService.saveBatchMsS4User(request, nikRequester);

            logger.info("Response >>> saveBatchMsS4UserManagement {}", gson.toJson(savedBatchS4UserId));
            return ResponseEntity.ok(savedBatchS4UserId);

        } catch (Exception e){
            logger.warn("Response >>> saveBatchMsS4UserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsS4UserManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> genereteExcelMsS4UserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< genereteExcelMsS4UserId {} from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = msS4UserManagementService.genereteExcelMsS4UserId();
                logger.info("Response >>> genereteExcelMsS4UserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> genereteExcelMsS4UserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("genereteExcelMsS4UserId");
        } catch (Exception e) {
            logger.warn("Response >>> genereteExcelMsS4UserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("genereteExcelMsS4UserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsS4UserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsS4UserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = msS4UserManagementService.directDownloadExcelMsS4UserId();
                return responseDownloadFile("directDownloadExcelMsS4UserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsS4UserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsS4UserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsS4UserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsS4UserId", e);
        }
    }

    private boolean isValidRequest(ReqUserIdBatchModel<MsS4> request) {
        return request != null
                && request.getType() != null
                && request.getBatchId() != null
                && request.getFileName() != null
                && request.getTotalData() > 0
                && request.getData() != null
                && request.getData().size() > 0
                && this.trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    private boolean isExistInInterval(String nik, String type) {
        return this.trxUserIdBatchService.existInInterval(nik, type, 30);
    }
}
