package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.BiCACUserModel;
import com.btpns.fin.model.request.ReqBiCACUserModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.MsBiCACUserManagementService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.ALREADY_EXIST;
import static com.btpns.fin.helper.ResponseStatus.NOT_FOUND;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000009")
@CrossOrigin("*")
public class MsBiCACUserManagementController {
    private static final Logger logger = LoggerFactory.getLogger(MsBiCACUserManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private MsBiCACUserManagementService msBiCACUserManagementService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private TrxUserIdBatchService trxUserIdBatchService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsBiCACUserManagement(@RequestHeader("XToken") String authorization,
                                                                                     @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                     @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                     @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                     @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsBiCACUserManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            int pageNumMin1 = pageNumber - 1;
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsBiCACUserManagement");
            }
            ResponseModel<ResponseListModel> response = this.msBiCACUserManagementService.getListBiCACUsers(pageNumMin1, pageNumber, pageSize, searchFlag, searchData);
            logger.info("Response >>> getMsBiCACUserManagement size {}", response.getDetails());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsBiCACUserManagement", exception);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<BiCACUserModel>> getMsBiCACUserManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                   @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsBiCACUserManagementByNik nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getMsBiCACUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsBiCACUserManagementByNik");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> getMsBiCACUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsBiCACUserManagementByNik");
            }
            ResponseModel<BiCACUserModel> response = this.msBiCACUserManagementService.getBiCACUserByNik(nik);
            logger.info("Response >>> getMsBiCACUserManagementByNik {}", gson.toJson(response.getDetails()));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsBiCACUserManagementByNik : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsBiCACUserManagementByNik", exception);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsBiCACUserManagement(@RequestHeader("XToken") String authorization,
                                                                                       @RequestBody RequestModel<ReqBiCACUserModel> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsBiCACUserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> addOrEditMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("addOrEditMsBiCACUserManagement");
            }
            if (!isValidRequest(request)) {
                logger.warn("Response >>> addOrEditMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsBiCACUserManagement");
            }
            if (request.getDetails().getType().equalsIgnoreCase(ADD)
                    && this.msBiCACUserManagementService.findByIdUser(request.getDetails().getIdUser()).isPresent()) {
                return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMsBiCACUserManagement", gson);
            }
            if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                ResponseModel<ResCUDUserIdModel> updatedBiCACUser = this.msBiCACUserManagementService.updateBiCACUser(request.getDetails());
                logger.info("Response >>> editMsBiCACUserManagement {} ", gson.toJson(updatedBiCACUser));

                return ResponseEntity.ok(updatedBiCACUser);
            }
            ResponseModel<ResCUDUserIdModel> savedBiCACUser = this.msBiCACUserManagementService.saveBiCACUser(request.getDetails());
            logger.info("Response >>> addMsBiCACUserManagement {} ", gson.toJson(savedBiCACUser));

            return ResponseEntity.ok(savedBiCACUser);
        } catch (Exception exception) {
            logger.warn("Response >>> addOrEditMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsBiCACUserManagement", exception);
        }
    }

    @DeleteMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsBiCACUserManagement(@RequestHeader("XToken") String authorization,
                                                                                    @PathVariable("nik") String nik) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsBiCACUserManagement nik {} from {}",nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, this.trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> deleteMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("deleteMsBiCACUserManagement");
            }
            if (!StringUtils.isNotBlank(nik)) {
                logger.warn("Response >>> deleteMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsBiCACUserManagement");
            }
            if (this.msBiCACUserManagementService.findByNik(nik).isEmpty()) {
                logger.warn("Response >>> deleteMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.NOT_FOUND));
                return responseFailed(TYPE_MS_BI_CAC_MANAGEMENT_DELETE, NOT_FOUND, "deleteMsBiCACUserManagement", gson);
            }

            ResponseModel<ResCUDUserIdModel> response = this.msBiCACUserManagementService.deleteBiCACUser(nik);
            logger.info("Response >>> deleteMsBiCACUserManagement : {}", gson.toJson(response));

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> deleteMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsBiCACUserManagement", exception);
        }
    }

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchMsBiCACUserManagement(@RequestHeader("XToken") String authorization,
                                                                                      @RequestBody ReqUserIdBatchModel<ReqBiCACUserModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsBiCACUserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> saveBatchMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("saveBatchMsBiCACUserManagement");
            }
            if (this.isExistInInterval(nikRequester, request.getType())) {
                logger.warn("Response >>> saveBatchMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            if(!this.isValidRequest(request) || CommonHelper.isDuplicateData(request.getData().stream().map(ReqBiCACUserModel::getIdUser).collect(Collectors.toList()))) {
                logger.warn("Response >>> saveBatchMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("saveBatchMsBiCACUserManagement");
            }
            ResponseModel<ResBatchUserId> savedBatchBiCACUserId = this.msBiCACUserManagementService.saveBatchMsBiCACUser(request, nikRequester);

            logger.info("Response >>> saveBatchMsBiCACUserManagement {}", gson.toJson(savedBatchBiCACUserId));
            return ResponseEntity.ok(savedBatchBiCACUserId);

        } catch (Exception e){
            logger.warn("Response >>> saveBatchMsBiCACUserManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsBiCACUserManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateExcelMsBiCACUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< generateExcelMsBiCACUserId {} from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = msBiCACUserManagementService.generateExcelMsBiCACUserId();
                logger.info("Response >>> generateExcelMsBiCACUserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> generateExcelMsBiCACUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("generateExcelMsBiCACUserId");
        } catch (Exception e) {
            logger.warn("Response >>> generateExcelMsBiCACUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("generateExcelMsBiCACUserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsBiCACUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsBiCACUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = msBiCACUserManagementService.directDownloadExcelMsBiCACUserId();
                return responseDownloadFile("directDownloadExcelMsBiCACUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsBiCACUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsBiCACUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsBiCACUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsBiCACUserId", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqBiCACUserModel> requestModel) {
        ReqBiCACUserModel request = requestModel.getDetails();

        return request.getIdUser() != null
                && request.getIdUser().length() > 4
                && request.getNamaUser() != null
                && request.getEmail() != null
                && request.getInstitusi() != null
                && request.getUnitKerja() != null
                && request.getGrupUser() != null
                && request.getStatusUser() != null
                && request.getStatusLogin() != null;
    }

    private boolean isValidRequest(ReqUserIdBatchModel<ReqBiCACUserModel> request) {
        return request != null
                && request.getType() != null
                && request.getBatchId() != null
                && request.getFileName() != null
                && request.getTotalData() > 0
                && request.getData() != null
                && request.getData().size() > 0
                && this.trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    private boolean isExistInInterval(String nik, String type) {
        return this.trxUserIdBatchService.existInInterval(nik, type, 30);
    }
}
