package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.Constants;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.entity.TrxUpmRole;
import com.btpns.fin.model.request.*;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.TrxUPMUARService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.*;

@RestController
@RequestMapping(value = "/tema/upm/uar")
@CrossOrigin("*")
public class TrxUPMUARController {
    private static final Logger logger = LoggerFactory.getLogger(TrxUPMUARController.class);

    @Autowired
    private TrxUPMUARService trxUPMUARService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping
    public ResponseEntity<ResponseModel<ResBatchUARModel>> saveUARRequest(@RequestHeader("XToken") String authorization, @RequestBody RequestModel<UARRequestModel> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< saveUARRequest with data {} from {}", gson.toJson(request), getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> saveUARRequest : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("saveUARRequest");
            }
            if (!this.isValidRequest(request.getDetails())) {
                logger.warn("Response >>> saveUARRequest : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("saveUARRequest");
            }
            ResponseModel<ResBatchUARModel> response = trxUPMUARService.saveUARRequest(request, token.getProfile());

            if(response.getDetails().getTotalData() == 0) {
                logger.warn("Response >>> saveUARRequest : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("saveUARRequest");
            }

            logger.info("Response >>> saveUARRequest {} ", gson.toJson(response));
            return ResponseEntity.ok(response);

        } catch (Exception exception) {
            logger.warn("Response >>> saveUARRequest : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveUARRequest", exception);
        }
    }

    @GetMapping
    public ResponseEntity<ResponseModel<ResponseListModel>> getListUAR(@RequestHeader("XToken") String authorization,
                                                                       @RequestParam(value = "status", defaultValue = Constants.STATUS_PENDING_USER) String status,
                                                                       @RequestParam(value = "aplikasi", required = false, defaultValue = Constants.UPM_FILTER_TYPE_ALL) String aplikasi,
                                                                       @RequestParam(value = "periodYear", required = false, defaultValue = Constants.FILTER_CODE_ALL) Integer periodYear,
                                                                       @RequestParam(value = "periodQuarter", required = false, defaultValue = Constants.UPM_FILTER_TYPE_ALL) String periodQuarter,
                                                                       @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                       @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                       @RequestParam(value = "searchFlag", defaultValue = "ticketId") String searchFlag,
                                                                       @RequestParam(value = "searchData", required = false) String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getListUAR status {} aplikasi {} periodYear {} periodQuarter {} searchFlag {} searchData {} page {} limit {} from {}", status, aplikasi, periodYear, periodQuarter, searchFlag, searchData, pageNumber, pageSize, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getListUAR : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getListUAR");
            }
            ResponseModel<ResponseListModel> response = trxUPMUARService.getListUAR(status, aplikasi, periodYear, periodQuarter, searchFlag, searchData, pageNumber, pageSize);

            logger.info("Response >>> getListUAR {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getListUAR : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListUAR", exception);
        }
    }

    private boolean isValidRequest(UARRequestModel request) {
        boolean isValidQuarter = request.getPeriodQuarter() != null && Constants.PERIOD_QUARTERS.contains(request.getPeriodQuarter());

        return request.getAplikasi() != null &&
                request.getPeriodYear() != null &&
                request.getPeriodYear() >= LocalDate.now().getYear() - 1 &&
                isValidQuarter;
    }

    @PostMapping(value = "/process")
    public ResponseEntity<ResponseModel<ResUARModel>> updateApprovalUpmUAR(@RequestHeader("XToken") String authorization, @RequestBody ReqApprovalModel request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< updateApprovalUpmUAR with data {} from {}", gson.toJson(request), getProfile(gson, token));

            TrxUpmRole upmRole = trxUpmRoleService.getTrxUpmRole(token.getProfile().getPreferred_username());
            if (!CommonHelper.validateUPMTicket(upmRole.getNik(), upmRole)) {
                logger.warn("Response >>> updateApprovalUpmUAR : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("updateApprovalUpmUAR");
            }
            if (!trxUPMUARService.isValidRequestByRole(request.getTicketId(), request.getType(), upmRole.getRole())) {
                logger.warn("Response >>> updateApprovalUpmUAR : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("updateApprovalUpmUAR");
            }
            ResponseModel<ResUARModel> response = trxUPMUARService.updateApprovalUpmUAR(request, token.getProfile(), upmRole);

            logger.info("Response >>> updateApprovalUpmUAR {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> updateApprovalUpmUAR : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("updateApprovalUpmUAR", exception);
        }
    }

    @PostMapping(value = "/send-reminder")
    public ResponseEntity<ResponseModel<ResBatchProcess>> sendReminderUAR(@RequestHeader("XToken") String authorization,
                                                                          @RequestBody ReqTicketBatchModel request) {
        try {
            Token token = new Token(authorization);
            String profile = getProfile(gson, token);
            logger.info("Received <<< sendReminderUAR from {}", profile);

            if (trxUpmRoleService.getTrxUpmRole(token.getProfile().getPreferred_username()) == null) {
                logger.warn("Response >>> sendReminderUAR : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("sendReminderUAR");
            }
            if (!Constants.TYPE_SEND_REMINDER.equals(request.getType()) && request.getTicketIds().size() == 0) {
                logger.warn("Response >>> sendReminderUAR : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("sendReminderUAR");
            }
            ResponseModel<ResBatchProcess> response = trxUPMUARService.sendReminderUAR(request, token.getProfile());

            logger.info("Response >>> sendReminderUAR {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> sendReminderUAR : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("sendReminderUAR", exception);
        }
    }

    @GetMapping("/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getListUARAsCSV(@RequestHeader("XToken") String authorization,
                                                                         @RequestParam(value = "status", defaultValue = Constants.STATUS_PENDING_USER) String status,
                                                                         @RequestParam(value = "aplikasi", required = false, defaultValue = Constants.UPM_FILTER_TYPE_ALL) String aplikasi,
                                                                         @RequestParam(value = "periodYear", required = false, defaultValue = Constants.FILTER_CODE_ALL) Integer periodYear,
                                                                         @RequestParam(value = "periodQuarter", required = false, defaultValue = Constants.UPM_FILTER_TYPE_ALL) String periodQuarter,
                                                                         @RequestParam(value = "searchFlag", defaultValue = "ticketId") String searchFlag,
                                                                         @RequestParam(value = "searchData", required = false) String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getListUARAsCSV status {} aplikasi {} periodYear {} periodQuarter {} searchFlag {} searchData {} from {}", status, aplikasi, periodYear, periodQuarter, searchFlag, searchData, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> getListUARAsCSV : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getListUARAsCSV");
            }
            ResponseModel<ResUploadModel> response = trxUPMUARService.getListUARAsCSV(status, aplikasi, periodYear, periodQuarter, searchFlag, searchData);

            logger.info("Response >>> getListUARAsCSV {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getListUARAsCSV : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListUARAsCSV", exception);
        }
    }

    @GetMapping("/direct-download")
    public ResponseEntity<Resource> directDownloadUARAsCSV(@RequestHeader("XToken") String authorization,
                                                           @RequestParam(value = "status", defaultValue = Constants.STATUS_PENDING_USER) String status,
                                                           @RequestParam(value = "aplikasi", required = false, defaultValue = Constants.UPM_FILTER_TYPE_ALL) String aplikasi,
                                                           @RequestParam(value = "periodYear", required = false, defaultValue = Constants.FILTER_CODE_ALL) Integer periodYear,
                                                           @RequestParam(value = "periodQuarter", required = false, defaultValue = Constants.UPM_FILTER_TYPE_ALL) String periodQuarter,
                                                           @RequestParam(value = "searchFlag", defaultValue = "ticketId") String searchFlag,
                                                           @RequestParam(value = "searchData", required = false) String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< directDownloadUARAsCSV status {} aplikasi {} periodYear {} periodQuarter {} searchFlag {} searchData {} from {}", status, aplikasi, periodYear, periodQuarter, searchFlag, searchData, getProfile(gson, token));

            String nikRequester = token.getProfile().getPreferred_username();
            if (!CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> directDownloadUARAsCSV : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("directDownloadUARAsCSV");
            }
            ResFileDownload response = trxUPMUARService.directDownloadUARAsCSV(status, aplikasi, periodYear, periodQuarter, searchFlag, searchData);

            return responseDownloadFile("directDownloadUARAsCSV", response);
        } catch (Exception exception) {
            logger.warn("Response >>> directDownloadUARAsCSV : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadUARAsCSV", exception);
        }
    }
}
