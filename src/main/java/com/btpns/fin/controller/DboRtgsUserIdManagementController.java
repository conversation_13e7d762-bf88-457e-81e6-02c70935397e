package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsDboRTGS;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IDboRtgsUserIdRepository;
import com.btpns.fin.service.DboRtgsUserIdService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.btpns.fin.service.TrxUserIdBatchService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Controller
@RequestMapping(value = "tema/upm/user-id/AU00000001")
@CrossOrigin("*")
public class DboRtgsUserIdManagementController {
    private static final Logger logger = LoggerFactory.getLogger(DboRtgsUserIdManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxUserIdBatchService trxUserIdBatchService;

    @Autowired
    DboRtgsUserIdService dboRtgsUserIdService;

    @Autowired
    IDboRtgsUserIdRepository dboRtgsUserIdRepository;

    @PostMapping(value = "/batch")
    public ResponseEntity<ResponseModel<ResBatchUserId>> saveBatchMsDboRtgsUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                            @RequestBody ReqUserIdBatchModel<MsDboRTGS> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< saveBatchMsDboRtgsUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (!CommonHelper.isExistInInterval(trxUserIdBatchService.existInInterval(nikRequester, request.getType(), 30))) {
                    if(isValidRequest(request) && !CommonHelper.isDuplicateData(request.getData().stream().map(d -> d.getNik()).collect(Collectors.toList()))) {
                        ResponseModel<ResBatchUserId> saveBatchDboRtgsUserId = dboRtgsUserIdService.saveBatchMsDboRtgsUserId(request, nikRequester);

                        logger.info("Response >>> saveBatchMsDboRtgsUserIdManagement {}", gson.toJson(saveBatchDboRtgsUserId));
                        return ResponseEntity.ok(saveBatchDboRtgsUserId);
                    }
                    logger.warn("Response >>> saveBatchMsDboRtgsUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                    return responseBadRequest("saveBatchMsDboRtgsUserIdManagement");
                }
                logger.warn("Response >>> saveBatchMsDboRtgsUserIdManagement : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            logger.warn("Response >>> saveBatchMsDboRtgsUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("saveBatchMsDboRtgsUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> saveBatchMsDboRtgsUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("saveBatchMsDboRtgsUserIdManagement", e);
        }
    }

    private boolean isValidRequest(ReqUserIdBatchModel<MsDboRTGS> request) {
        return request != null
               && request.getType() != null
               && request.getBatchId() != null
               && request.getFileName() != null
               && request.getTotalData() > 0
               && request.getData() != null
               && request.getData().size() > 0
               && trxUserIdBatchService.getUserIdBatchByBatchId(request.getBatchId()) == null;
    }

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel>> getMsDboRTGSUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                         @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                         @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                         @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                         @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsDboRTGSUserIdManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel> response = dboRtgsUserIdService.getListMsDboRTGS(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);

                logger.info("Response >>> getMsDboRTGSUserIdManagement {}", response.getDetails());
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsDboRTGSUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> getMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsDboRTGSUserIdManagement", e);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<UserIDModel>> getMsDboRTGSUserIdManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                        @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getMsDboRTGSUserIdManagementByNik nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(StringUtils.isNotBlank(nik)) {
                    ResponseModel<UserIDModel> response = dboRtgsUserIdService.getMsDboRTGSByNik(nik);

                    logger.info("Response >>> getMsDboRTGSUserIdManagementByNik {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                logger.warn("Response >>> getMsDboRTGSUserIdManagementByNik : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("getMsDboRTGSUserIdManagementByNik");
            }
            logger.warn("Response >>> getMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getMsDboRTGSUserIdManagementByNik");

        } catch (Exception e){
            logger.warn("Response >>> getMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getMsDboRTGSUserIdManagementByNik", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> addOrEditMsDboRTGSUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                               @RequestBody RequestModel<ReqUserIDModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsDboRTGSUserIdManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(CommonHelper.isValidUserIDRequest(request.getDetails())) {
                    if (dboRtgsUserIdRepository.findByNikUser(request.getDetails().getNik()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResCUDUserIdModel> updateMsDboRTGS = dboRtgsUserIdService.updateMsDboRTGSUserId(request.getDetails());

                            logger.info("Response >>> editMsDboRTGSUserIdManagement {} ", gson.toJson(updateMsDboRTGS));
                            return ResponseEntity.ok(updateMsDboRTGS);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditAlihDayaUserManagement", gson);
                    }
                    ResponseModel<ResCUDUserIdModel> saveMsDboRTGS = dboRtgsUserIdService.saveMsDboRTGSUserId(request.getDetails());

                    logger.info("Response >>> addMsDboRTGSUserIdManagement {} ", gson.toJson(saveMsDboRTGS));
                    return ResponseEntity.ok(saveMsDboRTGS);
                }
                logger.warn("Response >>> addOrEditMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("addOrEditMsDboRTGSUserIdManagement");
            }
            logger.warn("Response >>> addOrEditMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("addOrEditMsDboRTGSUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> addOrEditMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("addOrEditMsDboRTGSUserIdManagement", e);
        }
    }

    @DeleteMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<ResCUDUserIdModel>> deleteMsDboRTGSUserIdManagement(@RequestHeader("XToken") String authorization,
                                                                                            @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsDboRTGSUserIdManagement nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if(StringUtils.isNotBlank(nik)) {
                    if (dboRtgsUserIdRepository.findByNikUser(nik) != null) {
                        ResponseModel<ResCUDUserIdModel> response = dboRtgsUserIdService.deleteMsDboRTGSUserId(nik);

                        logger.info("Response >>> deleteMsDboRTGSUserIdManagement : {}", gson.toJson(response));
                        return ResponseEntity.ok(response);
                    }
                    return responseFailed(TYPE_MS_DBO_RTGS_MANAGEMENT_DELETE, NOT_FOUND, "deleteBranchDataManagement", gson);
                }
                logger.warn("Response >>> deleteMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("deleteMsDboRTGSUserIdManagement");
            }
            logger.warn("Response >>> deleteMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("deleteMsDboRTGSUserIdManagement");
        } catch (Exception e){
            logger.warn("Response >>> deleteMsDboRTGSUserIdManagement : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("deleteMsDboRTGSUserIdManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> genereteExcelMsDboRTGSUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< genereteExcelMsDboRTGSUserId {} from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = dboRtgsUserIdService.genereteExcelMsDboRTGSUserId();
                logger.info("Response >>> genereteExcelMsDboRTGSUserId : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> genereteExcelMsDboRTGSUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("genereteExcelMsDboRTGSUserId");
        } catch (Exception e) {
            logger.warn("Response >>> genereteExcelMsDboRTGSUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("genereteExcelMsDboRTGSUserId", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadExcelMsDboRTGSUserId(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = profile.getProfile().getPreferred_username();
            logger.info("Received <<< directDownloadExcelMsDboRTGSUserId from {}", getProfile(gson, profile));

            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = dboRtgsUserIdService.directDownloadExcelMsDboRTGSUserId();
                return responseDownloadFile("directDownloadExcelMsDboRTGSUserId", response);
            }
            logger.warn("Response >>> directDownloadExcelMsDboRTGSUserId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadExcelMsDboRTGSUserId");
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadExcelMsDboRTGSUserId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("directDownloadExcelMsDboRTGSUserId", e);
        }
    }
}
