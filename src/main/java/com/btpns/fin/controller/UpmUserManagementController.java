package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.request.ReqUpmUserMgntModel;
import com.btpns.fin.model.response.ResTrxUpmUserListModel;
import com.btpns.fin.model.response.ResUpmUserMgntModel;
import com.btpns.fin.model.entity.TrxUpmRole;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "/tema/upm/user/management")
@CrossOrigin("*")
public class UpmUserManagementController {
    private static final Logger logger = LoggerFactory.getLogger(UpmUserManagementController.class);

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping("/{nik}")
    public ResponseEntity<ResponseModel<TrxUpmRole>> getUpmUserManagementByNik(@RequestHeader("XToken") String authorization, @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getUpmUserManagementByNik nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isEmpty(nik)) {
                return responseBadRequest("getUpmUserManagementByNik");
            }
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                TrxUpmRole upmRole = trxUpmRoleService.getTrxUpmRole(nik);
                if (upmRole == null) {
                    TrxUpmRole ret = new TrxUpmRole();
                    ret.setNik(nik);
                    logger.info("Response >>> getUpmUserManagementByNik : {}", gson.toJson(ret));
                    return ResponseEntity.ok(buildResponse(SUCCESS, ret));
                }
                logger.info("Response >>> getUpmUserManagementByNik : {}", gson.toJson(upmRole));
                return ResponseEntity.ok(buildResponse(SUCCESS, upmRole));
            } else {
                return responseHttpSuccessWithStatus(TYPE_UPM_MANAGEMENT_USER_GET, FORBIDDEN, "getUpmUserManagementByNik", gson);
            }
        } catch (Exception e){
            return responseInternalServerError("getUpmUserManagementByNik", e);
        }
    }

    @GetMapping()
    public ResponseEntity<ResponseModel<ResTrxUpmUserListModel>> getUpmUserManagement(@RequestHeader("XToken") String authorization,
                                                                       @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                       @RequestParam(value = "limit", defaultValue = "50") Integer pageSize) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getUpmUserManagement from {}", getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                Page<TrxUpmRole> pageTU = trxUpmRoleService.getListTrxUpmRole(pageNumMin1, pageSize);
                List<TrxUpmRole> upmRole = pageTU.getContent();
                ResTrxUpmUserListModel userList = new ResTrxUpmUserListModel();
                userList.setUpmUser(upmRole);
                userList.setLimit(pageSize);
                userList.setPage(pageNumber);
                userList.setTotalPages(pageTU.getTotalPages());
                userList.setTotalItems(pageTU.getTotalElements());

                ResponseModel<ResTrxUpmUserListModel> response = buildResponse(SUCCESS, userList);
                logger.info("Response >>> getUpmUserManagement size {}", upmRole.size());
                return ResponseEntity.ok(response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_UPM_MANAGEMENT_USER_GET_LIST, FORBIDDEN, "getUpmUserManagement", gson);
            }
        } catch (Exception e){
            return responseInternalServerError("getUpmUserManagement", e);
        }
    }

    @DeleteMapping("/{nik}")
    public ResponseEntity<ResponseModel<ResUpmUserMgntModel>> deleteUpmUserManagement(@RequestHeader("XToken") String authorization, @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteUpmUserManagement nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isEmpty(nik)) {
                return responseBadRequest("deleteUpmUserManagement");
            }
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (trxUpmRoleService.getTrxUpmRole(nik) == null) {
                    return responseFailed(TYPE_UPM_MANAGEMENT_USER_DELETE, NOT_FOUND, "deleteUpmUserManagement", gson);
                }
                ResUpmUserMgntModel response = buildResponse(nik, DELETE);
                ResponseStatus status = FAILED;

                if(trxUpmRoleService.deleteTrxUpmRole(nik) > 0) {
                    status = SUCCESS;
                }

                logger.info("Response >>> deleteUpmUserManagement : {}", gson.toJson(response));
                return ResponseEntity.ok(buildResponse(TYPE_UPM_MANAGEMENT_USER_DELETE, status, response));
            } else {
                return responseHttpSuccessWithStatus(TYPE_UPM_MANAGEMENT_USER_DELETE, FORBIDDEN, "deleteUpmUserManagement", gson);
            }
        } catch (Exception e){
            return responseInternalServerError("deleteUpmUserManagement", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResUpmUserMgntModel>> addOrEditUpmUserManagement(@RequestHeader("XToken") String authorization, @RequestBody RequestModel<ReqUpmUserMgntModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditUpmUserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(!isValidRequest(request)) {
                return responseBadRequest("addOrEditUpmUserManagement");
            }
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (trxUpmRoleService.getTrxUpmRole(request.getDetails().getNik()) != null
                        && request.getDetails().getType().equalsIgnoreCase(ADD)) {
                    return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditUpmUserManagement", gson);
                }
                TrxUpmRole trxUpmRole = buildUpmRole(request);
                trxUpmRoleService.saveTrxUpmRole(trxUpmRole);
                ResUpmUserMgntModel userMgntModel = buildResponse(request.getDetails().getNik(), request.getDetails().getType());
                ResponseModel<ResUpmUserMgntModel> response = buildResponse(TYPE_UPM_MANAGEMENT_USER_ADD_EDIT, SUCCESS, userMgntModel);

                logger.info("Response >>> addOrEditUpmUserManagement : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_UPM_MANAGEMENT_USER_ADD_EDIT, FORBIDDEN, "addOrEditUpmUserManagement", gson);
            }
        } catch (Exception e){
            return responseInternalServerError("addOrEditUpmUserManagement", e);
        }
    }

    private TrxUpmRole buildUpmRole(RequestModel<ReqUpmUserMgntModel> request) {
        LocalDateTime dateNow = LocalDateTime.now();
        TrxUpmRole trxUpmRole = new TrxUpmRole();
        trxUpmRole.setNik(request.getDetails().getNik());
        trxUpmRole.setNama(request.getDetails().getNama());
        trxUpmRole.setRole(request.getDetails().getRole());
        trxUpmRole.setCreateDateTime(dateNow);

        return trxUpmRole;
    }

    private ResUpmUserMgntModel buildResponse(String nik, String type) {
        ResUpmUserMgntModel response = new ResUpmUserMgntModel();
        response.setNik(nik);
        response.setType(type);

        return response;
    }

    private ResponseModel<TrxUpmRole> buildResponse(ResponseStatus status, TrxUpmRole upmRole) {
        ResponseModel<TrxUpmRole> response = new ResponseModel<>();

        response.setType(TYPE_UPM_MANAGEMENT_USER_GET);
        response.setDetails(upmRole);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        return response;
    }

    private ResponseModel<ResTrxUpmUserListModel> buildResponse(ResponseStatus status, ResTrxUpmUserListModel resTrxUpmUserListModel) {
        ResponseModel<ResTrxUpmUserListModel> response = new ResponseModel<>();

        response.setType(TYPE_UPM_MANAGEMENT_USER_GET_LIST);
        response.setDetails(resTrxUpmUserListModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        return response;
    }

    private ResponseModel<ResUpmUserMgntModel> buildResponse(String type, ResponseStatus status, ResUpmUserMgntModel resUpmUserMgntModel) {
        ResponseModel<ResUpmUserMgntModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resUpmUserMgntModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        return response;
    }

    private boolean isValidRequest(RequestModel<ReqUpmUserMgntModel> request) {
        if(request.getDetails().getNik() != null){
            if(request.getDetails().getNik().trim().equals("")){
                return false;
            }
        }
        if(request.getDetails().getNama() != null){
            if(request.getDetails().getNama().trim().equals("")){
                return false;
            }
        }
        if(request.getDetails().getRole() != null){
            if(request.getDetails().getRole().trim().equals("")){
                return  false;
            }
        }
        return request != null
                && request.getType() != null
                && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
                && request.getDetails().getNik() != null
                && request.getDetails().getNama() != null
                && request.getDetails().getRole() != null
                && (request.getDetails().getRole().equalsIgnoreCase(UPM_ROLE_MAKER)
                   || request.getDetails().getRole().equalsIgnoreCase(UPM_ROLE_CHECKER)
                   || request.getDetails().getRole().equalsIgnoreCase(UPM_ROLE_ADMIN)
                   || request.getDetails().getRole().equalsIgnoreCase(UPM_ROLE_VIEWER)
                   || request.getDetails().getRole().equalsIgnoreCase(UPM_ROLE_INQUIRY));
    }
}
