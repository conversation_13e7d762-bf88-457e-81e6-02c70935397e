package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.AnalyticCountModel;
import com.btpns.fin.model.ListAnalyticCountModel;
import com.btpns.fin.model.response.ResAnalyticVolumeTrxUser;
import com.btpns.fin.model.entity.AnalyticCount;
import com.btpns.fin.model.entity.ApplicationMonthly;
import com.btpns.fin.model.response.ResAnalyticAppMonthly;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.AnalyticApplicationService;
import com.btpns.fin.service.AnalyticCountService;
import com.btpns.fin.service.AnalyticThreeMonthsService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.FORBIDDEN;

@RestController
@RequestMapping(value = "/tema/upm")
@CrossOrigin("*")
public class AnalyticCountController {
    private static final Logger logger = LoggerFactory.getLogger(AnalyticCountController.class);

    @Autowired
    AnalyticCountService analyticCountService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    AnalyticApplicationService analyticApplicationService;

    @Autowired
    AnalyticThreeMonthsService analyticThreeMonthsService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "/analytic/application-monthly")
    public ResponseEntity<ResponseModel<ResAnalyticAppMonthly>> getAnalyticAppMonthly(@RequestHeader("XToken") String authorization,
                                                                                      @RequestParam(value = "period", defaultValue = "") String period) {
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getAnalyticAppMonthly period {} from {}", period, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            String startEffectiveDTPeriod = "", endEffectiveDTPeriod = "";
            if (period.equalsIgnoreCase(EMPTY)) {
                period = DateTimeHelper.getDatePeriodMonth(new Date());
            } else if (period.length() != 7 && period.split("-").length != 2) {
                return responseBadRequest("getAnalyticAppMonthly");
            } else {
                startEffectiveDTPeriod = getStartAndEndPeriod(period, period).getKey().split(" ") [0];
                endEffectiveDTPeriod = getStartAndEndPeriod(period, period).getValue().split(" ") [0];
            }

            if(CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                List<ApplicationMonthly> topApplicationMonthly = analyticApplicationService.getTopApplicationMonthly(startEffectiveDTPeriod, endEffectiveDTPeriod);
                ResponseModel<ResAnalyticAppMonthly> response = buildResponse(ResponseStatus.SUCCESS, topApplicationMonthly);
                logger.info("Response >>> getAnalyticAppMonthly size {}", gson.toJson(response.getDetails().getData().size()));
                return ResponseEntity.ok(response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_ANALYTIC_APPLICATION_MONTHLY, FORBIDDEN, "getAnalyticAppMonthly", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("getAnalyticAppMonthly", e);
        }
    }

    private Map.Entry<String, String> getStartAndEndPeriod(String startDate, String endDate) {
        startDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getBeginningofMonth(startDate));
        endDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getEndofMonth(endDate));

        return Map.entry(startDate, endDate);
    }

    @GetMapping(value = "/analytic/{analytictype}")
    public ResponseEntity<ResponseModel<ListAnalyticCountModel>> getCountAnalyticMaker(@RequestHeader("XToken") String authorization,
                                                                        @PathVariable("analytictype") String analytictype,
                                                                        @RequestParam(value = "period", defaultValue = "daily") String period) {
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getCountAnalyticMaker analytictype{} from {}", analytictype, getProfile(gson, profile));

            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            boolean passValidationToken = false;
            passValidationToken = CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidationToken) {
                Map.Entry<String, String> mapPeriod = CommonHelper.getPeriod(period);
                String strStartDateTime = mapPeriod.getKey();
                String strEndDateTime = mapPeriod.getValue();
                logger.info("strStartDateTime: " + strStartDateTime);
                logger.info("strEndDateTime: " + strEndDateTime);

                ListAnalyticCountModel lACM = new ListAnalyticCountModel();
                List<AnalyticCountModel> listACM = new ArrayList<AnalyticCountModel>();
                if (analytictype.equals(Constants.UPM_ROLE_MAKER_LOWERCASE)) {
                    List<AnalyticCount> listAC = analyticCountService.getCountAnalyticMaker(strStartDateTime, strEndDateTime, Constants.UPM_ROLE_MAKER);
                    for (int i = 0; i < listAC.size(); i++) {
                        AnalyticCount ac = listAC.get(i);
                        AnalyticCountModel acm = Mapper.toAnalyticCountModel(ac);
                        listACM.add(acm);
                    }
                } else if (analytictype.equals(Constants.UPM_ROLE_CHECKER_LOWERCASE)) {
                    List<AnalyticCount> listAC = analyticCountService.getCountAnalyticMaker(strStartDateTime, strEndDateTime, Constants.UPM_ROLE_CHECKER);
                    for (int i = 0; i < listAC.size(); i++) {
                        AnalyticCount ac = listAC.get(i);
                        AnalyticCountModel acm = Mapper.toAnalyticCountModel(ac);
                        listACM.add(acm);
                    }
                }

                lACM.setAnalyticCountModels(listACM);
                ResponseModel<ListAnalyticCountModel> response = buildResponse(ResponseStatus.SUCCESS, lACM);
                logger.info("Response >>> getCountAnalyticMaker : {}", gson.toJson(lACM.getAnalyticCountModels().size()));
                return ResponseEntity.ok(response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_ANALYTIC_COUNT_UPM, FORBIDDEN, "getCountAnalyticMaker", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("getCountAnalyticMaker", e);
        }
    }

    private ResponseModel<ResAnalyticAppMonthly> buildResponse(ResponseStatus status, List<ApplicationMonthly> applicationMonthlies) {
        ResponseModel<ResAnalyticAppMonthly> response = new ResponseModel<>();
        ResAnalyticAppMonthly analyticAppMonthly = new ResAnalyticAppMonthly();
        analyticAppMonthly.setData(applicationMonthlies);

        response.setType(TYPE_ANALYTIC_APPLICATION_MONTHLY);
        response.setDetails(analyticAppMonthly);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        return response;
    }

    private ResponseModel<ListAnalyticCountModel> buildResponse(ResponseStatus status, ListAnalyticCountModel listAnalyticCountModel) {
        ResponseModel<ListAnalyticCountModel> response = new ResponseModel<>();

        response.setType(TYPE_ANALYTIC_COUNT_UPM);
        response.setDetails(listAnalyticCountModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        return response;
    }

    @GetMapping(value = "/analytic/volume-trx-user")
    public ResponseEntity<ResponseModel<ResAnalyticVolumeTrxUser>> getAnalyticTrxVolumeUsers(@RequestHeader("XToken") String authorization,
                                                                                             @RequestParam(value = "period1", defaultValue = "") String period1,
                                                                                             @RequestParam(value = "period2", defaultValue = "") String period2,
                                                                                             @RequestParam(value = "period3", defaultValue = "") String period3) {
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getCountAnalyticVolumeTrxUsers period1 : {}, periode2 : {}, periode3 : {}, from : {}", period1, period2, period3, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if(CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                if (isValidPeriodParamFormat(period1, period2, period3)){
                    ResponseModel<ResAnalyticVolumeTrxUser> response = analyticThreeMonthsService.getCountAnalyticVolumeTrxUsers(period1, period2, period3);
                    logger.info("Response >>> getCountAnalyticVolumeTrxUsers {}", gson.toJson(response.getDetails().getData()));

                    return ResponseEntity.ok(response);
                }
                return responseBadRequest("getCountAnalyticVolumeTrxUsers");
            } else {
                return responseHttpSuccessWithStatus(TYPE_ANALYTIC_TRX_VOLUME_USER, FORBIDDEN, "getCountAnalyticVolumeTrxUsers", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("getCountAnalyticVolumeTrxUsers", e);
        }
    }

    private boolean isValidPeriodParamFormat(String period1, String period2, String period3) {
        return !period1.isEmpty() && !period2.isEmpty() && !period3.isEmpty() &&
               period1.length() == 7 && period1.split("-").length == 2 &&
               period2.length() == 7 && period2.split("-").length == 2 &&
               period3.length() == 7 && period3.split("-").length == 2;
    }
}
