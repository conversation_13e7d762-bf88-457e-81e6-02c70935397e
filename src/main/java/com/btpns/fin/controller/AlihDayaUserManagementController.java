package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.model.AlihDayaUserModel;
import com.btpns.fin.model.request.ReqMsDataAlihDayaModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.service.ReportUserAlihDayaService;
import com.btpns.fin.service.TrxPUKVendorBatchService;
import com.btpns.fin.service.TrxPUKVendorService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


import java.util.List;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Controller
@RequestMapping(value = "tema/upm/alih-daya/user/management")
@CrossOrigin("*")
public class AlihDayaUserManagementController {
    private static final Logger logger = LoggerFactory.getLogger(AlihDayaUserManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxPUKVendorService trxPUKVendorService;

    @Autowired
    ReportUserAlihDayaService reportUserAlihDayaService;

    @Autowired
    TrxPUKVendorBatchService trxPUKVendorBatchService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel<AlihDayaUserModel>>> getAlihDayaUserManagement(@RequestHeader("XToken") String authorization,
                                                                                             @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                             @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                             @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                             @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getAlihDayaUserManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel<AlihDayaUserModel>> response = trxPUKVendorService.getListTrxPUKVendor(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);
                logger.info("Response >>> getAlihDayaUserManagement size {}", response.getDetails().getData().size());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getAlihDayaUserManagement");
        } catch (Exception e){
            return responseInternalServerError("getAlihDayaUserManagementByNikAndName", e);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<AlihDayaUserModel>> getAlihDayaUserManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                           @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getAlihDayaUserManagementByNik nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(nik)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    ResponseModel<AlihDayaUserModel> response = trxPUKVendorService.getTrxPUKVendorByNik(nik);
                    logger.info("Response >>> getAlihDayaUserManagementByNik {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                return responseForbidden("getAlihDayaUserManagementByNik");
            }
            return responseBadRequest("getAlihDayaUserManagementByNik");
        } catch (Exception e){
            return responseInternalServerError("getAlihDayaUserManagementByNik", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResAlihDayaUserModel>> addOrEditAlihDayaUserManagement(@RequestHeader("XToken") String authorization,
                                                                                               @RequestBody RequestModel<ReqMsDataAlihDayaModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditAlihDayaUserManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(isValidRequest(request)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (trxPUKVendorService.findByNikVendor(request.getDetails().getNik()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResAlihDayaUserModel> updateTrxPUKVendor = trxPUKVendorService.updatePUKVendor(request.getDetails());
                            logger.info("Response >>> editAlihDayaUserManagement {} ", gson.toJson(updateTrxPUKVendor));

                            return ResponseEntity.ok(updateTrxPUKVendor);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditAlihDayaUserManagement", gson);
                    }

                    ResponseModel<ResAlihDayaUserModel> saveTrxPUKVendor = trxPUKVendorService.savePUKVendor(request.getDetails());
                    logger.info("Response >>> addAlihDayaUserManagement {} ", gson.toJson(saveTrxPUKVendor));

                    return ResponseEntity.ok(saveTrxPUKVendor);
                }
                return responseForbidden("addOrEditAlihDayaUserManagement");
            }
            return responseBadRequest("addOrEditAlihDayaUserManagement");
        } catch (Exception e){
            return responseInternalServerError("addOrEditAlihDayaUserManagement", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqMsDataAlihDayaModel> request) {
        return request != null
               && request.getType() != null
               && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
               && request.getDetails().getNik() != null
               && request.getDetails().getNama() != null
               && request.getDetails().getNikPUK() != null
               && request.getDetails().getJabatanVendor() != null
               && request.getDetails().getMasaBerlakuSampai() != null;
    }

    @DeleteMapping()
    public ResponseEntity<ResponseModel<ResBatchProcess>> deleteAlihDayaUserManagement(@RequestHeader("XToken") String authorization,
                                                                                       @RequestBody RequestModel<List<String>> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteAlihDayaUserManagement nik {} from {}", request.getDetails(), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResBatchProcess> response = trxPUKVendorService.deleteTrxPUKVendor(request.getDetails());
                logger.info("Response >>> deleteAlihDayaUserManagement : {}", gson.toJson(response));

                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> deleteAlihDayaUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("deleteAlihDayaUserManagement");
        } catch (Exception e){
            logger.warn("Response >>> deleteAlihDayaUserManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseInternalServerError("deleteAlihDayaUserManagement", e);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getUserAlihDayaCSV(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getUserAlihDayaCSV from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            //validation access based on token
            if(CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = reportUserAlihDayaService.generateUserAlihDayaCsv();

                logger.info("Response >>> getUserAlihDayaCSV : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getUserAlihDayaCSV");
        } catch (Exception e) {
            return responseInternalServerError("getUserAlihDayaCSV", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadAlihDayaCSV(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadAlihDayaCSV from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if(CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = reportUserAlihDayaService.directDownloadAlihDayaCSV();
                return responseDownloadFile("directDownloadAlihDayaCSV", response);
            }
            return responseForbidden("directDownloadAlihDayaCSV");
        } catch (Exception e) {
            return responseInternalServerError("directDownloadAlihDayaCSV", e);
        }
    }

    @PostMapping(value = "/bulk")
    public ResponseEntity<ResponseModel<ResBatchUserId>> bulkAddEditAlihDayaUser(@RequestHeader("XToken") String authorization,
                                                                                           @RequestBody ReqUserIdBatchModel<AlihDayaUserModel> request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< bulkAddEditAlihDayaUser {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (!validateUPMTicket(nikRequester, trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                logger.warn("Response >>> bulkAddEditAlihDayaUser : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("bulkAddEditAlihDayaUser");
            }
            if (this.isExistInInterval(nikRequester)) {
                logger.warn("Response >>> bulkAddEditAlihDayaUser : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            if(!this.isValidRequest(request)) {
                logger.warn("Response >>> bulkAddEditAlihDayaUser : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("bulkAddEditAlihDayaUser");
            }
            ResponseModel<ResBatchUserId> response = trxPUKVendorService.updateDataALihDayaInBulk(request, token.getProfile());

            logger.info("Response >>> bulkAddEditAlihDayaUser {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> bulkAddEditAlihDayaUser : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("bulkAddEditAlihDayaUser", exception);
        }
    }

    private boolean isExistInInterval(String nikRequester) {
        return trxPUKVendorBatchService.isExistInInterval(nikRequester, 30);
    }

    private boolean isValidRequest(ReqUserIdBatchModel<AlihDayaUserModel> request) {
        boolean hasDuplicateNIK = isDuplicateData(request.getData().stream().map(AlihDayaUserModel::getNik).collect(Collectors.toList()));

        return !hasDuplicateNIK &&
                request.getData() != null &&
                !request.getData().isEmpty() &&
                request.getTotalData() > 0 &&
                request.getBatchId() != null &&
                request.getFileName() != null &&
                trxPUKVendorBatchService.getTrxPUKVendorBatchByBatchId(request.getBatchId()) == null;
    }
}
