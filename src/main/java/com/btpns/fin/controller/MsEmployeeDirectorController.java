package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.MsEmployeeDirectorModel;
import com.btpns.fin.model.request.ReqMsEmployeeDirectorModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.ResMsEmployeeDirectorModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import com.btpns.fin.service.MsEmployeeDirectorService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Controller
@RequestMapping(value = "/tema/upm/director/management")
@CrossOrigin("*")
public class MsEmployeeDirectorController {
    private static final Logger logger = LoggerFactory.getLogger(MsEmployeeDirectorController.class);

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "/{nikOptima:.+}")
    public ResponseEntity<ResponseModel<MsEmployeeDirectorModel>> getMsEmployeeDirectorByNIKOptima(@RequestHeader("XToken") String authorization,
                                                                                                   @PathVariable("nikOptima") String nikOptima){
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsEmployeeDirectorByNIKOptima nikOptima {} from {}", nikOptima, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(nikOptima)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    ResponseModel<MsEmployeeDirectorModel> response = msEmployeeDirectorService.getMsEmployeeDirectorByNIKOptima(nikOptima);
                    logger.info("Response >>> getMsEmployeeDirectorByNIKOptima {} ", gson.toJson(response));

                    return ResponseEntity.ok(response);
                }
                return responseForbidden("getMsEmployeeDirectorByNIKOptima");
            }
            return responseBadRequest("getMsEmployeeDirectorByNIKOptima");
        } catch (Exception e){
            return responseInternalServerError("getMsEmployeeDirectorByNIKOptima", e);
        }
    }

    @GetMapping
    ResponseEntity<ResponseModel<ResponseListModel<MsEmployeeDirectorModel>>> getMsEmployeeDirectorList(@RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                               @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                               @RequestHeader("XToken") String authorization){
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsEmployeeDirectorList page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel<MsEmployeeDirectorModel>> response = msEmployeeDirectorService.getMsEmployeeDirectorList(pageNumber, pageSize);
                logger.info("Response >>> getMsEmployeeDirectorList size {}", response.getDetails().getData().size());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getMsEmployeeDirectorList");
        } catch (Exception e) {
            return responseInternalServerError("getMsEmployeeDirectorList", e);
        }
    }

    @PostMapping
    public ResponseEntity<ResponseModel<ResMsEmployeeDirectorModel>> addOrEditMsEmployeeDirector(@RequestHeader("XToken") String authorization,
                                                                                                 @RequestBody RequestModel<ReqMsEmployeeDirectorModel> request){
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditMsEmployeeDirector {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(isValidRequest(request)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester)) &&
                    trxUpmRoleService.getTrxUpmRole(nikRequester).getRole().equals(UPM_ROLE_ADMIN)) {

                    ResponseModel<ResMsEmployeeDirectorModel> response = new ResponseModel<>();
                    if (iMsEmployeeDirectorRepository.getMsEmployeeDirectorByNIKOptima(request.getDetails().getNikOptima()) != null) {
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())) {
                            response = msEmployeeDirectorService.updateMsEmployeeDirector(request.getDetails());
                            logger.info("Response >>> editMsEmployeeDirector {} ", gson.toJson(response));

                            return ResponseEntity.ok(response);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMsEmployeeDirector", gson);
                    }

                    response = msEmployeeDirectorService.saveMsEmployeeDirector(request.getDetails());
                    logger.info("Response >>> addMsEmployeeDirector {} ", gson.toJson(response));

                    return ResponseEntity.ok(response);
                }
                return responseForbidden("addOrEditMsEmployeeDirector");
            }
            return responseBadRequest("addOrEditMsEmployeeDirector");
        } catch (Exception e){
            return responseInternalServerError("addOrEditMsEmployeeDirector", e);
        }
    }

    @DeleteMapping(value = "/{nikOptima:.+}")
    public ResponseEntity<ResponseModel<ResMsEmployeeDirectorModel>> deleteMsEmployeeDirector(@RequestHeader("XToken") String authorization,
                                                                                              @PathVariable("nikOptima") String nikOptima) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< deleteMsEmployeeDirector nikOptima {} from {}", nikOptima, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(nikOptima)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester)) &&
                    trxUpmRoleService.getTrxUpmRole(nikRequester).getRole().equals(UPM_ROLE_ADMIN)) {
                    if (iMsEmployeeDirectorRepository.getMsEmployeeDirectorByNIKOptima(nikOptima) != null) {
                        ResponseModel<ResMsEmployeeDirectorModel> response = msEmployeeDirectorService.deleteMsEmployeeDirectorByNIKOptima(nikOptima);
                        logger.info("Response >>> deleteMsEmployeeDirector : {}", gson.toJson(response));

                        return ResponseEntity.ok(response);
                    }
                    return responseFailed(TYPE_DIRECTOR_DELETE, NOT_FOUND, "deleteMsEmployeeDirector", gson);
                }
                return responseForbidden("deleteMsEmployeeDirector");
            }
            return responseBadRequest("deleteMsEmployeeDirector");
        } catch (Exception e){
            return responseInternalServerError("deleteMsEmployeeDirector", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqMsEmployeeDirectorModel> request) {
        return request != null
                && request.getType() != null
                && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
                && request.getDetails().getNikOptima() != null
                && request.getDetails().getNikLdap() != null
                && request.getDetails().getNamaLengkap() != null
                && request.getDetails().getEmail() != null;
    }
}
