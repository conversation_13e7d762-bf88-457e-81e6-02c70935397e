package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.ApprovalModel;
import com.btpns.fin.model.TrxFuidRequestDetailModel;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.TrxFuidRequestDTO;
import com.btpns.fin.model.response.ResTrxFuidRequestModel;
import com.btpns.fin.model.TimelineStatusModel;
import com.btpns.fin.model.ValidationPUKApprovalModel;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "/tema/fuid/approval")
@CrossOrigin("*")
public class TrxFuidApprovalController {
    private static final Logger logger = LoggerFactory.getLogger(TrxFuidApprovalController.class);

    @Autowired
    TrxFuidApprovalService trxFuidApprovalService;

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    EmailNotificationService emailNotificationService;

    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    @Autowired
    Mapper mapper;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping()
    public ResponseEntity<ResTrxFuidRequestModel> updateFuidApproval(@RequestBody ApprovalModel approvalModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< updateFuidApproval with data {} from {}", gson.toJson(approvalModel), getProfile(gson, profile));
            String nikPUKSSO = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            boolean isPuk1 = false; boolean isPuk2 = false;

            DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
            TrxFuidApproval tfra = trxFuidApprovalService.getTrxFuidApprovalByTicketId(approvalModel.getTicketId());
            LocalDateTime currStateDt = LocalDateTime.now();
            String sCurrStateDt = DateTimeHelper.getFullDate(currStateDt);
            tfra.setCurrentStateDT(DateTimeHelper.getFullDateAsDate(sCurrStateDt));
            //validation puk approval
            ValidationPUKApprovalModel vpam = validationPUKApproval(tfra, nikPUKSSO, approvalModel, sCurrStateDt);
            tfra = vpam.getTfra();
            isPuk1 = vpam.isPuk1();
            isPuk2 = vpam.isPuk2();

            ResTrxFuidRequestModel resTrxFuidRequestModel = new ResTrxFuidRequestModel();
            resTrxFuidRequestModel.setType(approvalModel.getStatus());
            resTrxFuidRequestModel.setTicketId(approvalModel.getTicketId());
            if(isPuk1 || isPuk2) {
                if (tfra.getPuk2NIK() != null && tfra.getCurrentState().equals(CURR_STATUS_WAITING_PUK1) && approvalModel.getStatus().equals(CURR_STATUS_APPROVED)) {
                    tfra.setCurrentState(CURR_STATUS_WAITING_PUK2);
                } else {
                    tfra.setCurrentState(approvalModel.getStatus());
                }
                TrxFuidApproval savedTFRA = trxFuidApprovalService.updateTrxFuidApproval(tfra);

                TrxAudittrail ta = new TrxAudittrail();
                String pukNik = tfra.getPuk1NIK();
                if (isPuk2) {
                    pukNik = tfra.getPuk2NIK();
                }
                ta.setNik(pukNik);
                ta.setTicketId(approvalModel.getTicketId());
                if (tfra.getPuk2NIK() != null && tfra.getCurrentState().equals(CURR_STATUS_WAITING_PUK2) && approvalModel.getStatus().equals(CURR_STATUS_APPROVED)) {
                    ta.setAction(CURR_STATUS_WAITING_PUK2);
                } else {
                    ta.setAction(approvalModel.getStatus());
                }
                ta.setCreateDateTime(DateTimeHelper.getFullDateAsDate(sCurrStateDt));
                //json additional info
                TimelineStatusModel tsm = new TimelineStatusModel();
                if (approvalModel.getStatus().equals(CURR_STATUS_REJECTED)) {
                    tsm.setStatus(TIMELINE_STATUS_REJECT_TICKET);
                    tsm.setNote(approvalModel.getNotes());
                } else {
                    tsm.setStatus(TIMELINE_STATUS_APPROVE_TICKET);
                }
                String pukName = msEmployeeService.getMsEmployeeByNik(pukNik).getFullName();
                //check delegation
                String delegationId = savedTFRA.getPuk1DelegationId();
                if (isPuk2) {
                    delegationId = savedTFRA.getPuk2DelegationId();
                }
                if (trxDelegationService.getTrxDelegationByDelegationId(delegationId) != null) {
                    tsm.setPic(TIMELINE_PIC_PUK_DELEGATION + pukNik + " - " + pukName);
                } else {
                    tsm.setPic(TIMELINE_PIC_PUK + pukNik + " - " + pukName);
                }
                tsm.setTimestamp(dateFormater3.format(ta.getCreateDateTime()));
                ta.setAdditionalInfo(new Gson().toJson(tsm));
                TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(ta);

                //send email approved or rejected to user
                TrxFuidRequestDTO savedTFR = trxFuidRequestService.getTrxFuidRequestByTicketIdData(approvalModel.getTicketId());

                //set current state
                savedTFR.setTrxFuidApproval(trxFuidRequestService.mappingTrxFuidApprovalDTO(savedTFRA));
                //set nama aplikasi
                String[] splitAplikasi = savedTFR.getAplikasi().split(",");
                List<String> listAplikasi = Arrays.asList(splitAplikasi);
                List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
                savedTFR.setAplikasi(msta.toString().replaceAll("\\[|\\]", ""));
                //set tujuan
                String valueTujuan = msSystemParamService.getMsSystemParamDetail(savedTFR.getTujuan()).getParamDetailDesc();
                savedTFR.setTujuan(valueTujuan);
                //set alasan
                String valueAlasan = msSystemParamService.getMsSystemParamDetail(savedTFR.getAlasan()).getParamDetailDesc();
                savedTFR.setAlasan(valueAlasan);
                //set status current
                String ecurrState = msSystemParamService.getMsSystemParamDetail(savedTFRA.getCurrentState()).getParamDetailDesc();
                savedTFR.getTrxFuidApproval().setCurrentState(ecurrState);



                sendEmailApproval(tfra, approvalModel, savedTFR, savedTFR.getNikRequester(), validateCCNikDirectPuk(savedTFR, isPuk1, isPuk2), msEmployeeService.getListSkippedNikPukFuid(tfra));
                if(savedTFRA != null && savedTa != null) {
                    resTrxFuidRequestModel.setStatus(SUCCESS.getCode());
                    resTrxFuidRequestModel.setStatusDesc(SUCCESS.getValue());
                } else {
                    resTrxFuidRequestModel.setStatus(FAILED.getCode());
                    resTrxFuidRequestModel.setStatusDesc(FAILED.getValue());
                }
            } else {
                resTrxFuidRequestModel.setStatus(FAILED.getCode());
                resTrxFuidRequestModel.setStatusDesc(FAILED.getValue());
            }

            logger.info("Response >>> updateFuidApproval : {}", gson.toJson(resTrxFuidRequestModel));
            return ResponseEntity.ok(resTrxFuidRequestModel);
        } catch (Exception e) {
            logger.error("Fail to post updateFuidApproval ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    public ValidationPUKApprovalModel validationPUKApproval(TrxFuidApproval tfra, String nikPUKSSO, ApprovalModel approvalModel, String sCurrStateDt){
        ValidationPUKApprovalModel vpam = new ValidationPUKApprovalModel();
        boolean isPuk1 = false; boolean isPuk2 = false;
        if(tfra.getPuk1NIK().toLowerCase().equals(nikPUKSSO.toLowerCase())
                && (tfra.getPuk1Status().equals(PUK1_STATUS_WAITING) || tfra.getPuk1Status().equals(CURR_STATUS_REJECTED))){
            tfra.setPuk1Status(approvalModel.getStatus());
            tfra.setPuk1Notes(approvalModel.getNotes());
            tfra.setPuk1Dt(DateTimeHelper.getFullDateAsDate(sCurrStateDt));
            isPuk1 = true;
        } else {
            if(tfra.getPuk2NIK() != null) {
                if (tfra.getPuk2NIK().toLowerCase().equals(nikPUKSSO.toLowerCase())
                        && tfra.getPuk1Status().equals(CURR_STATUS_APPROVED)
                        && (tfra.getPuk2Status().equals(PUK2_STATUS_WAITING) || tfra.getPuk2Status().equals(CURR_STATUS_REJECTED))) {
                    tfra.setPuk2Status(approvalModel.getStatus());
                    tfra.setPuk2Notes(approvalModel.getNotes());
                    tfra.setPuk2Dt(DateTimeHelper.getFullDateAsDate(sCurrStateDt));
                    isPuk2 = true;
                }
            }
        }
        vpam.setTfra(tfra);
        vpam.setPuk1(isPuk1);
        vpam.setPuk2(isPuk2);
        return vpam;
    }

    private String validateCCNikDirectPuk(TrxFuidRequestDTO savedTFR, boolean isPuk1, boolean isPuk2){
        String nikDirectPuk = "";
        TrxFuidApprovalDTO savedTFA = savedTFR.getTrxFuidApproval();
        MsEmployee directPUK = msEmployeeService.getDirectPUK(savedTFR.getNikRequester());
        if(directPUK != null) {
            boolean isCCDirectPuk = false;
            if (isPuk1 && !savedTFA.getPuk1NIK().equals(directPUK.getNik())) {
                isCCDirectPuk = true;
            }
            if (isPuk2 && !savedTFA.getPuk2NIK().equals(directPUK.getNik())) {
                isCCDirectPuk = true;
            }
            if(isCCDirectPuk){
                nikDirectPuk = directPUK.getNik();
            }
        }
        return nikDirectPuk;
    }

    private void sendEmailApproval(TrxFuidApproval tfra, ApprovalModel approvalModel, TrxFuidRequestDTO savedTFR, String nikRequester, String ccNikDirectPuk, List<String> skippedNikPUKs) {
        MsEmployee waitingPUK = new MsEmployee();
        if (tfra.getPuk2NIK() != null && tfra.getCurrentState().equals(CURR_STATUS_WAITING_PUK2) && approvalModel.getStatus().equals(CURR_STATUS_APPROVED)) {
            waitingPUK = msEmployeeService.getMsEmployeeByNik(tfra.getPuk2NIK());

            //send email to puk2
            createApproval2Async(savedTFR, skippedNikPUKs, waitingPUK);
            //send email to user
            createApprovedAsync(nikRequester, savedTFR, EMPTY, waitingPUK);
        } else if (approvalModel.getStatus().equals(CURR_STATUS_APPROVED)) {
            //send email approved.
            if(tfra.getPuk2NIK() == null && tfra.getCurrentState().equals(CURR_STATUS_APPROVED)){
                waitingPUK = msEmployeeService.getMsEmployeeByNik(tfra.getPuk1NIK());
            } else {
                waitingPUK = msEmployeeService.getMsEmployeeByNik(tfra.getPuk2NIK());
            }
            createApprovedAsync(nikRequester, savedTFR, ccNikDirectPuk, waitingPUK);
        }
        if (approvalModel.getStatus().equals(CURR_STATUS_REJECTED)) {
            String rejectedBy = "";
            if (CURR_STATUS_REJECTED.equalsIgnoreCase(savedTFR.getTrxFuidApproval().getPuk1Status())){
                rejectedBy = savedTFR.getTrxFuidApproval().getPuk1NIK();
            }else {
                rejectedBy = savedTFR.getTrxFuidApproval().getPuk2NIK();
            }
            MsEmployee rejectedPUK = msEmployeeService.getMsEmployeeByNik(rejectedBy);

            createRejectedAsync(nikRequester, savedTFR, rejectedPUK);
        }
    }

    public void createApproval2Async(TrxFuidRequestDTO trxFuidRequest, List<String> skippedNikPUKs, MsEmployee waitingPUK) {
        //check director
        HashMap<String, String> mapPUK2Director = msEmployeeDirectorService.isPukDirectorByNikOptima(trxFuidRequest.getTrxFuidApproval().getPuk2NIK());
        emailNotificationService.sendApprovalFuidToUpmTemaNotification(trxFuidRequest, skippedNikPUKs, waitingPUK, mapPUK2Director);
    }

    public void createApprovedAsync(String userNik, TrxFuidRequestDTO trxFuidRequest, String ccNikDirectPuk, MsEmployee waitingPUK) {
        emailNotificationService.sendApprovedFuidToUpmTemaNotification(userNik, trxFuidRequest, ccNikDirectPuk, waitingPUK);
    }

    public void createRejectedAsync(String userNik, TrxFuidRequestDTO trxFuidRequest, MsEmployee rejectedPUK) {
        emailNotificationService.sendCreateRejectedFuidToUpmTemaNotification(userNik, trxFuidRequest, rejectedPUK);
    }
}
