package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.NotificationModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.NotificationService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@RestController
@RequestMapping(value = "tema/notification")
@CrossOrigin("*")
public class NotificationController {
    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    NotificationService notificationService;

    @GetMapping()
    public ResponseEntity<ResponseModel<NotificationModel>> getDeliverStatusNotification(@RequestHeader("XToken") String authorization,
                                                                                         @RequestParam("userType") String userType) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getDeliverStatusNotification {} from {}", getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();

            ResponseModel<NotificationModel> response = notificationService.getNotificationStatus(userType, nikRequester);
            logger.info("Response >>> getDeliverStatusNotification {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e){
            return responseInternalServerError("getDeliverStatusNotification", e);
        }
    }

    @PostMapping("/reminders/{id}")
    public ResponseEntity<ResponseModel> updateReminderFlag(@RequestHeader("XToken") String authorization,
                                                            @PathVariable("id") BigInteger id) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< updateReminderFlag {} from {}", getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();

            ResponseModel response = notificationService.updateReminderFlag(id);
            logger.info("Response >>> updateReminderFlag {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            return responseInternalServerError("updateReminderFlag", exception);
        }
    }

}
