package com.btpns.fin.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.NoResultException;

@RestController
@RequestMapping(value = "/release/db/lock")
public class DatabaseLockController {
    @Autowired
    private EntityManagerFactory entityManagerFactory;

    @GetMapping
    @Transactional
    public String updateDbLock() {
        EntityManager session = entityManagerFactory.createEntityManager();
        try {
            session.createNativeQuery("UPDATE DATABASECHANGELOGLOCK SET LOCKED=0, LOCKGRANTED=null, LOCKEDBY=null where ID=1; SELECT top(1) * from DATABASECHANGELOGLOCK; ").getSingleResult();
            return "Success";
        }
        catch (NoResultException e){
        }
        finally {
            if(session.isOpen()) session.close();
        }
        return "Failed";
    }
}
