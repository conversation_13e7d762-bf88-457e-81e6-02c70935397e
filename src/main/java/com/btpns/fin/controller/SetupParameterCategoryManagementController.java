package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.ApplicationTypeDataModel;
import com.btpns.fin.model.request.ReqApplicationTypeModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.ResApplicationTypeDataListModel;
import com.btpns.fin.model.response.ResApplicationTypeDataModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsSystemParamDetailRepository;
import com.btpns.fin.service.MsSystemParamService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseHelper.responseBadRequest;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "tema/upm/kategori/setup-parameter/management")
@CrossOrigin("*")
public class SetupParameterCategoryManagementController {
    private static final Logger logger = LoggerFactory.getLogger(SetupParameterCategoryManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    IMsSystemParamDetailRepository iMsSystemParamDetailRepository;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResApplicationTypeDataListModel>> getSPCategoryDataManagement(@RequestHeader("XToken") String authorization,
                                                                                                      @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                      @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                                      @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                                      @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getSPCategoryDataManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResApplicationTypeDataListModel> response = msSystemParamService.getListSPCategory(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);

                logger.info("Response >>> getSPCategoryDataManagement size {}", response.getDetails().getApplicationTypeDetail().size());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getSPCategoryDataManagement");
        } catch (Exception e){
            return responseInternalServerError("getSPCategoryDataManagement", e);
        }
    }

    @GetMapping(value = "/{paramDetailId}")
    public ResponseEntity<ResponseModel<ApplicationTypeDataModel>> getSPCategoryDataManagementByParamDetailId(@RequestHeader("XToken") String authorization,
                                                                                                              @PathVariable("paramDetailId") String paramDetailId) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getSPCategoryDataManagementByParamDetailId paramDetailId {} from {}", paramDetailId, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ApplicationTypeDataModel> response = msSystemParamService.getSPCategoryByParamDetailId(paramDetailId);

                logger.info("Response >>> getSPCategoryDataManagementByParamDetailId size {}", response.getDetails());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getSPCategoryDataManagementByParamDetailId");
        } catch (Exception e){
            return responseInternalServerError("getSPCategoryDataManagementByParamDetailId", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResApplicationTypeDataModel>> addOrEditSPCategoryDataManagement(@RequestHeader("XToken") String authorization,
                                                                                                        @RequestBody RequestModel<ReqApplicationTypeModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditSPCategoryDataManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(isValidRequest(request)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (iMsSystemParamDetailRepository.findByParamDetailId(Arrays.asList(KODE_KATEGORI_PARAM), request.getDetails().getParamDetailId()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResApplicationTypeDataModel> updateSPCategoryData = msSystemParamService.updateSPCategory(request.getDetails());
                            logger.info("Response >>> editSPCategoryDataManagement {} ", gson.toJson(updateSPCategoryData));

                            return ResponseEntity.ok(updateSPCategoryData);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMMSDataManagement", gson);
                    }

                    ResponseModel<ResApplicationTypeDataModel> saveSPCategoryData = msSystemParamService.saveSPCategory(request.getDetails());
                    logger.info("Response >>> addSPCategoryDataManagement {} ", gson.toJson(saveSPCategoryData));

                    return ResponseEntity.ok(saveSPCategoryData);
                }
                return responseForbidden("addOrEditSPCategoryDataManagement");
            }
            return responseBadRequest("addOrEditSPCategoryDataManagement");
        } catch (Exception e){
            return responseInternalServerError("addOrEditSPCategoryDataManagement", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqApplicationTypeModel> request) {
        return request != null
                && request.getType() != null
                && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
                && (request.getDetails().getStatus().equalsIgnoreCase(STATUS_APPLICATION_ACTIVE) || request.getDetails().getStatus().equalsIgnoreCase(STATUS_APPLICATION_INACTIVE) )
                && KODE_KATEGORI_PARAM.equalsIgnoreCase(request.getDetails().getParamId())
                && request.getDetails().getParamDetailId() != null
                && request.getDetails().getParamDetailDesc() != null;
    }
}
