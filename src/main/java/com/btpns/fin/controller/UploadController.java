package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MinioService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.net.URLConnection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.responseInternalServerError;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

@RestController
@RequestMapping(value = "/tema")
@CrossOrigin("*")
public class UploadController {
    private static final Logger logger = LoggerFactory.getLogger(UploadController.class);
    @Value("${image.base.uri}")
    String imageBaseUri;
    @Value("${minio.default.bucket.name}")
    String bucketName;
    @Value("${spesimen.file.path}")
    String spesimenFilePath;
    @Value("${spesimen.file.name}")
    String spesimenFileName;

    @Autowired
    MinioService minioService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @CrossOrigin(exposedHeaders = {"Content-Disposition"})
    @RequestMapping(path = "/upload", method = POST)
    public ResponseEntity<ResUploadModel> uploadMMI(@RequestHeader("XToken") String authorization,
                                                        @RequestParam("docs") MultipartFile docs) throws Exception {
        try {
            if(CommonHelper.validateUploadExtension(docs)) {
                logger.info("Received request upload send file {} size {} contentType {} inputStream {} ", docs.getOriginalFilename(), docs.getSize(), docs.getContentType(), docs.getInputStream() != null);
                Map<String, String> result = minioService.uploadFile(docs,
                        DocumentHelper.generateMMIFilePath(docs, LocalDateTime.now()));

                ResUploadModel resUploadModel = new ResUploadModel(result);
                logger.info("Response >>> uploadMMI : {}", gson.toJson(resUploadModel));
                return ResponseEntity.ok(resUploadModel);
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e){
            logger.error("upload error ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @CrossOrigin(exposedHeaders = {"Content-Disposition"})
    @RequestMapping(path = "/upload/spesimen", method = POST)
    public ResponseEntity<ResUploadModel> uploadMMISpesimen(@RequestHeader("XToken") String authorization,
                                                    @RequestParam("docs") MultipartFile docs) throws Exception {
        try {
            logger.info("Received request upload send file {} size {} contentType {} inputStream {} ", docs.getOriginalFilename(), docs.getSize(), docs.getContentType(), docs.getInputStream() != null);
            Map<String, String> result = minioService.uploadFile(docs,
                    DocumentHelper.generateMMISpesimenFilePath(docs));

            ResUploadModel resUploadModel = new ResUploadModel(result);
            logger.info("Response >>> uploadMMISpesimen : {}", gson.toJson(resUploadModel));
            return ResponseEntity.ok(resUploadModel);
        } catch (Exception e){
            logger.error("upload spesimen error ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @RequestMapping(path = "/download/spesimen", method = GET)
    public ResponseEntity<ResponseModel<ResUploadModel>> downloadMMISpesimen() throws Exception {
        try {
            Map<String, String> result = new HashMap<String, String>();
            result.put(CONTENT_URL, imageBaseUri+ '/'+ bucketName + '/' + spesimenFilePath);
            result.put(CONTENT_FILEPATH, spesimenFilePath);
            result.put(CONTENT_FILENAME, spesimenFileName);
            File file = new File(spesimenFilePath);
            URLConnection connection = file.toURL().openConnection();
            result.put(CONTENT_TYPE, connection.getContentType());

            ResUploadModel resUploadModel = new ResUploadModel(result);
            ResponseModel<ResUploadModel> response = buildResponse(SUCCESS, resUploadModel);
            logger.info("Response >>> downloadMMISpesimen : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e){
            return responseInternalServerError("downloadMMISpesimen", e);
        }
    }

    private ResponseModel<ResUploadModel> buildResponse(ResponseStatus status, ResUploadModel result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(TYPE_SPESIMEN_DOWNLOAD);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }
}
