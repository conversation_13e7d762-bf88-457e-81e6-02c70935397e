package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.service.*;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseStatus.*;
import static com.btpns.fin.helper.ResponseStatus.FAILED;

@RestController
@RequestMapping(value = "/tema/comment")
@CrossOrigin("*")
public class CommentController {
    private static final Logger logger = LoggerFactory.getLogger(CommentController.class);

    @Autowired
    TrxCommentService trxCommentService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    TrxSetupParamRequestService trxSetupParamRequestService;

    @Autowired
    EmailNotificationService emailNotificationService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping
    public ResponseEntity<CommentPostResModel> saveTrxComment(@RequestBody CommentPostModel commentPostModel, @RequestHeader("XToken") String authorization){
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxComment with data {} from {}", gson.toJson(commentPostModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            String namaRequester = profile.getProfile().getName();
            LocalDateTime dateNow = LocalDateTime.now();
            String ticketId = commentPostModel.getTicketId();
            //check fuid or setupparam
            boolean isFU = false, isSP = false;
            TrxFuidRequest savedTFR = null;
            TrxSetupParamRequest savedTSPR = null;
            String savedNikRequester = "", puk1Nik = "", puk2Nik = "";
            if(ticketId.substring(0,2).equals("FU")){
                savedTFR = trxFuidRequestService.getTrxFuidRequestByTicketId(ticketId);
                savedNikRequester = savedTFR.getNikRequester();
                puk1Nik = savedTFR.getTrxFuidApproval().getPuk1NIK();
                puk2Nik = savedTFR.getTrxFuidApproval().getPuk2NIK();
                isFU = true;
            }
            if(ticketId.substring(0,2).equals("SP")){
                savedTSPR = trxSetupParamRequestService.getTrxSetupParamRequestByTicketId(ticketId);
                savedNikRequester = savedTSPR.getNikRequester();
                puk1Nik = savedTSPR.getTrxSetupParamApproval().getPuk1NIK();
                puk2Nik = savedTSPR.getTrxSetupParamApproval().getPuk2NIK();
                isSP = true;
            }
            //validation access based on token
            boolean passValidation = false;
            TrxUpmRole roleUPMNikRequester = trxUpmRoleService.getTrxUpmRole(nikRequester);
            passValidation = CommonHelper.validateGetDetail(nikRequester, savedNikRequester, puk1Nik, puk2Nik, roleUPMNikRequester);
            if(passValidation) {
                //create comment id
                String commentId = "CMDF99122800010001";
                TrxComment lastComment = trxCommentService.getLastCommentByTicketId(ticketId);
                if (lastComment == null) {
                    commentId = "CM" + ticketId + "0001";
                } else {
                    String lastCommentId = lastComment.getCommentId();
                    String strCommentNum = getStrCommentNum(ticketId, lastCommentId);
                    logger.info("strCommentNum: " + strCommentNum);
                    Integer commentNum = Integer.parseInt(strCommentNum) + 1;
                    commentId = "CM" + ticketId + String.format("%04d", commentNum);
                }
                //get pic picUpmMakerNik
                String picUpmMakerNik = "", picUpmMakerNama = "";
                if (isFU) {
                    TrxFuidApproval savedTFA = savedTFR.getTrxFuidApproval();
                    if (savedTFA.getUpmInputNIK() != null) {
                        picUpmMakerNik = savedTFA.getUpmInputNIK();
                    }
                }
                if (isSP) {
                    TrxSetupParamApproval savedTSPA = savedTSPR.getTrxSetupParamApproval();
                    if (savedTSPA.getUpmInputNIK() != null) {
                        picUpmMakerNik = savedTSPA.getUpmInputNIK();
                    }
                }
                //get pic picUpmMakerNama
                TrxUpmRole trxUpmRole = trxUpmRoleService.getTrxUpmRole(picUpmMakerNik);
                if (trxUpmRole != null) {
                    if (trxUpmRole.getRole().equals(Constants.UPM_ROLE_MAKER)) {
                        picUpmMakerNama = trxUpmRole.getNama();
                    }
                }
                //change last status deliver into read before new post
                if (lastComment != null) {
                    trxCommentService.updateTrxComment(lastComment, Constants.COMMENT_STATUS_READ);
                }

                TrxComment trxComment = new TrxComment();
                trxComment.setTicketId(ticketId);
                trxComment.setCommentId(commentId);
                trxComment.setNikComment(nikRequester);
                trxComment.setNamaComment(namaRequester);
                trxComment.setCreateDateTime(dateNow);
                trxComment.setStatus(Constants.COMMENT_STATUS_DELIVER);
                trxComment.setReadDateTime(dateNow);
                trxComment.setContent(commentPostModel.getContent());
                if (commentPostModel.getAttachment() != null){
                    List<AttachmentModel> lAttachment = commentPostModel.getAttachment();
                    Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
                    trxComment.setAttachment(new Gson().toJson(lAttachment, attachmentListType));
                }
                trxComment.setPicUpmMakerNik(picUpmMakerNik);
                trxComment.setPicUpmMakerNama(picUpmMakerNama);
                TrxComment savedTC = trxCommentService.saveTrxComment(trxComment);
                boolean isSuccesRequest = false;
                if (savedTC != null) {
                    isSuccesRequest = true;
                }

                //kirim email
                if (isSuccesRequest) {
                    //check role upm nikRequester, if = upm send email, != upm not send
                    if (roleUPMNikRequester != null) {
                        if (nikRequester.toLowerCase().equals(roleUPMNikRequester.getNik().toLowerCase())) {
                            if (isFU && savedTFR != null) {
                                sendEmailUPMComment(savedTFR.getNikRequester(), savedTC, commentPostModel.getAttachment());
                            }
                            if (isSP && savedTSPR != null) {
                                sendEmailUPMComment(savedTSPR.getNikRequester(), savedTC, commentPostModel.getAttachment());
                            }
                        }
                    }
                }

                CommentPostResModel commentPostResModel = new CommentPostResModel();
                commentPostResModel.setTicketId(ticketId);
                commentPostResModel.setCommentId(commentId);
                if (isSuccesRequest) {
                    commentPostResModel.setStatus(SUCCESS.getCode());
                    commentPostResModel.setStatusDesc(SUCCESS.getValue());
                } else {
                    commentPostResModel.setStatus(FAILED.getCode());
                    commentPostResModel.setStatusDesc(FAILED.getValue());
                }
                logger.info("Response >>> saveTrxComment : {}", gson.toJson(commentPostResModel));
                return ResponseEntity.ok(commentPostResModel);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
        } catch (Exception e) {
            logger.error("Fail to post saveTrxComment ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private String getStrCommentNum(String ticketId, String lastCommentId) {
        String strCommentNum = "";

        if (CommonHelper.isTicketFuidUPMManual(ticketId) ||
            CommonHelper.isTicketFuidResignOptima(ticketId) ||
            CommonHelper.isTicketFuidSimplifikasi(ticketId) ||
            CommonHelper.isTicketFuidProspera(ticketId)){
            strCommentNum = lastCommentId.substring(15, 19);
        }else {
            strCommentNum = lastCommentId.substring(14, 18);
        }

        return strCommentNum;
    }

    @PostMapping(value = "/status")
    public ResponseEntity<CommentUpdateFlagResModel> updateFlagTrxComment(@RequestBody CommentUpdateFlagModel commentUpdateFlagModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< updateFlagTrxComment with data {} from {}", gson.toJson(commentUpdateFlagModel), getProfile(gson, profile));
            String ticketId = commentUpdateFlagModel.getTicketId();
            String commentId = commentUpdateFlagModel.getCommentId();
            String status = commentUpdateFlagModel.getStatus();

            boolean isSuccesRequest = false;
            TrxComment tcFromDB = trxCommentService.getCommentByTicketIdAndCommentId(ticketId, commentId);
            TrxComment savedTC = trxCommentService.updateTrxComment(tcFromDB, status);
            if (savedTC != null) {
                isSuccesRequest = true;
            }

            CommentUpdateFlagResModel commentUpdateFlagResModel = new CommentUpdateFlagResModel();
            commentUpdateFlagResModel.setCommentId(commentId);
            commentUpdateFlagResModel.setTicketId(ticketId);
            if(isSuccesRequest){
                commentUpdateFlagResModel.setStatus(SUCCESS.getCode());
                commentUpdateFlagResModel.setStatusDesc(SUCCESS.getValue());
            } else {
                commentUpdateFlagResModel.setStatus(FAILED.getCode());
                commentUpdateFlagResModel.setStatusDesc(FAILED.getValue());
            }
            logger.info("Response >>> updateFlagTrxComment : {}", gson.toJson(commentUpdateFlagResModel));
            return ResponseEntity.ok(commentUpdateFlagResModel);
        } catch (Exception e) {
            logger.error("Fail to post updateFlagTrxComment ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping
    public ResponseEntity<CommentListModel> getDeliveryListComment(@RequestParam("page") int pageNumber, @RequestParam("limit") int pageSize, @RequestParam("status") String status, @RequestHeader("XToken") String authorization){
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getDeliveryListComment page {} limit {} status {} from {}", pageNumber, pageSize, status, getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            //offset start from 0
            int pageNumMin1 = pageNumber - 1;
            Page<TrxComment> pageTC= trxCommentService.getListCommentByStatus(status, pageNumMin1, pageSize);
            List<TrxComment> lTrxComment = pageTC.getContent();
            List<CommentModel> listCM = new ArrayList<CommentModel>();
            Iterator<TrxComment> iterator = lTrxComment.iterator();
            while(iterator.hasNext()){
                TrxComment tcm = iterator.next();
                CommentModel cm = Mapper.toCommentModel(tcm);
                listCM.add(cm);
            }
            CommentListModel clm = new CommentListModel();
            clm.setComment(listCM);
            clm.setPage(pageNumber);
            clm.setLimit(pageSize);
            clm.setTotalPages(pageTC.getTotalPages());
            clm.setTotalItems(pageTC.getTotalElements());

            logger.info("Response >>> getDeliveryListComment : with size {}", clm.getComment().size());
            return ResponseEntity.ok(clm);
        } catch (Exception e) {
            logger.error("Fail to getDeliveryListComment ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private void sendEmailUPMComment(String userNik, TrxComment savedTC, List<AttachmentModel> attachmentList) {
        //send to user pemohon from upm
        emailNotificationService.sendCreatePostComment(userNik, savedTC, attachmentList);
    }
}
