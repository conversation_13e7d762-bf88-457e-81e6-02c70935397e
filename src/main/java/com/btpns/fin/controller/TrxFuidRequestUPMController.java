package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.response.ResTrxFuidRequest;
import com.btpns.fin.model.TrxFuidRequestModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.TrxFuidRequestService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.ResponseHelper.*;

@RestController
@RequestMapping(value = "tema/upm/fuid")
@CrossOrigin("*")
public class TrxFuidRequestUPMController {
    private static final Logger logger = LoggerFactory.getLogger(TrxFuidRequestUPMController.class);

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping()
    public ResponseEntity<ResponseModel<ResTrxFuidRequest>> saveTrxFuidRequestUPM(@RequestHeader("XToken") String authorization,
                                                                                  @RequestBody TrxFuidRequestModel trxFuidRequestModel) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxFuidRequestUPM with data {} from {}", gson.toJson(trxFuidRequestModel), getProfile(gson, profile));
            String nikUPMRequester = profile.getProfile().getPreferred_username();
            encodeRequestFuid(trxFuidRequestModel);

            if (validateUPMTicket(nikUPMRequester,trxUpmRoleService.getTrxUpmRole(nikUPMRequester))){
                ResponseModel<ResTrxFuidRequest> response = trxFuidRequestService.saveTrxFuidRequestUPM(trxFuidRequestModel);

                logger.info("Response >>> saveTrxFuidRequestUPM {}", gson.toJson(response.getDetails()));
                return ResponseEntity.ok(response);
            }
            logger.warn("Invalid user for nik : {}", nikUPMRequester);
            return responseForbidden("saveTrxFuidRequestUPM");

        } catch (Exception e) {
            return responseInternalServerError("saveTrxFuidRequestUPM", e);
        }
    }
}
