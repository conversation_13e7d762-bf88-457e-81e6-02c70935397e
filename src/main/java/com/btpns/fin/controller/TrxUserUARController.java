package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.DataUserPerApp;
import com.btpns.fin.model.UARDetailModel;
import com.btpns.fin.model.entity.TrxUARRequest;
import com.btpns.fin.model.request.ReqApprovalBatch;
import com.btpns.fin.model.request.ReqConfirmUARModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.TrxUserUARService;
import com.google.gson.Gson;
import io.undertow.server.handlers.resource.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.*;

@RestController
@RequestMapping(value = "/tema/uar")
@CrossOrigin("*")
public class TrxUserUARController {
    private static final Logger logger = LoggerFactory.getLogger(TrxUserUARController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private TrxUserUARService trxUserUARService;

    @GetMapping(value = "/requests")
    public ResponseEntity<ResponseModel<List<DataUserPerApp>>> getListUARRequests(@RequestHeader("XToken") String authorization) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            logger.info("Received <<< getListUARRequests from {}", getProfile(gson, token));

            ResponseModel<List<DataUserPerApp>> response = trxUserUARService.getListUARRequests(nikRequester);

            logger.info("Response >>> getListUARRequests {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getListUARRequests : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListUARRequests", exception);
        }
    }

    @GetMapping(value = "/{ticketId}")
    public ResponseEntity<ResponseModel<UARDetailModel>> getUARDetailByTicketId(@PathVariable("ticketId") String ticketId,
                                                                                @RequestHeader("XToken") String authorization) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getUARDetailByTicketId from {}", getProfile(gson, token));

            if (!trxUserUARService.isValidGetDetailRequester(ticketId, token.getProfile().getPreferred_username())) {
                logger.warn("Response >>> getUARDetailByTicketId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getUARDetailByTicketId");
            }
            ResponseModel<UARDetailModel> response = trxUserUARService.getUARDetail(ticketId);

            logger.info("Response >>> getUARDetailByTicketId {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getUARDetailByTicketId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getUARDetailByTicketId", exception);
        }
    }

    @GetMapping(value = "/approvals")
    public ResponseEntity<ResponseModel<ResponseListModel>> getListUARApprovals(@RequestHeader("XToken") String authorization,
                                                                                @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                @RequestParam(value = "limit", defaultValue = "5") Integer pageSize) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            logger.info("Received <<< getListUARApprovals from {}", getProfile(gson, token));

            int pageNumMin1 = pageNumber - 1;

            ResponseModel<ResponseListModel> response = trxUserUARService.getListUARApprovals(pageNumMin1, pageNumber, pageSize, nikRequester);

            logger.info("Response >>> getListUARApprovals {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getListUARApprovals : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getListUARApprovals", exception);
        }
    }

    @PostMapping(value = "/confirm")
    public ResponseEntity<ResponseModel<ResUARModel>> updateConfirmationUAR(@RequestHeader("XToken") String authorization, @RequestBody ReqConfirmUARModel request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< updateConfirmationUAR from {}", getProfile(gson, token));

            TrxUARRequest foundUARRequest = trxUserUARService.findByTicketId(request.getTicketId());
            if (!token.getProfile().getPreferred_username().equalsIgnoreCase(foundUARRequest.getNik())) {
                logger.warn("Response >>> updateConfirmationUAR : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("updateConfirmationUAR");
            }

            if (!isValidRequest(request) || !trxUserUARService.isValidRequestTypeByTicketStatus(request, foundUARRequest)) {
                logger.warn("Response >>> updateConfirmationUAR : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("updateConfirmationUAR");
            }
            ResponseModel<ResUARModel> response = trxUserUARService.updateConfirmationUAR(request, token.getProfile());

            logger.info("Response >>> updateConfirmationUAR {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> updateConfirmationUAR : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("updateConfirmationUAR", exception);
        }
    }

    private boolean isValidRequest(ReqConfirmUARModel request) {
        boolean isValidConfirmation = request.getConfirmation() == 0 || request.getConfirmation() == 1;
        return request.getTicketId() != null &&
                isValidConfirmation &&
                request.getPuk() != null;
    }

    @PostMapping(value = "/approve")
    public ResponseEntity<ResponseModel<ResBatchProcess>> updateApprovalUAR(@RequestHeader("XToken") String authorization, @RequestBody ReqApprovalBatch request) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< updateApprovalUAR from {}", getProfile(gson, token));

            List<TrxUARRequest> foundUARs = trxUserUARService.getListUARFromTicketIds(trxUserUARService.getListTicketIdFromApprovalRequests(request.getData()));
            if (!trxUserUARService.isValidPUK(foundUARs, token.getProfile().getPreferred_username())) {
                logger.warn("Response >>> updateApprovalUAR : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("updateApprovalUAR");
            }
            if (!trxUserUARService.isValidPukApproval(request, foundUARs)) {
                logger.warn("Response >>> updateApprovalUAR : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return responseBadRequest("updateApprovalUAR");
            }
            ResponseModel<ResBatchProcess> response = trxUserUARService.updateApprovalUAR(request, foundUARs, token.getProfile());

            logger.info("Response >>> updateApprovalUAR {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> updateApprovalUAR : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("updateApprovalUAR", exception);
        }
    }

    @GetMapping(value = "/{ticketId}/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getUARDetailAsPDFByTicketId(@PathVariable("ticketId") String ticketId,
                                                                                     @RequestHeader("XToken") String authorization) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getUARDetailAsPDFByTicketId from {}", getProfile(gson, token));

            if (!trxUserUARService.isValidGetDetailRequester(ticketId, token.getProfile().getPreferred_username())) {
                logger.warn("Response >>> getUARDetailAsPDFByTicketId : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getUARDetailAsPDFByTicketId");
            }
            ResponseModel<ResUploadModel> response = trxUserUARService.getDetailUARAsPDF(ticketId);

            logger.info("Response >>> getUARDetailAsPDFByTicketId {} ", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getUARDetailAsPDFByTicketId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getUARDetailAsPDFByTicketId", exception);
        }
    }

    @GetMapping(value = "/{ticketId}/direct-download")
    public ResponseEntity<Resource> directDownloadUARRequestByTicketIdPdf(@PathVariable("ticketId") String ticketId,
                                                                          @RequestHeader("XToken") String authorization) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< directDownloadUARRequestByTicketIdPdf from {}", getProfile(gson, token));

            if (!trxUserUARService.isValidGetDetailRequester(ticketId, token.getProfile().getPreferred_username())) {
                logger.warn("Response >>> directDownloadUARRequestByTicketIdPdf : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("directDownloadUARRequestByTicketIdPdf");
            }

            ResFileDownload response = trxUserUARService.directDownloadPdfDetailUAR(ticketId);
            logger.info("Response >>> directDownloadUARRequestByTicketIdPdf {} ", gson.toJson(response));
            return responseDownloadFile("directDownloadUARRequestByTicketIdPdf", response);
        } catch (Exception exception) {
            logger.warn("Response >>> getUARDetailAsPDFByTicketId : {}", getHttpStatusDetail(HttpStatus.INTERNAL_SERVER_ERROR));
            return responseInternalServerError("getUARDetailAsPDFByTicketId", exception);
        }
    }

}

