package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.responseDownloadFile;
import static com.btpns.fin.helper.ResponseHelper.responseForbidden;

@RestController
@RequestMapping(value = "/tema/user")
@CrossOrigin("*")
public class UserController {
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    MsEmployeeHierarchyService msEmployeeHierarchyService;

    @Autowired
    UserService userService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping()
    public ResponseEntity<UserModel> getUserByNik(@RequestHeader("XToken") String authorization,
                                                  @RequestParam("nik") String nik,
                                                  @RequestParam(value = "type", required = false) String type,
                                                  @RequestParam(value = "kodeCabang", required = false) String kodeCabang,
                                                  @RequestParam(value = "tipeKewenanganLimit", required = false) String tipeKewenanganLimit,
                                                  @RequestParam(value = "aplikasi", required = false) String[] aplikasi,
                                                  @RequestParam(value = "tujuan", required = false) String tujuan,
                                                  @RequestParam(value = "alasan", required = false) String alasan,
                                                  @RequestParam(value = "isHavingAttachment", required = false) Integer isHavingAttachment,
                                                  @RequestParam(value = "isTunai", required = false) Boolean isTunai,
                                                  @RequestParam(value = "nominal", required = false) Double nominal){
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getUserByNik nik {} kodeCabang {} tipeKewenanganLimit {} aplikasi {} tujuan {} alasan {} isHavingAttachment {} from {}", nik, kodeCabang, tipeKewenanganLimit, aplikasi, tujuan, alasan, isHavingAttachment, getProfile(gson, token));
            UserTokenModel utm = gson.fromJson(token.getDataProfile(), UserTokenModel.class);

            UserModel userModel = userService.getDataUser(utm, type, nik, tipeKewenanganLimit, aplikasi, tujuan, alasan, isHavingAttachment, isTunai, nominal);
            logger.info("Response >>> getUserByNik listPUK : with size {} data {}", userModel.getPuk().size(), gson.toJson(userModel));
            return ResponseEntity.ok(userModel);
        } catch (Exception e) {
            logger.error("Fail getUserByNik ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/occupation/{nik}")
    public ResponseEntity<MsEmployeeHierarchyModel> getUserOccupationByNik(@RequestHeader("XToken") String authorization, @PathVariable("nik") String nik){
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getUserOccupationByNik nik {} from {}", nik, getProfile(gson, token));
            MsEmployeeHierarchy mh = msEmployeeHierarchyService.getMsEmployeeHierarchyByNik(nik);
            if (mh == null) {
                MsEmployeeHierarchyModel mhm = new MsEmployeeHierarchyModel();
                mhm.setNik(nik);
                logger.info("Response >>> getUserOccupationByNik : {}", gson.toJson(mhm));
                return ResponseEntity.ok(mhm);
            }
            MsEmployeeHierarchyModel mhm = Mapper.toMsEmployeeHierarchyModel(mh);
            logger.info("Response >>> getUserOccupationByNik : {}", gson.toJson(mhm));
            return ResponseEntity.ok(mhm);
        } catch (Exception e){
            logger.error("Fail getUserOccupationByNik ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/delegation")
    public ResponseEntity<ResponseModel<UserDelegationModel>> getPossibleUsersForDelegation(@RequestHeader("XToken") String authorization, @RequestParam("nik") String nik){
        try{
            logger.info("Received <<< getPossibleUsersForDelegation from {}", nik);

            ResponseModel<UserDelegationModel> response = userService.getPossibleUsersForDelegation(nik);
            logger.info("Response >>> getPossibleUsersForDelegation : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail getUserByNik ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/inquire")
    public ResponseEntity<UserInquiryModel> inquireUserDetailsAndPermission(@RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< inquireUserDetailsAndPermission from {}", getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            UserInquiryModel userInquiryModel = userService.inquireUserDetailsAndPermission(nikRequester, profile.getProfile().getEmail());
            logger.info("Response >>> inquireUserDetailsAndPermission : {}", gson.toJson(userInquiryModel));
            return ResponseEntity.ok(userInquiryModel);
        } catch (Exception e) {
            logger.error("Fail to inquireUserDetailsAndPermission ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "ticket/inquire")
    public ResponseEntity<ResponseModel<ResponseListModel<InquiryTicketModel>>> getListTicketUserInquiry(@RequestHeader("XToken") String authorization,
                                                                                                         @RequestParam("page") Integer pageNumber,
                                                                                                         @RequestParam("limit") Integer pageSize,
                                                                                                         @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                                                         @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getListTicketUserInquiry from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if (CommonHelper.isInquiryUPMRole(trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResponseListModel<InquiryTicketModel>> response = userService.getListTicketUserInquiry(startDate, endDate, pageNumber, pageSize, Boolean.FALSE);

                logger.info("Response >>> getListTicketUserInquiry {} ", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> getListTicketUserInquiry : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("getListTicketUserInquiry");
        } catch (Exception e) {
            logger.error("Fail to getListTicketUserInquiry ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "ticket/inquire/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> generateTicketUserInquiryCsv(@RequestHeader("XToken") String authorization,
                                                                                      @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                                      @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< generateTicketUserInquiryCsv from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if (CommonHelper.isInquiryUPMRole(trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResUploadModel> response = userService.getTicketUserInquiryCsv(startDate, endDate, null, null, Boolean.TRUE);

                logger.info("Response >>> generateTicketUserInquiryCsv {} ", gson.toJson(response));
                return ResponseEntity.ok(response);
            }
            logger.warn("Response >>> generateTicketUserInquiryCsv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("generateTicketUserInquiryCsv");
        } catch (Exception e) {
            logger.error("Fail to generateTicketUserInquiryCsv ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "ticket/inquire/direct-download")
    public ResponseEntity<Resource> directDownloadTicketUserInquiryCsv(@RequestHeader("XToken") String authorization,
                                                                       @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
                                                                       @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< directDownloadTicketUserInquiryCsv from {}", getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();
            if (CommonHelper.isInquiryUPMRole(trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResFileDownload response = userService.directDownloadTicketUserInquiryCsv(startDate, endDate, null, null, Boolean.TRUE);

                return responseDownloadFile("directDownloadTicketUserInquiryCsv", response);
            }
            logger.warn("Response >>> directDownloadTicketUserInquiryCsv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseForbidden("directDownloadTicketUserInquiryCsv");
        } catch (Exception e) {
            logger.error("Fail to directDownloadTicketUserInquiryCsv ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
