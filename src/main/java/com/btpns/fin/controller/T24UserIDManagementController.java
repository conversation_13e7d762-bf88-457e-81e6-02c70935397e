package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.MsEGLS;
import com.btpns.fin.model.MsT24;
import com.btpns.fin.model.entity.TrxUpmRole;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.T24UserIdService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.*;

@Controller
@RequestMapping("tema/upm/user-id/AU00000012")
@CrossOrigin("*")
public class T24UserIDManagementController {
    private static final Logger logger = LoggerFactory.getLogger(T24UserIDManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private T24UserIdService t24UserIdService;

    @Autowired
    private TrxUpmRoleService upmRoleService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResponseListModel<MsT24>>> getMsT24UserIDManagement(@RequestHeader("XToken") String authorization,
                                                                                            @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                            @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                            @RequestParam(value = "searchFlag", required = false, defaultValue = "namaUser") String searchFlag,
                                                                                            @RequestParam(value = "searchData", required = false) String searchData) {
        try {
            Token token = new Token(authorization);
            logger.info("Received <<< getMsT24UserIDManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = this.upmRoleService.getTrxUpmRole(nikRequester);

            if (!CommonHelper.validateUPMTicket(nikRequester, trxUpmRole)) {
                logger.warn("Response >>> getMsT24UserIDManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getMsT24UserIDManagement");
            }

            ResponseModel<ResponseListModel<MsT24>> response = t24UserIdService.getT24UserIDs(pageSize, pageNumber, searchFlag, searchData);
            logger.info("Response >>> getMsT24UserIDManagement size {}", response.getDetails().getTotalItems());

            return ResponseEntity.ok(response);
        } catch (Exception exception) {
            logger.warn("Response >>> getMsT24UserIDManagement : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseInternalServerError("getMsT24UserIDManagement", exception);
        }
    }

    @GetMapping(value = "/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getT24Csv(@RequestHeader("XToken") String authorization) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = this.upmRoleService.getTrxUpmRole(nikRequester);
            logger.info("Received <<< getT24Csv from {}", getProfile(gson, token));

            if (!CommonHelper.validateUPMTicket(nikRequester, trxUpmRole)) {
                logger.warn("Response >>> getT24Csv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("getT24Csv");
            }
            ResponseModel<ResUploadModel> response = t24UserIdService.getT24Csv();

            logger.info("Response >>> getT24Csv : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.warn("Response >>> getT24Csv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseInternalServerError("getT24Csv", e);
        }
    }

    @GetMapping(value = "/direct-download")
    public ResponseEntity<Resource> directDownloadT24Csv(@RequestHeader("XToken") String authorization) {
        try {
            Token token = new Token(authorization);
            String nikRequester = token.getProfile().getPreferred_username();
            TrxUpmRole trxUpmRole = this.upmRoleService.getTrxUpmRole(nikRequester);
            logger.info("Received <<< directDownloadT24Csv from {}", getProfile(gson, token));

            if (!CommonHelper.validateUPMTicket(nikRequester, trxUpmRole)) {
                logger.warn("Response >>> directDownloadT24Csv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
                return responseForbidden("directDownloadT24Csv");
            }
            ResFileDownload response = t24UserIdService.directDownloadT24Csv();
            return responseDownloadFile("directDownloadT24Csv", response);
        } catch (Exception e) {
            logger.warn("Response >>> directDownloadT24Csv : {}", getHttpStatusDetail(HttpStatus.FORBIDDEN));
            return responseInternalServerError("directDownloadT24Csv", e);
        }
    }
}
