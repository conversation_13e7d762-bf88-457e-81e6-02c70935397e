package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.FTEUserModel;
import com.btpns.fin.model.response.ResFTEUserListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.MsEmployeeHierarchyService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.ResponseHelper.*;

@RestController
@RequestMapping(value = "tema/upm/fte/user/management")
@CrossOrigin("*")
public class FTEUserManagementController {
    private static final Logger logger = LoggerFactory.getLogger(FTEUserManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsEmployeeHierarchyService msEmployeeHierarchyService;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResFTEUserListModel>> getFTEUserManagement(@RequestHeader("XToken") String authorization,
                                                                                   @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                   @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                   @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                   @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getFTEUserManagement page {} limit {} searchFlag {} searchData {} from {}", pageNumber, pageSize, searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResFTEUserListModel> response = msEmployeeHierarchyService.getListFTE(pageNumMin1, pageSize, pageNumber, searchFlag, searchData);
                logger.info("Response >>> getFTEUserManagement size {}", response.getDetails().getFteUser().size());

                return ResponseEntity.ok(response);
            }
            return responseForbidden("getFTEUserManagement");
        } catch (Exception e){
            return responseInternalServerError("getFTEUserManagement", e);
        }
    }

    @GetMapping(value = "/{nik:.+}")
    public ResponseEntity<ResponseModel<FTEUserModel>> getFTEUserManagementByNik(@RequestHeader("XToken") String authorization,
                                                                                 @PathVariable("nik") String nik) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getFTEUserManagementByNik nik {} from {}", nik, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(StringUtils.isNotBlank(nik)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    ResponseModel<FTEUserModel> response = msEmployeeHierarchyService.getFTEByNik(nik);
                    logger.info("Response >>> getFTEUserManagementByNik {}", gson.toJson(response.getDetails()));
                    return ResponseEntity.ok(response);
                }
                return responseForbidden("getFTEUserManagementByNik");
            }
            return responseBadRequest("getFTEUserManagementByNik");
        } catch (Exception e){
            return responseInternalServerError("getFTEUserManagementByNik", e);
        }
    }
}
