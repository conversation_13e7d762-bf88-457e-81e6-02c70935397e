package com.btpns.fin.controller;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Token;
import com.btpns.fin.model.MsTemaApplicationModel;
import com.btpns.fin.model.request.ReqTemaApplicationModel;
import com.btpns.fin.model.request.RequestModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMsTemaApplicationRepository;
import com.btpns.fin.service.MsTemaApplicationService;
import com.btpns.fin.service.TrxUpmRoleService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "tema/upm/tema-application/management")
@CrossOrigin("*")
public class TemaApplicationManagementController {
    private static final Logger logger = LoggerFactory.getLogger(TemaApplicationManagementController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    IMsTemaApplicationRepository iMsTemaApplicationRepository;

    @GetMapping()
    public ResponseEntity<ResponseModel<ResTemaApplicationDataListModel>> getTemaApplicationDataManagement(@RequestHeader("XToken") String authorization,
                                                                                                           @RequestParam(value = "page", defaultValue = "1") Integer pageNumber,
                                                                                                           @RequestParam(value = "limit", defaultValue = "50") Integer pageSize,
                                                                                                           @RequestParam(value = "applicationType", required = false, defaultValue = "") String applicationType,
                                                                                                           @RequestParam(value = "searchFlag", required = false, defaultValue = "") String searchFlag,
                                                                                                           @RequestParam(value = "searchData", required = false, defaultValue = "") String searchData) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getTemaApplicationDataManagement page {} limit {} applicationType {} searchFlag {} searchData {} from {}", pageNumber, pageSize, applicationType ,searchFlag, searchData, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            int pageNumMin1 = pageNumber - 1;
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<ResTemaApplicationDataListModel> response = msTemaApplicationService.getListTemaApplication(pageNumMin1, pageSize, pageNumber, applicationType, searchFlag, searchData);

                logger.info("Response >>> getTemaApplicationDataManagement size {}", response.getDetails().getApplicationTemaDetail().size());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getTemaApplicationDataManagement");
        } catch (Exception e){
            return responseInternalServerError("getTemaApplicationDataManagement", e);
        }
    }

        @GetMapping(value = "/{paramDetailId}")
    public ResponseEntity<ResponseModel<MsTemaApplicationModel>> getTemaApplicationDataManagementByParamDetailId(@RequestHeader("XToken") String authorization,
                                                                                                            @PathVariable("paramDetailId") String paramDetailId) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< getTemaApplicationDataManagementByParamDetailId paramDetailId {} from {}", paramDetailId, getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                ResponseModel<MsTemaApplicationModel> response = msTemaApplicationService.getTemaApplicationByParamDetailId(paramDetailId);

                logger.info("Response >>> getTemaApplicationDataManagementByParamDetailId size {}", response.getDetails());
                return ResponseEntity.ok(response);
            }
            return responseForbidden("getTemaApplicationDataManagementByParamDetailId");
        } catch (Exception e){
            return responseInternalServerError("getTemaApplicationDataManagementByParamDetailId", e);
        }
    }

    @PostMapping()
    public ResponseEntity<ResponseModel<ResTemaApplicationDataModel>> addOrEditTemaApplicationDataManagement(@RequestHeader("XToken") String authorization,
                                                                                                             @RequestBody RequestModel<ReqTemaApplicationModel> request) {
        try{
            Token token = new Token(authorization);
            logger.info("Received <<< addOrEditTemaApplicationDataManagement {} from {}", gson.toJson(request), getProfile(gson, token));
            String nikRequester = token.getProfile().getPreferred_username();
            if(isValidRequest(request)) {
                if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
                    if (iMsTemaApplicationRepository.findByParamDetailId(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM), request.getDetails().getParamDetailId()) != null){
                        if (EDIT.equalsIgnoreCase(request.getDetails().getType())){
                            ResponseModel<ResTemaApplicationDataModel> updateApplicationTypeData = msTemaApplicationService.updateTemaApplication(request.getDetails());
                            logger.info("Response >>> editTemaApplicationDataManagement {} ", gson.toJson(updateApplicationTypeData));

                            return ResponseEntity.ok(updateApplicationTypeData);
                        }
                        return responseFailed(request.getType(), ALREADY_EXIST, "addOrEditMMSDataManagement", gson);
                    }

                    ResponseModel<ResTemaApplicationDataModel> saveApplicationTypeData = msTemaApplicationService.saveTemaApplication(request.getDetails());
                    logger.info("Response >>> addTemaApplicationDataManagement {} ", gson.toJson(saveApplicationTypeData));

                    return ResponseEntity.ok(saveApplicationTypeData);
                }
                return responseForbidden("addOrEditTemaApplicationDataManagement");
            }
            return responseBadRequest("addOrEditTemaApplicationDataManagement");
        } catch (Exception e){
            return responseInternalServerError("addOrEditApplicationTemaDataManagement", e);
        }
    }

    private boolean isValidRequest(RequestModel<ReqTemaApplicationModel> request) {
        return request != null
               && request.getType() != null
               && (request.getDetails().getType().equalsIgnoreCase(ADD) || request.getDetails().getType().equalsIgnoreCase(EDIT))
               && (KODE_APLIKASI_FUID.equalsIgnoreCase(request.getDetails().getParamId()) || KODE_APLIKASI_SETUP_PARAM.equalsIgnoreCase(request.getDetails().getParamId()))
               && request.getDetails().getParamDetailId() != null
               && request.getDetails().getParamDetailDesc() != null;
    }
}
