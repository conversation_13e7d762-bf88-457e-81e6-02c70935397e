package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.service.TicketTimelineService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/tema")
@CrossOrigin("*")
public class TicketTimelineController {
    private static final Logger logger = LoggerFactory.getLogger(TicketTimelineController.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TicketTimelineService ticketTimelineService;

    @GetMapping(value = "/{ticketId}/timeline")
    public ResponseEntity<ResponseModel> getTicketTimeline(@PathVariable("ticketId") String ticketId) {
        try {
            logger.info("Received <<< getTicketTimeline ticketId {}", ticketId);

            ResponseModel response = ticketTimelineService.getTicketTimeline(ticketId);
            logger.info("Response >>> getTicketTimeline {}", gson.toJson(response.getDetails()));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail to getTicketTimeline ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
