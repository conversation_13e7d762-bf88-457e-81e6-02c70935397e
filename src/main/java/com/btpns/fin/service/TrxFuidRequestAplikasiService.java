package com.btpns.fin.service;

import com.btpns.fin.model.entity.TrxFuidRequestAplikasi;
import com.btpns.fin.repository.ITrxFuidRequestAplikasiRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
public class TrxFuidRequestAplikasiService {
    @Autowired
    ITrxFuidRequestAplikasiRepository itrxFuidRequestAplikasiRepository;

    @Transactional
    public TrxFuidRequestAplikasi saveTrxFuidRequestAplikasi (TrxFuidRequestAplikasi tfrap){
        return itrxFuidRequestAplikasiRepository.save(tfrap);
    }

    @Transactional
    public int deleteTrxFuidRequestAplikasiByTicketId (String ticketId){
        return itrxFuidRequestAplikasiRepository.deleteTrxFuidRequestAplikasiByTicketId(ticketId);
    }

    @Transactional
    public int updateTrxFuidRequestAplikasiByTicketId(String ticketId, String periodDateDone, String periodMonthDone) {
        return itrxFuidRequestAplikasiRepository.updateTrxFuidRequestAplikasiByTicketId(ticketId, periodDateDone, periodMonthDone);
    }
}
