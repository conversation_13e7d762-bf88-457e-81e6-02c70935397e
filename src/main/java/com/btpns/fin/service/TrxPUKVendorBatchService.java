package com.btpns.fin.service;

import com.btpns.fin.model.entity.TrxPUKVendorBatch;
import com.btpns.fin.repository.ITrxPUKVendorBatchRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class TrxPUKVendorBatchService {
    @Autowired
    private ITrxPUKVendorBatchRepository iTrxPUKVendorBatchRepository;

    public boolean isExistInInterval(String nik, int interval) {
        return iTrxPUKVendorBatchRepository.checkRequestInterval(LocalDateTime.now().minusSeconds(interval), LocalDateTime.now(), nik) > 0;
    }

    public TrxPUKVendorBatch getTrxPUKVendorBatchByBatchId(String batchId) {
        return iTrxPUKVendorBatchRepository.getTrxPUKVendorBatchByBatchId(batchId);
    }
}
