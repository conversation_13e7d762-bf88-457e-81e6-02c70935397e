package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.ApprovalKewenanganLimitModel;
import com.btpns.fin.model.entity.MsApprovalKewenanganLimit;
import com.btpns.fin.model.request.ReqApprovalKewenanganLimit;
import com.btpns.fin.model.response.ResApprovalKewenanganLimit;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsApprovalKewenanganLimitRepository;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
@Transactional
public class MsApprovalKewenanganLimitService {
    @Autowired
    private IMsApprovalKewenanganLimitRepository msApprovalKewenanganLimitRepository;

    public List<String> getApprovalLevelByNominal(boolean isKcKFO, Boolean isTunai, Double nominal) {
        if (isTunai != null && isTunai) {
            return msApprovalKewenanganLimitRepository.findTunaiApprovalLevelByNominal(isKcKFO, nominal);
        }
        return msApprovalKewenanganLimitRepository.findNonTunaiApprovalLevelByNominal(isKcKFO, nominal);
    }

    public ResponseModel<List<ApprovalKewenanganLimitModel>> getListApprovalKewenanganLimit(String officeLevel) {
        List<MsApprovalKewenanganLimit> msApprovalLimits = msApprovalKewenanganLimitRepository.findAllByOfficeLevel(officeLevel);
        List<ApprovalKewenanganLimitModel> responseDetails = msApprovalLimits.stream().map(MsApprovalKewenanganLimit::toApprovalKewenanganLimitModel).collect(Collectors.toList());

        return ResponseModel.<List<ApprovalKewenanganLimitModel>>builder().type(TYPE_APPROVAL_KEWENANGAN_LIMIT_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    public ResponseModel<ApprovalKewenanganLimitModel> getMsApprovalKewenanganLimitById(String id) {
        ResponseModel<ApprovalKewenanganLimitModel> response = new ResponseModel<>();
        MsApprovalKewenanganLimit msApprovalKewenanganLimit = msApprovalKewenanganLimitRepository.findMsApprovalKewenanganLimitById(id);
        if (msApprovalKewenanganLimit != null){
            response = ResponseModel.<ApprovalKewenanganLimitModel>builder().type(TYPE_APPROVAL_KEWENANGAN_LIMIT_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(msApprovalKewenanganLimit.toApprovalKewenanganLimitModel()).build();
        }
        return response;
    }

    public ResponseModel<ResApprovalKewenanganLimit> addOrEditMsApprovalKewenanganLimit(ReqApprovalKewenanganLimit request) {
        ResponseModel<ResApprovalKewenanganLimit> response = new ResponseModel<>();

        MsApprovalKewenanganLimit msApprovalKewenanganLimit = msApprovalKewenanganLimitRepository.findMsApprovalKewenanganLimitById(request.getId());
        if (msApprovalKewenanganLimit != null && EDIT.equalsIgnoreCase(request.getType())){
            response = updateMsApprovalKewenanganLimit(msApprovalKewenanganLimit, request);
        }else {
            response = saveMsApprovalKewenanganLimit(request);
        }

        return response;
    }

    private ResponseModel<ResApprovalKewenanganLimit> saveMsApprovalKewenanganLimit(ReqApprovalKewenanganLimit request) {
        ResponseModel<ResApprovalKewenanganLimit> response = new ResponseModel<>();

        String newId = CommonHelper.genereteNewParamDetailId(msApprovalKewenanganLimitRepository.getLastId());
        MsApprovalKewenanganLimit msApprovalKewenanganLimit = msApprovalKewenanganLimitRepository.save(mapToMsApprovalKewenanganLimit(request, newId));
        if (msApprovalKewenanganLimit != null){
            response = ResponseModel.<ResApprovalKewenanganLimit>builder().type(TYPE_APPROVAL_KEWENANGAN_LIMIT_ADD_EDIT).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(mapToResApprovalKewenanganLimit(ADD, newId)).build();
        }

        return response;
    }

    private MsApprovalKewenanganLimit mapToMsApprovalKewenanganLimit(ReqApprovalKewenanganLimit request, String newId) {
        MsApprovalKewenanganLimit msApprovalKewenanganLimit = new MsApprovalKewenanganLimit();
        msApprovalKewenanganLimit.setId(newId);
        msApprovalKewenanganLimit.setApprovalOccupation(request.getApprovalOccupation());
        msApprovalKewenanganLimit.setKcKFO(request.isKcKFO());
        msApprovalKewenanganLimit.setHO(request.isHO());
        msApprovalKewenanganLimit.setMinTunai(request.getMinTunai());
        msApprovalKewenanganLimit.setMaxTunai(request.getMaxTunai());
        msApprovalKewenanganLimit.setMinNonTunai(request.getMinNonTunai());
        msApprovalKewenanganLimit.setMaxNonTunai(request.getMaxNonTunai());
        msApprovalKewenanganLimit.setUpdatedAt(LocalDateTime.now());
        return msApprovalKewenanganLimit;
    }

    private ResponseModel<ResApprovalKewenanganLimit> updateMsApprovalKewenanganLimit(MsApprovalKewenanganLimit exisitingMsApprovalKewenanganLimit, ReqApprovalKewenanganLimit request) {
        ResponseModel<ResApprovalKewenanganLimit> response = new ResponseModel<>();

        MsApprovalKewenanganLimit msApprovalKewenanganLimit = msApprovalKewenanganLimitRepository.save(upadateMsApprovalKewenanganLimit(exisitingMsApprovalKewenanganLimit, request));
        if (msApprovalKewenanganLimit != null){
            response = ResponseModel.<ResApprovalKewenanganLimit>builder().type(TYPE_APPROVAL_KEWENANGAN_LIMIT_ADD_EDIT).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(mapToResApprovalKewenanganLimit(EDIT, request.getId())).build();
        }

        return response;
    }

    private MsApprovalKewenanganLimit upadateMsApprovalKewenanganLimit(MsApprovalKewenanganLimit exisitingMsApprovalKewenanganLimit, ReqApprovalKewenanganLimit request) {
        MsApprovalKewenanganLimit msApprovalKewenanganLimit = exisitingMsApprovalKewenanganLimit;
        msApprovalKewenanganLimit.setApprovalOccupation(!request.getApprovalOccupation().isEmpty() ? request.getApprovalOccupation() : exisitingMsApprovalKewenanganLimit.getApprovalOccupation());
        msApprovalKewenanganLimit.setKcKFO(request.isKcKFO());
        msApprovalKewenanganLimit.setHO(request.isHO());
        msApprovalKewenanganLimit.setMinTunai(request.getMinTunai() != null ? request.getMinTunai() : exisitingMsApprovalKewenanganLimit.getMinTunai());
        msApprovalKewenanganLimit.setMaxTunai(request.getMaxTunai() != null ? request.getMaxTunai() : exisitingMsApprovalKewenanganLimit.getMaxTunai());
        msApprovalKewenanganLimit.setMinNonTunai(request.getMinNonTunai() != null ? request.getMinNonTunai() : exisitingMsApprovalKewenanganLimit.getMinNonTunai());
        msApprovalKewenanganLimit.setMaxNonTunai(request.getMaxNonTunai() != null ? request.getMaxNonTunai() : exisitingMsApprovalKewenanganLimit.getMaxNonTunai());
        msApprovalKewenanganLimit.setUpdatedAt(LocalDateTime.now());
        return msApprovalKewenanganLimit;
    }

    public ResponseModel<ResApprovalKewenanganLimit> deleteMsApprovalKewenanganLimit(String id) {
        ResponseModel<ResApprovalKewenanganLimit> response = new ResponseModel<>();
        if(StringUtils.isNotBlank(id) && msApprovalKewenanganLimitRepository.findById(id) != null) {
            ResponseStatus status = FAILED;
            if (deleteMsApprovalKewenanganLimitById(id) > 0) {
                status = SUCCESS;
            }
            response = ResponseModel.<ResApprovalKewenanganLimit>builder().type(TYPE_APPROVAL_KEWENANGAN_LIMIT_DELETE).status(status.getCode()).statusDesc(status.getValue()).details(mapToResApprovalKewenanganLimit(DELETE, id)).build();
        }
        return response;

    }

    private int deleteMsApprovalKewenanganLimitById(String id) {
        return msApprovalKewenanganLimitRepository.deleteMsApprovalKewenanganLimitById(id);
    }

    private ResApprovalKewenanganLimit mapToResApprovalKewenanganLimit(String type, String id) {
        ResApprovalKewenanganLimit result = new ResApprovalKewenanganLimit();
        result.setType(type);
        result.setId(id);
        return result;

    }
}
