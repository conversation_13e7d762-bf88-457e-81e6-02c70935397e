package com.btpns.fin.service;

import com.btpns.fin.model.AttachmentModel;
import io.minio.*;
import io.minio.errors.*;
import liquibase.util.file.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import static com.btpns.fin.helper.Constants.*;

@Service
public class MinioService {
    @Autowired
    MinioClient minioClient;
    @Value("${minio.default.bucket.name}")
    String bucketName;
    @Value("${minio.uri}")
    public String minioUrl;
    @Value("${image.base.uri}")
    String imageBaseUri;

    public Map<String, String> uploadFile(MultipartFile file, String filepath) throws Exception{
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", file.getContentType());

        minioClient.putObject(
            PutObjectArgs.builder()
                .bucket(bucketName)
                .object(filepath)
                .stream(file.getInputStream(), file.getSize(), -1)
                .build());

        Map<String, String> result = new HashMap<>();

        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        String filename = FilenameUtils.getBaseName(file.getOriginalFilename());
        String convertedFileName = removeSpecialCharInFileName(filename);
        String newFileName = convertedFileName + "." + extension;
        result.put(CONTENT_URL, imageBaseUri+ '/'+ bucketName + '/' + filepath);
        result.put(CONTENT_FILEPATH, filepath);
        result.put(CONTENT_FILENAME, newFileName);
        result.put(CONTENT_TYPE, file.getContentType());
        return result;
    }

    public Map<String, String> uploadReportFile(byte[] b, String filepath, String fileName) throws Exception{
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", CONTENT_TYPE_CSV);

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(b);
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(filepath)
                        .stream(byteArrayInputStream, byteArrayInputStream.available(), -1)
                        .build());

        Map<String, String> result = new HashMap<>();

        result.put(CONTENT_URL, imageBaseUri+ '/'+ bucketName + '/' + filepath);
        result.put(CONTENT_FILEPATH, filepath);
        result.put(CONTENT_FILENAME, fileName + CSV_EXTENSION);
        result.put(CONTENT_TYPE, CONTENT_TYPE_CSV);
        return result;
    }

    public Map<String, String> uploadReportFileAsGzip(byte[] b, String filepath, String fileName) throws Exception{
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", CONTENT_TYPE_GZIP);

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(b);
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(filepath)
                        .stream(byteArrayInputStream, byteArrayInputStream.available(), -1)
                        .build());

        Map<String, String> result = new HashMap<>();

        result.put(CONTENT_URL, imageBaseUri+ '/'+ bucketName + '/' + filepath);
        result.put(CONTENT_FILEPATH, filepath);
        result.put(CONTENT_FILENAME, fileName);
        result.put(CONTENT_TYPE, CONTENT_TYPE_GZIP);
        return result;
    }

    public Map<String, String> uploadReportFilePDF(byte[] bytes, String filepath, String fileName) throws Exception{
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", CONTENT_TYPE_PDF);

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(filepath)
                        .stream(byteArrayInputStream, byteArrayInputStream.available(), -1)
                        .build());

        Map<String, String> result = new HashMap<>();

        String filename = fileName + PDF_EXTENSION;
        result.put(CONTENT_URL, imageBaseUri+ '/'+ bucketName + '/' + filepath);
        result.put(CONTENT_FILEPATH, filepath);
        result.put(CONTENT_FILENAME, filename);
        result.put(CONTENT_TYPE, CONTENT_TYPE_PDF);
        return result;
    }

    public Map<String, String> uploadFileExcel(byte[] bytes, String filePath, String fileName) throws Exception {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", CONTENT_TYPE_EXCEL);

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(filePath)
                        .stream(byteArrayInputStream, byteArrayInputStream.available(), -1)
                        .build());

        Map<String, String> result = new HashMap<>();

        String filename = fileName + XLSX_EXTENSION;
        result.put(CONTENT_URL, imageBaseUri+ '/'+ bucketName + '/' + filePath);
        result.put(CONTENT_FILEPATH, filePath);
        result.put(CONTENT_FILENAME, filename);
        result.put(CONTENT_TYPE, CONTENT_TYPE_EXCEL);

        return result;
    }

    public String getBase64FromFilePathMinio(String filepath) throws Exception{
        InputStream stream = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(filepath)
                        .build());
        byte[] bytes = stream.readAllBytes();
        return Base64.getEncoder().encodeToString(bytes);
    }

    private void createBucketIfNotExist() throws Exception {
        if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }

    }

    public static String removeSpecialCharInFileName(String fileName){
        String specialChar = "[^a-zA-Z0-9]+";
        return fileName.replaceAll(specialChar, "");
    }

}
