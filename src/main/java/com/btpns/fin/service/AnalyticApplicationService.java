package com.btpns.fin.service;

import com.btpns.fin.model.entity.ApplicationMonthly;
import com.btpns.fin.repository.IAnalyticApplicationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AnalyticApplicationService {
    @Autowired
    IAnalyticApplicationRepository iAnalyticApplicationRepository;

    public List<ApplicationMonthly> getTopApplicationMonthly(String startEffectiveDTPeriod, String endEffectiveDTPeriod) {
        return iAnalyticApplicationRepository.getTopAppMonthly(startEffectiveDTPeriod, endEffectiveDTPeriod);
    }
}
