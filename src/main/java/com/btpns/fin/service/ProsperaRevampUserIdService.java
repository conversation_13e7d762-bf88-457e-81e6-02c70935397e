package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.ProsperaUserIDModel;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class ProsperaRevampUserIdService {
    @Autowired
    private IMsOfficerNRRepository iMsOfficerNRRepository;

    @Autowired
    private IMsCabangRepository iMsCabangRepository;

    @Autowired
    private IMsProsperaRoleRepository iMsProsperaRoleRepository;

    @Autowired
    MinioService minioService;

    public ResponseModel<ResponseListModel<ProsperaUserIDModel>> getListProsperaRevampUserID(Integer pageSize, Integer pageNumber, String searchFlag, String searchData, String filterStatus, String filterRole, String filterOffice) {
        if (filterOffice.equalsIgnoreCase(KODE_KANTOR_PUSAT)) {
            filterOffice = PREFIX_HO;
        }
        if (!filterRole.equalsIgnoreCase(FILTER_CODE_ALL)) {
            filterRole = this.getProsperaRoleCode(filterRole).toString();
        }

        Page<MsOfficerNR> prosperaUserIdPageable = getProsperaRevampUserID(searchFlag, searchData, filterStatus, filterRole, filterOffice, PageRequest.of(pageNumber - 1, pageSize));

        List<ProsperaUserIDModel> prosperaUserIDModelList = prosperaUserIdPageable.getContent().stream().map(MsOfficerNR::toProsperaUserID).collect(Collectors.toList());
        ResponseListModel<ProsperaUserIDModel> details = ResponseListModel.<ProsperaUserIDModel>builder()
                .data(prosperaUserIDModelList)
                .page(pageNumber).limit(pageSize)
                .totalPages(prosperaUserIdPageable.getTotalPages())
                .totalItems(prosperaUserIdPageable.getTotalElements())
                .build();

        return ResponseModel.<ResponseListModel<ProsperaUserIDModel>>builder().type(TYPE_MS_OFFICER_MANAGEMENT_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(details).build();
    }

    private Page<MsOfficerNR> getProsperaRevampUserID(String searchFlag, String searchData, String filterStatus, String filterRole, String filterOffice, Pageable pageable) {
        boolean isKFO = filterOffice.startsWith(PREFIX_CABANG);

        if (searchData.isBlank()) {
            return this.iMsOfficerNRRepository.findAllProsperaByStatusByRoleByOffice(filterStatus, filterRole, filterOffice, isKFO, pageable);
        }
        if (SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag)) {
            return this.iMsOfficerNRRepository.findProsperaByNikByStatusByRoleByOffice(searchData, filterStatus, filterRole, filterOffice, isKFO, pageable);
        }

        if (SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag)) {
            return this.iMsOfficerNRRepository.findProsperaByOfficerNameByStatusByRoleByOffice(searchData, filterStatus, filterRole, filterOffice, isKFO, pageable);
        }

        if (SEARCH_FLAG_LOGIN_NAME.equalsIgnoreCase(searchFlag)) {
            return this.iMsOfficerNRRepository.findProsperaByLoginNameByStatusByRoleByOffice(searchData, filterStatus, filterRole, filterOffice, isKFO, pageable);
        }
        return new PageImpl<>(Collections.emptyList());
    }

    private Integer getProsperaRoleCode(String filterRole) {
        MsProsperaRole msProsperaRole = this.iMsProsperaRoleRepository.findAllByTemaRoleCode(filterRole);
        return msProsperaRole.getProsperaRoleCode();
    }

    public ResponseModel<ResUploadModel> getProsperaUserIDCsv(String searchFlag, String searchData, String filterStatus, String filterRole, String filterOffice) throws Exception{
        ResUploadModel result = generateReportProsperaCsv(searchFlag, searchData, filterStatus, filterRole, filterOffice);
        return buildResponse(SUCCESS, result);
    }

    private ResUploadModel generateReportProsperaCsv(String searchFlag, String searchData, String filterStatus, String filterRole, String filterOffice) throws Exception {
        List<MsOfficerNR> msOfficerList = getProsperaRevampUserID(searchFlag, searchData, filterStatus, filterRole, filterOffice, Pageable.unpaged()).getContent();
        List<ProsperaUserIDModel> prosperaUserIDs = msOfficerList.stream().map(MsOfficerNR::toProsperaUserID).collect(Collectors.toList());

        byte[] csvByte = makeProsepraCsv(prosperaUserIDs);

        Map<String, String> result = minioService.uploadReportFile(csvByte, DocumentHelper.generateReportFilePath(REPORT_PROSPERA_REVAMP_FILE_NAME, CSV_EXTENSION), REPORT_PROSPERA_REVAMP_FILE_NAME);

        return new ResUploadModel(result);
    }

    public ResFileDownload directDownloadProsperaRevampUserIDCsv(String searchFlag, String searchData, String filterStatus, String filterRole, String filterOffice) throws Exception {
        List<MsOfficerNR> msOfficerList = getProsperaRevampUserID(searchFlag, searchData, filterStatus, filterRole, filterOffice, Pageable.unpaged()).getContent();
        List<ProsperaUserIDModel> prosperaUserIDs = msOfficerList.stream().map(MsOfficerNR::toProsperaUserID).collect(Collectors.toList());

        byte[] csvByte = makeProsepraCsv(prosperaUserIDs);

        return new ResFileDownload(csvByte, CommonHelper.concateTwoString(REPORT_PROSPERA_REVAMP_FILE_NAME, CSV_EXTENSION));
    }

    public ResUploadModel generateProsperaCsv() throws Exception {
        List<MsOfficerNR> msOfficerList = iMsOfficerNRRepository.findProsperaByRoleAndStatus(OFFICER_STATUS_ACTIVE_CODE);
        List<ProsperaUserIDModel> prosperaUserIDs = msOfficerList.stream().map(MsOfficerNR::toProsperaUserID).collect(Collectors.toList());

        byte[] csvByte = makeProsepraCsv(prosperaUserIDs);

        Map<String, String> result = minioService.uploadReportFile(csvByte, DocumentHelper.generateReportFilePath(REPORT_PROSPERA_REVAMP_FILE_NAME, CSV_EXTENSION), REPORT_PROSPERA_REVAMP_FILE_NAME);

        return new ResUploadModel(result);
    }

    private byte[] makeProsepraCsv(List<ProsperaUserIDModel> prosperaUserIDs) throws Exception {
        StringBuffer csv = new StringBuffer();

        csv.append("\""+NIK_PROSPERA_COL_NAME+"\",\""+NAMA_COL_NAME+"\",\""+ROLE_NAME_COL_NAME+"\",\""+LOGIN_NAME_COL_NAME+"\",\""+KODE_KANTOR_COL_NAME+"\",\""+NAMA_KANTOR_COL_NAME+"\",\""+LIMIT_BWMP_COL_NAME+"\",\""+KODE_PEMBINA_SENTRA_COL_NAME+"\",\""+STATUS_COL_NAME+"\""+"\r\n");
        prosperaUserIDs.forEach(data -> {
            csv.append("\""+"\'"+data.getNik()+"\",\""+data.getOfficerName()+"\",\""+data.getRoleName()+"\",\""+data.getLoginName()+"\",\""+"\'"+data.getMmsCode()+"\",\""+data.getOfficeName()+"\",\""+data.getAmtApprovalLimit()+"\",\""+data.getOfficerCode()+"\",\""+data.getOfficerStatusDesc()+"\""+"\r\n");
        });

        return csv.toString().getBytes("windows-1252");
    }

    private ResponseModel<ResUploadModel> buildResponse(ResponseStatus status, ResUploadModel result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(TYPE_MS_OFFICER_DOWNLOAD);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }
}
