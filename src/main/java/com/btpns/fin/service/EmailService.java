package com.btpns.fin.service;

import com.btpns.fin.model.BodyEmail;
import com.btpns.fin.repository.IEmailServerRepository;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Response;
import java.io.IOException;
import java.util.Map;

@Service
public class EmailService {
    private static final Logger log = LoggerFactory.getLogger(EmailService.class);
    @Autowired
    private Gson gson;
    @Autowired
    private IEmailServerRepository repository;
    @Value("${mail.enable}")
    private boolean isMailEnable;
    public boolean sendEmail(Map<String, String> header, BodyEmail bodyEmail){
        if(isMailEnable) {
            try {
                Response<Object> execute = repository.sendMail(header, bodyEmail).execute();
                if (execute.isSuccessful()) {
                    log.info("Send mail success to {}", gson.toJson(bodyEmail.getTo()));
                    return true;
                } else {
                    log.warn("Send mail Failed {}", gson.toJson(bodyEmail.getTo()));
                }
            } catch (IOException e) {
                log.error("Fail executing Send Mail. Error: ", gson.toJson(bodyEmail.getTo()), e);
            }
        } else {
            log.info("Mail send is disable, mail not send {}", gson.toJson(bodyEmail.getTo()));
        }
        return false;
    }
}
