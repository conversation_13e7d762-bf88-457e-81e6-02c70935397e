package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.AttachmentModel;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.repository.*;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Component
public class SchedulerService {
    private static final Logger log = LoggerFactory.getLogger(SchedulerService.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    EmailNotificationService emailNotificationService;
    @Autowired
    ProsperaRevampUserIdService prosperaRevampUserIdService;
    @Autowired
    T24UserIdService  t24UserIdService;
    @Autowired
    ISchedulerRepository iSchedulerRepository;
    @Autowired
    ITrxEmailAudittrailRepository iTrxEmailAudittrailRepository;
    @Autowired
    ITrxUPMReminderRepository iTrxUPMReminderRepository;
    @Autowired
    TrxUpmRoleService upmRoleService;
    @Autowired
    MsEmployeeService msEmployeeService;
    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;
    @Autowired
    TrxFuidRequestService trxFuidRequestService;
    @Autowired
    IMsHolidayListRepository msHolidayListRepository;
    @Autowired
    IPUKApprovalReminderRepository pukApprovalReminderRepository;
    @Autowired
    ITrxFuidApprovalRepository trxFuidApprovalRepository;
    @Autowired
    ITrxSetupParamApprovalRepository trxSetupParamApprovalRepository;
    @Autowired
    RequestHelper requestHelper;
    @Autowired
    CommonHelper commonHelper;

    public void executeRetryEmail() {
        log.info("Execute retry email scheduler every 15 minutes on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));
        LocalDateTime currentDateTime = LocalDate.now().atStartOfDay().withHour(LocalTime.now().getHour()).withMinute(LocalTime.now().getMinute());
        try {
            Scheduler scheduler = iSchedulerRepository.save(createScheduler(currentDateTime, SCHEDULER_RETRY_EMAIL, SCHEDULER_STATUS_START));

            List<TrxEmailAudittrail> trxEmailAudittrail = iTrxEmailAudittrailRepository.findByStatus(FAILED.toString());
            if (trxEmailAudittrail.size() > 0){
                trxEmailAudittrail.forEach(data -> {
                    emailNotificationService.sendEmail(Mapper.toEmailNotification(data.getEmailSubject(), data.getEmailDestination(),
                    data.getEmailCc(), data.getEmailBcc(), data.getEmailMessage(), data.getEmailAttachments().isEmpty() ? Collections.EMPTY_LIST : gson.fromJson(data.getEmailAttachments(), new TypeToken<ArrayList<AttachmentModel>>(){}.getType())), data.getRequestId());
                });
            }else {
                log.info("There is no failed emails that need to be retry");
            }

            scheduler.setStatus(SCHEDULER_STATUS_END);
            iSchedulerRepository.save(scheduler);
        } catch (Exception exception){
            if (exception instanceof DataIntegrityViolationException){
                log.info("Scheduler in progress on other instance {}", currentDateTime);
            } else {
                log.warn("Found exception ", exception);
            }
        }
    }

    private Scheduler createScheduler(LocalDateTime currentDateTime, String schedulerKey, String schedulerStatus) {
        Scheduler scheduler = new Scheduler();

        scheduler.setExecuteDateTime(currentDateTime);
        scheduler.setStatus(schedulerStatus);
        scheduler.setSchedulerKey(schedulerKey);

        return scheduler;
    }

    public void executeSaveUARReminderFlag() {
        log.info("Execute save UAR reminder flag scheduler quarterly on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));
        LocalDateTime currentDateTime = LocalDateTime.now();
        try {
            Scheduler scheduler = createScheduler(currentDateTime, SCHEDULER_UAR_REMINDER, SCHEDULER_STATUS_END);
            iSchedulerRepository.save(scheduler);

            iTrxUPMReminderRepository.saveAll(buildUpmUARReminders(TYPE_UAR_REMINDER));
        } catch (Exception exception) {
            if (exception instanceof DataIntegrityViolationException){
                log.info("Scheduler in progress on other instance {}", currentDateTime);
            } else {
                log.warn("Found exception ", exception);
            }
        }
    }

    private List<TrxUPMReminder> buildUpmUARReminders(String type) {
        List<TrxUPMReminder> upmUARReminders = new ArrayList<>();
        List<TrxUpmRole> upmList =  upmRoleService.getListTrxUpmRole();
        for (TrxUpmRole upm : upmList) {
            String nikUPM = upm.getNik();
            if (!CommonHelper.isBot(upm) && CommonHelper.isNonViewerUPMRole(upm)) {
                upmUARReminders.add(new TrxUPMReminder(type, false, nikUPM));
            }
        }

        return upmUARReminders;
    }

    public void executeSaveMonthlyReminderFlag() {
        log.info("Execute save monitoring reminder flag scheduler monthly on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));
        LocalDateTime currentDateTime = LocalDateTime.now();

        try {
            Scheduler scheduler = createScheduler(currentDateTime, SCHEDULER_MONTHLY_MONITORING_REMINDER, SCHEDULER_STATUS_END);
            iSchedulerRepository.save(scheduler);

            iTrxUPMReminderRepository.saveAll(buildUpmUARReminders(TYPE_MONTHLY_REMINDER));
        } catch (Exception exception) {
            if (exception instanceof DataIntegrityViolationException){
                log.info("Scheduler in progress on other instance {}", currentDateTime);
            } else {
                log.warn("Found exception ", exception);
            }
        }
    }

    public void executeSendDataCoreSystemToUPM() {
        log.info("Execute send data core system monthly to UPM on {}", DateTimeHelper.getDateCreateDate(LocalDateTime.now()));
        LocalDateTime currentDateTime = LocalDateTime.now();
        try {
            Scheduler scheduler = iSchedulerRepository.save(createScheduler(currentDateTime, SCHEDULER_MONTHLY_SEND_DATA_CORE_TO_UPM, SCHEDULER_STATUS_START));

            AttachmentModel prospera = gson.fromJson(gson.toJson(prosperaRevampUserIdService.generateProsperaCsv()), AttachmentModel.class);
            AttachmentModel t24 = gson.fromJson(gson.toJson(t24UserIdService.generateT24Csv()), AttachmentModel.class);

            emailNotificationService.sendDataCoreSystemToUpmTemaNotification(prospera, t24);

            scheduler.setStatus(SCHEDULER_STATUS_END);
            iSchedulerRepository.save(scheduler);
        } catch (Exception exception) {
            if (exception instanceof DataIntegrityViolationException){
                log.info("Scheduler in progress on other instance {}", currentDateTime);
            } else {
                log.warn("Found exception ", exception);
            }
        }
    }

    public void executeSendReminderEmailTicketWaitingApprovalPUK() {
        LocalDateTime currentDateTime = LocalDateTime.now();
        log.info("Execute send reminder email ticket waiting approval PUK on {}", DateTimeHelper.getDateCreateDate(currentDateTime));
        try {
            Scheduler scheduler = iSchedulerRepository.save(createScheduler(currentDateTime, SCHEDULER_SEND_REMINDER_EMAIL_TICKET_WAITING_APPROVAL_PUK, SCHEDULER_STATUS_START));

            Optional<MsHolidayList> holiday = msHolidayListRepository.getMsHolidayByDate(LocalDate.now());
            if (!holiday.isPresent()){
                List<PUKApprovalReminder> pukApprovalReminderList = pukApprovalReminderRepository.getListReminderApprovalPUK();
                if (!pukApprovalReminderList.isEmpty()){
                    for (PUKApprovalReminder data : pukApprovalReminderList) {
                        updateCounterReminderAndSendEmail(data);
                    }
                }else {
                    log.info("There is no pending approval that need to be remind");
                }
            }else {
                log.info("Today is holiday");
            }

            scheduler.setStatus(SCHEDULER_STATUS_END);
            iSchedulerRepository.save(scheduler);
        } catch (Exception exception) {
            if (exception instanceof DataIntegrityViolationException){
                log.info("Scheduler in progress on other instance {}", currentDateTime);
            } else {
                log.warn("Found exception ", exception);
            }
        }
    }

    private void updateCounterReminderAndSendEmail(PUKApprovalReminder pukApprovalReminder) throws Exception {
        String pukNik = "";
        Boolean isFU = false;
        Boolean isSP = false;
        Boolean isWaitingPuk1 = false;
        Boolean isWaitingPuk2 = false;

        TrxFuidApproval tfa = new TrxFuidApproval();
        if (CommonHelper.isTicketFuid(pukApprovalReminder.getTicketId())){
            isFU = true;
            tfa = trxFuidApprovalRepository.findByTicketId(pukApprovalReminder.getTicketId());
            if (CURR_STATUS_WAITING_PUK1.equalsIgnoreCase(tfa.getCurrentState())){
                tfa.setPuk1ApprovalReminder(CommonHelper.generateCounterReminder(tfa.getPuk1ApprovalReminder()));
                pukNik = tfa.getPuk1NIK();
                isWaitingPuk1 = true;
            }
            if (CURR_STATUS_WAITING_PUK2.equalsIgnoreCase(tfa.getCurrentState())){
                tfa.setPuk2ApprovalReminder(CommonHelper.generateCounterReminder(tfa.getPuk2ApprovalReminder()));
                pukNik = tfa.getPuk2NIK();
                isWaitingPuk2 = true;
            }
            trxFuidApprovalRepository.save(tfa);
        }

        TrxSetupParamApproval tspa = new TrxSetupParamApproval();
        if (CommonHelper.isTicketSP(pukApprovalReminder.getTicketId())){
            isSP = true;
            tspa = trxSetupParamApprovalRepository.findByTicketId(pukApprovalReminder.getTicketId());
            if (CURR_STATUS_WAITING_PUK1.equalsIgnoreCase(tspa.getCurrentState())){
                tspa.setPuk1ApprovalReminder(CommonHelper.generateCounterReminder(tspa.getPuk1ApprovalReminder()));
                pukNik = tspa.getPuk1NIK();
                isWaitingPuk1 = true;
            }
            if (CURR_STATUS_WAITING_PUK2.equalsIgnoreCase(tspa.getCurrentState())){
                tspa.setPuk2ApprovalReminder(CommonHelper.generateCounterReminder(tspa.getPuk2ApprovalReminder()));
                pukNik = tspa.getPuk2NIK();
                isWaitingPuk2 = true;
            }
            trxSetupParamApprovalRepository.save(tspa);
        }

        sendEmailNotification(tfa, tspa, pukApprovalReminder, isFU, isSP, isWaitingPuk1, isWaitingPuk2, pukNik);
    }

    private void sendEmailNotification(TrxFuidApproval tfa, TrxSetupParamApproval tspa, PUKApprovalReminder pukApprovalReminder, Boolean isFU, Boolean isSP, Boolean isWaitingPuk1, Boolean isWaitingPuk2, String pukNik) throws Exception {
        MsEmployee waitingPUK = msEmployeeService.getMsEmployeeByNik(pukNik);
        HashMap<String, String> mapPUKDirector = msEmployeeDirectorService.isPukDirectorByNikOptima(pukNik);
        if (isWaitingPuk1){
            sendEmailNotificationWaitingPUK1(tfa, tspa, pukApprovalReminder, isFU, isSP, waitingPUK, mapPUKDirector, isWaitingPuk1);
        }
        if (isWaitingPuk2){
            sendEmailNotificationWaitingPUK2(tfa, tspa, pukApprovalReminder, isFU, isSP, waitingPUK, mapPUKDirector, isWaitingPuk1);
        }
    }

    private void sendEmailNotificationWaitingPUK1(TrxFuidApproval tfa, TrxSetupParamApproval tspa, PUKApprovalReminder pukApprovalReminder, Boolean isFU, Boolean isSP, MsEmployee waitingPUK, HashMap<String, String> mapPUKDirector, Boolean isWaitingPuk1) {
        if (isFU){
            String ccNIKDirectPUK = commonHelper.validateCCNikDirectPuk(tfa.getTrxFuidRequest().getNikRequester(), tfa.getPuk1NIK());
            emailNotificationService.sendReminderEmailTicketFuidWaitingApprovalPUK(tfa, pukApprovalReminder, waitingPUK, !ccNIKDirectPUK.equalsIgnoreCase(EMPTY) ? requestHelper.getEmailUsingNik(ccNIKDirectPUK) : EMPTY, mapPUKDirector, isWaitingPuk1);
        }
        if (isSP) {
            String ccNIKDirectPUK = commonHelper.validateCCNikDirectPuk(tspa.getTrxSetupParamRequest().getNikRequester(), tspa.getPuk1NIK());
            emailNotificationService.sendReminderEmailTicketSPWaitingApprovalPUK(tspa, pukApprovalReminder, waitingPUK, !ccNIKDirectPUK.equalsIgnoreCase(EMPTY) ? requestHelper.getEmailUsingNik(ccNIKDirectPUK) : EMPTY, mapPUKDirector, isWaitingPuk1);
        }
    }

    private void sendEmailNotificationWaitingPUK2(TrxFuidApproval tfa, TrxSetupParamApproval tspa, PUKApprovalReminder pukApprovalReminder, Boolean isFU, Boolean isSP, MsEmployee waitingPUK, HashMap<String, String> mapPUKDirector, Boolean isWaitingPuk1) throws Exception {
        if (isFU){
            List<String> skippedNikPUKs = msEmployeeService.getListSkippedNikPukFuid(tfa);
            emailNotificationService.sendReminderEmailTicketFuidWaitingApprovalPUK(tfa, pukApprovalReminder, waitingPUK, emailNotificationService.getEmailCc(skippedNikPUKs), mapPUKDirector, isWaitingPuk1);
        }
        if (isSP){
            List<String> skippedNikPUKs = msEmployeeService.getListSkippedNikPukSp(tspa);
            emailNotificationService.sendReminderEmailTicketSPWaitingApprovalPUK(tspa, pukApprovalReminder, waitingPUK, emailNotificationService.getEmailCc(skippedNikPUKs), mapPUKDirector, isWaitingPuk1);
        }
    }
}
