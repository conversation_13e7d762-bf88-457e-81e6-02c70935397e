package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.helper.Mapper;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.TrxSetupParamRequestDTO;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResTrxFuidRequest;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsCabangRepository;
import com.btpns.fin.repository.IMsSystemParamDetailRepository;
import com.btpns.fin.repository.ITrxAudittrailRepository;
import com.btpns.fin.repository.ITrxSetupParamRequestRepository;
import com.google.gson.Gson;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class TrxSetupParamRequestService {
    private static final Logger logger = LoggerFactory.getLogger(TrxSetupParamRequestService.class);

    @Autowired
    ITrxSetupParamRequestRepository trxSetupParamRequestRepository;

    @Autowired
    TrxSetupParamApprovalService trxSetupParamApprovalService;

    @Autowired
    TrxSetupParamRequestAplikasiService trxSetupParamRequestAplikasiService;

    @Autowired
    IMsCabangRepository iMsCabangRepository;

    @Autowired
    IMsSystemParamDetailRepository iMsSystemParamDetailRepository;

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxCommentService trxCommentService;

    @Autowired
    ITrxAudittrailRepository iTrxAudittrailRepository;

    @Autowired
    MinioService minioService;

    @Autowired
    Mapper mapper;

    @PersistenceContext
    private EntityManager entityManager;

    public TrxSetupParamRequest getTrxSetupParamRequestByTicketId(String ticketId) {
        return trxSetupParamRequestRepository.getOne(ticketId);
    }

    public TrxSetupParamRequestDTO getTrxSetupParamRequestByTicketIdData(String ticketId) {
        TrxSetupParamRequest ret = trxSetupParamRequestRepository.getOne(ticketId);
        entityManager.detach(ret);
        TrxSetupParamRequestDTO dto = mapTrxFspRequestToTrxFspRequestDTO(ret);
        return dto;
    }

    private TrxSetupParamRequestDTO mapTrxFspRequestToTrxFspRequestDTO(TrxSetupParamRequest original) {
        if (original == null) {
            return null;
        }

        TrxSetupParamRequestDTO dto = new TrxSetupParamRequestDTO();

        dto.setTicketId(original.getTicketId());
        dto.setCreateDateTime(original.getCreateDateTime());
        dto.setTanggalEfektif(original.getTanggalEfektif());
        dto.setDataNik(original.getDataNik());
        dto.setDataNamaLengkap(original.getDataNamaLengkap());
        dto.setDataJabatan(original.getDataJabatan());
        dto.setDataKodeCabang(original.getDataKodeCabang());
        dto.setDataNamaCabang(original.getDataNamaCabang());
        dto.setDataTelepon(original.getDataTelepon());
        dto.setNikRequester(original.getNikRequester());
        dto.setAplikasi(original.getAplikasi());
        dto.setParameterLama(original.getParameterLama());
        dto.setParameterBaru(original.getParameterBaru());
        dto.setTrxSetupParamApproval(mappingTrxSetupParamApprovalDTO(original.getTrxSetupParamApproval()));
        dto.setAttachment(original.getAttachment());
        dto.setFileName(original.getFileName());
        dto.setKategoriParamId(original.getKategoriParamId());
        dto.setKategoriParamName(original.getKategoriParamName());
        dto.setAlasanPengajuan(original.getAlasanPengajuan());
        dto.setDataEmail(original.getDataEmail());
        dto.setRequestId(original.getRequestId());
        dto.setInputType(original.getInputType());

        return dto;
    }

    public TrxSetupParamApprovalDTO mappingTrxSetupParamApprovalDTO(TrxSetupParamApproval trxSetupParamApproval) {
        TrxSetupParamApprovalDTO dto = new TrxSetupParamApprovalDTO();

        dto.setId(trxSetupParamApproval.getId());
        dto.setTicketId(trxSetupParamApproval.getTicketId());
        dto.setCurrentState(trxSetupParamApproval.getCurrentState());
        dto.setCurrentStateDT(trxSetupParamApproval.getCurrentStateDT());
        dto.setTrxSetupParamRequest(trxSetupParamApproval.getTrxSetupParamRequest());
        dto.setPuk1NIK(trxSetupParamApproval.getPuk1NIK());
        dto.setPuk2NIK(trxSetupParamApproval.getPuk2NIK());
        dto.setPuk1Name(trxSetupParamApproval.getPuk1Name());
        dto.setPuk2Name(trxSetupParamApproval.getPuk2Name());
        dto.setPuk1Occupation(trxSetupParamApproval.getPuk1Occupation());
        dto.setPuk2Occupation(trxSetupParamApproval.getPuk2Occupation());
        dto.setPuk1Status(trxSetupParamApproval.getPuk1Status());
        dto.setPuk1Dt(trxSetupParamApproval.getPuk1Dt());
        dto.setPuk1Notes(trxSetupParamApproval.getPuk1Notes());
        dto.setPuk2Status(trxSetupParamApproval.getPuk2Status());
        dto.setPuk2Dt(trxSetupParamApproval.getPuk2Dt());
        dto.setPuk2Notes(trxSetupParamApproval.getPuk2Notes());
        dto.setPuk1ApprovalReminder(trxSetupParamApproval.getPuk1ApprovalReminder());
        dto.setPuk2ApprovalReminder(trxSetupParamApproval.getPuk2ApprovalReminder());
        dto.setUpmInputNIK(trxSetupParamApproval.getUpmInputNIK());
        dto.setUpmInputStatus(trxSetupParamApproval.getUpmInputStatus());
        dto.setUpmInputDt(trxSetupParamApproval.getUpmInputDt());
        dto.setUpmInputNotes(trxSetupParamApproval.getUpmInputNotes());
        dto.setUpmCheckerNotes(trxSetupParamApproval.getUpmCheckerNotes());
        dto.setUpmInputAttachment(trxSetupParamApproval.getUpmInputAttachment());
        dto.setSlaValue(trxSetupParamApproval.getSlaValue());
        dto.setSlaInfo(trxSetupParamApproval.getSlaInfo());

        return dto;
    }

    ;

    public List<TrxSetupParamRequest> getTrxSetupParamRequestAll() {
        return trxSetupParamRequestRepository.findAll();
    }

    public Page<TrxSetupParamRequest> getTrxSetupParamReqJoinApproval(String nikRequesterString, int pageNumMin1, int pageSize) {
        Pageable sortedByCreateDt = PageRequest.of(pageNumMin1, pageSize, Sort.by("createDateTime").descending());
        return trxSetupParamRequestRepository.getTrxSetupParamReqJoinApproval(nikRequesterString, sortedByCreateDt);
    }

    public Page<TrxSetupParamRequest> getTrxSetupParamReqJoinApprovalWait(String nikRequesterString, int pageNumMin1, int pageSize) {
        Pageable sortedByCreateDt = PageRequest.of(pageNumMin1, pageSize, Sort.by("createDateTime").descending());
        return trxSetupParamRequestRepository.getTrxSetupParamReqJoinApprovalWait(nikRequesterString, sortedByCreateDt);
    }

    @Transactional
    public TrxSetupParamRequest saveTrxSetupParamRequest(TrxSetupParamRequest trxSetupParamRequest) {
        trxSetupParamRequest = getNamaFromCode(trxSetupParamRequest);
        return trxSetupParamRequestRepository.save(trxSetupParamRequest);
    }

    public String getLastTicketId() {
        return trxSetupParamRequestRepository.getLastTicketId();
    }

    private String getLastTicketIdManualUPM() {
        return trxSetupParamRequestRepository.getLastTicketIdManualUPM();
    }

    @Transactional
    public TrxSetupParamRequest updateTrxSetupParamRequest(TrxSetupParamRequest trxSetupParamRequest) {
        trxSetupParamRequest = getNamaFromCode(trxSetupParamRequest);
        Optional<TrxSetupParamRequest> existTrxSetupParamRequest = trxSetupParamRequestRepository.findById(trxSetupParamRequest.getTicketId());
        if (existTrxSetupParamRequest.isPresent()) {
            trxSetupParamRequest.setCreateDateTime(existTrxSetupParamRequest.get().getCreateDateTime());
            trxSetupParamRequest.setNikRequester(existTrxSetupParamRequest.get().getNikRequester());
        }
        return trxSetupParamRequestRepository.save(trxSetupParamRequest);
    }

    public TrxSetupParamRequest getNamaFromCode(TrxSetupParamRequest trxSetupParamRequest) {
        //get nama cabang mms
        String kodeCabangMms = trxSetupParamRequest.getDataKodeCabang();
        MsCabang cabang = iMsCabangRepository.getMsCabangById(kodeCabangMms).get(0);
        String namaCabangMms = cabang.getCabangDesc();
        trxSetupParamRequest.setDataNamaCabang(namaCabangMms);

        //get kategori param name
        String kategoriId = trxSetupParamRequest.getKategoriParamId();
        MsSystemParamDetail tempMspd = iMsSystemParamDetailRepository.getMsSystemParamDetail(kategoriId);
        String kategoriName = tempMspd.getParamDetailDesc();
        trxSetupParamRequest.setKategoriParamName(kategoriName);
        return trxSetupParamRequest;
    }

    public String checkDuplicateRequestId(String requestId) throws ParseException {
        LocalDate date = LocalDate.now();
        LocalDateTime dateTime = date.atStartOfDay();
        return trxSetupParamRequestRepository.checkDuplicateRequestId(requestId, dateTime);
    }

    public boolean existInInterval(String nik, int interval) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return trxSetupParamRequestRepository.checkRequestInterval(startDate, endDate, nik) > 0;
    }

    public TrxSetupParamRequestDetailModel getTrxSetupParamRequestDetail(String ticketId, TrxSetupParamRequest savedTrxSetupParamRequest) throws Exception {
        TrxSetupParamRequestDetailModel tsprdm = mapper.toTrxSetupParamRequestDetailModel(savedTrxSetupParamRequest);

        tsprdm.setKategoriParam(buildDataAplikasiModel(savedTrxSetupParamRequest.getKategoriParamId(), savedTrxSetupParamRequest.getKategoriParamName()));

        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_SETUP_PARAM));
        String[] splitAplikasi = savedTrxSetupParamRequest.getAplikasi().split(",");
        List<String> listAplikasi = Arrays.asList(splitAplikasi);
        tsprdm.setAplikasi(buildDataAplikasiModelList(listAplikasi, msTemaApplicationMap));

        //set curr state
        tsprdm.getStatus().setValueStatus(STATUS_PERMOHONAN_MAP.get(tsprdm.getStatus().getKeyStatus()));
        //mapping for pending
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
        Date dateEfektif = sdf.parse(tsprdm.getTanggalEfektif());
        String sDateNow = sdf.format(new Date());
        Date dateNow = sdf.parse(sDateNow);
        if (tsprdm.getStatus().getKeyStatus().equals(CURR_STATUS_APPROVED) && dateEfektif.compareTo(dateNow) > 0) {
            tsprdm.getStatus().setKeyStatus(CURR_STATUS_PENDING);
            tsprdm.getStatus().setValueStatus(CURR_STATUS_PENDING_DESC);
        }
        //comment
        List<TrxComment> lTrxComment = trxCommentService.getListCommentByTicketId(ticketId);
        List<CommentModel> listCM = new ArrayList<CommentModel>();
        Iterator<TrxComment> iterator = lTrxComment.iterator();
        while (iterator.hasNext()) {
            TrxComment tcm = iterator.next();
            CommentModel cm = Mapper.toCommentModel(tcm);
            listCM.add(cm);
        }
        tsprdm.setComment(listCM);

        return tsprdm;
    }

    private List<DataAplikasiModel> buildDataAplikasiModelList(List<String> aplikasi, Map<String, MsTemaApplication> msTemaApplicationMap) {
        List<DataAplikasiModel> dataAplikasiModelList = new ArrayList<>();

        aplikasi.forEach(app -> {
            dataAplikasiModelList.add(buildDataAplikasiModel(app, msTemaApplicationMap));
        });

        return dataAplikasiModelList;
    }

    private DataAplikasiModel buildDataAplikasiModel(String app, Map<String, MsTemaApplication> msTemaApplicationMap) {
        DataAplikasiModel dataAplikasiModel = new DataAplikasiModel();

        dataAplikasiModel.setCode(msTemaApplicationMap.get(app).getParamDetailId());
        dataAplikasiModel.setDesc(msTemaApplicationMap.get(app).getParamDetailDesc());

        return dataAplikasiModel;
    }

    private DataAplikasiModel buildDataAplikasiModel(String appCode, String appDesc) {
        DataAplikasiModel dataAplikasiModel = new DataAplikasiModel();

        dataAplikasiModel.setCode(appCode);
        dataAplikasiModel.setDesc(appDesc);

        return dataAplikasiModel;
    }

    public TrxUpmRole getUPMProcessRole(UPMProcessModel upmPM) {
        MsEmployee msEmployee = msEmployeeService.getMsEmployeeByNik(upmPM.getNik());
        TrxUpmRole trxUpmRole = new TrxUpmRole();
        trxUpmRole.setNik(upmPM.getNik());
        if (msEmployee == null) {
            trxUpmRole = trxUpmRoleService.getTrxUpmRole(upmPM.getNik());
            upmPM.setNama(trxUpmRole.getNama());
            upmPM.setJabatan(trxUpmRole.getRole());
        } else {
            trxUpmRole.setNama(msEmployee.getFullName());
            trxUpmRole.setRole(msEmployee.getOccupationDesc());
        }
        return trxUpmRole;
    }

    private TrxUpmRole getUPMCheckerRole(UPMCheckerModel upmCM) {
        MsEmployee msEmployee = msEmployeeService.getMsEmployeeByNik(upmCM.getNik());
        TrxUpmRole trxUpmRole = new TrxUpmRole();
        trxUpmRole.setNik(upmCM.getNik());
        if (msEmployee == null) {
            trxUpmRole = trxUpmRoleService.getTrxUpmRole(upmCM.getNik());
            upmCM.setNama(trxUpmRole.getNama());
            upmCM.setJabatan(trxUpmRole.getRole());
        } else {
            trxUpmRole.setNama(msEmployee.getFullName());
            trxUpmRole.setRole(msEmployee.getOccupationDesc());
        }
        return trxUpmRole;
    }

    public ResUploadModel generateDetailSetupParameterPdf(TrxSetupParamRequestDetailModel tsprdm) throws Exception {
        List<TimelineModel> listTM = getTimeLineTicket(tsprdm.getTicketId());
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(mappingToDataSourceList(tsprdm));

        File file = ResourceUtils.getFile("classpath:pdf_detail_setup_parameter_request.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

        Map<String, Object> parameters = mapper.toParameterMap(listTM);

        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

        ByteArrayOutputStream pdfReportStream = new ByteArrayOutputStream();
        JRPdfExporter exporter = new JRPdfExporter();
        exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
        exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(pdfReportStream));
        exporter.exportReport();

        Map<String, String> result = minioService.uploadReportFilePDF(pdfReportStream.toByteArray(), DocumentHelper.generateReportDetailFilePath(tsprdm.getTicketId(), DETAIL_SETUP_PARAMETER_FILE_NAME, PDF_EXTENSION), DETAIL_SETUP_PARAMETER_FILE_NAME);

        return new ResUploadModel(result);
    }

    public ResFileDownload directDownloadPdfDetailSP(TrxSetupParamRequestDetailModel tsprdm) throws Exception {
        File file = ResourceUtils.getFile("classpath:pdf_detail_setup_parameter_request.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

        List<TimelineModel> listTM = getTimeLineTicket(tsprdm.getTicketId());
        Map<String, Object> parameters = mapper.toParameterMap(listTM);

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(mappingToDataSourceList(tsprdm));

        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

        return new ResFileDownload(pdfReportStream.toByteArray(), DETAIL_SETUP_PARAMETER_FILE_NAME.concat(PDF_EXTENSION));
    }


    private List<TimelineModel> getTimeLineTicket(String ticketId) {
        List<TimelineModel> listTM = new ArrayList<>();
        List<TrxUpmRole> listUpmRole = trxUpmRoleService.getListTrxUpmRole();
        Map<String, TrxUpmRole> mapUpmRole = this.toMap(listUpmRole);

        List<TrxAudittrail> listTa = trxAudittrailService.getTrxAudittrailByTicketId(ticketId);
        Iterator<TrxAudittrail> iterator = listTa.iterator();
        while (iterator.hasNext()) {
            TrxAudittrail ta = iterator.next();
            Gson gson = new Gson();
            TimelineModel timelineModel = gson.fromJson(ta.getAdditionalInfo(), TimelineModel.class);
            timelineModel.setAction(ta.getAction());
            timelineModel.setNik(ta.getNik());
            if (ta.getAction().equalsIgnoreCase(REASSIGN_UPM_ACTION)) {
                timelineModel.setName(mapUpmRole.get(timelineModel.getNik().toUpperCase()).getNama());
            }

            listTM.add(timelineModel);
        }

        return listTM;
    }

    private Map<String, TrxUpmRole> toMap(List<TrxUpmRole> listUpmRole) {
        Map<String, TrxUpmRole> ret = new HashMap<>();
        listUpmRole.forEach(upmRole -> {
            ret.putIfAbsent(upmRole.getNik().toUpperCase(), upmRole);
        });
        return ret;
    }

    private List mappingToDataSourceList(TrxSetupParamRequestDetailModel tsprdm) {
        List dataSourceList = new ArrayList();
        Map<String, String> dataSourceMap = new HashMap<>();

        dataSourceMap.put("ticketId", tsprdm.getTicketId());
        dataSourceMap.put("tanggalEfektif", tsprdm.getTanggalEfektif());
        dataSourceMap.put("NIK", tsprdm.getData().getNIK());
        dataSourceMap.put("nama", tsprdm.getData().getNamaLengkap());
        dataSourceMap.put("jabatan", tsprdm.getData().getJabatan());
        dataSourceMap.put("kodeDanNamaCabang", tsprdm.getData().getKodeCabang().concat(" - ").concat(tsprdm.getData().getNamaCabang()));
        dataSourceMap.put("email", tsprdm.getData().getEmail());
        dataSourceMap.put("telepon", tsprdm.getData().getTelepon());
        dataSourceMap.put("aplikasi", getListAplikasi(tsprdm.getAplikasi()));
        dataSourceMap.put("kategoriParam", tsprdm.getKategoriParam().getDesc());
        dataSourceMap.put("parameterLama", tsprdm.getParameterLama());
        dataSourceMap.put("parameterBaru", tsprdm.getParameterBaru());
        dataSourceMap.put("alasan", tsprdm.getAlasanPengajuan());
        if (tsprdm.getProgress().containsKey("puk1")) {
            PUK1Model puk1 = (PUK1Model) tsprdm.getProgress().get("puk1");
            if (CURR_STATUS_APPROVED.equalsIgnoreCase(puk1.getStatus().getKeyStatus())) {
                dataSourceMap.put("nikPUK1", puk1.getNik());
                dataSourceMap.put("namaPUK1", puk1.getNama());
                dataSourceMap.put("jabantanPUK1", puk1.getJabatan());
            }
        }
        if (tsprdm.getProgress().containsKey("puk2")) {
            PUK2Model puk2 = (PUK2Model) tsprdm.getProgress().get("puk2");
            if (CURR_STATUS_APPROVED.equalsIgnoreCase(puk2.getStatus().getKeyStatus())) {
                dataSourceMap.put("nikPUK2", puk2.getNik());
                dataSourceMap.put("namaPUK2", puk2.getNama());
                dataSourceMap.put("jabantanPUK2", puk2.getJabatan());
            }
        }
        dataSourceMap.put("catatan", "");
        if (tsprdm.getProgress().containsKey("upmProcess")) {
            UPMProcessModel upmPM = (UPMProcessModel) tsprdm.getProgress().get("upmProcess");
            if (UPM_STATUS_VERIFICATION.equalsIgnoreCase(upmPM.getStatus().getKeyStatus())) {
                dataSourceMap.put("catatan", upmPM.getNotes() != null ? upmPM.getNotes() : "");
            }
        }
        dataSourceMap.put("statusTiket", tsprdm.getStatus().getValueStatus());

        dataSourceList.add(dataSourceMap);
        return dataSourceList;
    }

    private String getListAplikasi(List<DataAplikasiModel> dataAplikasiModelList) {
        List<String> applicationList = new ArrayList<>();

        dataAplikasiModelList.forEach(app -> {
            applicationList.add(app.getDesc());
        });

        return StringUtils.join(applicationList, ", ");
    }

    public ResponseModel<ResTrxFuidRequest> saveTrxSetupParamRequestUPM(TrxSetupParamRequestModel trxSetupParamRequestModel) throws Exception {
        DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
        String ticketId = "DF9912280001";
        if (trxSetupParamRequestModel.getTicketId() == null || trxSetupParamRequestModel.getTicketId() == "") {
            String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());
            // cek last ticket di database
            String lastTicketId = getLastTicketIdManualUPM();
            logger.info("getLastTicketId: " + lastTicketId);
            if (lastTicketId == null) {
                ticketId = "SPM" + currDtTicket + "0001";
            } else {
                String strlastDate = lastTicketId.substring(3, 9);
                logger.info("strlastDate: " + strlastDate);
                // cek tanggalnya apakah sama dengan currentdate
                if (strlastDate.equals(currDtTicket)) {
                    String strTicketNum = lastTicketId.substring(9, 13);
                    logger.info("strTicketNum: " + strTicketNum);
                    Integer ticketNum = Integer.parseInt(strTicketNum) + 1;
                    ticketId = "SPM" + currDtTicket + String.format("%04d", ticketNum);
                } else {
                    ticketId = "SPM" + currDtTicket + "0001";
                }
            }
        }
        trxSetupParamRequestModel.setTicketId(ticketId);

        // cek duplicate request
        boolean isSuccesRequest = false;

        if (!isDuplicateRequestId(trxSetupParamRequestModel.getRequestId())) {
            // save to TrxSetupParamRequest
            TrxSetupParamRequest tspr = Mapper.toTrxSetupParamRequestEntity(trxSetupParamRequestModel);
            tspr.setNikRequester(trxSetupParamRequestModel.getSetupParameter().getNikRequester());
            tspr.setInputType(INPUT_TYPE_MANUAL);
            TrxSetupParamRequest savedTspr = saveTrxSetupParamRequest(tspr);

            // save to TrxSetupParamRequestAplikasi
            if (trxSetupParamRequestModel.getSetupParameter().getAplikasi().size() > 0) {
                List<String> lAplikasi = trxSetupParamRequestModel.getSetupParameter().getAplikasi();
                for (String aplikasi : lAplikasi) {
                    MsTemaApplication msta = msTemaApplicationService.getMsTemaApplication(aplikasi);
                    TrxSetupParamRequestAplikasi tsprap = Mapper.toTrxSetupParamRequestAplikasiEntity(ticketId, msta);
                    trxSetupParamRequestAplikasiService.saveTrxSetupParamRequestAplikasi(tsprap);
                }
            }

            // save to TrxFuidApproval
            String upmInputNIK = "", upmCheckerNIK = "";
            TrxSetupParamApproval tspa = Mapper.toTrxSetupParamApprovalEntity(trxSetupParamRequestModel);
            tspa.setCurrentState(UPM_STATUS_DONE);
            upmInputNIK = trxSetupParamRequestModel.getSetupParameter().getUpmInputNIK() == null ? "" : trxSetupParamRequestModel.getSetupParameter().getUpmInputNIK();
            if (!upmInputNIK.isEmpty()) {
                tspa.setUpmInputNIK(upmInputNIK);
                tspa.setUpmInputStatus(UPM_STATUS_VERIFICATION);
                tspa.setUpmInputDt(LocalDateTime.now());
            }
            upmCheckerNIK = trxSetupParamRequestModel.getSetupParameter().getUpmCheckerNIK() == null ? "" : trxSetupParamRequestModel.getSetupParameter().getUpmCheckerNIK();
            if (!upmCheckerNIK.isEmpty()) {
                tspa.setUpmCheckerNIK(upmCheckerNIK);
                tspa.setUpmCheckerStatus(UPM_STATUS_DONE);
                tspa.setUpmCheckerDt(LocalDateTime.now());
            }
            TrxSetupParamApproval savedTsa = trxSetupParamApprovalService.saveTrxSetupParamApproval(tspa);

            // save to TrxAudittrail
            List<TrxAudittrail> trxAudittrailList = new ArrayList<>();
            trxAudittrailList.add(mapper.buildTrxAudittrail(ticketId, upmInputNIK, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM));
            trxAudittrailList.add(mapper.buildTrxAudittrail(ticketId, upmCheckerNIK, UPM_STATUS_DONE, TIMELINE_STATUS_DONE, TIMELINE_PIC_UPM_APPROVE));
            List<TrxAudittrail> savedTa = iTrxAudittrailRepository.saveAll(trxAudittrailList);

            if (savedTspr != null && savedTsa != null && savedTa != null) {
                isSuccesRequest = true;
            }
        }
        ResTrxFuidRequest resTSPRM = new ResTrxFuidRequest();
        resTSPRM.setTicketId(trxSetupParamRequestModel.getTicketId());

        ResponseModel<ResTrxFuidRequest> response = new ResponseModel<>();
        response.setType(TYPE_UPM_SETUP_PARAMETER_REQUEST);
        response.setDetails(resTSPRM);
        if (isSuccesRequest) {
            response.setStatus(SUCCESS.getCode());
            response.setStatusDesc(SUCCESS.getValue());
        } else {
            response.setStatus(FAILED.getCode());
            response.setStatusDesc(FAILED.getValue());
        }

        return response;
    }

    public boolean isDuplicateRequestId(String requestId) throws ParseException {
        boolean isDuplicate = false;
        //jika requestId ada di db return true, lainnya false
        if (checkDuplicateRequestId(requestId) != null) {
            isDuplicate = true;
        }
        return isDuplicate;
    }

    public List<TrxSetupParamRequest> getTrxSetupParamReqJoinApprovalPuk1(String puk1NIK) {
        return trxSetupParamRequestRepository.getTrxSetupParamReqJoinApprovalPuk1(puk1NIK);
    }

    public List<TrxSetupParamRequest> getTrxSetupParamReqJoinApprovalPuk2(String puk2NIK) {
        return trxSetupParamRequestRepository.getTrxSetupParamReqJoinApprovalPuk2(puk2NIK);
    }
}
