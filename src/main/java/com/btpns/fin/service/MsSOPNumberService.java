package com.btpns.fin.service;

import com.btpns.fin.model.SOPNumberModel;
import com.btpns.fin.model.entity.MsSOPNumber;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsSOPNumberRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class MsSOPNumberService {
    @Autowired
    IMsSOPNumberRepository iMsSOPNumberRepository;

    public ResponseModel<SOPNumberModel> getMsSOPNumberData() {
        MsSOPNumber msSOPNumber = iMsSOPNumberRepository.findAllMsSOPNumberData();
        return ResponseModel.<SOPNumberModel>builder().type(TYPE_MS_SOP_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(msSOPNumber.toMsSOPNumberModel()).build();
    }

    public ResponseModel<SOPNumberModel> editMsSOPNumberData(SOPNumberModel request) {
        iMsSOPNumberRepository.deleteAll();
        iMsSOPNumberRepository.save(new MsSOPNumber(request.getSopNumber(), LocalDateTime.now()));
        return ResponseModel.<SOPNumberModel>builder().type(TYPE_MS_SOP_EDIT).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(new SOPNumberModel(request.getSopNumber())).build();
    }
}
