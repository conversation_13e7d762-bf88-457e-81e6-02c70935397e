package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Mapper;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsUserIDApplication;
import com.btpns.fin.model.entity.TrxUploadUserIdAudittrail;
import com.btpns.fin.model.TrxUploadUserIdAudittrailModel;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.ITrxUploadUserIdAudittrailRepository;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.lang.reflect.Type;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class TrxUploadUserIdAudittrailService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    ITrxUploadUserIdAudittrailRepository trxUploadUserIdAudittrailRepository;

    @Autowired
    MsUserIDApplicationService msUserIDApplicationService;

    @Autowired
    MinioService minioService;

    @Autowired
    Mapper mapper;

    public void saveTrxUploadUserIdAudittrail(TrxUploadUserIdAudittrail trxUploadUserIdAudittrail) {
        trxUploadUserIdAudittrailRepository.save(trxUploadUserIdAudittrail);
    }

    public ResponseModel<ResponseListModel<TrxUploadUserIdAudittrailModel>> getMonitoringUploadUserId(String aplikasi, String startDate, String endDate, Integer page, Integer limit) {
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)){
            Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
            startDate = period.getKey();
            endDate = period.getValue();
        }else {
            startDate = null;
            endDate = null;
        }

        Page<TrxUploadUserIdAudittrail> trxUploadUserIdAudittrailPage = trxUploadUserIdAudittrailRepository.getMonitoringUploadUserId(aplikasi, startDate, endDate, PageRequest.of(page - 1, limit));
        Map<String, MsUserIDApplication> applicationMap = msUserIDApplicationService.getUserIDAppMap();
        List<TrxUploadUserIdAudittrailModel> trxUploadUserIdAudittrailModelList = trxUploadUserIdAudittrailPage.getContent().stream().map(data -> data.toTrxUploadUserIdAudittrailModel(data, applicationMap)).collect(Collectors.toList());

        ResponseListModel<TrxUploadUserIdAudittrailModel> responseDetails = ResponseListModel.<TrxUploadUserIdAudittrailModel>builder()
                .data(trxUploadUserIdAudittrailModelList)
                .page(page)
                .limit(page)
                .totalPages(trxUploadUserIdAudittrailPage.getTotalPages())
                .totalItems(trxUploadUserIdAudittrailPage.getTotalElements())
                .build();

        return ResponseModel.<ResponseListModel<TrxUploadUserIdAudittrailModel>>builder().type(TYPE_UPLOAD_USERID_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    public ResFileDownload directDownloadReportUploadUserIdPdf(Integer id) throws Exception {
        ResFileDownload response = new ResFileDownload();

        Optional<TrxUploadUserIdAudittrail> trxUploadUserIdAudittrail = trxUploadUserIdAudittrailRepository.findById(BigInteger.valueOf(id));
        if (trxUploadUserIdAudittrail.isPresent()){
            List<UserIDModel> userIDModelList = gson.fromJson(trxUploadUserIdAudittrail.get().getDataUser(), new TypeToken<List<UserIDModel>>(){}.getType());
            String application = trxUploadUserIdAudittrail.get().getParamDetailId();
            LocalDateTime uploadDate = trxUploadUserIdAudittrail.get().getUploadAt();

            JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(userIDModelList);

            File file = ResourceUtils.getFile("classpath:pdf_report_upload_userId.jrxml");
            JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

            String appDesc = msUserIDApplicationService.getAppDesc(application);
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, mapper.generatePdfUploadUserIdParameters(appDesc, uploadDate), dataSource);
            ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

            response = new ResFileDownload(pdfReportStream.toByteArray(), REPORT_UPLOAD_USERID_FILE_NAME.concat(PDF_EXTENSION));
        }

        return response;
    }
}
