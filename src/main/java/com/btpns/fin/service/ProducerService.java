package com.btpns.fin.service;

import com.btpns.fin.model.MessageHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFutureCallback;

@Service
@ConditionalOnProperty(name = "spring.kafka.enabled", havingValue = "true", matchIfMissing = true)
public class ProducerService {
    private final Logger log = LoggerFactory.getLogger(ProducerService.class);

    @Autowired
    private KafkaTemplate<String, byte[]> kafkaTemplate;

    public void sendMessage(String topic, MessageHeader downloadMessageHeader, String data, String key) {
        Message<byte[]> message = buildMessage(topic, downloadMessageHeader, data, key);
        produceMessage(message, topic, downloadMessageHeader, data, key);
    }

    private void produceMessage(Message<byte[]> message, String topic, MessageHeader downloadMessageHeader, String data, String key) {
        log.info("Sending to topic {} with type {} key {} message {}", topic, downloadMessageHeader.getMessageType(), key, data);
        kafkaTemplate.send(message).addCallback(new ListenableFutureCallback<SendResult<String, byte[]>>() {
            @Override
            public void onFailure(Throwable throwable) {
                log.error("Fail to send message: ", throwable);
            }

            @Override
            public void onSuccess(SendResult<String, byte[]> result) {
//                log.info("Success to send to topic {} key {} message: {}", result.getProducerRecord().topic(), result.getProducerRecord().key(), new String (result.getProducerRecord().value()));
            }
        });
    }

    private Message<byte[]> buildMessage(String topic, MessageHeader messageHeader, String data, String key) {
        MessageBuilder<byte[]> messageBuilder = MessageBuilder
                .withPayload(data.getBytes())
                .setHeader(KafkaHeaders.TOPIC, topic.getBytes())
                .setHeader(KafkaHeaders.MESSAGE_KEY, key.getBytes());

        if (messageHeader != null && messageHeader.getRequestId() != null) {
            messageBuilder.setHeader("requestId", messageHeader.getRequestId().getBytes());
        }
        if (messageHeader != null && messageHeader.getKey() != null){
            messageBuilder.setHeader("ticketId", messageHeader.getKey().getBytes());
        }
        if (messageHeader != null && messageHeader.getMessageType() != null) {
            messageBuilder.setHeader("messageType", messageHeader.getMessageType().getBytes());
        }

        return messageBuilder.build();
    }
}
