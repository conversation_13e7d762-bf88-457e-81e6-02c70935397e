package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.model.MsT24;
import com.btpns.fin.model.entity.MsOfficerNR;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsOfficerNRRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
public class T24UserIdService {
    @Autowired
    private IMsOfficerNRRepository msOfficerNRRepository;

    @Autowired
    private MinioService minioService;

    public ResponseModel<ResponseListModel<MsT24>> getT24UserIDs(Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsOfficerNR> msT24Pageable = findT24BySearchFlag(pageSize, pageNumber, searchFlag, searchData);

        List<MsT24> msT24List = msT24Pageable.getContent().stream().map(MsOfficerNR::toT24).collect(Collectors.toList());
        ResponseListModel<MsT24> responseDetails = ResponseListModel.<MsT24>builder().data(msT24List).page(pageNumber).limit(pageSize).totalPages(msT24Pageable.getTotalPages()).totalItems(msT24Pageable.getTotalElements()).build();

        return ResponseModel.<ResponseListModel<MsT24>>builder().type(TYPE_MS_T24_MANAGEMENT_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    private Page<MsOfficerNR> findT24BySearchFlag(Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
        LocalDate currentDate = LocalDate.now();
        if (searchData.isBlank()) {
            return msOfficerNRRepository.findAllActiveT24(currentDate, pageable);
        }
        if (SEARCH_FLAG_SIGN_ON_ID.equals(searchFlag)) {
            return msOfficerNRRepository.findActiveT24ByLoginName(currentDate, searchData, pageable);
        }
        if (SEARCH_FLAG_USER_ID.equals(searchFlag)) {
            return msOfficerNRRepository.findActiveT24ByOfficerCode(currentDate, searchData, pageable);
        }
        if (SEARCH_FLAG_NAMA_USER.equals(searchFlag)) {
            return msOfficerNRRepository.findActiveT24ByOfficerName(currentDate, searchData, pageable);
        }
        return new PageImpl<>(Collections.emptyList());
    }

    public ResponseModel<ResUploadModel> getT24Csv() throws Exception {
        ResUploadModel result = generateT24Csv();
        return ResponseModel.<ResUploadModel>builder().type(TYPE_MS_T24_MANAGEMENT_DOWNLOAD).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(result).build();
    }

    public ResUploadModel generateT24Csv() throws Exception {
        List<MsOfficerNR> officerNRList = msOfficerNRRepository.findAllActiveT24(LocalDate.now(), Pageable.unpaged()).getContent();
        List<MsT24> t24List = officerNRList.stream().map(MsOfficerNR::toT24).collect(Collectors.toList());

        byte[] csvByte = makeT24Csv(t24List);

        Map<String, String> result = minioService.uploadReportFile(csvByte, DocumentHelper.generateReportFilePath(T24, CSV_EXTENSION), T24);

        return new ResUploadModel(result);
    }

    public ResFileDownload directDownloadT24Csv() throws Exception {
        List<MsOfficerNR> officerNRList = msOfficerNRRepository.findAllActiveT24(LocalDate.now(), Pageable.unpaged()).getContent();
        List<MsT24> t24List = officerNRList.stream().map(MsOfficerNR::toT24).collect(Collectors.toList());

        byte[] csvByte = makeT24Csv(t24List);

        return new ResFileDownload(csvByte, CommonHelper.concateTwoString(T24, CSV_EXTENSION));
    }

    private byte[] makeT24Csv(List<MsT24> t24List) throws Exception {
        StringBuffer csv = new StringBuffer();
        csv.append("\""+SIGN_ON_ID_COL_NAME+"\",\""+USER_ID_COL_NAME+"\",\""+NAMA_USER_COL_NAME+"\",\""+KODE_CABANG_COL_NAME+"\",\""+NAMA_CABANG_COL_NAME+"\",\""+DEPT_COL_NAME+"\",\""+NAMA_DEPARTEMEN_COL_NAME+"\",\""+MENU_COL_NAME+"\",\""+TGL_MULAI_PROFIL_COL_NAME+"\",\""+TGL_BERAKHIR_PROFIL_COL_NAME+"\",\""+VALIDITAS_PWD_COL_NAME+"\",\""+TGL_LOGIN_TERAKHIR+"\""+"\r\n");
        t24List.forEach(data -> csv.append("\""+"\'"+data.getSignOnID()+"\",\""+data.getUserID()+"\",\""+data.getNamaUser()+"\",\""+data.getKodeCabang()+"\",\""+data.getNamaCabang()+"\",\""+data.getDeptID()+"\",\""+data.getNamaDepartemen()+"\",\""+data.getMenu()+"\",\""+data.getTanggalMulaiProfil()+"\",\""+data.getTanggalBerakhirProfil()+"\",\""+data.getValiditasPwd()+"\",\""+data.getTanggalLoginTerakhir()+"\""+"\r\n"));

        return csv.toString().getBytes("windows-1252");
    }
}
