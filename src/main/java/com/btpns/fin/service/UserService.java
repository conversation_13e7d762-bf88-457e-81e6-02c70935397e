package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
public class UserService {
    @Value("${user.whitelist.menu.prospera.by.nik}")
    protected Set<String> userWhitelistMenuProsperaByNik;

    @Value("${user.whitelist.menu.prospera.by.officecode}")
    protected Set<String> userWhitelistMenuProsperaByOfficeCode;

    @Value("${max.upm.limit.for.bwmp.nominal.input}")
    private Integer maxUpmLimitForBwmpNominalInput;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxPUKVendorService trxPUKVendorService;

    @Autowired
    MsEmployeeHierarchyService msEmployeeHierarchyService;

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    TrxUpmRoleService upmRoleService;

    @Autowired
    MsUserIDApplicationService userIDApplicationService;

    @Autowired
    NotificationService notificationService;

    @Autowired
    IMsEmployeeRepository iMsEmployeeRepository;

    @Autowired
    IInquiryTicketRepository inquiryTicketRepository;

    @Autowired
    ITrxPUKVendorRepository iTrxPUKVendorRepository;

    @Autowired
    IMsOfficerNRRepository iMsOfficerNRRepository;

    @Autowired
    IUserIDOwnershipRepository userIDOwnershipRepository;

    @Autowired
    MsApprovalKewenanganLimitService msApprovalKewenanganLimitService;

    @Autowired
    IMsApprovalKewenanganLimitRepository iMsApprovalKewenanganLimitRepository;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MinioService minioService;

    @Autowired
    CommonHelper commonHelper;

    public UserModel getDataUser(UserTokenModel utm, String type, String nik, String tipeKewenanganLimit, String[] aplikasi, String tujuan, String alasan, Integer isHavingAttachment, Boolean isTunai, Double nominal) throws ParseException {
        UserModel userModel = new UserModel();

        MsEmployee msEmployee = msEmployeeService.getMsEmployeeByNik(nik);
        //check jika vendor
        boolean isVendor = false;
        if(msEmployee == null){
            TrxPUKVendor trxPUKVendor = trxPUKVendorService.findByNikVendor(nik);
            if(trxPUKVendor != null) {
                msEmployee = Mapper.toMsEmployee(trxPUKVendor);
                isVendor = true;
            }
        }

        if(msEmployee != null){
            if (utm != null){
                userModel = buildUserModel(msEmployee, utm, nik, isVendor);
                nik = userModel.getNik();
            }

            List<PukModel> listPM = new ArrayList<>();
            List<MsEmployee> listPUK = msEmployeeService.getListPUK(nik);

            boolean isUserKFO = checkIsUserKfo(listPUK, msEmployee);
            boolean isUserKC = checkIsUserKC(listPUK, msEmployee);
            boolean isUserHO = isUserHO(listPUK, msEmployee);

            if (listPUK.size() > 0){
                if (FORM_SETUP_PARAMETER.equalsIgnoreCase(type)){
                    MsEmployee meTemp = listPUK.get(0);
                    PukModel pm = Mapper.toPUKModel(meTemp, 1);
                    listPM.add(pm);
                    for (int i = 2; i < listPUK.size(); i++) {
                        if(listPUK.get(i).getOccupation().contains(Constants.DIRECTOR) && !listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR)){
                            meTemp = listPUK.get(i-1);
                            pm = Mapper.toPUKModel(meTemp, 2);
                            listPM.add(pm);
                            break;
                        } else if(listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR) && listPUK.get(i-1).getOccupation().contains(Constants.HEAD)){
                            meTemp = listPUK.get(i-1);
                            pm = Mapper.toPUKModel(meTemp, 2);
                            listPM.add(pm);
                            break;
                        }
                    }
                    userModel.setPuk(resolveDelegatedPUK(listPM));
                } else if (FORM_FUID.equalsIgnoreCase(type)){
                    //validation matriks
                    boolean isLimitTransaksi = false;
                    boolean isEmail = false;
                    boolean isEmailGroup = false;
                    boolean isEmailKeluar = false;

                    if(tipeKewenanganLimit != null){
                        if(tipeKewenanganLimit.equals(Constants.TIPE_KEWENANGAN_LIMIT_TRANSAKSI)){
                            isLimitTransaksi = true;
                        }
                    }

                    if(aplikasi != null) {
                        if (aplikasi.length > 0) {
                            if (Arrays.asList(aplikasi).contains(Constants.KODE_APLIKASI_EMAIL)){
                                isEmail = true;
                            } else if (Arrays.asList(aplikasi).contains(Constants.KODE_APLIKASI_EMAIL_GROUP)) {
                                isEmailGroup = true;
                            } else if(Arrays.asList(aplikasi).contains(Constants.KODE_APLIKASI_EMAIL_KELUAR)){
                                isEmailKeluar = true;
                            }
                        }
                    }
                    if (isLimitTransaksi) {
                        List<String> approvalLevels = new ArrayList<>();

                        if (isUserKC || isUserKFO) {
                            approvalLevels = msApprovalKewenanganLimitService.getApprovalLevelByNominal(true, isTunai, nominal);
                        } else if (isUserHO) {
                            approvalLevels = msApprovalKewenanganLimitService.getApprovalLevelByNominal(false, isTunai, nominal);
                        }

                        if (approvalLevels.size() > 0){
                            List<String> fixedApprovalLevels = getFixedApprovalLevel(listPUK, approvalLevels, isUserKC, isTunai);

                            for (int i = 0; i < listPUK.size(); i++) {
                                if (fixedApprovalLevels.contains(listPUK.get(i).getOccupation())) {
                                    MsEmployee puk = listPUK.get(i);
                                    if (isHavingAttachment == 1 && fixedApprovalLevels.get(0).contains(DIRECTOR)) {
                                        puk = i > 0 ? listPUK.get(i - 1) : listPUK.get(i);
                                    }
                                    listPM.add(Mapper.toPUKModel(puk, 1));
                                    break;
                                }
                            }
                        }
                    }else {
                        if (isEmail && (alasan != null && ALASAN_KARYAWAN_BARU.equals(alasan))) {
                            if (isUserKFO){
                                listPM.add(getPuk1KFO(listPUK));
                                listPM.add(getPuk2Kfo(listPUK, isEmail, FALSE_FLAG_BOOL, FALSE_FLAG_BOOL, 0));
                            }else {
                                listPM.add(Mapper.toPUKModel(listPUK.get(0), 1));
                                if (listPUK.size() > 1){
                                    listPM.add(Mapper.toPUKModel(listPUK.get(1), 2));
                                }
                            }
                        } else if (isEmailGroup && (tujuan != null && TUJUAN_PENDAFTARAN_BARU.equals(tujuan))) {
                            if (isUserKFO){
                                listPM.add(getPuk1KFO(listPUK));
                                listPM.add(getPuk2Kfo(listPUK, FALSE_FLAG_BOOL, isEmailGroup, FALSE_FLAG_BOOL, 0));
                            }else {
                                listPM.add(Mapper.toPUKModel(listPUK.get(0), 1));
                                if (listPUK.size() > 1){
                                    for (int i = 2; i < listPUK.size(); i++) {
                                        if(listPUK.get(i).getOccupation().contains(Constants.DIRECTOR) && !listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR)){
                                            listPM.add(Mapper.toPUKModel(listPUK.get(i-1), 2));
                                            break;
                                        } else if(listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR) && listPUK.get(i-1).getOccupation().contains(Constants.HEAD)){
                                            listPM.add(Mapper.toPUKModel(listPUK.get(i-1), 2));
                                            break;
                                        }
                                    }
                                }
                            }
                        } else if (isEmailKeluar) {
                            if (isUserKFO){
                                listPM.add(getPuk1KFO(listPUK));
                                if (listPUK.size() > 1){
                                    if (isHavingAttachment == 1) {
                                        listPM.add(getPuk2Kfo(listPUK, FALSE_FLAG_BOOL, FALSE_FLAG_BOOL, isEmailKeluar, 1));
                                    } else {
                                        listPM.add(getPuk2Kfo(listPUK, FALSE_FLAG_BOOL, FALSE_FLAG_BOOL, isEmailKeluar, 0));
                                    }
                                }
                            }else {
                                boolean isIT = isDivisionContain(listPUK, INFORMATION_TECHNOLOGY);
                                boolean isHC = isDivisionContain(listPUK, HUMAN_CAPITAL);
                                boolean isBD = isDivisionContain(listPUK, BUSINESS_DEVELOPMENT);
                                boolean isFunding = isDivisionContain(listPUK, FUNDING);
                                boolean isWholsale = isDivisionContain(listPUK, WHOLESALE);

                                listPM.add(Mapper.toPUKModel(listPUK.get(0), 1));
                                if (isIT || isHC){
                                    listPM.add(getITHCPuk2EmailKeluar(listPUK));
                                }else if (isFunding || isWholsale){
                                    listPM.add(getFundingWholsalePuk2EmailKeluar(listPUK));
                                }else if (isBD){
                                    listPM.add(getBDPuk2EmailKeluar(listPUK, isHavingAttachment));
                                }else {
                                    if (listPUK.size() > 1){
                                        if (isHavingAttachment == 1) {
                                            listPM.add(Mapper.toPUKModel(listPUK.get(1), 2));
                                        } else {
                                            for (int i = 0; i < listPUK.size(); i++) {
                                                if (listPUK.get(i).getOccupation().contains(Constants.DIRECTOR) && !listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR)) {
                                                    listPM.add(Mapper.toPUKModel(listPUK.get(i), 2));
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            if (isUserKFO){
                                listPM.add(getPuk1KFO(listPUK));
                            }else if (isUserKC){
                                if (tujuan != null && (tujuan.equals(TUJUAN_ALTERNATE_DELEGASI) || tujuan.equals(TUJUAN_ALTERNATE_DELEGASI_PROSPERA))){
                                    for (int i = 0; i < listPUK.size(); i++) {
                                        if(listPUK.get(i).getOccupation().equals(Constants.OPERATION_DISTRIBUTION_HEAD)){
                                            MsEmployee meTemp = listPUK.get(i);
                                            PukModel pm = Mapper.toPUKModel(meTemp, 1);
                                            listPM.add(pm);
                                            break;
                                        }
                                    }
                                }else {
                                    listPM.add(Mapper.toPUKModel(listPUK.get(0), 1));
                                }
                            }else {
                                listPM.add(Mapper.toPUKModel(listPUK.get(0), 1));
                            }
                        }
                    }
                    userModel.setPuk(resolveDelegatedPUK(listPM));
                }else {
                    if (isUserKFO){
                        listPM.add(getPuk1KFO(listPUK));
                    }else {
                        listPM.add(Mapper.toPUKModel(listPUK.get(0), 1));
                    }
                    userModel.setPuk(resolveDelegatedPUK(listPM));
                }
            }else {
                userModel.setPuk(Collections.EMPTY_LIST);
            }
        } else {
            userModel.setPuk(Collections.EMPTY_LIST);
        }

        return userModel;
    }

    private List<String> getFixedApprovalLevel(List<MsEmployee> listPUK, List<String> approvalLevels, boolean isUserKC, Boolean isTunai) {
        List<String> fixedApprovalLevels = new ArrayList<>();
        for (int i = 0; i < listPUK.size(); i++) {
            if (approvalLevels.contains(listPUK.get(i).getOccupation())) {
                fixedApprovalLevels.add(listPUK.get(i).getOccupation());
                break;
            }else {
                if (i == listPUK.size() - 1){
                    fixedApprovalLevels = iMsApprovalKewenanganLimitRepository.getHigherLevelApprovals(approvalLevels.get(0), isUserKC, isTunai);
                }
            }
        }
        return fixedApprovalLevels;
    }

    private boolean checkIsUserKfo(List<MsEmployee> listPUK, MsEmployee msEmployee) {
        boolean result = false;
        if (KFO_OCCUPATION.contains(msEmployee.getOccupation())){
            result = true;
        }else {
            if (listPUK.size() > 0){
                for (int i = 0; i < listPUK.size(); i++) {
                    if(NETWORK_OPERATION_MANAGER.equalsIgnoreCase(listPUK.get(i).getOccupation())){
                        result = true;
                        break;
                    }
                }
            }
        }
        return result;
    }

    private boolean checkIsUserKC(List<MsEmployee> listPUK, MsEmployee msEmployee) {
        boolean result = false;
        if (KC_OCCUPATION.contains(msEmployee.getOccupation())){
            result = true;
        }else {
            if (listPUK.size() > 0){
                for (int i = 0; i < listPUK.size(); i++) {
                    if(BRANCH_OPERATION_MANAGER.equalsIgnoreCase(listPUK.get(i).getOccupation())){
                        result = true;
                        break;
                    }
                }
            }
        }
        return result;
    }

    private boolean isUserHO(List<MsEmployee> listPUK, MsEmployee employee) {
        boolean result = false;
        if (employee.getBranchCode() != null && employee.getBranchCode().equals(KODE_KANTOR_PUSAT)) {
            result = true;
        } else if (isOccupationInHO(employee.getOccupation())) {
            result = true;
        } else {
            for (MsEmployee puk : listPUK) {
                if (isOccupationInHO(puk.getOccupation())) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    private boolean isOccupationInHO(String occupation) {
        return TROPS_TRANSFER_SERVICE_MANAGER.equalsIgnoreCase(occupation)
                || TIME_DEPOSIT_SERVICES_MANAGER.equalsIgnoreCase(occupation)
                || PAYMENT_SERVICES_MANAGER.equalsIgnoreCase(occupation);
    }

    private List<PukModel> resolveDelegatedPUK(List<PukModel> listPM) throws ParseException {
        int index = 0;
        for (PukModel puk : listPM) {
            if (puk.getNik() != null){
                TrxDelegation delegation = trxDelegationService.getTrxDelegationByNikRequester(puk.getNik());
                if (delegation != null) {
                    puk.setNik(delegation.getNikDelegation());
                    puk.setNama(delegation.getNamaDelegation());
                    puk.setJabatan(delegation.getJabatanDelegation());
                }
                index++;
            }
        }

        if (listPM.size() == 1) {
            listPM = listPM.get(0).getNik() != null ? listPM.stream().distinct().collect(Collectors.toList()) : Collections.EMPTY_LIST;

            if (index > 1){
                MsEmployee lastPUK = msEmployeeService.getDirectPUK(listPM.get(0).getNik());
                PukModel pukModel = new PukModel();
                if (lastPUK != null){
                    pukModel = Mapper.toPUKModel(lastPUK, 2);
                }
                listPM.add(pukModel);
            }
        }

        return resolveDelegatedPUKRole(listPM);
    }

    private List<PukModel> resolveDelegatedPUKRole(List<PukModel> listPM) {
        int index = 1;
        for (PukModel puk : listPM) {
            if (puk.getNik() != null){
                puk.setRole(KEY_PUK + index);
                index++;
            }
        }

        return listPM;
    }

    private UserModel buildUserModel(MsEmployee msEmployee, UserTokenModel utm, String nik, boolean isVendor) {
        UserModel userModel = new UserModel();

        if(utm.getPreferred_username().toLowerCase().equals(nik.toLowerCase())){
            userModel.setNik(utm.getPreferred_username());
            userModel.setNama(utm.getName());
            if(utm.getProfile() != null) {
                userModel.setJabatan(utm.getProfile().getPosition());
            }
        } else {
            userModel.setNik(msEmployee.getNik());
            userModel.setNama(msEmployee.getFullName());
            userModel.setJabatan(msEmployee.getOccupationDesc());
        }
        userModel.setIsFTE(TRUE_FLAG_BOOL.equals(isVendor) ? FALSE_FLAG_BOOL : TRUE_FLAG_BOOL);

        return userModel;
    }

    public List<PukModel> getListPukModel(String nik, boolean isVendor) throws ParseException {
        List<PukModel> listPM = new ArrayList<>();
        List<MsEmployee> listPUK = new ArrayList<>();

        if(isVendor){
            listPUK = trxPUKVendorService.getListPUKVendor(nik);
        } else {
            listPUK = msEmployeeService.getListPUK(nik);
        }

        //check MsEmployeeHierarchy
        if(listPUK.size() == 0){
            listPUK = msEmployeeHierarchyService.getListPUKMsEmployeeHierarchy(nik);
        }

        if(listPUK.size() > 0) {
            for (int i = 0; i < listPUK.size(); i++) {
                MsEmployee meTemp = listPUK.get(i);
                PukModel pm = Mapper.toPUKModel(meTemp, i+1);
                listPM.add(pm);
            }
        }
        return  listPM;
    }

    private PukModel getPuk1KFO(List<MsEmployee> listPUK) {
        PukModel pm = new PukModel();
        if(listPUK.size() > 0){
            for (int i = 1; i < listPUK.size(); i++) {
                if(listPUK.get(i).getOccupation().equals(Constants.NETWORK_OPERATION_MANAGER)){
                    pm = Mapper.toPUKModel(listPUK.get(i), 1);
                    break;
                }else {
                    pm = Mapper.toPUKModel(listPUK.get(i-1), 1);
                    break;
                }
            }
        }
        return pm;
    }

    private PukModel getPuk2Kfo(List<MsEmployee> listPUK, boolean isEmail, boolean isEmailGroup, boolean isEmailKeluar, Integer isHavingAttachment) {
        PukModel pm = new PukModel();
        for (int i = 1; i < listPUK.size(); i++) {
            if (isEmail){
                if(listPUK.get(i).getOccupation().equals(Constants.NETWORK_OPERATION_MANAGER)) {
                    pm = Mapper.toPUKModel(listPUK.get(i + 1), 2);
                    break;
                }else {
                    pm = Mapper.toPUKModel(listPUK.get(i), 2);
                    break;
                }
            }

            if (isEmailGroup){
                if(listPUK.get(i).getOccupation().contains(Constants.DIRECTOR) && !listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR)){
                    pm = Mapper.toPUKModel(listPUK.get(i-1), 2);
                    break;
                } else if(listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR) && listPUK.get(i-1).getOccupation().contains(Constants.HEAD)){
                    pm = Mapper.toPUKModel(listPUK.get(i-1), 2);
                    break;
                }
            }

            if (isEmailKeluar){
                if (isHavingAttachment == 0){
                    if(listPUK.get(i).getOccupation().contains(Constants.DIRECTOR) && !listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR)){
                        pm = Mapper.toPUKModel(listPUK.get(i), 2);
                        break;
                    }
                }else if (isHavingAttachment == 1){
                    if(listPUK.get(i).getOccupation().equals(Constants.NETWORK_OPERATION_MANAGER)){
                        pm = Mapper.toPUKModel(listPUK.get(i+1), 2);
                        break;
                    }else {
                        pm = Mapper.toPUKModel(listPUK.get(i - 1), 2);
                        break;
                    }
                }
            }
        }
        return pm;
    }

    private boolean isDivisionContain(List<MsEmployee> listPUK, String occupation) {
        boolean result = false;

        if(listPUK.size() > 0) {
            for (MsEmployee puk : listPUK) {
                if (puk.getOccupation().contains(occupation)){
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    private PukModel getITHCPuk2EmailKeluar(List<MsEmployee> listPUK) {
        PukModel pm = new PukModel();
        if (listPUK.size() > 1){
            for (int i = 0; i < listPUK.size(); i++) {
                if(listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR) && listPUK.get(i-1).getOccupation().contains(Constants.HEAD)){
                    pm = Mapper.toPUKModel(listPUK.get(i-1), 2);
                    break;
                }
            }
        }
        return pm;
    }

    private PukModel getFundingWholsalePuk2EmailKeluar(List<MsEmployee> listPUK) {
        PukModel pm = new PukModel();
        if (listPUK.size() > 1){
            for (int i = 0; i < listPUK.size(); i++) {
                if(listPUK.get(i).getOccupation().equals(Constants.DIRECTOR) && listPUK.get(i-1).getOccupation().contains(Constants.HEAD)){
                    pm = Mapper.toPUKModel(listPUK.get(i-1), 2);
                    break;
                }
            }
        }
        return pm;
    }

    private PukModel getBDPuk2EmailKeluar(List<MsEmployee> listPUK, Integer isHavingAttachment) {
        PukModel pm = new PukModel();

        if (listPUK.size() > 1){
            if (isHavingAttachment == 1) {
                pm = Mapper.toPUKModel(listPUK.get(1), 2);
            } else {
                for (int i = 0; i < listPUK.size(); i++) {
                    if (listPUK.get(i).getOccupation().contains(Constants.DIRECTOR) && !listPUK.get(i).getOccupation().equals(Constants.PRESIDENT_DIRECTOR)) {
                        pm = Mapper.toPUKModel(listPUK.get(i), 2);
                        break;
                    }
                }
            }
        }
        return pm;
    }

    public ResponseModel<UserDelegationModel> getPossibleUsersForDelegation(String nik) {
        UserDelegationModel userDelegationModel = new UserDelegationModel();

        MsEmployee msEmployee = iMsEmployeeRepository.getMsEmployeeByNik(nik);
        if (msEmployee == null){
            TrxPUKVendor trxPUKVendor = iTrxPUKVendorRepository.findByNikVendor(nik);
            if (trxPUKVendor != null){
                buildUserDelegationModel(userDelegationModel, nik, trxPUKVendor.getNameVendor(), trxPUKVendor.getOccupationDescVendor());
            }
        }else {
            buildUserDelegationModel(userDelegationModel, nik, msEmployee.getFullName(), msEmployee.getOccupationDesc());
        }

        buildPossibleUserForDelegation(userDelegationModel, nik);

        return buildResponse(SUCCESS, TYPE_POSSIBLE_USERS_FOR_DELEGATION_GET, userDelegationModel);
    }

    private void buildUserDelegationModel(UserDelegationModel userDelegationModel, String nik, String nama, String jabatan) {
        userDelegationModel.setNik(nik);
        userDelegationModel.setNama(nama);
        userDelegationModel.setJabatan(jabatan);
    }

    private void buildPossibleUserForDelegation(UserDelegationModel userDelegationModel, String nik) {
        List<UserDelegationModelDetail> userDelegationModelList = new ArrayList<>();

        if (isPossibleUserForDelegation(nik)) {
            List<MsEmployee> listPuk = msEmployeeService.getListPUK(nik);
            if (listPuk.size() > 0) {
                int maxLimit = listPuk.size() == 1 ? 1 : 2;
                for (int i = 0; i < maxLimit; i++) {
                    userDelegationModelList.add(buildUserDelegationModelDetail(listPuk.get(i)));
                    List<MsEmployee> peer = iMsEmployeeRepository.getListEmployeesActiveByDirectSupervisorNIK(listPuk.get(i).getNik());
                    if (!peer.isEmpty() && peer.size() > 0) {
                        for (MsEmployee employee : peer) {
                            if (!employee.getNik().equals(nik) && isPossibleUserForDelegation(employee.getNik())) {
                                userDelegationModelList.add(buildUserDelegationModelDetail(employee));
                            }
                        }
                    }
                }
            }
        }

        userDelegationModel.setPossibleUsersForDelegation(removeDuplicateDataDelegationModelList(userDelegationModelList));
    }

    private boolean isPossibleUserForDelegation(String nik) {
        return iMsEmployeeRepository.getListEmployeesActiveByDirectSupervisorNIK(nik).size() > 0;
    }

    private List<UserDelegationModelDetail> removeDuplicateDataDelegationModelList(List<UserDelegationModelDetail> userDelegationModelList) {
        Set<String> nikSet = new HashSet<>();
        List<UserDelegationModelDetail> dataDistinctByNik = userDelegationModelList.stream().filter(e -> nikSet.add(e.getNik())).collect(Collectors.toList());

        return dataDistinctByNik;
    }

    private UserDelegationModelDetail buildUserDelegationModelDetail(MsEmployee msEmployee) {
        UserDelegationModelDetail userDelegationModelDetail = new UserDelegationModelDetail();

        userDelegationModelDetail.setNik(msEmployee.getNik());
        userDelegationModelDetail.setNama(msEmployee.getFullName());
        userDelegationModelDetail.setJabatan(msEmployee.getOccupationDesc());

        return userDelegationModelDetail;
    }

    private ResponseModel<UserDelegationModel> buildResponse(ResponseStatus status, String type, UserDelegationModel userDelegationModel) {
        ResponseModel response = new ResponseModel();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(userDelegationModel);

        return response;
    }

    public UserInquiryModel inquireUserDetailsAndPermission(String nikRequester, String email) {
        MsEmployee msEmployee = msEmployeeService.getEmployeeByNik(nikRequester);
        TrxUpmRole upmRole = upmRoleService.getTrxUpmRole(msEmployee.getNik());
        Boolean isUserPUK = iMsEmployeeRepository.getListEmployeesActiveByDirectSupervisorNIK(nikRequester).size() > 0;

        return buildUserInquiryModel(msEmployee, upmRole, nikRequester, email, isUserPUK);
    }

    private UserInquiryModel buildUserInquiryModel(MsEmployee employee, TrxUpmRole upmRole, String nikRequester, String email, Boolean isUserPUK) {
        UserInquiryModel userInquiryModel = new UserInquiryModel();

        userInquiryModel.setUserInfo(buildUserInfoModel(employee, email));
        userInquiryModel.setUserRole(buildUserRoleModel(employee, upmRole, isUserPUK));

        boolean isNonViewerUPM = CommonHelper.isNonViewerUPMRole(upmRole);

        String userType = isNonViewerUPM ? UPM : USER;
        userInquiryModel.setNotifications(notificationService.buildNotificationModel(userType, employee.getNik()));
        userInquiryModel.setAplikasiUserID(isNonViewerUPM ? userIDApplicationService.getAllActiveUserIDApplications() : Collections.emptyList());
        userInquiryModel.setOwnedActiveUserID(getListOwnedActiveUserID(nikRequester));

        return userInquiryModel;
    }

    private List<String> getListOwnedActiveUserID(String nik) {
        String ownedActiveUserID = userIDOwnershipRepository.findOwnedActiveUserIDByNIK(nik);

        return ownedActiveUserID.isEmpty() ? Collections.emptyList() : Arrays.asList(ownedActiveUserID.replaceAll("\\s", "").split(","));
    }

    private UserInfoModel buildUserInfoModel(MsEmployee employee, String email) {
        UserInfoModel userInfoModel = new UserInfoModel();

        userInfoModel.setNik(employee.getNik());
        userInfoModel.setNama(employee.getFullName());
        userInfoModel.setJabatan(employee.getOccupationDesc());
        userInfoModel.setEmail(email != null ? email : CommonHelper.generateBtpnsEmailFormat(employee.getNik()));
        userInfoModel.setNoTelepon(EMPTY);

        return userInfoModel;
    }

    private UserRoleModel buildUserRoleModel(MsEmployee employee, TrxUpmRole upmRole, Boolean isUserPUK) {
        UserRoleModel userRoleModel = new UserRoleModel();

        userRoleModel.setUpmGroup(upmRole != null && !UPM_ROLE_INQUIRY.equalsIgnoreCase(upmRole.getRole()));
        userRoleModel.setRole(upmRole != null ? upmRole.getRole() : EMPTY);
        userRoleModel.setUserPUK(isUserPUK);
        userRoleModel.setUserMMS(CommonHelper.isUserMMS(employee.getOccupation()));
        userRoleModel.setUserWhitelistMenuProspera(validateUserWhitelistPropsera(employee.getNik()));
        userRoleModel.setMaxUpmLimitForBwmpNominalInput(upmRole != null ? maxUpmLimitForBwmpNominalInput : null);


        return userRoleModel;
    }

    private Boolean validateUserWhitelistPropsera(String nik) {
        List<MsOfficerNR> msOfficerNR = iMsOfficerNRRepository.findProsperaByRoleByStatusByNik(OFFICER_STATUS_ACTIVE_CODE, nik);
        return CommonHelper.isUserWhitelistPropsera(userWhitelistMenuProsperaByNik, userWhitelistMenuProsperaByOfficeCode, nik, msOfficerNR);
    }

    public ResponseModel<ResponseListModel<InquiryTicketModel>> getListTicketUserInquiry(String startDate, String endDate, Integer pageNumber, Integer pageSize, Boolean isGenerateCsv) {
        Page<InquiryTicket> inquiryTicketPage = getInquiryTicketModelPageable(startDate, endDate, pageNumber, pageSize, isGenerateCsv);
        List<InquiryTicketModel> inquiryTicketModelList = buildInquiryTicketModelList(inquiryTicketPage.getContent());
        ResponseListModel<InquiryTicketModel> responseDetail = ResponseListModel.<InquiryTicketModel>builder()
                .data(inquiryTicketModelList)
                .page(pageNumber)
                .limit(pageSize)
                .totalItems(inquiryTicketPage.getTotalElements())
                .totalPages(inquiryTicketPage.getTotalPages())
                .build();
        return ResponseModel.<ResponseListModel<InquiryTicketModel>>builder().type(TYPE_TICKET_USER_INQUIRY_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetail).build();
    }

    private Page<InquiryTicket> getInquiryTicketModelPageable(String startDate, String endDate, Integer pageNumber, Integer pageSize, Boolean isGenerateCsv) {
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey().split(" ")[0];
        endDate = period.getValue().split(" ")[0];
        Pageable pageable = isGenerateCsv ? Pageable.unpaged() : PageRequest.of(pageNumber - 1, pageSize);
        Page<InquiryTicket> inquiryTicketPage = inquiryTicketRepository.findAllTicketUserInquiry(startDate, endDate, pageable);

        return inquiryTicketPage;
    }

    private List<InquiryTicketModel> buildInquiryTicketModelList(List<InquiryTicket> inquiryTicketList) {
        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM));

        List<InquiryTicketModel> result = new ArrayList<>();
        inquiryTicketList.forEach(data -> {
            result.add(mapToInquiryTicketModel(data));
        });
        return result;
    }

    private InquiryTicketModel mapToInquiryTicketModel(InquiryTicket inquiryTicket) {
        InquiryTicketModel inquiryTicketModel = new InquiryTicketModel();
        inquiryTicketModel.setTicketId(inquiryTicket.getTicketId());
        inquiryTicketModel.setTanggalEfektif(DateTimeHelper.getDateToDateStringYYYYMMDD(inquiryTicket.getTanggalEfektif()));
        inquiryTicketModel.setDataNIK(inquiryTicket.getDataNIK());
        inquiryTicketModel.setDataNamaLengkap(inquiryTicket.getDataNamaLengkap());
        inquiryTicketModel.setDataJabatan(inquiryTicket.getDataJabatan());
        inquiryTicketModel.setDataKodeCabang(inquiryTicket.getDataKodeCabang());
        inquiryTicketModel.setDataNamaCabang(inquiryTicket.getDataNamaCabang());
        inquiryTicketModel.setAplikasi(commonHelper.getApplicationDesc(inquiryTicket.getAplikasi()));
        inquiryTicketModel.setJenisPengajuan(inquiryTicket.getJenisPengajuan());
        inquiryTicketModel.setDeskripsiAlasan(inquiryTicket.getDeskripsiAlasan());
        inquiryTicketModel.setKategori(inquiryTicket.getKategori());
        return inquiryTicketModel;
    }

    public ResponseModel<ResUploadModel> getTicketUserInquiryCsv(String startDate, String endDate, Integer pageNumber, Integer pageSize, Boolean isGenerateCsv) throws Exception {
        Page<InquiryTicket> inquiryTicketPage = getInquiryTicketModelPageable(startDate, endDate, pageNumber, pageSize, isGenerateCsv);
        List<InquiryTicketModel> inquiryTicketModelList = buildInquiryTicketModelList(inquiryTicketPage.getContent());

        StringBuffer csv = new StringBuffer();
        csv.append("\""+TICKET_ID_COL_NAME+"\",\""+TANGGAL_EFEKTIF_COL_NAME+"\",\""+NIK_COL_NAME+"\",\""+NAMA_COL_NAME+"\",\""+JABATAN_COL_NAME+"\",\""+KODE_CABANG_COL_NAME+"\",\""+NAMA_CABANG_COL_NAME+"\",\""+JENIS_APLIKASI_COL_NAME+"\",\""+JENIS_PENGAJUAN_COL_NAME+"\",\""+DESKRIPSI_ALASAN_COL_NAME+"\",\""+KATEGORI_COL_NAME+"\""+"\r\n");
        inquiryTicketModelList.forEach(data -> {
            csv.append("\""+data.getTicketId()+"\",\""+data.getTanggalEfektif()+"\",\""+"\'"+data.getDataNIK()+"\",\""+data.getDataNamaLengkap()+"\",\""+data.getDataJabatan()+"\",\""+"\'"+data.getDataKodeCabang()+"\",\""+data.getDataNamaCabang()+"\",\""+data.getAplikasi()+"\",\"" +data.getJenisPengajuan()+"\",\""+data.getDeskripsiAlasan()+"\",\""+data.getKategori()+"\""+"\r\n");
        });

        Map<String, String> result = minioService.uploadReportFile(csv.toString().getBytes("windows-1252"), DocumentHelper.generateReportFilePath(REPORT_TICKET_USER_INQUIRY_FILE_NAME, CSV_EXTENSION), REPORT_TICKET_USER_INQUIRY_FILE_NAME);

        return ResponseModel.<ResUploadModel>builder().type(TYPE_TICKET_USER_INQUIRY_DOWNLOAD).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(new ResUploadModel(result)).build();
    }

    public ResFileDownload directDownloadTicketUserInquiryCsv(String startDate, String endDate, Integer pageNumber, Integer pageSize, Boolean isGenerateCsv) throws Exception {
        Page<InquiryTicket> inquiryTicketPage = getInquiryTicketModelPageable(startDate, endDate, pageNumber, pageSize, isGenerateCsv);
        List<InquiryTicketModel> inquiryTicketModelList = buildInquiryTicketModelList(inquiryTicketPage.getContent());

        StringBuffer csv = new StringBuffer();
        csv.append("\""+TICKET_ID_COL_NAME+"\",\""+TANGGAL_EFEKTIF_COL_NAME+"\",\""+NIK_COL_NAME+"\",\""+NAMA_COL_NAME+"\",\""+JABATAN_COL_NAME+"\",\""+KODE_CABANG_COL_NAME+"\",\""+NAMA_CABANG_COL_NAME+"\",\""+JENIS_APLIKASI_COL_NAME+"\",\""+JENIS_PENGAJUAN_COL_NAME+"\",\""+DESKRIPSI_ALASAN_COL_NAME+"\",\""+KATEGORI_COL_NAME+"\""+"\r\n");
        inquiryTicketModelList.forEach(data -> {
            csv.append("\""+data.getTicketId()+"\",\""+data.getTanggalEfektif()+"\",\""+"\'"+data.getDataNIK()+"\",\""+data.getDataNamaLengkap()+"\",\""+data.getDataJabatan()+"\",\""+"\'"+data.getDataKodeCabang()+"\",\""+data.getDataNamaCabang()+"\",\""+data.getAplikasi()+"\",\"" +data.getJenisPengajuan()+"\",\""+data.getDeskripsiAlasan()+"\",\""+data.getKategori()+"\""+"\r\n");
        });

        return new ResFileDownload(csv.toString().getBytes("windows-1252"), CommonHelper.concateTwoString(REPORT_TICKET_USER_INQUIRY_FILE_NAME, CSV_EXTENSION));
    }
}
