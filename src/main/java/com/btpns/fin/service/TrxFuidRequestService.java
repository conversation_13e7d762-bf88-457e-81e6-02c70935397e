package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.TrxFuidRequestDTO;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResTrxFuidRequest;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.*;
import com.google.gson.Gson;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import java.io.*;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class TrxFuidRequestService {
    private static final Logger logger = LoggerFactory.getLogger(TrxFuidRequestService.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    ITrxFuidRequestRepository trxFuidRequestRepository;

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    TrxFuidApprovalService trxFuidApprovalService;

    @Autowired
    TrxFuidRequestAplikasiService trxFuidRequestAplikasiService;

    @Autowired
    IMsCabangRepository iMsCabangRepository;

    @Autowired
    IMmsUPMRepository iMmsUPMRepository;

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    TrxPUKVendorService trxPUKVendorService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    TrxCommentService trxCommentService;

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    MinioService minioService;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    MsEmployeeHierarchyService msEmployeeHierarchyService;

    @Autowired
    EmailNotificationService emailNotificationService;

    @Autowired
    Mapper mapper;

    @Autowired
    ProsperaRepository prosperaRepository;

    @Autowired
    IMsSystemParamDetailRepository iMsSystemParamDetailRepository;

    @Autowired
    IMsProsperaRoleRepository iMsProsperaRoleRepository;

    @Autowired
    ITrxExpiredFuidRepository trxExpiredFuidRepository;

    @Autowired
    ITrxAudittrailRepository iTrxAudittrailRepository;

    @Autowired
    ITrxFuidApprovalRepository trxFuidApprovalRepository;

    @PersistenceContext
    EntityManager entityManager;

    public TrxFuidRequest getTrxFuidRequestByTicketId(String ticketId) {
        return trxFuidRequestRepository.getOne(ticketId);
    }

    public TrxFuidRequestDTO getTrxFuidRequestByTicketIdData(String ticketId) {
        TrxFuidRequest ret = trxFuidRequestRepository.getOne(ticketId);
        entityManager.detach(ret);
        TrxFuidRequestDTO dto = mapTrxFuidRequestToTrxFuidRequestDTO(ret);
        return dto;
    }

    private TrxFuidRequestDTO mapTrxFuidRequestToTrxFuidRequestDTO(TrxFuidRequest original) {
        if (original == null) {
            return null;
        }

        TrxFuidRequestDTO copy = new TrxFuidRequestDTO();

        copy.setTicketId(original.getTicketId());
        copy.setCreateDateTime(original.getCreateDateTime());
        copy.setTujuan(original.getTujuan());
        copy.setAlasan(original.getAlasan());
        copy.setTanggalEfektif(original.getTanggalEfektif());
        copy.setDataNik(original.getDataNik());
        copy.setDataNamaLengkap(original.getDataNamaLengkap());
        copy.setDataJabatan(original.getDataJabatan());
        copy.setDataKodeCabang(original.getDataKodeCabang());
        copy.setDataNamaCabang(original.getDataNamaCabang());
        copy.setDataTelepon(original.getDataTelepon());
        copy.setDataNamaVendor(original.getDataNamaVendor());
        copy.setNikRequester(original.getNikRequester());
        copy.setAplikasi(original.getAplikasi());
        copy.setTingkatan(original.getTingkatan());
        copy.setStatusMasaBerlaku(original.getStatusMasaBerlaku());
        copy.setMasaBerlakuSampai(original.getMasaBerlakuSampai());
        copy.setAlasanPengajuan(original.getAlasanPengajuan());
        copy.setInfoTambahan(original.getInfoTambahan());
        copy.setTipeKewenanganLimit(original.getTipeKewenanganLimit());
        copy.setTrxFuidApproval(mappingTrxFuidApprovalDTO(original.getTrxFuidApproval()));
        copy.setAttachment(original.getAttachment());
        copy.setFileName(original.getFileName());
        copy.setDataUserId(original.getDataUserId());
        copy.setDataEmail(original.getDataEmail());
        copy.setTipeLimitTransaksi(original.getTipeLimitTransaksi());
        copy.setNominalTransaksi(original.getNominalTransaksi());
        copy.setNominalTransaksiUPM(original.getNominalTransaksiUPM());
        copy.setUnitKerjaLama(original.getUnitKerjaLama());
        copy.setUnitKerjaBaru(original.getUnitKerjaBaru());
        copy.setTipeKaryawanBaru(original.getTipeKaryawanBaru());
        copy.setRequestId(original.getRequestId());
        copy.setOccupationDesc(original.getOccupationDesc());
        copy.setOrganization(original.getOrganization());
        copy.setLocation(original.getLocation());
        copy.setInputType(original.getInputType());
        copy.setBatchId(original.getBatchId());
        copy.setRole(original.getRole());
        copy.setIsInActivePersonnelProspera(original.getIsInActivePersonnelProspera());
        copy.setPicEmailGroup(original.getPicEmailGroup());
        copy.setPicEmailGroupName(original.getPicEmailGroupName());
        copy.setPicEmailGroupOccupation(original.getPicEmailGroupOccupation());
        copy.setAltPicEmailGroup(original.getAltPicEmailGroup());
        copy.setAltPicEmailGroupName(original.getAltPicEmailGroupName());
        copy.setAltPicEmailGroupOccupation(original.getAltPicEmailGroupOccupation());
        copy.setPukVendorNIK(original.getPukVendorNIK());
        copy.setPukVendorName(original.getPukVendorName());
        copy.setPukVendorOccupation(original.getPukVendorOccupation());

        return copy;
    }

    public List<TrxFuidRequest> getTrxFuidRequestAll(){
        return trxFuidRequestRepository.findAll();
    }

    public Page<TrxFuidRequest> getTrxFuidReqJoinApproval(String nikRequester, int pageNumMin1, int pageSize){
        Pageable sortedByCreateDt = PageRequest.of(pageNumMin1, pageSize, Sort.by("createDateTime").descending());
        return trxFuidRequestRepository.getTrxFuidReqJoinApproval(nikRequester, sortedByCreateDt);
    }

    public  Page<TrxFuidRequest> getTrxFuidReqJoinApprovalWait(String nikRequester, int pageNumMin1, int pageSize){
        Pageable sortedByCreateDt = PageRequest.of(pageNumMin1, pageSize, Sort.by("createDateTime").descending());
        return trxFuidRequestRepository.getTrxFuidReqJoinApprovalWait(nikRequester, sortedByCreateDt);
    }

    public int countAllTrxFuidReqJoinApproval(String nikRequester){
        return trxFuidRequestRepository.countAllTrxFuidReqJoinApproval(nikRequester);
    }

    @Transactional
    public TrxFuidRequest saveTrxFuidRequest(TrxFuidRequest trxFuidRequest) {
        TrxFuidRequest tfr = null;
        Optional<TrxFuidRequest> existTrxFuidRequest = trxFuidRequestRepository.getTrxFuidRequestByTicketId(trxFuidRequest.getTicketId());
        Optional<TrxFuidApproval> existTrxFuidApproval = trxFuidApprovalRepository.getTrxFuidApprovalByTicketId(trxFuidRequest.getTicketId());
        if (!existTrxFuidRequest.isPresent() && !existTrxFuidApproval.isPresent()) {
            tfr = trxFuidRequestRepository.save(trxFuidRequest);
        }
        return tfr;
    }

    public String getLastTicketId(){
        return trxFuidRequestRepository.getLastTicketId();
    }

    public String getLastTicketIdByInputType(String inputType) {
        return trxFuidRequestRepository.getLastTicketIdByInputType(inputType);
    }

    @Transactional
    public TrxFuidRequest updateTrxFuidRequest(TrxFuidRequest trxFuidRequest){
        trxFuidRequest = getNamaCabangMms(trxFuidRequest);
        Optional<TrxFuidRequest> existTrxFuidRequest = trxFuidRequestRepository.findById(trxFuidRequest.getTicketId());
        if (existTrxFuidRequest.isPresent()) {
            trxFuidRequest.setCreateDateTime(existTrxFuidRequest.get().getCreateDateTime());
            trxFuidRequest.setNikRequester(existTrxFuidRequest.get().getNikRequester());
        }
        return trxFuidRequestRepository.save(trxFuidRequest);
    }

    public TrxFuidRequest getNamaCabangMms(TrxFuidRequest trxFuidRequest){
        //get nama cabang mms
        String kodeCabangMms = trxFuidRequest.getDataKodeCabang();
        String namaCabangMms = "";
        if(iMsCabangRepository.getMsCabangById(kodeCabangMms).size() == 0){
            MMS_UPM mms = iMmsUPMRepository.getMsMmsById(kodeCabangMms).get(0);
            namaCabangMms = mms.getMmsName();
        } else {
            MsCabang cabang = iMsCabangRepository.getMsCabangById(kodeCabangMms).get(0);
            namaCabangMms = cabang.getCabangDesc();
        }
        trxFuidRequest.setDataNamaCabang(namaCabangMms);
        return trxFuidRequest;
    }

    public String getNamaCabangMms(String kodeCabang){
        String namaCabangMms = "";
        if(iMsCabangRepository.getMsCabangById(kodeCabang).size() == 0){
            MMS_UPM mms = iMmsUPMRepository.getMsMmsById(kodeCabang).get(0);
            namaCabangMms = mms.getMmsName();
        } else {
            MsCabang cabang = iMsCabangRepository.getMsCabangById(kodeCabang).get(0);
            namaCabangMms = cabang.getCabangDesc();
        }

        return namaCabangMms;
    }

    public String checkDuplicateRequestId(String requestId) throws ParseException {
        LocalDate date = LocalDate.now();
        LocalDateTime dateTime = date.atStartOfDay();
        return trxFuidRequestRepository.checkDuplicateRequestId(requestId, dateTime);
    }

    public boolean existInInterval(String nik, int interval) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return trxFuidRequestRepository.checkRequestInterval(startDate, endDate, nik) > 0;
    }

    public ResponseModel<TrxFuidRequestDetailModel> getTrxFuidRequestDetail(String ticketId, Boolean isHitProspera, TrxFuidRequest savedTrxFuidRequest, String authorization) throws Exception {
        ResponseStatus status = SUCCESS;

        TrxFuidRequestDetailModel tfrdm = buildTrxFuidRequestDetailModel(savedTrxFuidRequest, ticketId);
        if (isValidEnrichPersonnelProspera(tfrdm.getTicketId(), isHitProspera, tfrdm.getStatus().getKeyStatus())){
            tfrdm.setIsHitProspera(TRUE_FLAG_INT);
            List<UserProspera> userProsperaList = prosperaRepository.getPersonnelProsperaByNik(savedTrxFuidRequest.getDataNik(), authorization);
            if (userProsperaList == null){
                status = FAILED_ENRICH_DATA_FROM_PROSPERA;
            }else {
                tfrdm.setPersonnelProspera(mapToPersonnelProsperaList(userProsperaList, savedTrxFuidRequest));
            }
        }else {
            tfrdm.setIsHitProspera(FALSE_FLAG_INT);
        }

        return buildResponse(status, tfrdm);
    }

    public TrxFuidRequestDetailModel buildTrxFuidRequestDetailModel(TrxFuidRequest savedTrxFuidRequest, String ticketId) throws Exception {
        TrxFuidRequestDetailModel tfrdm = mapper.toTrxFuidRequestDetailModel(savedTrxFuidRequest);

        if (ALASAN_MUTASI_ROTASI_PROMOSI.equals(tfrdm.getAlasan())) {
            resolveOptimaInfo(savedTrxFuidRequest, tfrdm);
        }

        if (savedTrxFuidRequest.getAplikasi().contains(KODE_APLIKASI_EMAIL_GROUP)) {
            if (savedTrxFuidRequest.getPicEmailGroup() != null) {
                tfrdm.setPicEmailGroup(this.enrichPICEmailGroup(savedTrxFuidRequest.getPicEmailGroup(), savedTrxFuidRequest.getPicEmailGroupName(), savedTrxFuidRequest.getPicEmailGroupOccupation()));
            }
            if (savedTrxFuidRequest.getAltPicEmailGroup() != null) {
                tfrdm.setAltPicEmailGroup(this.enrichPICEmailGroup(savedTrxFuidRequest.getAltPicEmailGroup(), savedTrxFuidRequest.getAltPicEmailGroupName(), savedTrxFuidRequest.getAltPicEmailGroupOccupation()));
            }
        }

        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID));
        String[] splitAplikasi = savedTrxFuidRequest.getAplikasi().split(",");
        List<String> listAplikasi = Arrays.asList(splitAplikasi);
        tfrdm.setAplikasi(buildDataAplikasiModelList(listAplikasi, msTemaApplicationMap));

        //set curr state
        tfrdm.getStatus().setValueStatus(STATUS_PERMOHONAN_MAP.get(tfrdm.getStatus().getKeyStatus()));
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
        Date dateEfektif = sdf.parse(tfrdm.getTanggalEfektif());
        String sDateNow = sdf.format(new Date());
        Date dateNow = sdf.parse(sDateNow);
        if (tfrdm.getStatus().getKeyStatus().equals(CURR_STATUS_APPROVED) && dateEfektif.compareTo(dateNow) > 0) {
            tfrdm.getStatus().setKeyStatus(CURR_STATUS_PENDING);
            tfrdm.getStatus().setValueStatus(CURR_STATUS_PENDING_DESC);
        }
        //check non fte pendaftaran baru
        if (tfrdm.getTujuan().equals(TUJUAN_PENDAFTARAN_BARU)
                && tfrdm.getAlasan().equals(ALASAN_KARYAWAN_BARU)
                && tfrdm.getTipeKaryawanBaru().equals(TIPE_KARYAWAN_BARU_NON_FTE)) {
            TrxPUKVendor trxPUKVendor = trxPUKVendorService.findByNikVendor(savedTrxFuidRequest.getDataNik());
            //set nik
            tfrdm.getData().setNikPUKVendor(resolveNIKPuk(savedTrxFuidRequest.getPukVendorNIK(), trxPUKVendor));
            tfrdm.setPukVendor(resolvePUKVendor(savedTrxFuidRequest, trxPUKVendor));
        }
        //comment
        List<TrxComment> lTrxComment = trxCommentService.getListCommentByTicketId(ticketId);
        List<CommentModel> listCM = new ArrayList<CommentModel>();
        Iterator<TrxComment> iterator = lTrxComment.iterator();
        while(iterator.hasNext()){
            TrxComment tcm = iterator.next();
            CommentModel cm = Mapper.toCommentModel(tcm);
            listCM.add(cm);
        }
        tfrdm.setComment(listCM);

        return tfrdm;
    }

    private void resolveOptimaInfo(TrxFuidRequest savedTrxFuidRequest, TrxFuidRequestDetailModel tfrdm) {
        MsEmployeeHierarchy employeeHierarchy = msEmployeeHierarchyService.getMsEmployeeHierarchyByNik(savedTrxFuidRequest.getNikRequester());

        if (tfrdm.getOccupationDesc() == null || tfrdm.getOccupationDesc().isEmpty()) {
            tfrdm.setOccupationDesc(employeeHierarchy != null ? employeeHierarchy.getOccupationDesc() : EMPTY);
        }

        if (tfrdm.getOrganization() == null || tfrdm.getOrganization().isEmpty()) {
            tfrdm.setOrganization(employeeHierarchy != null ? employeeHierarchy.getOrganization() : EMPTY);
        }

        if (tfrdm.getLocation() == null || tfrdm.getLocation().isEmpty()) {
            tfrdm.setLocation(employeeHierarchy != null ? employeeHierarchy.getLocation() : EMPTY);
        }
    }

    private String resolveNIKPuk(String pukVendorNIK, TrxPUKVendor trxPUKVendor) {
        if (pukVendorNIK != null) {
            return pukVendorNIK;
        } else if (trxPUKVendor != null) {
            return trxPUKVendor.getNikPUK();
        } else {
            return EMPTY;
        }
    }

    private PUK1Model resolvePUKVendor(TrxFuidRequest trxFuidRequest, TrxPUKVendor trxPUKVendor) {
        MsEmployee msEmployee = null;

        if (trxPUKVendor != null) {
            msEmployee = msEmployeeService.getMsEmployeeByNik(trxPUKVendor.getNikPUK());
        }

        PUK1Model pukVendor = new PUK1Model();
        if (trxFuidRequest.getPukVendorNIK() != null) {
            pukVendor.setNik(trxFuidRequest.getPukVendorNIK());
        } else if (msEmployee != null) {
            pukVendor.setNik(msEmployee.getNik());
        } else {
            pukVendor.setNik(EMPTY);
        }

        if (trxFuidRequest.getPukVendorName() != null) {
            pukVendor.setNama(trxFuidRequest.getPukVendorName());
        } else if (msEmployee != null) {
            pukVendor.setNama(msEmployee.getFullName());
        } else {
            pukVendor.setNama(EMPTY);
        }

        if (trxFuidRequest.getPukVendorOccupation() != null) {
            pukVendor.setJabatan(trxFuidRequest.getPukVendorOccupation());
        } else if (msEmployee != null) {
            pukVendor.setJabatan(msEmployee.getOccupationDesc());
        } else {
            pukVendor.setJabatan(EMPTY);
        }

        return pukVendor;
    }

    private PICEmailGroupModel enrichPICEmailGroup(String nik, String name, String occupation) {
        PICEmailGroupModel picEmailGroup = new PICEmailGroupModel();
        if (!(nik == null || nik.equals(""))) {
            MsEmployee msEmployee = this.msEmployeeService.getEmployeeByNik(nik);

            picEmailGroup.setNik(msEmployee.getNik());
            picEmailGroup.setNama(name == null ? msEmployee.getFullName() : name);
            picEmailGroup.setJabatan(occupation == null ? msEmployee.getOccupation() : occupation);
        }

        return picEmailGroup;
    }

    private boolean isValidEnrichPersonnelProspera(String ticketId, Boolean isHitProspera, String currState) {
        return TRUE_FLAG_BOOL.equals(isHitProspera)
               && (CommonHelper.isTicketFuidProspera(ticketId) || CommonHelper.isTicketFuidSimplifikasi(ticketId))
               && (CURR_STATUS_APPROVED.equalsIgnoreCase(currState)
               || UPM_STATUS_INPROGRESS.equalsIgnoreCase(currState)
               || UPM_STATUS_VERIFICATION.equalsIgnoreCase(currState)
               || UPM_STATUS_PENDING.equalsIgnoreCase(currState))
               || isExpiredTicket(ticketId, currState);
    }

    private boolean isExpiredTicket(String ticketId, String currState) {
        return trxExpiredFuidRepository.findExpiredTicketFuidByTicketId(ticketId) > 0 && UPM_STATUS_DONE.equalsIgnoreCase(currState);
    }

    private List<DataAplikasiModel> buildDataAplikasiModelList(List<String> aplikasi, Map<String, MsTemaApplication> msTemaApplicationMap) {
        List<DataAplikasiModel> dataAplikasiModelList = new ArrayList<>();

        aplikasi.forEach(app -> {
            dataAplikasiModelList.add(buildDataAplikasiModel(app, msTemaApplicationMap));
        });

        return dataAplikasiModelList;
    }

    private DataAplikasiModel buildDataAplikasiModel(String app, Map<String, MsTemaApplication> msTemaApplicationMap) {
        DataAplikasiModel dataAplikasiModel = new DataAplikasiModel();

        dataAplikasiModel.setCode(msTemaApplicationMap.get(app).getParamDetailId());
        dataAplikasiModel.setDesc(msTemaApplicationMap.get(app).getParamDetailDesc());

        return dataAplikasiModel;
    }

    private ResponseModel<TrxFuidRequestDetailModel> buildResponse(ResponseStatus status, TrxFuidRequestDetailModel result) {
        ResponseModel<TrxFuidRequestDetailModel> response = new ResponseModel<>();

        response.setType(TYPE_DETAIL_FUID_GET);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }

    public TrxUpmRole getUPMProcessRole(UPMProcessModel upmPM) {
        MsEmployee msEmployee = msEmployeeService.getMsEmployeeByNik(upmPM.getNik());
        TrxUpmRole trxUpmRole = new TrxUpmRole();
        trxUpmRole.setNik(upmPM.getNik());
        if(msEmployee == null) {
            trxUpmRole = trxUpmRoleService.getTrxUpmRole(upmPM.getNik());
            upmPM.setNama(trxUpmRole.getNama());
            upmPM.setJabatan(trxUpmRole.getRole());
        } else {
            trxUpmRole.setNama(msEmployee.getFullName());
            trxUpmRole.setRole(msEmployee.getOccupationDesc());
        }
        return trxUpmRole;
    }

    public TrxUpmRole getUPMCheckerRole(UPMCheckerModel upmCM) {
        MsEmployee msEmployee = msEmployeeService.getMsEmployeeByNik(upmCM.getNik());
        TrxUpmRole trxUpmRole = new TrxUpmRole();
        trxUpmRole.setNik(upmCM.getNik());
        if(msEmployee == null) {
            trxUpmRole = trxUpmRoleService.getTrxUpmRole(upmCM.getNik());
            upmCM.setNama(trxUpmRole.getNama());
            upmCM.setJabatan(trxUpmRole.getRole());
        } else {
            trxUpmRole.setNama(msEmployee.getFullName());
            trxUpmRole.setRole(msEmployee.getOccupationDesc());
        }
        return trxUpmRole;
    }

    private List<UserProspera> mapToPersonnelProsperaList(List<UserProspera> userProspera, TrxFuidRequest tfr) {
        List<UserProspera> userProsperaList = new ArrayList<>();

        userProspera.forEach(data -> {
            if (tfr.getDataNik().equalsIgnoreCase(data.getNik())){
                if (isValidGetAllPersonnelProspera(tfr.getTrxFuidApproval().getCurrentState(), tfr.getTujuan(), tfr.getAlasan())){
                    userProsperaList.add(data);
                }else {
                    if (isValidGetActivePersonnelProspera(data, tfr.getDataNik())){
                        userProsperaList.add(data);
                    }
                }
            }
        });
        if (!userProsperaList.isEmpty()) {
            userProsperaList.forEach(data -> {
                data.setLoginName(data.getLoginName().strip());
                data.setNik(data.getNik().strip());
            });
        }
        return userProsperaList.stream().sorted(Comparator.comparing(UserProspera::getStatusId)).collect(Collectors.toList());
    }

    private boolean isValidGetAllPersonnelProspera(String currentState, String tujuan, String alasan) {
        return (UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) || CURR_STATUS_APPROVED.equalsIgnoreCase(currentState)) &&
               ((TUJUAN_PERUBAHAN_PROSPERA.equalsIgnoreCase(tujuan) || TUJUAN_ALTERNATE_DELEGASI_PROSPERA.equalsIgnoreCase(tujuan)) ||
               (TUJUAN_PENDAFTARAN_BARU_PROSPERA.equalsIgnoreCase(tujuan) && ALASAN_KARYAWAN_BARU.equalsIgnoreCase(alasan) || ALASAN_RANGKAP_JABATAN.equalsIgnoreCase(alasan) || ALASAN_LAINNYA.equalsIgnoreCase(alasan)));
    }

    private boolean isValidGetActivePersonnelProspera(UserProspera userProspera, String dataNik) {
        return STATUS_PERSONNEL_PROSPERA_ACTIVE.equalsIgnoreCase(userProspera.getStatus())
               && userProspera.getRoles() != null
               && userProspera.getRoles().size() > 0
               && dataNik.equalsIgnoreCase(userProspera.getNik());
    }

    public ResUploadModel generateDetailFuidReqPdf(TrxFuidRequestDetailModel tfrdm) throws Exception {
        List<TimelineModel> listTM = getTimeLineTicket(tfrdm.getTicketId());
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(mappingToDataSourceList(tfrdm));

        File file = ResourceUtils.getFile("classpath:pdf_detail_fuid_request.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

        Map<String, Object> parameters = mapper.toParameterMap(listTM);

        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

        ByteArrayOutputStream pdfReportStream = new ByteArrayOutputStream();
        JRPdfExporter exporter = new JRPdfExporter();
        exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
        exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(pdfReportStream));
        exporter.exportReport();

        Map<String, String> result = minioService.uploadReportFilePDF(pdfReportStream.toByteArray(), DocumentHelper.generateReportDetailFilePath(tfrdm.getTicketId(), DETAIL_FUID_FILE_NAME, PDF_EXTENSION), DETAIL_FUID_FILE_NAME);

        return new ResUploadModel(result);
    }

    private List<TimelineModel> getTimeLineTicket(String ticketId) {
        List<TimelineModel> listTM = new ArrayList<>();
        List<TrxUpmRole> listUpmRole = trxUpmRoleService.getListTrxUpmRole();
        Map<String, TrxUpmRole> mapUpmRole = this.toMap(listUpmRole);

        List<TrxAudittrail> listTa = trxAudittrailService.getTrxAudittrailByTicketId(ticketId);
        Iterator<TrxAudittrail> iterator = listTa.iterator();
        while(iterator.hasNext()) {
            TrxAudittrail ta = iterator.next();
            Gson gson = new Gson();
            TimelineModel timelineModel = gson.fromJson(ta.getAdditionalInfo(), TimelineModel.class);
            timelineModel.setAction(ta.getAction());
            timelineModel.setNik(ta.getNik());
            if (ta.getAction().equalsIgnoreCase(REASSIGN_UPM_ACTION)) {
                timelineModel.setName(mapUpmRole.get(timelineModel.getNik().toUpperCase()).getNama());
            }

            listTM.add(timelineModel);
        }

        return listTM;
    }

    private Map<String, TrxUpmRole> toMap(List<TrxUpmRole> listUpmRole) {
        Map<String, TrxUpmRole> ret = new HashMap<>();
        listUpmRole.forEach(upmRole -> {
            ret.putIfAbsent(upmRole.getNik().toUpperCase(), upmRole);
        });
        return ret;
    }

    private List mappingToDataSourceList(TrxFuidRequestDetailModel tfrdm) {
        List dataSourceList = new ArrayList();
        Map<String, String> dataSourceMap = new HashMap<>();
        boolean isProsperaTransaksiBwmp = tfrdm.getTicketId().startsWith(PREFFIX_TICKET_FUID_PROSPERA) && TIPE_KEWENANGAN_LIMIT_BWMP.equalsIgnoreCase(tfrdm.getTipeKewenanganLimit());
        boolean isProsperaPenghapusan = tfrdm.getTicketId().startsWith(PREFFIX_TICKET_FUID_PROSPERA) && TUJUAN_PENGHAPUSAN_PROSPERA.equalsIgnoreCase(tfrdm.getTujuan());
        String tujuan = this.iMsSystemParamDetailRepository.getMsSystemParamDetail(tfrdm.getTujuan()).getParamDetailDesc();
        String alasan = this.iMsSystemParamDetailRepository.getMsSystemParamDetail(tfrdm.getAlasan()).getParamDetailDesc();

        dataSourceMap.put("ticketId", tfrdm.getTicketId());
        dataSourceMap.put("tanggalEfektif", tfrdm.getTanggalEfektif());
        dataSourceMap.put("NIK", tfrdm.getData().getNIK());
        dataSourceMap.put("nama", tfrdm.getData().getNamaLengkap());
        dataSourceMap.put("jabatan", tfrdm.getData().getJabatan());
        dataSourceMap.put("userId", tfrdm.getData().getUserId());
        dataSourceMap.put("kodeDanNamaCabang", tfrdm.getData().getKodeCabang().concat(" - ").concat(tfrdm.getData().getNamaCabang()));
        dataSourceMap.put("email", tfrdm.getData().getEmail());
        dataSourceMap.put("telepon", tfrdm.getData().getTelepon());
        dataSourceMap.put("aplikasi", getListAplikasi(tfrdm.getAplikasi()));
        dataSourceMap.put("tingkatan", StringUtils.capitalize(tfrdm.getTingkatan()));
        dataSourceMap.put("statusMasaBerlaku", STATUS_MASA_BERLAKU_TETAP.equalsIgnoreCase(tfrdm.getStatusMasaBerlaku()) ? tfrdm.getStatusMasaBerlaku() : tfrdm.getMasaBerlaku());
        dataSourceMap.put("tujuan", tujuan);
        dataSourceMap.put("alasan", isProsperaTransaksiBwmp ? alasan.concat(" BWMP") : alasan);
        dataSourceMap.put("role", getRole(tfrdm));
        dataSourceMap.put("infoTambahan", tfrdm.getInfoTambahan() != null ? tfrdm.getInfoTambahan() : "");
        dataSourceMap.put("keterangan", tfrdm.getAlasanPengajuan());
        dataSourceMap.put("userIDUnitKerjaLama",  getIsInnactivePersonnelProspera(tfrdm));
        if (!StringUtils.isBlank(tfrdm.getTipeLimitTransaksi())) {
            dataSourceMap.put("limitTransaksi", tfrdm.getTipeLimitTransaksi());
        }
        if((tfrdm.getNominalTransaksi() != null || tfrdm.getNominalTransaksiUPM() != null) && !isProsperaPenghapusan) {
            NumberFormat numberFormat = NumberFormat.getInstance();
            String nominal = tfrdm.getNominalTransaksi() != null ? numberFormat.format(tfrdm.getNominalTransaksi()) : numberFormat.format(tfrdm.getNominalTransaksiUPM());
            dataSourceMap.put("nominal", CommonHelper.concateTwoString(RUPIAH_SYMBOL, nominal));
        }
        if (tfrdm.getPicEmailGroup() != null) {
            dataSourceMap.put("picEmailGroup", tfrdm.getPicEmailGroup().getNik().concat(" - ").concat(tfrdm.getPicEmailGroup().getNama()));
            dataSourceMap.put("altPicEmailGroup", tfrdm.getAltPicEmailGroup().getNik().concat(" - ").concat(tfrdm.getAltPicEmailGroup().getNama()));
        }
        if (tfrdm.getProgress().containsKey("puk1")){
            PUK1Model puk1 = (PUK1Model) tfrdm.getProgress().get("puk1");
            if (CURR_STATUS_APPROVED.equalsIgnoreCase(puk1.getStatus().getKeyStatus())){
                dataSourceMap.put("nikPUK1", puk1.getNik());
                dataSourceMap.put("namaPUK1", puk1.getNama());
                dataSourceMap.put("jabatanPUK1", puk1.getJabatan());
            }
        }
        if (tfrdm.getProgress().containsKey("puk2")){
            PUK2Model puk2 = (PUK2Model) tfrdm.getProgress().get("puk2");
            if (CURR_STATUS_APPROVED.equalsIgnoreCase(puk2.getStatus().getKeyStatus())){
                dataSourceMap.put("nikPUK2", puk2.getNik());
                dataSourceMap.put("namaPUK2", puk2.getNama());
                dataSourceMap.put("jabatanPUK2", puk2.getJabatan());
            }
        }

        if (tfrdm.getProgress().containsKey("upmProcess")){
            UPMProcessModel upmPM = (UPMProcessModel) tfrdm.getProgress().get("upmProcess");
            if(UPM_STATUS_VERIFICATION.equalsIgnoreCase(upmPM.getStatus().getKeyStatus())){
                dataSourceMap.put("catatan", reformatUPMNotes(upmPM.getNotes()));
            }
        }
        dataSourceMap.put("statusTiket", tfrdm.getStatus().getValueStatus());

        dataSourceList.add(dataSourceMap);
        return dataSourceList;
    }

    private String reformatUPMNotes(String notes) {
        if (notes == null) {
            return EMPTY;
        }

        return notes.replaceAll("\t+", " ");
    }

    private String getRole(TrxFuidRequestDetailModel tfrdm) {
        return StringUtils.isNotBlank(tfrdm.getRole().getCode()) ? iMsProsperaRoleRepository.findAllByTemaRoleCode(tfrdm.getRole().getCode()).getRoleDesc() : EMPTY;
    }

    private String getIsInnactivePersonnelProspera(TrxFuidRequestDetailModel fuidRequestDetail) {
        if (fuidRequestDetail.getTicketId().startsWith(PREFFIX_TICKET_FUID_PROSPERA)) {
            return fuidRequestDetail.getIsInActivePersonnelProspera() == 1 ? HAPUS : TIDAK_HAPUS;
        }

        return EMPTY;
    }

    private String getListAplikasi(List<DataAplikasiModel> dataAplikasiModelList) {
        List<String> applicationList = new ArrayList<>();

        dataAplikasiModelList.forEach(app -> {
           applicationList.add(app.getDesc());
        });

        return StringUtils.join(applicationList, ", ");
    }

    public ResponseModel<ResTrxFuidRequest> saveTrxFuidRequestUPM(TrxFuidRequestModel trxFuidRequestModel) throws Exception {
        DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
        String ticketId = "DF9912280001";

        if (trxFuidRequestModel.getTicketId() == null || trxFuidRequestModel.getTicketId() == "") {
            String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());
            // cek last ticket di database
            String lastTicketId = getLastTicketIdByInputType(INPUT_TYPE_MANUAL);
            logger.info("getLastTicketId: " + lastTicketId);
            if (lastTicketId == null) {
                ticketId = "FUM" + currDtTicket + "0001";
            } else {
                String strlastDate = lastTicketId.substring(3, 9);
                logger.info("strlastDate: " + strlastDate);
                // cek tanggalnya apakah sama dengan currentdate
                if (strlastDate.equals(currDtTicket)) {
                    String strTicketNum = lastTicketId.substring(9, 13);
                    logger.info("strTicketNum: " + strTicketNum);
                    Integer ticketNum = Integer.parseInt(strTicketNum) + 1;
                    ticketId = "FUM" + currDtTicket + String.format("%04d", ticketNum);
                } else {
                    ticketId = "FUM" + currDtTicket + "0001";
                }
            }
        }
        trxFuidRequestModel.setTicketId(ticketId);

        // cek duplicate request
        boolean isSuccesRequest = false;

        if (!isDuplicateRequestId(trxFuidRequestModel.getRequestId())) {
            trxFuidRequestModel = enrichDataByAplikasiAndAlasan(trxFuidRequestModel);
            // save to TrxFuidRequest
            TrxFuidRequest tfr = Mapper.toTrxFuidRequestEntity(trxFuidRequestModel);
            tfr.setNikRequester(trxFuidRequestModel.getFuid().getNikRequester());
            tfr.setDataNamaCabang(trxFuidRequestService.getNamaCabangMms(tfr.getDataKodeCabang()));
            tfr.setInputType(INPUT_TYPE_MANUAL);
            TrxFuidRequest savedTFR = saveTrxFuidRequest(tfr);

            // save to TrxFuidRequestAplikasi
            if (trxFuidRequestModel.getFuid().getAplikasi().size() > 0) {
                List<String> lAplikasi = trxFuidRequestModel.getFuid().getAplikasi();
                for (String aplikasi : lAplikasi) {
                    MsTemaApplication msta = msTemaApplicationService.getMsTemaApplication(aplikasi);
                    TrxFuidRequestAplikasi tfrap = Mapper.toTrxFuidRequestAplikasiEntity(ticketId, msta);
                    trxFuidRequestAplikasiService.saveTrxFuidRequestAplikasi(tfrap);
                }
            }

            // save to TrxFuidApproval
            String upmInputNIK = "", upmCheckerNIK = "";
            TrxFuidApproval tfra = Mapper.toTrxFuidApprovalEntity(trxFuidRequestModel.getTicketId());
            tfra.setCurrentState(UPM_STATUS_DONE);
            upmInputNIK = trxFuidRequestModel.getFuid().getUpmInputNIK() == null ? "" : trxFuidRequestModel.getFuid().getUpmInputNIK();
            if (!upmInputNIK.isEmpty()) {
                tfra.setUpmInputNIK(upmInputNIK);
                tfra.setUpmInputStatus(UPM_STATUS_VERIFICATION);
                tfra.setUpmInputDt(LocalDateTime.now());
            }
            upmCheckerNIK = trxFuidRequestModel.getFuid().getUpmCheckerNIK() == null ? "" : trxFuidRequestModel.getFuid().getUpmCheckerNIK();
            if (!upmCheckerNIK.isEmpty()){
                tfra.setUpmCheckerNIK(upmCheckerNIK);
                tfra.setUpmCheckerStatus(UPM_STATUS_DONE);
                tfra.setUpmCheckerDt(LocalDateTime.now());
            }
            TrxFuidApproval savedTFRA = trxFuidApprovalService.saveTrxFuidApproval(tfra);

            // save to TrxPUKVendor if non FTE
            if (isValidSaveTrxPUKVEndor(savedTFR, trxFuidRequestModel)) {
                TrxPUKVendor trxPUKVendor = Mapper.toTrxPUKVendor(savedTFR, trxFuidRequestModel);
                trxPUKVendorService.saveTrxPUKVendor(trxPUKVendor);
            }

            // save to TrxAudittrail
            List<TrxAudittrail> trxAudittrailList = new ArrayList<>();
            trxAudittrailList.add(mapper.buildTrxAudittrail(ticketId, upmInputNIK, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM));
            trxAudittrailList.add(mapper.buildTrxAudittrail(ticketId, upmCheckerNIK, UPM_STATUS_DONE, TIMELINE_STATUS_DONE, TIMELINE_PIC_UPM_APPROVE));
            List<TrxAudittrail> savedTa = iTrxAudittrailRepository.saveAll(trxAudittrailList);

            if (savedTFR != null && savedTFRA != null && savedTa != null) {
                isSuccesRequest = true;
            }
        }

        return buildResponse(trxFuidRequestModel, isSuccesRequest);
    }

    private ResponseModel<ResTrxFuidRequest> buildResponse(TrxFuidRequestModel trxFuidRequestModel, boolean isSuccesRequest) {
        ResponseModel<ResTrxFuidRequest> response = new ResponseModel<>();

        response.setType(TYPE_UPM_FUID_REQUEST);
        response.setDetails(buildResTrxFuidRequestModel(trxFuidRequestModel));
        if(isSuccesRequest){
            response.setStatus(SUCCESS.getCode());
            response.setStatusDesc(SUCCESS.getValue());
        } else {
            response.setStatus(FAILED.getCode());
            response.setStatusDesc(FAILED.getValue());
        }

        return response;
    }

    public boolean isDuplicateRequestId(String requestId) throws ParseException {
        boolean isDuplicate = false;
        //jika requestId ada di db return true, lainnya false
        if(checkDuplicateRequestId(requestId) != null){
            isDuplicate = true;
        }
        return isDuplicate;
    }

    private boolean isValidSaveTrxPUKVEndor(TrxFuidRequest savedTFR, TrxFuidRequestModel trxFuidRequestModel) {
        return savedTFR.getTujuan().equals(TUJUAN_PENDAFTARAN_BARU)
                && savedTFR.getAlasan().equals(ALASAN_KARYAWAN_BARU)
                && savedTFR.getTipeKaryawanBaru().equals(TIPE_KARYAWAN_BARU_NON_FTE)
                && trxFuidRequestModel.getFuid().getData().getNIK() != null
                && trxFuidRequestModel.getFuid().getData().getNikPUKVendor() != null;
    }

    public ResponseModel<ResTrxFuidRequest> saveTrxFuidRequestProspera(TrxFuidRequestModel trxFuidRequestModel, Token profile) throws Exception {
        DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
        String nikRequester = profile.getProfile().getPreferred_username();
        String ticketId = "FUP9912280001";
        boolean isUpdated = false, isSuccesRequest = false, isSuccessSendNotif = false;

        if (trxFuidRequestModel.getTicketId() == null || trxFuidRequestModel.getTicketId() == "") {
            String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());
            // cek last ticket di database
            String lastTicketId = getLastTicketIdByInputType(INPUT_TYPE_PROSPERA);
            logger.info("getLastTicketId: " + lastTicketId);
            if (lastTicketId == null) {
                ticketId = "FUP" + currDtTicket + "0001";
            } else {
                String strlastDate = lastTicketId.substring(3, 9);
                logger.info("strlastDate: " + strlastDate);
                // cek tanggalnya apakah sama dengan currentdate
                if (strlastDate.equals(currDtTicket)) {
                    String strTicketNum = lastTicketId.substring(9, 13);
                    logger.info("strTicketNum: " + strTicketNum);
                    Integer ticketNum = Integer.parseInt(strTicketNum) + 1;
                    ticketId = "FUP" + currDtTicket + String.format("%04d", ticketNum);
                } else {
                    ticketId = "FUP" + currDtTicket + "0001";
                }
            }
        }else {
            //update ticket if rejected
            isUpdated = true;
            ticketId = trxFuidRequestModel.getTicketId();
        }
        trxFuidRequestModel.setTicketId(ticketId);
        trxFuidRequestModel.getFuid().setNikRequester(nikRequester);

        // cek duplicate request
        if (!isDuplicateRequestId(trxFuidRequestModel.getRequestId())) {
            String currState = "", puk1State = "", puk2State = "";

            // save to TrxFuidRequest
            trxFuidRequestModel = enrichDataByAplikasiAndAlasan(trxFuidRequestModel);
            TrxFuidRequest tfr = Mapper.toTrxFuidRequestEntity(trxFuidRequestModel);
            tfr.setNikRequester(nikRequester);
            tfr.setDataNamaCabang(trxFuidRequestService.getNamaCabangMms(tfr.getDataKodeCabang()));
            tfr.setInputType(INPUT_TYPE_PROSPERA);
            tfr.setIsInActivePersonnelProspera(ALASAN_MUTASI_ROTASI_PROMOSI.equalsIgnoreCase(trxFuidRequestModel.getFuid().getAlasan()) ? trxFuidRequestModel.getFuid().getIsInActivePersonnelProspera() : 0);
            String role = "";
            if(trxFuidRequestModel.getFuid().getRole() != null && !trxFuidRequestModel.getFuid().getRole().equals("")) {
                role = trxFuidRequestModel.getFuid().getRole();
            }
            tfr.setRole(role);
            TrxFuidRequest savedTFR = new TrxFuidRequest();
            if (isUpdated) {
                savedTFR = trxFuidRequestService.updateTrxFuidRequest(tfr);
            } else {
                savedTFR = trxFuidRequestService.saveTrxFuidRequest(tfr);
            }

            // save to TrxFuidRequestAplikasi
            if (isUpdated) {
                trxFuidRequestAplikasiService.deleteTrxFuidRequestAplikasiByTicketId(ticketId);
            }else {
                if (trxFuidRequestModel.getFuid().getAplikasi().size() > 0) {
                    List<String> lAplikasi = trxFuidRequestModel.getFuid().getAplikasi();
                    for (String aplikasi : lAplikasi) {
                        MsTemaApplication msta = msTemaApplicationService.getMsTemaApplication(aplikasi);
                        TrxFuidRequestAplikasi tfrap = Mapper.toTrxFuidRequestAplikasiEntity(ticketId, msta);
                        trxFuidRequestAplikasiService.saveTrxFuidRequestAplikasi(tfrap);
                    }
                }
            }

            // save to TrxFuidApproval
            String puk1Nik = "", puk2Nik = "";
            TrxFuidApproval tfra = Mapper.toTrxFuidApprovalEntity(trxFuidRequestModel.getTicketId());
            puk1Nik = trxFuidRequestModel.getFuid().getApproval().getPuk1() == null ? "" : trxFuidRequestModel.getFuid().getApproval().getPuk1();
            if (puk1Nik != "") {
                //check delegation
                if (trxDelegationService.getTrxDelegationByNikRequester(puk1Nik) != null) {
                    tfra.setPuk1DelegationId(trxDelegationService.getTrxDelegationByNikRequester(puk1Nik).getDelegationId());
                    puk1Nik = trxDelegationService.getTrxDelegationByNikRequester(puk1Nik).getNikDelegation();
                }
                MsEmployee puk1 = msEmployeeService.getEmployeeByNik(puk1Nik);
                currState = CURR_STATUS_WAITING_PUK1;
                puk1State = PUK1_STATUS_WAITING;
                tfra.setPuk1NIK(puk1Nik);
                tfra.setPuk1Dt(tfr.getCreateDateTime());
                tfra.setPuk1Name(puk1.getFullName());
                tfra.setPuk1Occupation(puk1.getOccupationDesc());
                tfra.setPuk1Status(puk1State);
                tfra.setPuk1Notes("");
            }
            puk2Nik = trxFuidRequestModel.getFuid().getApproval().getPuk2() == null ? "" : trxFuidRequestModel.getFuid().getApproval().getPuk2();
            if (puk2Nik != "") {
                //check delegation
                if (trxDelegationService.getTrxDelegationByNikRequester(puk2Nik) != null) {
                    tfra.setPuk2DelegationId(trxDelegationService.getTrxDelegationByNikRequester(puk2Nik).getDelegationId());
                    puk2Nik = trxDelegationService.getTrxDelegationByNikRequester(puk2Nik).getNikDelegation();
                }
                MsEmployee puk2 = msEmployeeService.getEmployeeByNik(puk1Nik);
                puk2State = PUK2_STATUS_WAITING;
                tfra.setPuk2NIK(puk2Nik);
                tfra.setPuk2Name(puk2.getFullName());
                tfra.setPuk2Occupation(puk2.getOccupationDesc());
                tfra.setPuk2Dt(tfr.getCreateDateTime());
                tfra.setPuk2Status(puk2State);
                tfra.setPuk2Notes("");
            }
            tfra.setCurrentState(currState);
            TrxFuidApproval savedTFRA = new TrxFuidApproval();
            if (isUpdated) {
                savedTFRA = trxFuidApprovalService.updateTrxFuidApproval(tfra);
            } else {
                savedTFRA = trxFuidApprovalService.saveTrxFuidApproval(tfra);
            }

            // save to TrxPUKVendor if non FTE
            if (isValidSaveTrxPUKVEndor(savedTFR, trxFuidRequestModel)) {
                TrxPUKVendor trxPUKVendor = Mapper.toTrxPUKVendor(savedTFR, trxFuidRequestModel);
                LocalDateTime currDt = LocalDateTime.now();
                if (isUpdated || trxPUKVendorService.findByNikVendor(trxFuidRequestModel.getFuid().getData().getNIK()) != null) {
                    trxPUKVendor.setUpdateDateTime(currDt);
                    trxPUKVendorService.updateTrxPUKVendor(trxPUKVendor);
                } else {
                    trxPUKVendor.setCreateDatetime(currDt);
                    trxPUKVendor.setUpdateDateTime(currDt);
                    trxPUKVendorService.saveTrxPUKVendor(trxPUKVendor);
                }
            }

            // save to TrxAudittrail
            TrxAudittrail ta = Mapper.toTrxAudittrailEntity(trxFuidRequestModel.getTicketId());
            ta.setNik(nikRequester);
            ta.setAction(currState);
            //json additional info
            TimelineStatusModel tsm = new TimelineStatusModel();
            if (isUpdated) {
                tsm.setStatus(TIMELINE_STATUS_RESUBMIT_TICKET);
            } else {
                tsm.setStatus(TIMELINE_STATUS_CREATE_TICKET);
            }
            tsm.setPic(TIMELINE_PIC_USER + nikRequester + " - " + profile.getProfile().getName());
            tsm.setTimestamp(dateFormater3.format(ta.getCreateDateTime()));
            ta.setAdditionalInfo(new Gson().toJson(tsm));
            TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(ta);

            //send email
            if (puk1Nik != "") {
                TrxFuidRequest email = mapper.buildFormatedContentEmailTicketFU(savedTFR, savedTFRA);
                MsEmployee waitingApprovalPUK = msEmployeeService.getMsEmployeeByNik(savedTFRA.getPuk1NIK());

                sendEmailRequest(profile, email, puk1Nik, validateCCNikDirectPuk(email), waitingApprovalPUK);
            }

            if (savedTFR != null && savedTFRA != null && savedTa != null) {
                isSuccesRequest = true;
            }
        }

        return buildResponseFSP(trxFuidRequestModel, isSuccesRequest);
    }

    private String validateCCNikDirectPuk(TrxFuidRequest savedTFR) {
        String nikDirectPuk = "";
        TrxFuidApproval savedTFA = savedTFR.getTrxFuidApproval();
        MsEmployee directPUK = msEmployeeService.getDirectPUK(savedTFR.getNikRequester());
        if (directPUK != null) {
            boolean isCCDirectPuk = false;
            if (!savedTFA.getPuk1NIK().equals(directPUK.getNik())) {
                isCCDirectPuk = true;
            }
            if (isCCDirectPuk) {
                nikDirectPuk = directPUK.getNik();
            }
        }
        return nikDirectPuk;
    }

    private void sendEmailRequest(Token profile, TrxFuidRequest savedTFR, String puk1Nik, String ccNikDirectPuk, MsEmployee waitingApprovalPUK) {
        //send to requester
        createRequestAsync(profile.getProfile().getPreferred_username(), savedTFR, waitingApprovalPUK);
        //send to approval
        createApprovalAsync(puk1Nik, savedTFR, ccNikDirectPuk, waitingApprovalPUK);
    }

    private void createRequestAsync(String userNik, TrxFuidRequest trxFuidRequest, MsEmployee waitingApprovalPUK) {
        emailNotificationService.sendCreateRequestFuidToUpmTemaNotification(userNik, trxFuidRequest, waitingApprovalPUK);
    }

    private void createApprovalAsync(String pukNik, TrxFuidRequest trxFuidRequest, String ccNikDirectPuk, MsEmployee waitingPUK) {
        //check director
        HashMap<String, String> mapPUKDirector = msEmployeeDirectorService.isPukDirectorByNikOptima(pukNik);
        emailNotificationService.sendCreateApprovalFuidToUpmTemaNotification(pukNik, trxFuidRequest, ccNikDirectPuk, waitingPUK, mapPUKDirector);
    }

    private ResponseModel<ResTrxFuidRequest> buildResponseFSP(TrxFuidRequestModel trxFuidRequestModel, boolean isSuccesRequest) {
        ResponseModel<ResTrxFuidRequest> response = new ResponseModel<>();

        response.setType(TYPE_FUID_REQUEST_PROSPERA);
        response.setDetails(buildResTrxFuidRequestModel(trxFuidRequestModel));
        if(isSuccesRequest){
            response.setStatus(SUCCESS.getCode());
            response.setStatusDesc(SUCCESS.getValue());
        } else {
            response.setStatus(FAILED.getCode());
            response.setStatusDesc(FAILED.getValue());
        }

        return response;
    }

    private ResTrxFuidRequest buildResTrxFuidRequestModel(TrxFuidRequestModel trxFuidRequestModel) {
        ResTrxFuidRequest resTrxFuidRequestModel = new ResTrxFuidRequest();
        resTrxFuidRequestModel.setTicketId(trxFuidRequestModel.getTicketId());

        return resTrxFuidRequestModel;
    }

    public List<TrxFuidRequest> getTrxFuidReqJoinApprovalWaitPuk1(String puk1NIK){
        return trxFuidRequestRepository.getTrxFuidReqJoinApprovalWaitPuk1(puk1NIK);
    }

    public List<TrxFuidRequest> getTrxFuidReqJoinApprovalWaitPuk2(String puk2NIK){
        return trxFuidRequestRepository.getTrxFuidReqJoinApprovalWaitPuk2(puk2NIK);
    }

    public TrxFuidRequestModel enrichDataByAplikasiAndAlasan(TrxFuidRequestModel fuidRequestModel) {
        if (TIPE_KARYAWAN_BARU_NON_FTE.equals(fuidRequestModel.getFuid().getTipeKaryawanBaru())) {
            enrichDataPUKVendor(fuidRequestModel);
        }
        if (ALASAN_MUTASI_ROTASI_PROMOSI.equals(fuidRequestModel.getFuid().getAlasan())) {
            enrichDataEmployeeOptima(fuidRequestModel);
        }
        if (fuidRequestModel.getFuid().getAplikasi().contains(KODE_APLIKASI_EMAIL_GROUP)) {
            enrichDataPICEmailGroup(fuidRequestModel);
        }

        return fuidRequestModel;
    }

    private void enrichDataEmployeeOptima(TrxFuidRequestModel fuidRequestModel) {
        MsEmployeeHierarchy msEmployeeHierarchy = msEmployeeHierarchyService.getMsEmployeeHierarchyByNik(fuidRequestModel.getFuid().getNikRequester());

        FuidModel fuidModel = fuidRequestModel.getFuid();

        fuidModel.setOccupationDesc(msEmployeeHierarchy != null ? msEmployeeHierarchy.getOccupationDesc() : EMPTY);
        fuidModel.setOrganization(msEmployeeHierarchy != null ? msEmployeeHierarchy.getOrganization() : EMPTY);
        fuidModel.setLocation(msEmployeeHierarchy != null ? msEmployeeHierarchy.getLocation() : EMPTY);
    }

    private void enrichDataPICEmailGroup(TrxFuidRequestModel fuidRequestModel) {
        MsEmployee picEmailGroup = msEmployeeService.getEmployeeByNik(fuidRequestModel.getFuid().getPicEmailGroup());
        MsEmployee altPicEmailGroup = msEmployeeService.getEmployeeByNik(fuidRequestModel.getFuid().getAltPicEmailGroup());

        FuidModel fuidModel = fuidRequestModel.getFuid();

        fuidModel.setPicEmailGroupName(picEmailGroup.getFullName());
        fuidModel.setPicEmailGroupOccupation(picEmailGroup.getOccupationDesc());
        fuidModel.setAltPicEmailGroupName(altPicEmailGroup.getFullName());
        fuidModel.setAltPicEmailGroupOccupation(altPicEmailGroup.getOccupationDesc());
    }

    private void enrichDataPUKVendor(TrxFuidRequestModel fuidRequestModel) {
        MsEmployee pukVendor = msEmployeeService.getEmployeeByNik(fuidRequestModel.getFuid().getData().getNikPUKVendor());

        DataFuidModel dataFuidModel = fuidRequestModel.getFuid().getData();

        dataFuidModel.setNamaPUKVendor(pukVendor.getFullName());
        dataFuidModel.setJabatanPUKVendor(pukVendor.getOccupationDesc());
    }

    public ResFileDownload directDownloadPdfDetailFuid(TrxFuidRequestDetailModel tfrdm) throws Exception {
        File file = ResourceUtils.getFile("classpath:pdf_detail_fuid_request.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

        List<TimelineModel> listTM = getTimeLineTicket(tfrdm.getTicketId());
        Map<String, Object> parameters = mapper.toParameterMap(listTM);

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(mappingToDataSourceList(tfrdm));

        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

        return new ResFileDownload(pdfReportStream.toByteArray(), DETAIL_FUID_FILE_NAME.concat(PDF_EXTENSION));
    }

    public TrxFuidApprovalDTO mappingTrxFuidApprovalDTO(TrxFuidApproval trxFuidApproval) {
        TrxFuidApprovalDTO dto = new TrxFuidApprovalDTO();

        dto.setId(trxFuidApproval.getId());
        dto.setTicketId(trxFuidApproval.getTicketId());
        dto.setCurrentState(trxFuidApproval.getCurrentState());
        dto.setCurrentStateDT(trxFuidApproval.getCurrentStateDT());
        dto.setTrxFuidRequest(trxFuidApproval.getTrxFuidRequest());
        dto.setPuk1NIK(trxFuidApproval.getPuk1NIK());
        dto.setPuk2NIK(trxFuidApproval.getPuk2NIK());
        dto.setPuk1Name(trxFuidApproval.getPuk1Name());
        dto.setPuk2Name(trxFuidApproval.getPuk2Name());
        dto.setPuk1Occupation(trxFuidApproval.getPuk1Occupation());
        dto.setPuk2Occupation(trxFuidApproval.getPuk2Occupation());
        dto.setPuk1Status(trxFuidApproval.getPuk1Status());
        dto.setPuk1Dt(trxFuidApproval.getPuk1Dt());
        dto.setPuk1Notes(trxFuidApproval.getPuk1Notes());
        dto.setPuk2Status(trxFuidApproval.getPuk2Status());
        dto.setPuk2Dt(trxFuidApproval.getPuk2Dt());
        dto.setPuk2Notes(trxFuidApproval.getPuk2Notes());
        dto.setPuk1DelegationId(trxFuidApproval.getPuk1DelegationId());
        dto.setPuk1ApprovalReminder(trxFuidApproval.getPuk1ApprovalReminder());
        dto.setPuk2DelegationId(trxFuidApproval.getPuk2DelegationId());
        dto.setPuk2ApprovalReminder(trxFuidApproval.getPuk2ApprovalReminder());
        dto.setUpmInputNIK(trxFuidApproval.getUpmInputNIK());
        dto.setUpmInputStatus(trxFuidApproval.getUpmInputStatus());
        dto.setUpmInputDt(trxFuidApproval.getUpmInputDt());
        dto.setUpmInputNotes(trxFuidApproval.getUpmInputNotes());
        dto.setUpmCheckerNIK(trxFuidApproval.getUpmCheckerNIK());
        dto.setUpmCheckerStatus(trxFuidApproval.getUpmCheckerStatus());
        dto.setUpmCheckerDt(trxFuidApproval.getUpmCheckerDt());
        dto.setUpmCheckerNotes(trxFuidApproval.getUpmCheckerNotes());
        dto.setUpmInputAttachment(trxFuidApproval.getUpmInputAttachment());
        dto.setSlaValue(trxFuidApproval.getSlaValue());
        dto.setSlaInfo(trxFuidApproval.getSlaInfo());
        return dto;
    }
}
