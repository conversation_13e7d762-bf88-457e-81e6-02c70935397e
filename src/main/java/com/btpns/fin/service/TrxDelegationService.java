package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.DelegationDetailModel;
import com.btpns.fin.model.entity.TrxDelegation;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.ITrxDelegationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class TrxDelegationService {
    @Autowired
    ITrxDelegationRepository trxDelegationRepository;

    @Autowired
    MinioService minioService;

    @Transactional
    public TrxDelegation saveTrxDelegation(TrxDelegation trxDelegation){
        return trxDelegationRepository.save(trxDelegation);
    }

    public String getLastDelegationId(){
        return trxDelegationRepository.getLastDelegationId();
    }

    public TrxDelegation getTrxDelegationByNikRequester(String nikRequester) throws ParseException {
        Date dateNow = new Date();
        String sDateNow = DateTimeHelper.getDateDelegationAsString(dateNow);
        return trxDelegationRepository.getTrxDelegationByNikRequester(nikRequester, DateTimeHelper.getDateDelegation(sDateNow), Constants.DELEGATION_STATUS_ACTIVE);
    }

    public TrxDelegation getTrxDelegationByDelegationId(String delegationId) throws ParseException {
        return trxDelegationRepository.getTrxDelegationByDelegationId(delegationId);
    }

    @Transactional
    public int deleteTrxDelegationByNikRequester(String nikrequester){
        return trxDelegationRepository.deleteTrxDelegationByNikRequester(nikrequester);
    }

    @Transactional
    public TrxDelegation updateTrxDelegationToInactive(TrxDelegation trxDelegation){
        Optional<TrxDelegation> existTrxDelegation = trxDelegationRepository.findById(trxDelegation.getDelegationId());
        if (existTrxDelegation.isPresent()) {
            LocalDateTime dateNow = LocalDateTime.now();
            trxDelegation.setStatus(Constants.DELEGATION_STATUS_INACTIVE);
            trxDelegation.setUpdateDateTime(dateNow);
        }
        return trxDelegationRepository.save(trxDelegation);
    }

    public Page<TrxDelegation> getListTrxDelegationByNikRequester(String nikrequester, int pageNumMin1, int pageSize) throws ParseException{
        Pageable sortedByDelegationId = PageRequest.of(pageNumMin1, pageSize, Sort.by("delegationId").descending());
        return trxDelegationRepository.getListTrxDelegationByNikRequester(nikrequester, sortedByDelegationId);
    }

    public TrxDelegation getDelegationActiveByNikRequester(String nikRequester) throws ParseException {
        return trxDelegationRepository.getDelegationActiveByNikRequester(nikRequester, Constants.DELEGATION_STATUS_ACTIVE);
    }

    public boolean existInInterval(String nik, int interval) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return trxDelegationRepository.checkRequestInterval(startDate, endDate, nik) > 0;
    }

    public Page<TrxDelegation> getListTrxDelegationByDateAndStatus(String startDate, String endDate, String status, int pageNumMin1, int pageSize) throws ParseException {
        Pageable pageable = PageRequest.of(pageNumMin1, pageSize);
        return trxDelegationRepository.getListTrxDelegationByDateAndStatus(startDate, endDate, pageable, status);
    }

    public ResponseModel<ResUploadModel> generateReportDelegasiCsv(String startPeriod, String endPeriod, String status) throws Exception {
        List<TrxDelegation> trxDelegationList = trxDelegationRepository.getListTrxDelegationByDateAndStatus(startPeriod, endPeriod, status);
        List<DelegationDetailModel> detailModelList = trxDelegationList.stream().map(a -> Mapper.toDelegationDetailModel(a)).collect(Collectors.toList());
        byte[] byteCSV = makeCsv(detailModelList);
        Map<String, String> result = minioService.uploadReportFile(byteCSV, DocumentHelper.generateReportFilePath(REPORT_DELEGASI_FILE_NAME, CSV_EXTENSION), REPORT_DELEGASI_FILE_NAME);
        
        return buildResponse(SUCCESS, new ResUploadModel(result));
    }

    public ResFileDownload directDownloadReportDelegasiCSV(String startPeriod, String endPeriod, String status) throws Exception {
        List<TrxDelegation> trxDelegationList = trxDelegationRepository.getListTrxDelegationByDateAndStatus(startPeriod, endPeriod, status);
        List<DelegationDetailModel> detailModelList = trxDelegationList.stream().map(a -> Mapper.toDelegationDetailModel(a)).collect(Collectors.toList());
        byte[] byteCSV = makeCsv(detailModelList);

        return new ResFileDownload(byteCSV, CommonHelper.concateTwoString(REPORT_DELEGASI_FILE_NAME, CSV_EXTENSION));
    }

    private byte[] makeCsv(List<DelegationDetailModel> detailModelList) throws UnsupportedEncodingException {
        StringBuffer csv= new StringBuffer();
        csv.append("\""+TICKET_ID_COL_NAME+"\",\""+NIK_USER_COL_NAME+"\",\""+NAMA_USER_COL_NAME+"\",\""+JABATAN_USER_COL_NAME+"\",\""
                   +TANGGAL_MULAI_COL_NAME+"\",\""+TANGGAL_SELESAI_COL_NAME+"\",\""+KETERANGAN_COL_NAME+"\",\""+NIK_PENERIMA_TUGAS_COL_NAME+"\",\""
                   +NAMA_PENERIMA_TUGAS_COL_NAME+"\",\""+JABATAN_PENERIMA_TUGAS_COL_NAME+"\",\""+STATUS_COL_NAME+"\""+"\r\n");

        detailModelList.forEach(data -> {
            csv.append("\""+data.getId()+"\",\""+"\'"+data.getUser().getNik()+"\",\""+data.getUser().getNama()+"\",\""+data.getUser().getJabatan()+"\",\""
                    +data.getStartDate()+"\",\""+data.getEndDate()+"\",\""+data.getInfo()+"\",\""+"\'"+data.getDelegatedUser().getNik()+"\",\""
                    +data.getDelegatedUser().getNama()+"\",\""+data.getDelegatedUser().getJabatan()+"\",\""+data.getStatus()+"\""+"\r\n");
        });

        return csv.toString().getBytes("windows-1252");
    }

    private ResponseModel<ResUploadModel> buildResponse(ResponseStatus status, ResUploadModel result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(TYPE_DELEGASI_DOWNLOAD);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;

    }
}
