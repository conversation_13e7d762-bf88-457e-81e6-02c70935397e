package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.BiCACUserModel;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsBiCAC;
import com.btpns.fin.model.entity.MsCMS;
import com.btpns.fin.model.entity.TrxUserIdBatch;
import com.btpns.fin.model.request.ReqBiCACUserModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMsBiCACUserManagementRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.FAILED;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
@Transactional
public class MsBiCACUserManagementService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    private IMsBiCACUserManagementRepository iMsBiCACUserManagementRepository;
    
    @Autowired
    private ITrxUserIdBatchRepository iTrxUserIdBatchRepository;
    
    @Autowired
    private MsEmployeeService msEmployeeService;

    @Autowired
    private ExcelHelper excelHelper;

    @Autowired
    private MinioService minioService;

    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResponseListModel> getListBiCACUsers(int pageNumMin1, Integer pageNumber, Integer pageSize, String searchFlag, String searchData) {
        Page<MsBiCAC> msBiCACUsers = new PageImpl<>(new ArrayList<>());
        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)) {
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NIK)) {
                msBiCACUsers = this.iMsBiCACUserManagementRepository.findAllByNik(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NAME)) {
                msBiCACUsers = this.iMsBiCACUserManagementRepository.findAllByNamaUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        } else {
            msBiCACUsers = this.iMsBiCACUserManagementRepository.findAll(PageRequest.of(pageNumMin1, pageSize));
        }

        return Mapper.buildResponseList(TYPE_MS_BI_CAC_MANAGEMENT_GET_LIST, SUCCESS, buildResUserIdListModel(pageSize, pageNumber, msBiCACUsers.getContent(), msBiCACUsers.getTotalPages(), msBiCACUsers.getTotalElements()));
    }

    private ResponseListModel buildResUserIdListModel(Integer pageSize, Integer pageNumber, List<MsBiCAC> msBiCAC, int totalPages, long totalItems) {
        ResponseListModel responseListModel = new ResponseListModel();

        responseListModel.setData(mapToBiCACUserModelList(msBiCAC));
        responseListModel.setLimit(pageSize);
        responseListModel.setPage(pageNumber);
        responseListModel.setTotalPages(totalPages);
        responseListModel.setTotalItems(totalItems);

        return responseListModel;
    }

    private List<BiCACUserModel> mapToBiCACUserModelList(List<MsBiCAC> biCACUsers) {
        List<BiCACUserModel>  biCACUserModelList = new ArrayList<>();
        for (MsBiCAC biCACUser : biCACUsers) {
            BiCACUserModel biCACUserModel = this.mapToBiCACUserModel(biCACUser);
            biCACUserModelList.add(biCACUserModel);
        }

        return biCACUserModelList;
    }

    private BiCACUserModel mapToBiCACUserModel(MsBiCAC biCACUser) {
        BiCACUserModel biCACUserModel = new BiCACUserModel();
        biCACUserModel.setIdUser(biCACUser.getIdUser());
        biCACUserModel.setNik(biCACUser.getNik());
        biCACUserModel.setNamaUser(biCACUser.getNamaUser());
        biCACUserModel.setEmail(biCACUser.getEmail());
        biCACUserModel.setInstitusi(biCACUser.getInstitusi());
        biCACUserModel.setUnitKerja(biCACUser.getUnitKerja());
        biCACUserModel.setGrupUser(biCACUser.getGrupUser());
        biCACUserModel.setStatusUser(biCACUser.getStatusUser());
        biCACUserModel.setStatusLogin(biCACUser.getStatusLogin());
        if (biCACUser.getTanggalAktif() != null) {
            biCACUserModel.setTanggalAktif(DateTimeHelper.getDateToDateStringDDMMYYYY(biCACUser.getTanggalAktif()));
        }
        if (biCACUser.getTanggalNonAktif() != null) {
            biCACUserModel.setTanggalNonAktif(DateTimeHelper.getDateToDateStringDDMMYYYY(biCACUser.getTanggalNonAktif()));
        }

        return biCACUserModel;
    }

    public ResponseModel<BiCACUserModel> getBiCACUserByNik(String nik) {
        BiCACUserModel biCACUserModel = new BiCACUserModel();
        Optional<MsBiCAC> optionalMsBiCAC = this.findByNik(nik);
        if (optionalMsBiCAC.isPresent()) {
            biCACUserModel = this.mapToBiCACUserModel(optionalMsBiCAC.get());
        }

        return this.buildResponse(TYPE_MS_BI_CAC_MANAGEMENT_GET, SUCCESS, biCACUserModel);
    }

    private ResponseModel<BiCACUserModel> buildResponse(String type, ResponseStatus status, BiCACUserModel biCACUserModel) {
        ResponseModel<BiCACUserModel> response = new ResponseModel<>();
        response.setType(type);
        response.setDetails(biCACUserModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    public Optional<MsBiCAC> findByNik(String nik) {
        return this.iMsBiCACUserManagementRepository.findByNik(nik);
    }

    public Optional<MsBiCAC> findByIdUser(String idUser) {
        return this.iMsBiCACUserManagementRepository.findByIdUser(idUser);
    }

    public ResponseModel<ResCUDUserIdModel> updateBiCACUser(ReqBiCACUserModel reqBiCACUserModel) throws ParseException {
        MsBiCAC updatedMsBiCACUser = new MsBiCAC();
        Optional<MsBiCAC> optionalMsBiCAC = this.findByIdUser(reqBiCACUserModel.getIdUser());
        if (optionalMsBiCAC.isPresent()) {
            MsBiCAC existingMsBiCACUser = this.mapToBiCACUser(reqBiCACUserModel);
            existingMsBiCACUser.setId(optionalMsBiCAC.get().getId());
            existingMsBiCACUser.setIdUser(optionalMsBiCAC.get().getIdUser());
            existingMsBiCACUser.setNik(optionalMsBiCAC.get().getNik());

            updatedMsBiCACUser = this.iMsBiCACUserManagementRepository.save(existingMsBiCACUser);

            return Mapper.tobuildResponseCUDUserId(TYPE_MS_BI_CAC_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, updatedMsBiCACUser.getNik()));
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_BI_CAC_MANAGEMENT_ADD_EDIT, FAILED, Mapper.toResCUDUserIdModel(EDIT, reqBiCACUserModel.getIdUser()));
    }

    private MsBiCAC mapToBiCACUser(ReqBiCACUserModel reqBiCACUserModel) throws ParseException {
        MsBiCAC msBiCAC = new MsBiCAC();
        msBiCAC.setIdUser(reqBiCACUserModel.getIdUser());
        msBiCAC.setNamaUser(reqBiCACUserModel.getNamaUser());
        msBiCAC.setEmail(reqBiCACUserModel.getEmail());
        msBiCAC.setInstitusi(reqBiCACUserModel.getInstitusi());
        msBiCAC.setUnitKerja(reqBiCACUserModel.getUnitKerja());
        msBiCAC.setGrupUser(reqBiCACUserModel.getGrupUser());
        msBiCAC.setStatusUser(reqBiCACUserModel.getStatusUser());
        msBiCAC.setStatusLogin(reqBiCACUserModel.getStatusLogin());
        if (reqBiCACUserModel.getTanggalAktif() != null && !reqBiCACUserModel.getTanggalAktif().isEmpty()) {
            msBiCAC.setTanggalAktif(this.getFormattedString(reqBiCACUserModel.getTanggalAktif()));
        }
        if (reqBiCACUserModel.getTanggalNonAktif() != null && !reqBiCACUserModel.getTanggalNonAktif().isEmpty()) {
            msBiCAC.setTanggalNonAktif(this.getFormattedString(reqBiCACUserModel.getTanggalNonAktif()));
        }

        return msBiCAC;
    }

    private Date getFormattedString(String date) throws ParseException {
        String[] split = date.split("-");
        return DateTimeHelper.getStringToDateYYYYMMDD(split[2] + "-" + split[1] + "-" + split[0]);
    }

    public ResponseModel<ResCUDUserIdModel> saveBiCACUser(ReqBiCACUserModel request) throws ParseException {
        MsBiCAC newBiCAC = this.mapToBiCACUser(request);
        newBiCAC.setNik(this.generateNik(request.getIdUser()));

        MsBiCAC msBiCAC = this.iMsBiCACUserManagementRepository.save(newBiCAC);

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_BI_CAC_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, msBiCAC.getNik()));
    }

    private String generateNik(String idUser) {
        return idUser.startsWith(PREFIX_BNKBNK) ? idUser.substring(6) : idUser.substring(3);
    }

    public ResponseModel<ResCUDUserIdModel> deleteBiCACUser(String nik) {
        ResCUDUserIdModel response = Mapper.toResCUDUserIdModel(DELETE, nik);

        ResponseStatus status = FAILED;
        if (this.iMsBiCACUserManagementRepository.deleteMsBiCACUser(nik) > 0) {
            status = SUCCESS;
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_BI_CAC_MANAGEMENT_DELETE, status, response);
    }

    public ResponseModel<ResBatchUserId> saveBatchMsBiCACUser(ReqUserIdBatchModel<ReqBiCACUserModel> request, String nikRequester) throws Exception {
        this.iMsBiCACUserManagementRepository.deleteAll();
        this.saveTrxUserIdBatch(request, nikRequester);
        List<MsBiCAC> biCACList = new ArrayList<>();
        for (ReqBiCACUserModel reqBiCACUserModel : request.getData()) {
            MsBiCAC biCACUser = this.mapToBiCACUser(reqBiCACUserModel);
            biCACUser.setNik(this.generateNik(biCACUser.getIdUser()));
            biCACList.add(biCACUser);
        }
        this.iMsBiCACUserManagementRepository.saveAll(biCACList);
        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_TEPAT_BICAC, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_BI_CAC_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsBiCAC> msBiCACList) {
        return msBiCACList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<ReqBiCACUserModel> request, String nikRequester) {
        TrxUserIdBatch trxUserIdBatch = Mapper.toTrxUserIdBatch(request.getBatchId(),
                                                                request.getFileName(),
                                                                request.getTotalData(),
                                                                request.getType(),
                                                                nikRequester,
                                                                this.msEmployeeService.getEmployeeOrVendor(nikRequester));
        this.iTrxUserIdBatchRepository.save(trxUserIdBatch);
    }

    public ResponseModel<ResUploadModel> generateExcelMsBiCACUserId() throws Exception {
        List<MsBiCAC> msBiCACList = this.iMsBiCACUserManagementRepository.findAll();
        byte[] excelBytes = this.excelHelper.exportExcelBiCAC(msBiCACList, BI_CAC_USER_ID_FILE_NAME);

        Map<String, String> result = this.minioService.uploadFileExcel(excelBytes, DocumentHelper.generateReportFilePath(BI_CAC_USER_ID_FILE_NAME, XLSX_EXTENSION), BI_CAC_USER_ID_FILE_NAME);

        return Mapper.buildResponse(TYPE_MS_BI_CAC_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsBiCACUserId() throws Exception {
        List<MsBiCAC> msBiCACList = this.iMsBiCACUserManagementRepository.findAll();
        byte[] excelByte = this.excelHelper.exportExcelBiCAC(msBiCACList, BI_CAC_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(BI_CAC_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
