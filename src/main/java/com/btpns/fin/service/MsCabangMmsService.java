package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.helper.Mapper;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.BranchDataModel;
import com.btpns.fin.model.CabangMmsModel;
import com.btpns.fin.model.DataCabangMmsModel;
import com.btpns.fin.model.entity.MMS_UPM;
import com.btpns.fin.model.entity.MsCabang;
import com.btpns.fin.model.request.ReqBranchDataModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.model.request.ReqMMSDataModel;
import com.btpns.fin.repository.IMsCabangRepository;
import com.btpns.fin.repository.IMmsUPMRepository;
import com.btpns.fin.repository.IProsperaRepository;
import com.btpns.fin.repository.ProsperaRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;


@Service @Transactional
public class MsCabangMmsService {
    @Autowired
    IMsCabangRepository iMsCabangRepository;

    @Autowired
    IMmsUPMRepository iMmsUPMRepository;

    @Autowired
    ProsperaRepository prosperaRepository;

    @Autowired
    MinioService minioService;

    public List<MsCabang> getMsCabangAll(){
        return iMsCabangRepository.findAll();
    }

    public List<MsCabang> getMsCabangById(String cabangId){
        return iMsCabangRepository.getMsCabangById(cabangId);
    }

    public List<MMS_UPM> getMsMmsAll(){
        return iMmsUPMRepository.findAll();
    }

    public List<MMS_UPM> getMsMmsById(String mmsCode){
        return iMmsUPMRepository.getMsMmsById(mmsCode);
    }

    public String getNameCabangOrMMS(String code) {
        String namaCabang = EMPTY;
        List<MsCabang> msCabang = iMsCabangRepository.getMsCabangById(code);
        if (msCabang.size() > 0){
            namaCabang = msCabang.get(0).getCabangDesc();
        }else {
            if (code != null && code.startsWith(PREFIX_MMS)){
                namaCabang = iMmsUPMRepository.getMsMmsById(code).get(0).getMmsName();
            }
        }
        return namaCabang;
    }

    public ResponseModel<ResBranchDataListModel> getListDataBranch(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsCabang> branchDataPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if (SEARCH_FLAG_BRANCH_ID.equalsIgnoreCase(searchFlag)){
                branchDataPageable = iMsCabangRepository.findAllByBranchIdPageable(searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_BRANCH_NAME.equalsIgnoreCase(searchFlag)){
                branchDataPageable = iMsCabangRepository.findAllByBranchNamePageable(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            branchDataPageable = iMsCabangRepository.findAllBranchDataPageable(PageRequest.of(pageNumMin1, pageSize));
        }

        return buildResponeGetListDataBranch(SUCCESS, pageSize, pageNumber, branchDataPageable.getContent(), branchDataPageable.getTotalPages(), branchDataPageable.getTotalElements());
    }

    private ResponseModel<ResBranchDataListModel> buildResponeGetListDataBranch(ResponseStatus status, Integer pageSize, Integer pageNumber, List<MsCabang> msCabangList, int totalPages, long totalItems) {
        ResponseModel<ResBranchDataListModel> response = new ResponseModel<>();

        response.setType(TYPE_BRANCH_MANAGEMENT_GET_LIST);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(buildResBranchDataListModel(pageSize, pageNumber, msCabangList, totalPages, totalItems));

        return response;
    }

    private ResBranchDataListModel buildResBranchDataListModel(Integer pageSize, Integer pageNumber, List<MsCabang> msCabangList, int totalPages, long totalItems) {
        ResBranchDataListModel resBranchDataListModel = new ResBranchDataListModel();

        resBranchDataListModel.setBranch(mapToBranchDataModelList(msCabangList));
        resBranchDataListModel.setLimit(pageSize);
        resBranchDataListModel.setPage(pageNumber);
        resBranchDataListModel.setTotalPages(totalPages);
        resBranchDataListModel.setTotalItems(totalItems);

        return resBranchDataListModel;
    }

    private List<BranchDataModel> mapToBranchDataModelList(List<MsCabang> msCabangList) {
        List<BranchDataModel> branchDataModelList = new ArrayList<>();

        msCabangList.forEach(data -> {
            branchDataModelList.add(mapToBranchDataModel(data));
        });

        return branchDataModelList;
    }

    private BranchDataModel mapToBranchDataModel(MsCabang msCabang) {
        BranchDataModel branchDataModel = new BranchDataModel();

        branchDataModel.setBranchId(msCabang.getCabangId());
        branchDataModel.setBranchName(msCabang.getCabangDesc());

        return branchDataModel;
    }

    public ResponseModel<BranchDataModel> getBranchDataByBranchId(String branchId) {
        ResponseModel<BranchDataModel> response = new ResponseModel<>();

        MsCabang msCabang = iMsCabangRepository.findByBranchId(branchId);
        if (msCabang != null){
            response = buildRespone(SUCCESS, msCabang);
        }

        return response;
    }

    private ResponseModel<BranchDataModel> buildRespone(ResponseStatus status, MsCabang msCabang) {
        ResponseModel<BranchDataModel> response = new ResponseModel<>();

        response.setType(TYPE_BRANCH_MANAGEMENT_GET);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(mapToBranchDataModel(msCabang));

        return response;
    }

    public ResponseModel<ResBranchDataModel> updateBranch(ReqBranchDataModel request) {
        ResponseModel<ResBranchDataModel> response = new ResponseModel<>();

        MsCabang updateMmsData = updateBranchData(request);
        if (updateMmsData != null){
            response = buildRespone(TYPE_BRANCH_MANAGEMENT_ADD_EDIT, SUCCESS, buildResBranchDataModel(request.getBranchId(), EDIT));
        }

        return response;

    }

    private MsCabang updateBranchData(ReqBranchDataModel request) {
        MsCabang branchData = iMsCabangRepository.findByBranchId(request.getBranchId());
        if (branchData != null){
            branchData = iMsCabangRepository.save(mapToMsCabang(branchData, request));
        }

        return branchData;

    }

    private MsCabang mapToMsCabang(MsCabang branchData, ReqBranchDataModel request) {
        MsCabang updatedMsCabang = branchData;

        updatedMsCabang.setCabangId(request.getBranchId());
        updatedMsCabang.setCabangDesc(request.getBranchName());

        return updatedMsCabang;
    }

    private ResponseModel<ResBranchDataModel> buildRespone(String type, ResponseStatus status, ResBranchDataModel resBranchDataModel) {
        ResponseModel<ResBranchDataModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resBranchDataModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }


    private ResBranchDataModel buildResBranchDataModel(String branchId, String type) {
        ResBranchDataModel resBranchDataModel = new ResBranchDataModel();

        resBranchDataModel.setType(type);
        resBranchDataModel.setBranchId(branchId);

        return resBranchDataModel;
    }

    public ResponseModel<ResBranchDataModel> saveBranch(ReqBranchDataModel request) {
        ResponseModel<ResBranchDataModel> response = new ResponseModel<>();

        MsCabang saveBranchData = saveBranchData(request);
        if (saveBranchData != null){
            response = buildRespone(TYPE_BRANCH_MANAGEMENT_ADD_EDIT, SUCCESS, buildResBranchDataModel(request.getBranchId(), ADD));
        }

        return response;

    }

    private MsCabang saveBranchData(ReqBranchDataModel request) {
        return iMsCabangRepository.save(mapToMsCabang(request));
    }

    private MsCabang mapToMsCabang(ReqBranchDataModel request) {
        MsCabang msCabang = new MsCabang();

        msCabang.setCabangId(request.getBranchId());
        msCabang.setCabangDesc(request.getBranchName());

        return msCabang;
    }

    public ResponseModel<ResBranchDataModel> deleteBranch(String branchId) {
        ResBranchDataModel response = buildResBranchDataModel(branchId, DELETE);

        ResponseStatus status = FAILED;
        if (deleteBranchDataByBranchId(branchId) > 0) {
            status = SUCCESS;
        }

        return buildRespone(TYPE_BRANCH_MANAGEMENT_DELETE, status, response);
    }

    private int deleteBranchDataByBranchId(String branchId) {
        return iMsCabangRepository.deleteBranchData(branchId);
    }

    public ResponseModel<ResMMSDataListModel> getListDataMMS(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MMS_UPM> mmsDataPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if (SEARCH_FLAG_MMSCODE.equalsIgnoreCase(searchFlag)){
                mmsDataPageable = iMmsUPMRepository.findAllByMmsCodePageable(searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_MMSNAME.equalsIgnoreCase(searchFlag)){
                mmsDataPageable = iMmsUPMRepository.findAllByMmsNamePageable(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            mmsDataPageable = iMmsUPMRepository.findAllMmsDataPageable(PageRequest.of(pageNumMin1, pageSize));
        }

        return buildResponeGetListDataMMS(SUCCESS, pageSize, pageNumber, mmsDataPageable.getContent(), mmsDataPageable.getTotalPages(), mmsDataPageable.getTotalElements());
    }

    private ResponseModel<ResMMSDataListModel> buildResponeGetListDataMMS(ResponseStatus status, Integer pageSize, Integer pageNumber, List<MMS_UPM> mmsDataList, int totalPages, long totalItems) {
        ResponseModel<ResMMSDataListModel> response = new ResponseModel<>();

        response.setType(TYPE_MMS_MANAGEMENT_GET_LIST);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(buildResMMSDataListModel(pageSize, pageNumber, mmsDataList, totalPages, totalItems));

        return response;
    }

    private ResMMSDataListModel buildResMMSDataListModel(Integer pageSize, Integer pageNumber, List<MMS_UPM> mmsDataList, int totalPages, long totalItems) {
        ResMMSDataListModel resMMSDataListModel = new ResMMSDataListModel();

        resMMSDataListModel.setMms(mmsDataList);
        resMMSDataListModel.setLimit(pageSize);
        resMMSDataListModel.setPage(pageNumber);
        resMMSDataListModel.setTotalPages(totalPages);
        resMMSDataListModel.setTotalItems(totalItems);

        return resMMSDataListModel;
    }

    public ResponseModel<MMS_UPM> getMmsDataByMmsCode(String mmsCode) {
        ResponseModel<MMS_UPM> response = new ResponseModel<>();

        MMS_UPM mmsData = iMmsUPMRepository.findByMmsCode(mmsCode);
        if (mmsData != null){
            response = buildRespone(SUCCESS, mmsData);
        }

        return response;
    }

    private ResponseModel<MMS_UPM> buildRespone(ResponseStatus status, MMS_UPM mmsData) {
        ResponseModel<MMS_UPM> response = new ResponseModel<>();

        response.setType(TYPE_MMS_MANAGEMENT_GET);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(mmsData);

        return response;
    }

    public ResponseModel<ResMMSDataModel> updateMms(ReqMMSDataModel request) {
        ResponseModel<ResMMSDataModel> response = new ResponseModel<>();

        MMS_UPM updateMmsData = updateMmsData(request);
        if (updateMmsData != null){
            response = buildRespone(TYPE_MMS_MANAGEMENT_ADD_EDIT, SUCCESS, buildResMMSDataModel(request.getMmsCode(), EDIT));
        }

        return response;
    }

    private MMS_UPM updateMmsData(ReqMMSDataModel request) {
        MMS_UPM mmsData = iMmsUPMRepository.findByMmsCode(request.getMmsCode());

        if (mmsData != null){
            mmsData = iMmsUPMRepository.save(mapToMmsUPM(mmsData, request));
        }

        return mmsData;
    }

    private MMS_UPM mapToMmsUPM(MMS_UPM mmsData, ReqMMSDataModel request) {
        MMS_UPM updatedMmsData = mmsData;

        updatedMmsData.setMmsCode(request.getMmsCode());
        updatedMmsData.setMmsName(request.getMmsName());
        updatedMmsData.setAddress(request.getAddress());
        updatedMmsData.setKfoCode(request.getKfoCode());
        updatedMmsData.setKfoName(request.getKfoName());
        updatedMmsData.setKcsCode(request.getKcsCode());
        updatedMmsData.setKcsName(request.getKcsName());

        return updatedMmsData;
    }


    private ResponseModel<ResMMSDataModel> buildRespone(String type, ResponseStatus status, ResMMSDataModel resMMSDataModel) {
        ResponseModel<ResMMSDataModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resMMSDataModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    private ResMMSDataModel buildResMMSDataModel(String mmsCode, String type) {
        ResMMSDataModel resMMSDataModel = new ResMMSDataModel();

        resMMSDataModel.setType(type);
        resMMSDataModel.setMmsCode(mmsCode);

        return resMMSDataModel;
    }


    public ResponseModel<ResMMSDataModel> saveMms(ReqMMSDataModel request) {
        ResponseModel<ResMMSDataModel> response = new ResponseModel<>();

        MMS_UPM updateMmsData = saveMmsData(request);
        if (updateMmsData != null){
            response = buildRespone(TYPE_MMS_MANAGEMENT_ADD_EDIT, SUCCESS, buildResMMSDataModel(request.getMmsCode(), ADD));
        }

        return response;
    }

    private MMS_UPM saveMmsData(ReqMMSDataModel request) {
        return iMmsUPMRepository.save(mapToMmsUPM(request));
    }

    private MMS_UPM mapToMmsUPM(ReqMMSDataModel request) {
        MMS_UPM saveMmsData = new MMS_UPM();

        saveMmsData.setMmsCode(request.getMmsCode());
        saveMmsData.setMmsName(request.getMmsName());
        saveMmsData.setAddress(request.getAddress());
        saveMmsData.setKfoCode(request.getKfoCode());
        saveMmsData.setKfoName(request.getKfoName());
        saveMmsData.setKcsCode(request.getKcsCode());
        saveMmsData.setKcsName(request.getKcsName());

        return saveMmsData;
    }

    public ResponseModel<ResMMSDataModel> deleteMms(String mmsCode) {
        ResMMSDataModel response = buildResMMSDataModel(mmsCode, DELETE);

        ResponseStatus status = FAILED;
        if (deleteMmsDataByMmsCode(mmsCode) > 0) {
            status = SUCCESS;
        }

        return buildRespone(TYPE_MMS_MANAGEMENT_DELETE, status, response);
    }

    private int deleteMmsDataByMmsCode(String mmsCode) {
        return iMmsUPMRepository.deleteMmsData(mmsCode);
    }

    public ResponseModel<ResUploadModel> generateBranchDataCsv() throws Exception {
        List<MsCabang> msCabangList = iMsCabangRepository.findAll();
        byte[] byteCSV = makeMsCabangCsv(msCabangList);
        Map<String, String> result = minioService.uploadReportFile(byteCSV, DocumentHelper.generateReportFilePath(REPORT_MS_CABANG_FILE_NAME, CSV_EXTENSION), REPORT_MS_CABANG_FILE_NAME);

        return buildResponse(TYPE_MS_CABANG_DOWNLOAD, SUCCESS, new ResUploadModel(result));
    }

    public ResFileDownload directDownloadAlihDayaCSV() throws Exception {
        List<MsCabang> msCabangList = iMsCabangRepository.findAll();
        byte[] byteCSV = makeMsCabangCsv(msCabangList);

        return new ResFileDownload(byteCSV, CommonHelper.concateTwoString(REPORT_MS_CABANG_FILE_NAME, CSV_EXTENSION));
    }

    private byte[] makeMsCabangCsv(List<MsCabang> msCabangList) throws Exception {
        StringBuffer csv = new StringBuffer();

        csv.append("\""+ID_CABANG_COL_NAME+"\",\""+NAMA_CABANG_COL_NAME+"\""+"\r\n");
        msCabangList.forEach(data -> {
            csv.append(("\""+"\'"+data.getCabangId()+"\",\""+data.getCabangDesc()+"\""+"\r\n"));
        });

        return csv.toString().getBytes("windows-1252");
    }

    private ResponseModel<ResUploadModel> buildResponse(String type, ResponseStatus status, ResUploadModel result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }

    public ResponseModel<ResUploadModel> generateMMSDataCsv() throws Exception {
        List<MMS_UPM> msMmsList = iMmsUPMRepository.findAll();
        byte[] byteCSV = makeMsMmsCsv(msMmsList);
        Map<String, String> result = minioService.uploadReportFile(byteCSV, DocumentHelper.generateReportFilePath(REPORT_MS_MMS_FILE_NAME, CSV_EXTENSION), REPORT_MS_MMS_FILE_NAME);

        return buildResponse(TYPE_MS_MMS_DOWNLOAD, SUCCESS, new ResUploadModel(result));
    }

    public ResFileDownload directDownloadMMSDataCsv() throws Exception {
        List<MMS_UPM> msMmsList = iMmsUPMRepository.findAll();
        byte[] byteCSV = makeMsMmsCsv(msMmsList);

        return new ResFileDownload(byteCSV, CommonHelper.concateTwoString(REPORT_MS_MMS_FILE_NAME, CSV_EXTENSION));
    }

    private byte[] makeMsMmsCsv(List<MMS_UPM> msMmsList) throws Exception {
        StringBuffer csv = new StringBuffer();

        csv.append("\""+KODE_MMS_COL_NAME+"\",\""+NAMA_MMS_COL_NAME+"\",\""+ALAMAT_COL_NAME+"\",\""+KODE_KFO_COL_NAME+"\",\""+NAMA_KFO_COL_NAME+"\",\""+KODE_KCS_COL_NAME+"\",\""+NAMA_KCS_COL_NAME+"\""+"\r\n");
        msMmsList.forEach(data -> {
            csv.append(("\""+"\'"+data.getMmsCode()+"\",\""+data.getMmsName()+"\",\""+data.getAddress()+"\",\""+"\'"+data.getKfoCode()+"\",\""+data.getKfoName()+"\",\""+"\'"+data.getKcsCode()+"\",\""+data.getKcsName()+"\""+"\r\n"));
        });

        return csv.toString().getBytes("windows-1252");
    }

    public CabangMmsModel getListCabang(String data, Boolean isProsperaSource, String authorization) {
        CabangMmsModel cabangMmsModel = new CabangMmsModel();
        List<DataCabangMmsModel> dataCabangMmsModelList = new ArrayList<>();
        List<MsCabang> msCabangList = new ArrayList<>();
        List<MMS_UPM> mmsUpmList = new ArrayList<>();

        if (isProsperaSource){
            Map<String, Object> paramMap = buildParamMapGetListOfficeProspera();
            List<ResListOfficeProspera> resOfficeDetailProspera = prosperaRepository.getListOfficeProspera(authorization, paramMap);
            dataCabangMmsModelList = Mapper.toDataCabangMmsModelsProspera(resOfficeDetailProspera);
        }else {
            if (data.equals(FILTER_CABANG_MMS)) {
                msCabangList = getMsCabangAll();
                dataCabangMmsModelList = Mapper.toDataCabangMmsModelsFC(msCabangList);
                mmsUpmList = getMsMmsAll();
                dataCabangMmsModelList.addAll(Mapper.toDataCabangMmsModelsFM(mmsUpmList));
            } else if (data.equals(FILTER_CABANG)) {
                msCabangList = getMsCabangAll();
                dataCabangMmsModelList = Mapper.toDataCabangMmsModelsFC(msCabangList);
            } else {
                mmsUpmList = getMsMmsAll();
                dataCabangMmsModelList = Mapper.toDataCabangMmsModelsFM(mmsUpmList);
            }
        }
        cabangMmsModel.setData(dataCabangMmsModelList);

        return cabangMmsModel;
    }

    private Map<String, Object> buildParamMapGetListOfficeProspera() {
        HashMap<String, Object> paramMap = new HashMap<>() {{
            put("status", STATUS_ACTIVE);
        }};
        return paramMap;
    }

    public String generateKodeNamaCabang(String officeCode) {
        StringBuilder sb = new StringBuilder();
        sb.append(officeCode)
          .append(" - ")
          .append(getNameCabangOrMMS(officeCode));
        return sb.toString();
    }
}
