package com.btpns.fin.service;

import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.FTEUserModel;
import com.btpns.fin.model.entity.MsEmployee;
import com.btpns.fin.model.entity.MsEmployeeHierarchy;
import com.btpns.fin.model.response.ResFTEUserListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsEmployeeHierarchyRepository;
import com.btpns.fin.repository.IMsEmployeeRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
public class MsEmployeeHierarchyService {
    @Autowired
    IMsEmployeeHierarchyRepository iMsEmployeeHierarchyRepository;

    @Autowired
    IMsEmployeeRepository iMsEmployeeRepository;

    public MsEmployeeHierarchy getMsEmployeeHierarchyByNik(String nik){
        return iMsEmployeeHierarchyRepository.getMsEmployeeHierarchyByNik(nik);
    }

    public List<MsEmployee> getListPUKMsEmployeeHierarchy(String nik){
        String nikPUK = "";
        List<MsEmployee> listPUK = new ArrayList<MsEmployee>();

        MsEmployeeHierarchy msEmployeeHierarchy = iMsEmployeeHierarchyRepository.getMsEmployeeHierarchyByNik(nik);

        if (msEmployeeHierarchy != null){
            //puk1 != 0, else puk1 == 0 && puk2 != 0
            if(!msEmployeeHierarchy.getDirectSupervisorNik().equals("0")){
                nikPUK = msEmployeeHierarchy.getDirectSupervisorNik();
            } else if(msEmployeeHierarchy.getDirectSupervisorNik().equals("0") && !msEmployeeHierarchy.getDirectSupervisor2Nik().equals("0")){
                nikPUK = msEmployeeHierarchy.getDirectSupervisor2Nik();
            }

            while(!nikPUK.equals("0")){
                MsEmployee msEmployee = iMsEmployeeRepository.getMsEmployeeByNik(nikPUK);
                if(msEmployee != null) {
                    listPUK.add(msEmployee);
                    nikPUK = msEmployee.getDirectSupervisorNIK();
                } else {
                    nikPUK = "0";
                }
            }
        }

        return listPUK;
    }

    public ResponseModel<ResFTEUserListModel> getListFTE(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsEmployeeHierarchy> fteDataPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if(SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag)){
                fteDataPageable = iMsEmployeeHierarchyRepository.findAllByNikEmployee(searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag)){
                fteDataPageable = iMsEmployeeHierarchyRepository.findAllByNameEmployee(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            fteDataPageable = iMsEmployeeHierarchyRepository.getListEmployee(PageRequest.of(pageNumMin1, pageSize));
        }

        return buildResponse(SUCCESS, pageSize, pageNumber, fteDataPageable.getContent(), fteDataPageable.getTotalPages(), fteDataPageable.getTotalElements());
    }

    private ResponseModel<ResFTEUserListModel> buildResponse(ResponseStatus status, Integer pageSize, Integer pageNumber, List<MsEmployeeHierarchy> msEmployeeHierarchy, int totalPages, long totalItems) {
        ResponseModel<ResFTEUserListModel> response = new ResponseModel<>();

        response.setType(TYPE_FTE_USER_MANAGEMENT_GET_LIST);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(buildResFTEUserListModel(pageSize, pageNumber, msEmployeeHierarchy, totalPages, totalItems));

        return response;
    }

    private ResFTEUserListModel buildResFTEUserListModel(Integer pageSize, Integer pageNumber, List<MsEmployeeHierarchy> msEmployeeHierarchy, int totalPages, long totalItems) {
        ResFTEUserListModel fteUserList = new ResFTEUserListModel();

        fteUserList.setFteUser(mapToFTEUserModelList(msEmployeeHierarchy));
        fteUserList.setLimit(pageSize);
        fteUserList.setPage(pageNumber);
        fteUserList.setTotalPages(totalPages);
        fteUserList.setTotalItems(totalItems);

        return fteUserList;
    }

    private List<FTEUserModel> mapToFTEUserModelList(List<MsEmployeeHierarchy> msEmployeeHierarchy) {
        List<FTEUserModel> fteUserModelList = new ArrayList<>();
        
        msEmployeeHierarchy.forEach(data -> {
            fteUserModelList.add(mapToFTEUserModel(data));
        });
        
        return fteUserModelList;
    }

    private FTEUserModel mapToFTEUserModel(MsEmployeeHierarchy msEmployeeHierarchy) {
        FTEUserModel fteUserModel = new FTEUserModel();

        fteUserModel.setNik(msEmployeeHierarchy.getNik());
        fteUserModel.setFullName(msEmployeeHierarchy.getFullName());
        fteUserModel.setOccupationDesc(msEmployeeHierarchy.getOccupationDesc());
        fteUserModel.setOrganization(msEmployeeHierarchy.getOrganization());
        fteUserModel.setLocation(msEmployeeHierarchy.getLocation());
        fteUserModel.setDirectSupervisorNik(msEmployeeHierarchy.getDirectSupervisorNik().equalsIgnoreCase(NOL) ? "" : msEmployeeHierarchy.getDirectSupervisorNik());
        fteUserModel.setDirectSupervisorName(msEmployeeHierarchy.getDirectSupervisorName().equalsIgnoreCase(NOL) ? "" : msEmployeeHierarchy.getDirectSupervisorName());
        fteUserModel.setDirectSupervisorOccupation(msEmployeeHierarchy.getDirectSupervisorOccupation());
        fteUserModel.setDirectSupervisor2Nik(msEmployeeHierarchy.getDirectSupervisor2Nik().equalsIgnoreCase(NOL) ? "" : msEmployeeHierarchy.getDirectSupervisor2Nik());
        fteUserModel.setDirectSupervisor2Name(msEmployeeHierarchy.getDirectSupervisor2Name().equalsIgnoreCase(NOL) ? "" : msEmployeeHierarchy.getDirectSupervisor2Name());
        fteUserModel.setDirectSupervisor2Occupation(msEmployeeHierarchy.getDirectSupervisor2Occupation());
        fteUserModel.setDtTermination(msEmployeeHierarchy.getDtTermination() != null ? msEmployeeHierarchy.getDtTermination() : "");

        return fteUserModel;
    }

    public ResponseModel<FTEUserModel> getFTEByNik(String nik) {
        ResponseModel<FTEUserModel> response = new ResponseModel<>();

        MsEmployeeHierarchy msEmployeeHierarchy = iMsEmployeeHierarchyRepository.getMsEmployeeHierarchyByNik(nik);
        if (msEmployeeHierarchy != null){
            response = buildRespone(SUCCESS, msEmployeeHierarchy);
        }

        return response;
    }

    private ResponseModel<FTEUserModel> buildRespone(ResponseStatus status, MsEmployeeHierarchy msEmployeeHierarchy) {
        ResponseModel<FTEUserModel> response = new ResponseModel<>();

        response.setType(TYPE_FTE_USER_MANAGEMENT_GET);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(mapToFTEUserModel(msEmployeeHierarchy));

        return response;
    }
}
