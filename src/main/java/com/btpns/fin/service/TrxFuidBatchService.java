package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResSimplifikasiBulkModel;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;

import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class TrxFuidBatchService {
    private static final Logger logger = LoggerFactory.getLogger(TrxFuidBatchService.class);
    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    MsCabangMmsService msCabangMmsService;

    @Autowired
    ITrxFuidBatchRepository trxFuidBatchRepository;

    @Autowired
    ITrxFuidRequestRepository trxFuidRequestRepository;

    @Autowired
    ITrxFuidApprovalRepository trxFuidApprovalRepository;

    @Autowired
    ITrxAudittrailRepository trxAudittrailRepository;

    @Autowired
    ITrxFuidRequestAplikasiRepository itrxFuidRequestAplikasiRepository;

    @Autowired
    ExcelHelper excelHelper;

    @Autowired
    MinioService minioService;

    @Autowired
    Mapper mapper;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    public TrxFuidBatch getFuidBatchByBatchId(String batchId) {
        return trxFuidBatchRepository.getFuidBatchByBatchId(batchId);
    }

    public boolean existInInterval(String nik, String type, int interval) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return trxFuidBatchRepository.checkRequestInterval(startDate, endDate, nik, type) > 0;
    }

    @Transactional(rollbackFor = { SQLException.class })
    public boolean saveFuidBatchData(TrxFuidBatchDataModel batchData) throws SQLException {
        trxFuidRequestRepository.saveAll(batchData.getTrxFuidRequestList());
        trxFuidApprovalRepository.saveAll(batchData.getTrxFuidApprovalList());
        itrxFuidRequestAplikasiRepository.saveAll(batchData.getTrxFuidRequestAplikasiList());
        trxAudittrailRepository.saveAll(batchData.getTrxAudittrailList());
        trxFuidBatchRepository.save(batchData.getTrxFuidBatch());
        return true;
    }

    public ResponseModel<ResSimplifikasiBulkModel> saveTrxFuidBulkResignOptimaUser(TrxFuidBatchModel trxFuidBatchModel, String lastTicket, String nikRequester) throws Exception {
        List<TrxFuidRequest> trxFuidRequestList = new ArrayList<>();
        List<TrxFuidApproval> trxFuidApprovalList = new ArrayList<>();
        List<TrxFuidRequestAplikasi> trxFuidRequestAplikasiList = new ArrayList<>();
        List<TrxAudittrail> trxAudittrailList = new ArrayList<>();

        List<FuidModel> data = trxFuidBatchModel.getData();
        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID));

        String ticketId = lastTicket;

        for (FuidModel fuid: data) {
            // BUILD DATA FUID REQUEST
            TrxFuidRequest tfr =  Mapper.toTrxFuidRequestEntity(fuid, trxFuidBatchModel, ticketId);
            tfr.setDataNamaCabang(msCabangMmsService.getNameCabangOrMMS(tfr.getDataKodeCabang()));
            trxFuidRequestList.add(tfr);

            // BUILD DATA FUID REQUEST APLIKASI
            if (fuid.getAplikasi().size() > 0) {
                List<String> lAplikasi = fuid.getAplikasi();
                for (String aplikasi : lAplikasi) {
                    MsTemaApplication mspd = msTemaApplicationMap.get(aplikasi);
                    TrxFuidRequestAplikasi tfrap = Mapper.toTrxFuidRequestAplikasiEntity(ticketId, mspd);
                    trxFuidRequestAplikasiList.add(tfrap);
                }
            }

            // BUILD DATA FUID APPROVAL
            TrxFuidApproval tfra = Mapper.toTrxFuidApprovalEntity(ticketId);
            tfra.setCurrentState(UPM_STATUS_DONE);
            tfra.setUpmInputNIK(fuid.getUpmInputNIK());
            tfra.setUpmInputStatus(UPM_STATUS_VERIFICATION);
            tfra.setUpmInputDt(LocalDateTime.now());
            tfra.setUpmInputNotes(FUR_UPM_NOTES);
            tfra.setUpmCheckerNIK(fuid.getUpmCheckerNIK());
            tfra.setUpmCheckerStatus(UPM_STATUS_DONE);
            tfra.setUpmCheckerDt(LocalDateTime.now());
            trxFuidApprovalList.add(tfra);

            // BUILD DATA AUDITTRAIL
            trxAudittrailList.add(mapper.buildTrxAudittrail(ticketId, fuid.getUpmInputNIK(), UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM));
            trxAudittrailList.add(mapper.buildTrxAudittrail(ticketId, fuid.getUpmCheckerNIK(), UPM_STATUS_DONE, TIMELINE_STATUS_DONE, TIMELINE_PIC_UPM_APPROVE));

            ticketId = incrementTicketId(ticketId);
        }

        // BUILD DATA TRXFUIDBATCH
        TrxFuidBatch trxFuidBatch = buildTrxFuidBatch(trxFuidBatchModel, nikRequester);

        // SAVE DATA
        boolean resultSuccess = saveFuidBatchData(buildTrxFuidBatchDataModel(trxFuidBatch, trxFuidRequestList, trxFuidApprovalList, trxFuidRequestAplikasiList, trxAudittrailList));

        ResponseModel<ResSimplifikasiBulkModel> response = resultSuccess ? buildResponse(trxFuidBatchModel, SUCCESS, TYPE_SIMPLIFIKASI_BULK_USER_RESIGN_OPTIMA) : buildResponse(trxFuidBatchModel, FAILED, TYPE_SIMPLIFIKASI_BULK_USER_RESIGN_OPTIMA);
        return response;
    }

    private String incrementTicketId(String ticketId) {
        String firstPart = ticketId.substring(0, 9);
        int incrementPart = Integer.parseInt(ticketId.substring(9)) + 1;
        return firstPart + String.format("%04d", incrementPart);
    }

    private TrxFuidBatch buildTrxFuidBatch(TrxFuidBatchModel trxFuidBatchModel, String nikRequester) {
        TrxFuidBatch trxFuidBatch = new TrxFuidBatch();

        trxFuidBatch.setBatchId(trxFuidBatchModel.getBatchId());
        trxFuidBatch.setBatchFileName(trxFuidBatchModel.getFileName());
        trxFuidBatch.setTotalData(trxFuidBatchModel.getTotalData());
        trxFuidBatch.setType(trxFuidBatchModel.getType());
        trxFuidBatch.setUploaderNIK(nikRequester);
        trxFuidBatch.setUploaderName(msEmployeeService.getEmployeeOrVendor(nikRequester));
        trxFuidBatch.setCreateDateTime(LocalDateTime.now());

        return trxFuidBatch;
    }

    private TrxFuidBatchDataModel buildTrxFuidBatchDataModel(TrxFuidBatch trxFuidBatch, List<TrxFuidRequest> trxFuidRequestList, List<TrxFuidApproval> trxFuidApprovalList, List<TrxFuidRequestAplikasi> trxFuidRequestAplikasiList, List<TrxAudittrail> trxAudittrailList) {
        TrxFuidBatchDataModel ret = new TrxFuidBatchDataModel();

        ret.setTrxFuidBatch(trxFuidBatch);
        ret.setTrxFuidRequestList(trxFuidRequestList);
        ret.setTrxFuidApprovalList(trxFuidApprovalList);
        ret.setTrxFuidRequestAplikasiList(trxFuidRequestAplikasiList);
        ret.setTrxAudittrailList(trxAudittrailList);

        return ret;
    }

    private ResponseModel<ResSimplifikasiBulkModel> buildResponse(TrxFuidBatchModel trxFuidBatchModel, ResponseStatus status, String type) {
        ResponseModel<ResSimplifikasiBulkModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(buildResTrxFuidBatchModel(trxFuidBatchModel));

        return response;
    }

    private ResSimplifikasiBulkModel buildResTrxFuidBatchModel(TrxFuidBatchModel trxFuidBatchModel) {
        ResSimplifikasiBulkModel resSimplifikasiBulkModel = new ResSimplifikasiBulkModel();

        resSimplifikasiBulkModel.setType(trxFuidBatchModel.getType());
        resSimplifikasiBulkModel.setBatchId(trxFuidBatchModel.getBatchId());
        resSimplifikasiBulkModel.setTotalDatas(trxFuidBatchModel.getTotalData());

        return resSimplifikasiBulkModel;
    }

    public ResponseModel<ResSimplifikasiBulkModel> saveTrxFuidBulkUserId(TrxFuidBatchModel trxFuidBatchModel, String nikRequester) throws Exception {
        if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
            if (isValidRequestBulkNewUserId(trxFuidBatchModel, 1000)){
                if (!existInInterval(nikRequester, trxFuidBatchModel.getType(), 30)){
                    TrxFuidBatchDataModel trxFuidBatchDataModel = generateTrxFuidBatchDataNewUserId(trxFuidBatchModel, nikRequester);
                    boolean resultSuccess = saveFuidBatchData(trxFuidBatchDataModel);
                    ResponseModel<ResSimplifikasiBulkModel> response = resultSuccess ? buildResponse(trxFuidBatchModel,  SUCCESS, TYPE_SIMPLIFIKASI_BULK_USERID) : buildResponse(trxFuidBatchModel, FAILED, TYPE_SIMPLIFIKASI_BULK_USERID);
                    return response;
                }
                throw new HttpClientErrorException(HttpStatus.TOO_MANY_REQUESTS);
            }
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST);
        }
        throw new HttpClientErrorException(HttpStatus.FORBIDDEN);
    }

    public boolean isValidRequestBulkNewUserId(TrxFuidBatchModel request, int maxData) {
        return request != null
               && request.getType() != null && BATCH_NEW_USERID.equalsIgnoreCase(request.getType())
               && request.getBatchId() != null && getFuidBatchByBatchId(request.getBatchId()) == null
               && request.getFileName() != null
               && request.getTotalData() > 0
               && request.getData() != null
               && request.getData().size() > 0
               && request.getData().size() <= maxData;
    }

    private TrxFuidBatchDataModel generateTrxFuidBatchDataNewUserId(TrxFuidBatchModel trxFuidBatchModel, String nikRequester) throws Exception {
        List<TrxFuidRequest> trxFuidRequestList = new ArrayList<>();
        List<TrxFuidApproval> trxFuidApprovalList = new ArrayList<>();
        List<TrxFuidRequestAplikasi> trxFuidRequestAplikasiList = new ArrayList<>();
        List<TrxAudittrail> trxAudittrailList = new ArrayList<>();

        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationWithParamDetailIdDescKeyMap(Arrays.asList(KODE_APLIKASI_FUID));
        String ticketId = getLastTicketIdUserId();

        for (FuidModel fuid : trxFuidBatchModel.getData()) {
            TrxFuidRequest tfr = mapper.toTrxFuidRequest(fuid, trxFuidBatchModel, ticketId);
            tfr.setDataNamaCabang(msCabangMmsService.getNameCabangOrMMS(tfr.getDataKodeCabang()));
            trxFuidRequestList.add(tfr);

            if (fuid.getAplikasi().size() > 0) {
                List<String> lAplikasi = fuid.getAplikasi();
                for (String aplikasi : lAplikasi) {
                    MsTemaApplication msta = msTemaApplicationMap.get(aplikasi.replaceFirst("^\\s*", ""));
                    TrxFuidRequestAplikasi tfrap = Mapper.toTrxFuidRequestAplikasiEntity(ticketId, msta);
                    trxFuidRequestAplikasiList.add(tfrap);
                }
            }

            TrxFuidApproval tfra = Mapper.toTrxFuidApprovalEntity(ticketId);
            tfra.setCurrentState(UPM_STATUS_DONE);
            tfra.setUpmInputNIK(fuid.getUpmInputNIK());
            tfra.setUpmInputStatus(UPM_STATUS_VERIFICATION);
            tfra.setUpmInputDt(LocalDateTime.now());
            tfra.setUpmInputNotes(FUB_UPM_NOTES);
            tfra.setUpmCheckerNIK(fuid.getUpmCheckerNIK());
            tfra.setUpmCheckerStatus(UPM_STATUS_DONE);
            tfra.setUpmCheckerDt(LocalDateTime.now());
            trxFuidApprovalList.add(tfra);

            trxAudittrailList.add(mapper.buildTrxAudittrail(ticketId, fuid.getUpmInputNIK(), UPM_STATUS_INPROGRESS, TIMELINE_STATUS_INPROGRESS_UPM, TIMELINE_PIC_UPM));
            trxAudittrailList.add(mapper.buildTrxAudittrail(ticketId, fuid.getUpmCheckerNIK(), UPM_STATUS_DONE, TIMELINE_STATUS_DONE, TIMELINE_PIC_UPM_APPROVE));

            ticketId = incrementTicketId(ticketId);
        }
        return buildTrxFuidBatchDataModel(buildTrxFuidBatch(trxFuidBatchModel, nikRequester), trxFuidRequestList, trxFuidApprovalList, trxFuidRequestAplikasiList, trxAudittrailList);
    }

    private String getLastTicketIdUserId() {
        String lastTicketId = trxFuidRequestRepository.getLastTicketIdByInputType(INPUT_TYPE_BULK);
        String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());

        logger.info("getLastTicketId: {} ", lastTicketId);
        if (lastTicketId == null) {
            return "FUB" + currDtTicket + "0001";
        } else {
            String strlastDate = lastTicketId.substring(3, 9);
            logger.info("strlastDate: " + strlastDate);
            if (strlastDate.equals(currDtTicket)) {
                String strTicketNum = lastTicketId.substring(9, 13);
                logger.info("strTicketNum: " + strTicketNum);
                Integer ticketNum = Integer.parseInt(strTicketNum) + 1;
                return "FUB" + currDtTicket + String.format("%04d", ticketNum);
            } else {
                return "FUB" + currDtTicket + "0001";
            }
        }
    }

    public ResponseModel<ResUploadModel> downloadTemplateFuidBulkUserId(String nikRequester) throws Exception {
        if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
            byte[] excelByte = excelHelper.exportExcelTemplateFuidBulkUserId(TEMPLATE_BULK_USERID_FILE_NAME);
            Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(TEMPLATE_BULK_USERID_FILE_NAME, XLSX_EXTENSION), TEMPLATE_BULK_USERID_FILE_NAME);
            return Mapper.buildResponse(TYPE_TEMPLATE_BULK_USERID_DOWNLOAD, SUCCESS, result);
        }
        throw new HttpClientErrorException(HttpStatus.FORBIDDEN);
    }

    public ResFileDownload directDownloadTemplateTrxFuidBulkUserId(String nikRequester) throws Exception {
        if (CommonHelper.validateUPMTicket(nikRequester,trxUpmRoleService.getTrxUpmRole(nikRequester))) {
            byte[] excelByte = excelHelper.exportExcelTemplateFuidBulkUserId(TEMPLATE_BULK_USERID_FILE_NAME);
            return new ResFileDownload(excelByte, CommonHelper.concateTwoString(TEMPLATE_BULK_USERID_FILE_NAME, XLSX_EXTENSION));
        }
        throw new HttpClientErrorException(HttpStatus.FORBIDDEN);
    }
}
