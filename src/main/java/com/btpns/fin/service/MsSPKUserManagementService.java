package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsS4;
import com.btpns.fin.model.entity.MsSPK;
import com.btpns.fin.model.entity.TrxUserIdBatch;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMsSPKUserManagementRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.FAILED;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
@Transactional
public class MsSPKUserManagementService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private IMsSPKUserManagementRepository iMsSPKUserManagementRepository;

    @Autowired
    private ITrxUserIdBatchRepository iTrxUserIdBatchRepository;

    @Autowired
    private MsEmployeeService msEmployeeService;

    @Autowired
    private MinioService minioService;

    @Autowired
    private ExcelHelper excelHelper;

    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResponseListModel> getListSPKUsers(int pageNumMin1, Integer pageNumber, Integer pageSize, String searchFlag, String searchData) {
        Page<MsSPK> msSPKUsers = new PageImpl<>(new ArrayList<>());
        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)) {
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NIK)) {
                msSPKUsers = this.iMsSPKUserManagementRepository.findAllByNik(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NAME)) {
                msSPKUsers = this.iMsSPKUserManagementRepository.findAllByNamaUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        } else {
            msSPKUsers = this.iMsSPKUserManagementRepository.findAll(PageRequest.of(pageNumMin1, pageSize));
        }

        ResponseListModel<UserIDModel> responseListModel = Mapper.buildResUserIdListModel(pageSize, pageNumber, mapToUserIDModelList(msSPKUsers.getContent()), msSPKUsers.getTotalPages(), msSPKUsers.getTotalElements());

        return Mapper.buildResponseList(TYPE_MS_SPK_MANAGEMENT_GET_LIST, SUCCESS, responseListModel);
    }

    private List<UserIDModel> mapToUserIDModelList(List<MsSPK> spkUsers) {
        return spkUsers.stream().map(MsSPK::toUserIDModel).collect(Collectors.toList());
    }

    public ResponseModel<UserIDModel> getSPKUserByNik(String nik) {
        UserIDModel userIDModel = new UserIDModel();
        Optional<MsSPK> optionalMsSPK = this.findByNik(nik);
        if (optionalMsSPK.isPresent()) {
            MsSPK msSPK = optionalMsSPK.get();
            userIDModel = msSPK.toUserIDModel();
        }

        return ResponseModel.<UserIDModel>builder().type(TYPE_MS_SPK_MANAGEMENT_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(userIDModel).build();
    }

    public Optional<MsSPK> findByNik(String nik) {
        return this.iMsSPKUserManagementRepository.findByNik(nik);
    }

    public ResponseModel<ResCUDUserIdModel> saveSPKUser(ReqUserIDModel reqUserIDModel) {
        MsSPK msSPKUser = this.iMsSPKUserManagementRepository.save(reqUserIDModel.toSPK());

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_SPK_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, msSPKUser.getNik()));
    }

    public ResponseModel<ResCUDUserIdModel> updateSPKUser(ReqUserIDModel reqUserIDModel) {
        MsSPK savedMsSPKUser = new MsSPK();
        Optional<MsSPK> optionalMsSPK = this.findByNik(reqUserIDModel.getNik());
        if (optionalMsSPK.isPresent()) {
            MsSPK newMsSPKUser = reqUserIDModel.toSPK();
            newMsSPKUser.setId(optionalMsSPK.get().getId());
            newMsSPKUser.setNik(optionalMsSPK.get().getNik());

            savedMsSPKUser = this.iMsSPKUserManagementRepository.save(newMsSPKUser);

            return Mapper.tobuildResponseCUDUserId(TYPE_MS_SPK_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, savedMsSPKUser.getNik()));
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_SPK_MANAGEMENT_ADD_EDIT, FAILED, Mapper.toResCUDUserIdModel(EDIT, reqUserIDModel.getNik()));
    }

    public ResponseModel<ResCUDUserIdModel> deleteSPKUser(String nik) {
        ResCUDUserIdModel response = Mapper.toResCUDUserIdModel(DELETE, nik);

        ResponseStatus status = FAILED;
        if (this.iMsSPKUserManagementRepository.deleteMsSPKUser(nik) > 0) {
            status = SUCCESS;
        }
        return Mapper.tobuildResponseCUDUserId(TYPE_MS_SPK_MANAGEMENT_DELETE, status, response);
    }

    public ResponseModel<ResBatchUserId> saveBatchMsSPKUser(ReqUserIdBatchModel<MsSPK> request, String nikRequester) throws Exception {
        this.iMsSPKUserManagementRepository.deleteAll();
        this.saveTrxUserIdBatch(request, nikRequester);
        this.saveBatchMsSPK(request.getData());
        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_SPK, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_SPK_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsSPK> spkList) {
        return spkList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<MsSPK> request, String nikRequester) {
        TrxUserIdBatch trxUserIdBatch = Mapper.toTrxUserIdBatch(request.getBatchId(),
                                                                request.getFileName(),
                                                                request.getTotalData(),
                                                                request.getType(),
                                                                nikRequester,
                                                                msEmployeeService.getEmployeeOrVendor(nikRequester));
        this.iTrxUserIdBatchRepository.save(trxUserIdBatch);
    }

    private void saveBatchMsSPK(List<MsSPK> msSPKList) {
        this.iMsSPKUserManagementRepository.saveAll(msSPKList);
    }

    public ResponseModel<ResUploadModel> generateExcelMsSPKUserId() throws Exception {
        List<MsSPK> spkList = iMsSPKUserManagementRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDModelList(spkList), SPK_USER_ID_FILE_NAME);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(SPK_USER_ID_FILE_NAME, XLSX_EXTENSION), SPK_USER_ID_FILE_NAME);

        return Mapper.buildResponse(TYPE_MS_SPK_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsSPKUserId() throws Exception {
        List<MsSPK> spkList = iMsSPKUserManagementRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDModelList(spkList), SPK_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(SPK_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
