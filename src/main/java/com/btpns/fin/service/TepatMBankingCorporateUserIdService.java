package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsTepatMBankingCorporate;
import com.btpns.fin.model.entity.MsTepatMBankingIndividu;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.ITepatMBankingCorporateUserIdRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service @Transactional
public class TepatMBankingCorporateUserIdService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    ITepatMBankingCorporateUserIdRepository tepatMBankingCorporateUserIdRepository;

    @Autowired
    ITrxUserIdBatchRepository trxUserIdBatchRepository;

    @Autowired
    ExcelHelper excelHelper;

    @Autowired
    MinioService minioService;

    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResBatchUserId> saveBatchMsTepatMBankingCorporateUserId(ReqUserIdBatchModel<MsTepatMBankingCorporate> request, String nikRequester) throws Exception {
        List<MsTepatMBankingCorporate> msTepatMBankingCorporateList = tepatMBankingCorporateUserIdRepository.findAll();
        if (!msTepatMBankingCorporateList.isEmpty() && msTepatMBankingCorporateList.size() > 0){
            if(deleteMsMBankingCorporate() > 0){
                saveTrxUserIdBatch(request, nikRequester);
                saveBatchMsMBankingCorporate(request.getData());
            }
        }else {
            saveTrxUserIdBatch(request, nikRequester);
            saveBatchMsMBankingCorporate(request.getData());
        }
        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_TEPAT_MBANKING_CORP, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsTepatMBankingCorporate> msTepatMBankingCorporateList) {
        return msTepatMBankingCorporateList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveBatchMsMBankingCorporate(List<MsTepatMBankingCorporate> msTepatMBankingCorporateList) {
        tepatMBankingCorporateUserIdRepository.saveAll(msTepatMBankingCorporateList);
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<MsTepatMBankingCorporate> request, String nikRequester) {
        trxUserIdBatchRepository.save(Mapper.toTrxUserIdBatch(request.getBatchId(), request.getFileName(), request.getTotalData(), request.getType(), nikRequester, msEmployeeService.getEmployeeOrVendor(nikRequester)));
    }

    private int deleteMsMBankingCorporate() {
        return tepatMBankingCorporateUserIdRepository.deleteAllMssaveBatchMsMBankingCorporate();
    }

    public ResponseModel<ResponseListModel> getListMsTepatMBankingCorporate(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsTepatMBankingCorporate> msTepatMBankingCorporatePageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if(SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag)){
                msTepatMBankingCorporatePageable = tepatMBankingCorporateUserIdRepository.findAllByNikUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag)){
                msTepatMBankingCorporatePageable = tepatMBankingCorporateUserIdRepository.findAllByNameUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            msTepatMBankingCorporatePageable = tepatMBankingCorporateUserIdRepository.getListMsTepatMBankingCorporate(PageRequest.of(pageNumMin1, pageSize));
        }
        ResponseListModel<UserIDModel> responseListModel = Mapper.buildResUserIdListModel(pageSize, pageNumber, mapToUserIDListModel(msTepatMBankingCorporatePageable.getContent()), msTepatMBankingCorporatePageable.getTotalPages(), msTepatMBankingCorporatePageable.getTotalElements());

        return Mapper.buildResponseList(TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_GET_LIST, SUCCESS, responseListModel);
    }

    private List<UserIDModel> mapToUserIDListModel(List<MsTepatMBankingCorporate> msTepatMBankingCorporate) {
        return msTepatMBankingCorporate.stream().map(MsTepatMBankingCorporate::toUserIDModel).collect(Collectors.toList());
    }

    public ResponseModel<UserIDModel> getMsTepatMBankingCorporateByNik(String nik) {
        ResponseModel<UserIDModel> response = new ResponseModel<>();

        MsTepatMBankingCorporate msTepatMBankingCorporate = tepatMBankingCorporateUserIdRepository.findByNikUser(nik);
        if (msTepatMBankingCorporate != null){
            response = ResponseModel.<UserIDModel>builder().type(TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(msTepatMBankingCorporate.toUserIDModel()).build();
        }

        return response;
    }

    public ResponseModel<ResCUDUserIdModel> saveMsTepatMBankingCorporateUserId(ReqUserIDModel request) {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        MsTepatMBankingCorporate msTepatMBankingCorporate = saveMsTepatMBankingCorporateData(request.toTepatMBankingCorporate());
        if (msTepatMBankingCorporate != null){
            response = Mapper.tobuildResponseCUDUserId(TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, request.getNik()));
        }

        return response;
    }

    private MsTepatMBankingCorporate saveMsTepatMBankingCorporateData(MsTepatMBankingCorporate msTepatMBankingCorporate) {
        return tepatMBankingCorporateUserIdRepository.save(msTepatMBankingCorporate);
    }

    public ResponseModel<ResCUDUserIdModel> updateMsTepatMBankingCorporateUserId(ReqUserIDModel request) {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        MsTepatMBankingCorporate updatedMsTepatMBankingCorporate = updateMsTepatMBankingCorporateData(request);
        if (updatedMsTepatMBankingCorporate != null){
            response = Mapper.tobuildResponseCUDUserId(TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, request.getNik()));
        }

        return response;
    }

    private MsTepatMBankingCorporate updateMsTepatMBankingCorporateData(ReqUserIDModel request) {
        MsTepatMBankingCorporate msTepatMBankingCorporate = tepatMBankingCorporateUserIdRepository.findByNikUser(request.getNik());
        if (msTepatMBankingCorporate != null){
            MsTepatMBankingCorporate newTepatMBankingCorp = request.toTepatMBankingCorporate();
            newTepatMBankingCorp.setId(msTepatMBankingCorporate.getId());
            newTepatMBankingCorp.setNik(msTepatMBankingCorporate.getNik());

            msTepatMBankingCorporate = tepatMBankingCorporateUserIdRepository.save(newTepatMBankingCorp);
        }

        return msTepatMBankingCorporate;
    }

    public ResponseModel<ResCUDUserIdModel> deleteMsTepatMBankingCorporateUserId(String nik) {
        ResponseStatus status = FAILED;
        if (deleteMsTepatMBankingCorporateByNikUser(nik) > 0) {
            status = SUCCESS;
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_DELETE, status, Mapper.toResCUDUserIdModel(DELETE, nik));
    }

    private int deleteMsTepatMBankingCorporateByNikUser(String nik) {
        return tepatMBankingCorporateUserIdRepository.deleteMsTepatMBankingCorporateByNik(nik);
    }

    public ResponseModel<ResUploadModel> generateExcelMsTepatMBankingCorporateUserId() throws Exception {
        List<MsTepatMBankingCorporate> tepatMBankingCorporateList = tepatMBankingCorporateUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(tepatMBankingCorporateList), TEPAT_MBANKING_CORPORATE_USER_ID_FILE_NAME);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(TEPAT_MBANKING_CORPORATE_USER_ID_FILE_NAME, XLSX_EXTENSION), TEPAT_MBANKING_CORPORATE_USER_ID_FILE_NAME);

        return Mapper.buildResponse(TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsTepatMBankingCorporateUserId() throws Exception {
        List<MsTepatMBankingCorporate> tepatMBankingCorporateList = tepatMBankingCorporateUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(tepatMBankingCorporateList), TEPAT_MBANKING_CORPORATE_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(TEPAT_MBANKING_CORPORATE_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
