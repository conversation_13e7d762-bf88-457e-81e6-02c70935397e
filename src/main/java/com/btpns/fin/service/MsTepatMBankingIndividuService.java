package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsS4;
import com.btpns.fin.model.entity.MsTepatMBankingIndividu;
import com.btpns.fin.model.entity.TrxUserIdBatch;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMsTepatMBankingIndividuRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.FAILED;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
@Transactional
public class MsTepatMBankingIndividuService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private IMsTepatMBankingIndividuRepository iMsTepatMBankingIndividuRepository;

    @Autowired
    private ITrxUserIdBatchRepository iTrxUserIdBatchRepository;

    @Autowired
    private MsEmployeeService msEmployeeService;

    @Autowired
    private ExcelHelper excelHelper;

    @Autowired
    private MinioService minioService;

    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResponseListModel> getListTepatMBankingIndividuUsers(int pageNumMin1,
                                                                              Integer pageNumber,
                                                                              Integer pageSize,
                                                                              String searchFlag,
                                                                              String searchData) {
        Page<MsTepatMBankingIndividu> tepatMBankingIndividuPage = new PageImpl<>(new ArrayList<>());
        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)) {
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NIK)) {
                tepatMBankingIndividuPage = this.iMsTepatMBankingIndividuRepository.findAllByNik(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NAME)) {
                tepatMBankingIndividuPage = this.iMsTepatMBankingIndividuRepository.findAllByNamaUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        } else {
            tepatMBankingIndividuPage = this.iMsTepatMBankingIndividuRepository.findAll(PageRequest.of(pageNumMin1, pageSize));
        }
        ResponseListModel<UserIDModel> responseListModel = Mapper.buildResUserIdListModel(pageSize, pageNumber, mapToUserIDListModel(tepatMBankingIndividuPage.getContent()), tepatMBankingIndividuPage.getTotalPages(), tepatMBankingIndividuPage.getTotalElements());

        return Mapper.buildResponseList(TYPE_MS_TEPAT_MBANKING_INDIVIDU_GET_LIST, SUCCESS, responseListModel);
    }

    private List<UserIDModel> mapToUserIDListModel(List<MsTepatMBankingIndividu> tepatMBankingIndividuList) {
        return tepatMBankingIndividuList.stream().map(MsTepatMBankingIndividu::toUserIDModel).collect(Collectors.toList());
    }

    public ResponseModel<UserIDModel> getTepatMBankingIndividuByNik(String nik) {
        UserIDModel userIDModel = new UserIDModel();
        Optional<MsTepatMBankingIndividu> optional = this.findByNik(nik);
        if (optional.isPresent()) {
            userIDModel = optional.get().toUserIDModel();
        }

        return ResponseModel.<UserIDModel>builder().type(TYPE_MS_TEPAT_MBANKING_INDIVIDU_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(userIDModel).build();
    }

    public Optional<MsTepatMBankingIndividu> findByNik(String nik) {
        return this.iMsTepatMBankingIndividuRepository.findByNik(nik);
    }

    public ResponseModel<ResCUDUserIdModel> saveTepatMBankingIndividu(ReqUserIDModel request) {
        MsTepatMBankingIndividu savedTepatMBankingIndividu = this.iMsTepatMBankingIndividuRepository.save(request.toTepatMBankingIndividu());

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_TEPAT_MBANKING_INDIVIDU_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, savedTepatMBankingIndividu.getNik()));
    }

    public ResponseModel<ResCUDUserIdModel> updateTepatMBankingIndividu(ReqUserIDModel request) {
        MsTepatMBankingIndividu updatedMsTepatMBankingIndividu = new MsTepatMBankingIndividu();
        Optional<MsTepatMBankingIndividu> optional = this.findByNik(request.getNik());
        if (optional.isPresent()) {
            MsTepatMBankingIndividu newMsTepatMBankingIndividu = request.toTepatMBankingIndividu();
            newMsTepatMBankingIndividu.setId(optional.get().getId());
            newMsTepatMBankingIndividu.setNik(optional.get().getNik());

            updatedMsTepatMBankingIndividu = this.iMsTepatMBankingIndividuRepository.save(newMsTepatMBankingIndividu);

            return Mapper.tobuildResponseCUDUserId(TYPE_MS_TEPAT_MBANKING_INDIVIDU_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, updatedMsTepatMBankingIndividu.getNik()));
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_SPK_MANAGEMENT_ADD_EDIT, FAILED, Mapper.toResCUDUserIdModel(EDIT, request.getNik()));
    }

    public ResponseModel<ResCUDUserIdModel> deleteTepatMBankingIndividu(String nik) {
        ResCUDUserIdModel response = Mapper.toResCUDUserIdModel(DELETE, nik);

        ResponseStatus status = FAILED;
        if (this.iMsTepatMBankingIndividuRepository.deleteMsTepatMBankingIndividuUser(nik) > 0) {
            status = SUCCESS;
        }
        return Mapper.tobuildResponseCUDUserId(TYPE_MS_TEPAT_MBANKING_INDIVIDU_DELETE, status, response);
    }

    public boolean hasDuplicate(List<MsTepatMBankingIndividu> tepatMBankingIndividuList) {
        Set<String> set = new HashSet<>();
        for (MsTepatMBankingIndividu tepatMBankingIndividu : tepatMBankingIndividuList) {
            if (!set.add(tepatMBankingIndividu.getNik().toUpperCase())) {
                return true;
            }
        }
        return false;
    }

    public ResponseModel<ResBatchUserId> saveBatchMsTepatMBankingIndividu(ReqUserIdBatchModel<MsTepatMBankingIndividu> request, String nikRequester) throws Exception {
        this.iMsTepatMBankingIndividuRepository.deleteAll();
        this.saveTrxUserIdBatch(request, nikRequester);
        this.iMsTepatMBankingIndividuRepository.saveAll(request.getData());
        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_TEPAT_MBANKING_INDIVIDU, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_TEPAT_MBANKING_INDIVIDU_BATCH,
                                                    SUCCESS,
                                                    Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsTepatMBankingIndividu> msTepatMBankingIndividuList) {
        return msTepatMBankingIndividuList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<MsTepatMBankingIndividu> request, String nikRequester) {
        TrxUserIdBatch trxUserIdBatch = Mapper.toTrxUserIdBatch(request.getBatchId(),
                                                                request.getFileName(),
                                                                request.getTotalData(),
                                                                request.getType(),
                                                                nikRequester,
                                                                msEmployeeService.getEmployeeOrVendor(nikRequester));

        this.iTrxUserIdBatchRepository.save(trxUserIdBatch);
    }

    public ResponseModel<ResUploadModel> generateExcelMsTepatMBankingIndividuUserId() throws Exception {
        List<MsTepatMBankingIndividu> tepatMBankingIndividuList = iMsTepatMBankingIndividuRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(tepatMBankingIndividuList), TEPAT_MBANKING_INDIVIDU_USER_ID_FILE_NAME);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(TEPAT_MBANKING_INDIVIDU_USER_ID_FILE_NAME, XLSX_EXTENSION), TEPAT_MBANKING_INDIVIDU_USER_ID_FILE_NAME);

        return Mapper.buildResponse(TYPE_MS_TEPAT_MBANKING_INDIVIDU_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsTepatMBankingIndividuUserId() throws Exception {
        List<MsTepatMBankingIndividu> tepatMBankingIndividuList = iMsTepatMBankingIndividuRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(tepatMBankingIndividuList), TEPAT_MBANKING_INDIVIDU_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(TEPAT_MBANKING_INDIVIDU_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
