package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.ReqTicketBatchModel;
import com.btpns.fin.model.request.ReqPersonnelProspera;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.*;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.math.BigInteger;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service @Transactional
public class UpmTicketService {
    private static final Logger logger = LoggerFactory.getLogger(UpmTicketService.class);

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    TrxSetupParamRequestService trxSetupParamRequestService;

    @Autowired
    TrxFuidApprovalService trxFuidApprovalService;

    @Autowired
    TrxSetupParamApprovalService trxSetupParamApprovalService;

    @Autowired
    TrxFuidRequestAplikasiService trxFuidRequestAplikasiService;

    @Autowired
    TrxSetupParamRequestAplikasiService trxSetupParamRequestAplikasiService;

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    MsHolidayListService msHolidayListService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    EmailNotificationService emailNotificationService;

    @Autowired
    DelegationService delegationService;

    @Autowired
    TrxUPMUARService trxUPMUARService;

    @Autowired
    IUpmTicketRepository upmTicketRepository;

    @Autowired
    IUpmCountTicketRepository upmCountTicketRepository;

    @Autowired
    IUpmCountTicketOnHoldSummaryRepository upmCountTicketOnHoldSummaryRepository;

    @Autowired
    IUpmCountTicketSummaryRepository upmCountTicketSummaryRepository;

    @Autowired
    ITrxFuidRequestRepository trxFuidRequestRepository;

    @Autowired
    ITrxFuidApprovalRepository trxFuidApprovalRepository;

    @Autowired
    ITrxAudittrailRepository trxAudittrailRepository;

    @Autowired
    ITrxFuidRequestAplikasiRepository trxFuidRequestAplikasiRepository;

    @Autowired
    ITrxSetupParamRequestRepository trxSetupParamRequestRepository;

    @Autowired
    ITrxSetupParamApprovalRepository trxSetupParamApprovalRepository;

    @Autowired
    ITrxSetupParamRequestAplikasiRepository trxSetupParamRequestAplikasiRepository;

    @Autowired
    ProsperaRepository prosperaRepository;

    @Autowired
    IMsMmsRepository msMmsRepository;

    @Autowired
    IMsProsperaRoleRepository prosperaRoleRepository;

    @Autowired
    ITrxProsperaRequestRepository prosperaRequestRepository;

    @Autowired
    IMsCenterCodeOfficerProsperaRepository iMsCenterCodeOfficerProsperaRepository;

    @Autowired
    ITrxExpiredFuidRepository iTrxExpiredFuidRepository;

    @Autowired
    IMsProsperaRoleRepository iMsProsperaRoleRepository;

    @Autowired
    ITrxUARRequestRepository iTrxUARRequestRepository;

    @Autowired
    ITrxUARApprovalRepository iTrxUARApprovalRepository;

    @Autowired
    ITrxUARAudittrailRepository iTrxUARAudittrailRepository;

    @Autowired
    private Mapper mapper;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Value("${max.upm.limit.for.bwmp.nominal.input}")
    private Integer maxUpmLimitForBwmpNominalInput;

    public Page<UpmTicket> getListTicketUPM(int pageNumMin1, int pageSize, String status, String dateNow, String ticketId, String upmProcess, Integer isPending, String searchFlag, List<String> nik, String name){
        Pageable pageable = PageRequest.of(pageNumMin1, pageSize);
        return upmTicketRepository.getListTicketUPM(pageable, status, dateNow, ticketId, upmProcess, isPending, searchFlag, nik, name);
    }

    public Page<UpmTicket> getListTicketFuidUPM(int pageNumMin1, int pageSize, String status, String dateNow, String ticketId, String upmProcess, Integer isPending, String searchFlag, List<String> nik, String name){
        Pageable pageable = PageRequest.of(pageNumMin1, pageSize);
        return upmTicketRepository.getListTicketFuidUPM(pageable, status, dateNow, ticketId, upmProcess, isPending, searchFlag, nik, name);
    }

    public Page<UpmTicket> getListTicketSetupParamUPM(int pageNumMin1, int pageSize, String status, String dateNow, String ticketId, String upmProcess, Integer isPending, String searchFlag, List<String> nik, String name){
        Pageable pageable = PageRequest.of(pageNumMin1, pageSize);
        return upmTicketRepository.getListTicketSetupParamUPM(pageable, status, dateNow, ticketId, upmProcess, isPending, searchFlag, nik, name);
    }

    public BigInteger getCountTicketFuid(String status, String sDateNow, Integer isPending, String startDate, String endDate, String startDateEfektif, String endDateEfektif){
        return upmCountTicketRepository.getCountTicketFuid(status, sDateNow, isPending, startDate, endDate, startDateEfektif, endDateEfektif);
    }

    public BigInteger getCountTicketSetupParam(String status, String sDateNow, Integer isPending, String startDate, String endDate, String startDateEfektif, String endDateEfektif){
        return upmCountTicketRepository.getCountTicketSetupParam(status, sDateNow, isPending, startDate, endDate, startDateEfektif, endDateEfektif);
    }

    public List<UpmCountTicketSummary> getListTicketSummaryUPM(String startDate, String endDate, String startDateEfektif, String endDateEfektif) {
        return upmCountTicketSummaryRepository.getListTicketSummaryUPM(startDate, endDate, startDateEfektif, endDateEfektif);
    }

    public List<UpmCountTicketOnHoldSummary> getOnHoldTicketSummaryUPM(String startDateEffectiveOnHold, String endDateEffectiveOnHold) {
        return upmCountTicketOnHoldSummaryRepository.getOnHoldTicketUPM(startDateEffectiveOnHold, endDateEffectiveOnHold);
    }

    public ResponseModel<ResUpmManageTicketModel> setUPMTicket(UpmManageTicketModel umtModel, Token profile, String authorization, String nik) throws Exception {
        ResponseModel<ResUpmManageTicketModel> response = new ResponseModel<>();

        //check role
        boolean isMaker = false, isChecker = false, isAdmin = false;
        if (trxUpmRoleService.getTrxUpmRole(nik) != null) {
            TrxUpmRole tur = trxUpmRoleService.getTrxUpmRole(nik);
            if (tur.getRole().equals(UPM_ROLE_MAKER)) {
                isMaker = true;
            }
            if (tur.getRole().equals(UPM_ROLE_CHECKER) || tur.getRole().equalsIgnoreCase(UPM_ROLE_ADMIN)) {
                isChecker = true;
            }
            if (tur.getRole().equalsIgnoreCase(UPM_ROLE_ADMIN)) {
                isAdmin = true;
            }
        }

        //check type ticket fuid or setup-param
        boolean isFU = false, isSP = false, isProspera = false, isFUS = false, isUAR = false;
        if(umtModel.getTicketId().substring(0,2).equals("FU")){
            isFU = true;
        }
        if(umtModel.getTicketId().substring(0,2).equals("SP")){
            isSP = true;
        }
        if (CommonHelper.isTicketFuidProspera(umtModel.getTicketId())){
            isProspera = true;
        }
        if (CommonHelper.isTicketFuidSimplifikasi(umtModel.getTicketId())){
            isFUS = true;
        }
        if (CommonHelper.isTicketUAR(umtModel.getTicketId())) {
            isUAR = true;
        }

        if (isAdmin && DELETE.equalsIgnoreCase(umtModel.getType())) {
            response = deleteTicketUPM(umtModel.getTicketId(), isFU, isSP, isUAR);
        }else if (isMaker && (isProspera || isFUS) && (TYPE_PROCESS_PROSPERA.contains(umtModel.getType()))){
            response = setProsperaTicketUPM(umtModel, authorization, nik);
        }else if ((isMaker || isChecker) && (!DELETE.equalsIgnoreCase(umtModel.getType()))){
            response = processTicketUPM(umtModel, profile, isMaker, isChecker, isFU, isSP, isProspera, nik);
        }else {
            throw new Exception(String.valueOf(ResponseStatus.FORBIDDEN));
        }

        return response;
    }

    public ResponseModel<ResUpmManageTicketModel> deleteTicketUPM(String ticketId, boolean isFU, boolean isSP, boolean isUAR) {
        boolean isSuccessDeleteReq = false, isSuccessDeleteAppr = false, isSuccessDeleteReqApp = false, isSuccessDeleteAud = false;
        ResponseStatus status = FAILED;

        if (isFU){
            if (deleteTrxFuidApprovalByTicketId(ticketId) > 0){
                isSuccessDeleteAppr = true;
            }
            if (deleteTrxFuidReqAppByTicketId(ticketId) > 0){
                isSuccessDeleteReqApp = true;
            }
            if (deleteTrxAudditrailByTicketId(ticketId) > 0){
                isSuccessDeleteAud = true;
            }
            if (deleteTrxFuidRequestByTicketId(ticketId) > 0){
                isSuccessDeleteReq = true;
            }
        }

        if (isSP){
            if (deleteTrxSetupParamApprovalByTicketId(ticketId) > 0){
                isSuccessDeleteAppr = true;
            }
            if (deleteTrxSetupParamReqAppByTicketId(ticketId) > 0){
                isSuccessDeleteReqApp = true;
            }
            if (deleteTrxAudditrailByTicketId(ticketId) > 0){
                isSuccessDeleteAud = true;
            }
            if (deleteTrxSetupParamRequestByTicketId(ticketId) > 0){
                isSuccessDeleteReq = true;
            }
        }

        if (isUAR){
            if (deleteTrxUARApprovalByTicketId(ticketId) > 0){
                isSuccessDeleteAppr = true;
            }
            isSuccessDeleteReqApp = true;
            if (deleteTrxUARAudditrailByTicketId(ticketId) > 0){
                isSuccessDeleteAud = true;
            }
            if (deleteTrxUARRequestByTicketId(ticketId) > 0){
                isSuccessDeleteReq = true;
            }
        }

        if (isSuccessDeleteAppr && isSuccessDeleteReqApp && isSuccessDeleteAud && isSuccessDeleteReq){
            status = SUCCESS;
        }

        return buildResponse(status, buildResUpmManageTicketModel(ticketId, DELETE));
    }

    private int deleteTrxFuidApprovalByTicketId(String ticketId) {
        return trxFuidApprovalRepository.deleteTrxFuidRequestByTicketId(ticketId);
    }

    private int deleteTrxFuidReqAppByTicketId(String ticketId) {
        return trxFuidRequestAplikasiRepository.deleteTrxFuidRequestAplikasiByTicketId(ticketId);
    }

    private int deleteTrxAudditrailByTicketId(String ticketId) {
        return trxAudittrailRepository.deleteTrxFuidRequestByTicketId(ticketId);
    }

    private int deleteTrxFuidRequestByTicketId(String ticketId) {
        return trxFuidRequestRepository.deleteTrxFuidRequestByTicketId(ticketId);
    }

    private int deleteTrxSetupParamApprovalByTicketId(String ticketId) {
        return trxSetupParamApprovalRepository.deleteTrxSetupParamRequestByTicketId(ticketId);
    }

    private int deleteTrxSetupParamReqAppByTicketId(String ticketId) {
        return trxSetupParamRequestAplikasiRepository.deleteTrxSetupParamRequestAplikasiByTicketId(ticketId);
    }

    private int deleteTrxSetupParamRequestByTicketId(String ticketId) {
        return trxSetupParamRequestRepository.deleteTrxSetupParamRequestByTicketId(ticketId);
    }

    private int deleteTrxUARApprovalByTicketId(String ticketId) {
        return iTrxUARApprovalRepository.deleteTrxUARApprovalByTicketId(ticketId);
    }

    private int deleteTrxUARAudditrailByTicketId(String ticketId) {
        return iTrxUARAudittrailRepository.deleteTrxUARAudditrailByTicketId(ticketId);
    }

    private int deleteTrxUARRequestByTicketId(String ticketId) {
        return iTrxUARRequestRepository.deleteTrxUARRequestByTicketId(ticketId);
    }

    private ResUpmManageTicketModel buildResUpmManageTicketModel(String ticketId, String type) {
        ResUpmManageTicketModel resUpmManageTicketModel = new ResUpmManageTicketModel();

        resUpmManageTicketModel.setTicketId(ticketId);
        resUpmManageTicketModel.setType(type);

        return resUpmManageTicketModel;
    }

    private ResponseModel<ResUpmManageTicketModel> buildResponse(ResponseStatus status, ResUpmManageTicketModel resUpmManageTicketModel) {
        ResponseModel<ResUpmManageTicketModel> response = new ResponseModel<>();

        response.setType(TYPE_UPM_TICKET_SET);
        response.setDetails(resUpmManageTicketModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    public ResponseModel<ResUpmManageTicketModel> processTicketUPM(UpmManageTicketModel umtModel, Token profile, boolean isMaker, boolean isChecker, boolean isFU, boolean isSP, boolean isProspera, String nik) throws Exception {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        Date dateNow = new Date();
        LocalDateTime currStateDt = LocalDateTime.now();
        String sCurrStateDt = dateFormater1.format(currStateDt);
        String statusAfter = "", notes = "", notesUPMMaker = "";
        boolean isPassValidation = false, isSuccessSendNotif = true;
        boolean isRejected = false, isRejectedWithUpdateUPM = false;

        //UPM maker
        if (isMaker) {
            if (umtModel.getType().equals(UPM_TICKET_TYPE_PICK)) {
                statusAfter = UPM_STATUS_INPROGRESS;
            } else if (umtModel.getType().equals(UPM_TICKET_TYPE_PROCESS)) {
                statusAfter = UPM_STATUS_VERIFICATION;
                notes = umtModel.getNotes();
            } else if (umtModel.getType().equals(UPM_TICKET_TYPE_REJECT)) {
                statusAfter = CURR_STATUS_REJECTED;
                notes = umtModel.getNotes();
                isRejected = true;
                //for email
                notesUPMMaker = notes;
            }
            if (isFU) {
                TrxFuidRequest tfr = trxFuidRequestService.getTrxFuidRequestByTicketId(umtModel.getTicketId());
                TrxFuidApproval tfra = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
                //set attachment upm input if exist
                if(umtModel.getAttachmentUPMInput() != null){
                    List<AttachmentModel> lAttachUPMInput = umtModel.getAttachmentUPMInput();
                    Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
                    tfra.setUpmInputAttachment(new Gson().toJson(lAttachUPMInput, attachmentListType));
                }
                //validation
                isPassValidation = validationAction(umtModel, isMaker, isChecker, tfra.getCurrentState(), tfr.getTanggalEfektif());
                if (isPassValidation) {
                    //check rejected with status before
                    if (isRejected && tfra.getCurrentState().equals(UPM_STATUS_INPROGRESS)) {
                        isRejectedWithUpdateUPM = true;
                    }
                    if (isRejected && tfra.getCurrentState().equals(CURR_STATUS_APPROVED)){
                        isRejectedWithUpdateUPM = true;
                    }
                    if (!isRejected || isRejectedWithUpdateUPM) {
                        tfra.setUpmInputNIK(nik);
                        tfra.setUpmInputStatus(statusAfter);
                        tfra.setUpmInputDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                        tfra.setUpmInputNotes(notes);
                    }
                    if (isProspera && UPM_STATUS_INPROGRESS.equalsIgnoreCase(statusAfter)){
                        tfra.setUpmInputNotes(generateTemplatedUpmNotesProspera(tfr));
                    }
                    tfra.setCurrentState(statusAfter);
                    tfra.setCurrentStateDT(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                    trxFuidApprovalService.updateTrxFuidApproval(tfra);
                }
            } else if (isSP) {
                TrxSetupParamRequest tspr = trxSetupParamRequestService.getTrxSetupParamRequestByTicketId(umtModel.getTicketId());
                TrxSetupParamApproval tspa = trxSetupParamApprovalService.getTrxSetupParamApprovalByTicketId(umtModel.getTicketId());
                //set attachment upm input if exist
                if (umtModel.getAttachmentUPMInput() != null) {
                    List<AttachmentModel> lAttachUPMInput = umtModel.getAttachmentUPMInput();
                    Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>() {
                    }.getType();
                    tspa.setUpmInputAttachment(new Gson().toJson(lAttachUPMInput, attachmentListType));
                }
                //validation
                isPassValidation = validationAction(umtModel, isMaker, isChecker, tspa.getCurrentState(), tspr.getTanggalEfektif());
                if (isPassValidation) {
                    //check rejected with status before
                    if (isRejected && tspa.getCurrentState().equals(UPM_STATUS_INPROGRESS)) {
                        isRejectedWithUpdateUPM = true;
                    }
                    if (isRejected && tspa.getCurrentState().equals(CURR_STATUS_APPROVED)){
                        isRejectedWithUpdateUPM = true;
                    }
                    if (!isRejected || isRejectedWithUpdateUPM) {
                        tspa.setUpmInputNIK(nik);
                        tspa.setUpmInputStatus(statusAfter);
                        tspa.setUpmInputDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                        tspa.setUpmInputNotes(notes);
                    }
                    tspa.setCurrentState(statusAfter);
                    tspa.setCurrentStateDT(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                    trxSetupParamApprovalService.updateTrxSetupParamApproval(tspa);
                }
            }
        }

        //UPM checker
        boolean isRejectedFromVerifyChecker = false;
        boolean isReassign = false;
        if (isChecker) {
            Map<String, MsHolidayList> msHolidayMap = msHolidayListService.getHolidayMap(String.valueOf(LocalDateTime.now().getYear()));
            if (umtModel.getType().equals(UPM_TICKET_TYPE_ASSIGN)) {
                statusAfter = UPM_STATUS_INPROGRESS;
                nik = umtModel.getNikMaker();
            } else if (umtModel.getType().equals(UPM_TICKET_TYPE_CHECKER_VERIFY)) {
                statusAfter = UPM_STATUS_DONE;
                notes = umtModel.getNotes();
            } else if (umtModel.getType().equals(UPM_TICKET_TYPE_REJECT)) {
                statusAfter = CURR_STATUS_REJECTED;
                notes = umtModel.getNotes();
                isRejected = true;
                //for email
                notesUPMMaker = notes;
            }else if (umtModel.getType().equals(UPM_TICKET_TYPE_REASSIGN)){
                isReassign =  true;
                statusAfter = UPM_STATUS_INPROGRESS;
            }
            if (isFU) {
                TrxFuidRequest tfr = trxFuidRequestService.getTrxFuidRequestByTicketId(umtModel.getTicketId());
                TrxFuidApproval tfra = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
                //validation
                isPassValidation = validationAction(umtModel, isMaker, isChecker, tfra.getCurrentState(), tfr.getTanggalEfektif());
                if (isPassValidation) {
                    //check rejected with status before
                    if (isRejected && tfra.getCurrentState().equals(UPM_STATUS_VERIFICATION)) {
                        statusAfter = UPM_STATUS_INPROGRESS;
                        nik = tfra.getUpmInputNIK();
                        isRejectedWithUpdateUPM = true;
                        isRejectedFromVerifyChecker = true;
                    }
                    //check reject after finish all approve
                    if (isRejected && tfra.getCurrentState().equals(CURR_STATUS_APPROVED)) {
                        tfra.setUpmCheckerNIK(nik);
                        tfra.setUpmCheckerStatus(statusAfter);
                        tfra.setUpmCheckerDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                        tfra.setUpmCheckerNotes(notes);
                    }
                    if (umtModel.getType().equals(UPM_TICKET_TYPE_ASSIGN) || isRejectedWithUpdateUPM) {
                        tfra.setUpmInputNIK(nik);
                        tfra.setUpmInputStatus(statusAfter);
                        tfra.setUpmInputDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                        tfra.setUpmInputNotes(notes);
                    }
                    if (umtModel.getType().equals(UPM_TICKET_TYPE_CHECKER_VERIFY)) {
                        tfra.setUpmCheckerNIK(nik);
                        tfra.setUpmCheckerStatus(statusAfter);
                        tfra.setUpmCheckerDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                        tfra.setUpmCheckerNotes(notes);
                        //set note UPM Maker
                        notesUPMMaker = tfra.getUpmInputNotes();
                        trxFuidRequestAplikasiService.updateTrxFuidRequestAplikasiByTicketId(tfra.getTicketId(), DateTimeHelper.getDatePeriodDate(dateNow), DateTimeHelper.getDatePeriodMonth(dateNow));
                    }
                    if (isReassign && tfra.getCurrentState().equals(UPM_STATUS_INPROGRESS)){
                        tfra.setUpmInputNIK(umtModel.getNikMaker());
                        tfra.setUpmInputStatus(statusAfter);
                        tfra.setUpmInputDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                    }
                    LocalDateTime approveDate = LocalDateTime.parse(sCurrStateDt, dateFormater1);
                    if (isProspera && UPM_STATUS_INPROGRESS.equalsIgnoreCase(statusAfter)){
                        tfra.setUpmInputNotes(generateTemplatedUpmNotesProspera(tfr));
                    }
                    LocalDateTime dateArriveUPM = getDateArriveUPM(tfra.getPuk2Dt(), tfra.getPuk1Dt(), tfr.getCreateDateTime(), tfr.getTanggalEfektif());
                    SlaModel slaModel = calculateSLA(dateArriveUPM, approveDate, msHolidayMap);
                    tfra.setCurrentState(statusAfter);
                    tfra.setCurrentStateDT(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                    tfra.setSlaValue(slaModel.getSlaValue());
                    tfra.setSlaInfo(slaModel.getSlaInfo());
                    trxFuidApprovalService.updateTrxFuidApproval(tfra);
                }
            } else if (isSP) {
                TrxSetupParamRequest tspr = trxSetupParamRequestService.getTrxSetupParamRequestByTicketId(umtModel.getTicketId());
                TrxSetupParamApproval tspa = trxSetupParamApprovalService.getTrxSetupParamApprovalByTicketId(umtModel.getTicketId());
                //validation
                isPassValidation = validationAction(umtModel, isMaker, isChecker, tspa.getCurrentState(), tspr.getTanggalEfektif());
                if (isPassValidation) {
                    //check rejected with status before
                    if (isRejected && tspa.getCurrentState().equals(UPM_STATUS_VERIFICATION)) {
                        statusAfter = UPM_STATUS_INPROGRESS;
                        nik = tspa.getUpmInputNIK();
                        isRejectedWithUpdateUPM = true;
                        isRejectedFromVerifyChecker = true;
                    }
                    //check reject after finish all approve
                    if (isRejected && tspa.getCurrentState().equals(CURR_STATUS_APPROVED)) {
                        tspa.setUpmCheckerNIK(nik);
                        tspa.setUpmCheckerStatus(statusAfter);
                        tspa.setUpmCheckerDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                        tspa.setUpmCheckerNotes(notes);
                    }
                    if (umtModel.getType().equals(UPM_TICKET_TYPE_ASSIGN) || isRejectedWithUpdateUPM) {
                        tspa.setUpmInputNIK(nik);
                        tspa.setUpmInputStatus(statusAfter);
                        tspa.setUpmInputDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                        tspa.setUpmInputNotes(notes);
                    }
                    if (umtModel.getType().equals(UPM_TICKET_TYPE_CHECKER_VERIFY)) {
                        tspa.setUpmCheckerNIK(nik);
                        tspa.setUpmCheckerStatus(statusAfter);
                        tspa.setUpmCheckerDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                        tspa.setUpmCheckerNotes(notes);
                        //set note UPM Maker
                        notesUPMMaker = tspa.getUpmInputNotes();
                        trxSetupParamRequestAplikasiService.updateTrxFuidRequestAplikasiByTicketId(tspa.getTicketId(), DateTimeHelper.getDatePeriodDate(dateNow), DateTimeHelper.getDatePeriodMonth(dateNow));
                    }
                    if (isReassign && tspa.getCurrentState().equals(UPM_STATUS_INPROGRESS)){
                        tspa.setUpmInputNIK(umtModel.getNikMaker());
                        tspa.setUpmInputStatus(statusAfter);
                        tspa.setUpmInputDt(LocalDateTime.parse(sCurrStateDt, dateFormater1));
                    }
                    LocalDateTime approveDate = LocalDateTime.parse(sCurrStateDt, dateFormater1);
                    LocalDateTime dateArriveUPM = getDateArriveUPM(tspa.getPuk2Dt(), tspa.getPuk1Dt(), tspr.getCreateDateTime(), tspr.getTanggalEfektif());
                    SlaModel slaModel = calculateSLA(dateArriveUPM, approveDate, msHolidayMap);
                    tspa.setCurrentState(statusAfter);
                    tspa.setCurrentStateDT(approveDate);
                    tspa.setSlaInfo(slaModel.getSlaInfo());
                    tspa.setSlaValue(slaModel.getSlaValue());
                    trxSetupParamApprovalService.updateTrxSetupParamApproval(tspa);
                }
            }
        }

        if (isPassValidation) {
            TrxAudittrail ta = new TrxAudittrail();
            ta.setNik(nik);
            ta.setTicketId(umtModel.getTicketId());
            ta.setAction(statusAfter);
            ta.setCreateDateTime(LocalDateTime.parse(sCurrStateDt, dateFormater1));
            //json additional info
            DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
            String nikName = getUpmNikName(nik);
            TimelineStatusModel tsm = new TimelineStatusModel();
            if (statusAfter.equals(UPM_STATUS_INPROGRESS)) {
                tsm.setStatus(TIMELINE_STATUS_INPROGRESS_UPM);
                tsm.setPic(TIMELINE_PIC_UPM + nik + " - " + nikName);
            }
            if (statusAfter.equals(UPM_STATUS_VERIFICATION)) {
                tsm.setStatus(TIMELINE_STATUS_DONE_BY_UPM_MAKER);
                tsm.setPic(TIMELINE_PIC_UPM + nik + " - " + nikName);
            }
            if (statusAfter.equals(UPM_STATUS_DONE)) {
                tsm.setStatus(TIMELINE_STATUS_DONE);
                tsm.setPic(TIMELINE_PIC_UPM_APPROVE + nik + " - " + nikName);
            }
            if (isRejected) {
                tsm.setStatus(TIMELINE_STATUS_REJECT);
                tsm.setNote(umtModel.getNotes());
                if (isMaker) {
                    tsm.setPic(TIMELINE_PIC_UPM + nik + " - " + nikName);
                } else {
                    nik = profile.getProfile().getPreferred_username();
                    nikName = getUpmNikName(nik);
                    tsm.setPic(TIMELINE_PIC_UPM_APPROVE + nik + " - " + nikName);
                }
            }
            if (isReassign){
                ta.setAction(REASSIGN_UPM_ACTION);
                tsm.setStatus(TIMELINE_STATUS_REASSIGN_UPM);
                nikName = getUpmNikName(umtModel.getNikMaker());
                tsm.setPic(TIMELINE_PIC_UPM + umtModel.getNikMaker() + " - " + nikName);
            }
            tsm.setTimestamp(dateFormater3.format(ta.getCreateDateTime()));
            ta.setAdditionalInfo(new Gson().toJson(tsm));
            TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(ta);

            //send email
            String userNik = ""; String puk1NikSP = ""; String puk2NikSP = "";
            MsEmployee upmInfo = msEmployeeService.getMsEmployeeByNik(nik);
            EmailUpmModel emailUpmModel = new EmailUpmModel();
            emailUpmModel.setTicketId(umtModel.getTicketId());
            ContentEmail contentEmail = new ContentEmail();
            if (isFU) {
                TrxFuidRequest savedTFR = trxFuidRequestService.getTrxFuidRequestByTicketId(umtModel.getTicketId());
                userNik = savedTFR.getNikRequester();

                contentEmail = buildFuidContentEmail(savedTFR, umtModel, isRejected);
            }
            if (isSP) {
                TrxSetupParamRequest savedTSPR = trxSetupParamRequestService.getTrxSetupParamRequestByTicketId(umtModel.getTicketId());
                userNik = savedTSPR.getNikRequester();
                //set attachment
                TrxSetupParamApproval savedTSPA = savedTSPR.getTrxSetupParamApproval();
                puk1NikSP = savedTSPA.getPuk1NIK();
                puk2NikSP = savedTSPA.getPuk2NIK();

                contentEmail = buildSPContentEmail(savedTSPR, umtModel, savedTSPA.getUpmInputAttachment(), isRejected);
            }
            if (statusAfter.equals(UPM_STATUS_INPROGRESS) && !isRejectedFromVerifyChecker) {
                //set status
                String ecurrState = msSystemParamService.getMsSystemParamDetail(UPM_STATUS_INPROGRESS).getParamDetailDesc();
                contentEmail.setStatusTiket(ecurrState);

                sendRequestUPMInprogressToUpmTemaNotification(userNik, upmInfo, contentEmail);
            }
            if (statusAfter.equals(UPM_STATUS_DONE)) {
                //set status
                String ecurrState = msSystemParamService.getMsSystemParamDetail(UPM_STATUS_DONE).getParamDetailDesc();
                contentEmail.setStatusTiket(ecurrState);
                emailUpmModel.setStatus(ecurrState);
                //set UPM maker notes
                emailUpmModel.setNotes(notesUPMMaker);
                //if isSP cc ke puk1, puk2
                emailUpmModel.setSP(isSP);
                emailUpmModel.setPuk1NikSP(puk1NikSP);
                emailUpmModel.setPuk2NikSP(puk2NikSP);

                sendRequestUPMDoneToUpmTemaNotification(userNik, upmInfo, emailUpmModel, contentEmail);
            }
            if (isRejected && !isRejectedFromVerifyChecker) {
                //set status
                String ecurrState = msSystemParamService.getMsSystemParamDetail(CURR_STATUS_REJECTED).getParamDetailDesc();
                contentEmail.setStatusTiket(ecurrState);
                emailUpmModel.setStatus(ecurrState);
                //set UPM maker notes
                emailUpmModel.setNotes(notesUPMMaker);

                sendRequestUPMRejectToUpmTemaNotification(userNik, upmInfo, emailUpmModel, contentEmail);
            }
        }

        ResponseStatus resStatus;
        if(isPassValidation) {
            resStatus = SUCCESS;
        } else {
            resStatus = FAILED;
        }

        return buildResponse(resStatus, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    private boolean validationAction(UpmManageTicketModel umtModel, boolean isMaker, boolean isChecker, String currentState, Date tanggalEfektif) {
        boolean isOnHold = false;
        Date dateNow = new Date();
        if(dateNow.before(tanggalEfektif)){
            isOnHold = true;
        }
        if(isOnHold && CURR_STATUS_APPROVED.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_REJECT.equals(umtModel.getType()) && !CommonHelper.isTicketFuidSimplifikasi(umtModel.getTicketId())){
            return true;
        }
        if (isMaker){
            if (CURR_STATUS_APPROVED.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_PICK.equalsIgnoreCase(umtModel.getType())){
                return true;
            }else if ((UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) &&
                     UPM_TICKET_TYPE_PROCESS.equals(umtModel.getType()) &&
                     CommonHelper.isTicketFuidSimplifikasi(umtModel.getTicketId())) ||
                     ((UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_PROCESS.equals(umtModel.getType())) ||
                     (UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_REJECT.equals(umtModel.getType()) && !CommonHelper.isTicketFuidSimplifikasi(umtModel.getTicketId())))){
                return true;
            }
        }

        if (isChecker){
            if (CURR_STATUS_APPROVED.equals(currentState) && UPM_TICKET_TYPE_ASSIGN.equals(umtModel.getType())){
                return true;
            }else if ((UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) &&
                       UPM_TICKET_TYPE_PROCESS.equals(umtModel.getType()) &&
                       CommonHelper.isTicketFuidSimplifikasi(umtModel.getTicketId())) ||
                       ((UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_PROCESS.equals(umtModel.getType())) ||
                       (UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_REJECT.equals(umtModel.getType()) && !CommonHelper.isTicketFuidSimplifikasi(umtModel.getTicketId())))){
                return true;
            }else if (UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_REASSIGN.equalsIgnoreCase(umtModel.getType())){
                return true;
            }else if (UPM_STATUS_VERIFICATION.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_CHECKER_VERIFY.equalsIgnoreCase(umtModel.getType())){
                return true;
            }else if (!CURR_STATUS_REJECTED.equalsIgnoreCase(currentState) && UPM_STATUS_VERIFICATION.equalsIgnoreCase(currentState) && UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(umtModel.getType())){
                return true;
            }
        }

        return false;
    }

    private String getUpmNikName(String nik) {
        MsEmployee msEmployee = msEmployeeService.getMsEmployeeByNik(nik);
        String nikName = "";
        if(msEmployee == null) {
            nikName = trxUpmRoleService.getTrxUpmRole(nik).getNama();
        } else {
            nikName = msEmployee.getFullName();
        }
        return nikName;
    }

    private String generateTemplatedUpmNotesProspera(TrxFuidRequest tfr) {
        String notesUPM = "";

        if (ALASAN_KEWENANGAN_LIMIT.equalsIgnoreCase(tfr.getAlasan())){
            notesUPM = CommonHelper.buildTemplateUPMNotesUpdateLimitProspera(STATUS_SEDANG_DIPROSES);
        }else if (TUJUAN_RESET_PASSWORD_PROSPERA.equalsIgnoreCase(tfr.getTujuan())){
            notesUPM = CommonHelper.buildTemplateUPMNotesResetPswProspera(EMPTY, EMPTY, STATUS_SEDANG_DIPROSES);
        }else if (TUJUAN_ALTERNATE_DELEGASI_PROSPERA.equalsIgnoreCase(tfr.getTujuan())){
            notesUPM = CommonHelper.buildTemplateUPMNotesAlternateProspera(tfr, EMPTY, EMPTY, EMPTY, EMPTY, STATUS_SEDANG_DIPROSES, UPM_TICKET_TYPE_PICK);
        }else if (TUJUAN_PENDAFTARAN_BARU_PROSPERA.equalsIgnoreCase(tfr.getTujuan()) && !ALASAN_KEWENANGAN_LIMIT.equalsIgnoreCase(tfr.getAlasan())){
            if(ALASAN_MUTASI_ROTASI_PROMOSI.equalsIgnoreCase(tfr.getAlasan())){
                notesUPM = CommonHelper.buildTemplateUPMNotesMutasiProspera(tfr, getProsperaRoleDesc(tfr.getRole()), EMPTY, EMPTY, EMPTY, STATUS_SEDANG_DIPROSES);
            }else{
                notesUPM = CommonHelper.buildTemplateUPMNotesRegisterProspera(tfr, getProsperaRoleDesc(tfr.getRole()), EMPTY, EMPTY, STATUS_SEDANG_DIPROSES);
            }
        }else if (TUJUAN_PENGHAPUSAN_PROSPERA.equalsIgnoreCase(tfr.getTujuan()) && !ALASAN_KEWENANGAN_LIMIT.equalsIgnoreCase(tfr.getAlasan())){
            notesUPM = CommonHelper.buildTemplateUPMNotesDeleteProspera(tfr, STATUS_SEDANG_DIPROSES);
        }else if (TUJUAN_PERUBAHAN_PROSPERA.equalsIgnoreCase(tfr.getTujuan()) && !ALASAN_KEWENANGAN_LIMIT.equalsIgnoreCase(tfr.getAlasan())){
            notesUPM = CommonHelper.buildTemplateUPMNotesPerubahanProspera(tfr, getProsperaRoleDesc(tfr.getRole()), EMPTY, EMPTY, EMPTY, STATUS_SEDANG_DIPROSES, UPM_TICKET_TYPE_PICK);
        }

        return notesUPM;
    }

    private String getProsperaRoleDesc(String role) {
        return iMsProsperaRoleRepository.findAllByTemaRoleCode(role).getRoleDesc();
    }

    public ContentEmail buildFuidContentEmail(TrxFuidRequest trxFuidRequest, UpmManageTicketModel umtModel, boolean isRejected) {
        ContentEmail contentEmail = new ContentEmail();

        contentEmail.setNomorTiket(trxFuidRequest.getTicketId());
        contentEmail.setNik(trxFuidRequest.getDataNik());
        contentEmail.setNama(trxFuidRequest.getDataNamaLengkap());
        contentEmail.setJabatan(trxFuidRequest.getDataJabatan());
        contentEmail.setKodeDanNamaCabang(trxFuidRequest.getDataKodeCabang() + " - " + trxFuidRequest.getDataNamaCabang());

        String valueTujuan = msSystemParamService.getMsSystemParamDetail(trxFuidRequest.getTujuan()).getParamDetailDesc();
        contentEmail.setJenisPengajuan(valueTujuan);

        String[] splitAplikasi = trxFuidRequest.getAplikasi().split(",");
        List<String> listAplikasi = Arrays.asList(splitAplikasi);
        List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
        contentEmail.setAplikasiPengajuan(msta.toString().replaceAll("\\[|\\]", ""));

        String valueAlasan = msSystemParamService.getMsSystemParamDetail(trxFuidRequest.getAlasan()).getParamDetailDesc();
        contentEmail.setDeskripsi(valueAlasan);

        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        contentEmail.setTanggalTiket(dateFormater1.format(trxFuidRequest.getCreateDateTime()));

        if (isRejected && umtModel.getAttachmentUPMInput() != null && umtModel.getAttachmentUPMInput().size() > 0) {
            contentEmail.setAttachmentUPM(umtModel.getAttachmentUPMInput());
        } else if(trxFuidRequest.getTrxFuidApproval().getUpmInputAttachment() != null) {
            contentEmail.setAttachmentUPM(new Gson().fromJson(trxFuidRequest.getTrxFuidApproval().getUpmInputAttachment(), new TypeToken<ArrayList<AttachmentModel>>(){}.getType()));
        }

        return contentEmail;
    }

    public ContentEmail buildSPContentEmail(TrxSetupParamRequest trxSetupParamRequest, UpmManageTicketModel umtModel, String upmInputAttachment, boolean isRejected) {
        ContentEmail contentEmail = new ContentEmail();

        contentEmail.setNomorTiket(trxSetupParamRequest.getTicketId());
        contentEmail.setNik(trxSetupParamRequest.getDataNik());
        contentEmail.setNama(trxSetupParamRequest.getDataNamaLengkap());
        contentEmail.setJabatan(trxSetupParamRequest.getDataJabatan());
        contentEmail.setKodeDanNamaCabang(trxSetupParamRequest.getDataKodeCabang() + " - " + trxSetupParamRequest.getDataNamaCabang());
        contentEmail.setJenisPengajuan(trxSetupParamRequest.getKategoriParamName());

        String[] splitAplikasi = trxSetupParamRequest.getAplikasi().split(",");
        List<String> listAplikasi = Arrays.asList(splitAplikasi);
        List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
        contentEmail.setAplikasiPengajuan(msta.toString().replaceAll("\\[|\\]", ""));

        StringBuilder deskripsi = new StringBuilder();
        String paramLama = trxSetupParamRequest.getParameterLama() == null ? "" : trxSetupParamRequest.getParameterLama();
        String paramBaru = trxSetupParamRequest.getParameterBaru() == null ? "" : trxSetupParamRequest.getParameterBaru();
        String alasanPengajuan = trxSetupParamRequest.getAlasanPengajuan() == null ? "" : trxSetupParamRequest.getAlasanPengajuan();
        deskripsi.append(paramLama)
                .append("<tr><td></td><td>  ").append(paramBaru).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>");
        contentEmail.setDeskripsi(deskripsi.toString());

        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        contentEmail.setTanggalTiket(dateFormater1.format(trxSetupParamRequest.getCreateDateTime()));

        if (isRejected && umtModel.getAttachmentUPMInput() != null && umtModel.getAttachmentUPMInput().size() > 0) {
            contentEmail.setAttachmentUPM(umtModel.getAttachmentUPMInput());
        } else if(upmInputAttachment != null) {
            contentEmail.setAttachmentUPM(new Gson().fromJson(upmInputAttachment, new TypeToken<ArrayList<AttachmentModel>>(){}.getType()));
        }

        return contentEmail;
    }

    private void sendRequestUPMInprogressToUpmTemaNotification(String userNik, MsEmployee upmInfo, ContentEmail contentEmail) {
        emailNotificationService.sendRequestUPMInprogressToUpmTemaNotification(userNik, upmInfo, contentEmail);
    }

    private void sendRequestUPMDoneToUpmTemaNotification(String userNik, MsEmployee upmInfo, EmailUpmModel emailUpmModel, ContentEmail contentEmail) {
        emailNotificationService.sendRequestUPMDoneToUpmTemaNotification(userNik, upmInfo, emailUpmModel, contentEmail);
    }

    private void sendRequestUPMRejectToUpmTemaNotification(String userNik, MsEmployee upmInfo, EmailUpmModel emailUpmModel, ContentEmail contentEmail) {
        emailNotificationService.sendRequestUPMRejectToUpmTemaNotification(userNik, upmInfo, emailUpmModel, contentEmail);
    }

    private ResponseModel<ResUpmManageTicketModel> setProsperaTicketUPM(UpmManageTicketModel umtModel, String authorization, String nik) throws Exception {
        ResponseModel<ResUpmManageTicketModel> response = new ResponseModel<>();

        TrxFuidRequest tfr = trxFuidRequestService.getTrxFuidRequestByTicketId(umtModel.getTicketId());
        boolean isMms = false, isKcKfo = false, isHO = false;
        if (tfr.getDataNamaCabang().startsWith(MMS)){
            isMms = true;
        }
        if (tfr.getDataNamaCabang().startsWith(KC) || tfr.getDataNamaCabang().startsWith(KFO)){
            isKcKfo = true;
        }
        if (tfr.getDataNamaCabang().startsWith(KANTOR_PUSAT)){
            isHO = true;
        }

        if (UPM_TICKET_TYPE_PROCESS_PROSPERA.equalsIgnoreCase(umtModel.getType())){
            response = processProsperaTicketUPM(tfr, umtModel, authorization, nik, isMms, isKcKfo, isHO);
        }else if (UPM_TICKET_TYPE_DELETE_PROSPERA.equalsIgnoreCase(umtModel.getType())) {
            response = deleteProsperaTicketUPM(tfr, umtModel, authorization, nik);
        }else if (UPM_TICKET_TYPE_RESET_PASSWORD_PROSPERA.equalsIgnoreCase(umtModel.getType())){
            response = resetPswTicketUPM(tfr, umtModel, authorization, nik);
        }else if (UPM_TICKET_TYPE_RESUBMIT_DELETE_PROSPERA.equalsIgnoreCase(umtModel.getType())){
            response = resubmitDeleteProsperaTicket(tfr, umtModel, authorization, nik);
        }else if (UPM_TICKET_TYPE_MUTASI_PROSPERA.equalsIgnoreCase(umtModel.getType())){
            response = mutasiProsperaTicketUPM(tfr, umtModel, authorization, nik, isMms, isKcKfo, isHO);
        }else if (UPM_TICKET_TYPE_ALTERNATE_DELEGASI_PROSPERA.equalsIgnoreCase(umtModel.getType()) || UPM_TICKET_TYPE_PERUBAHAN_PROSPERA.equalsIgnoreCase(umtModel.getType())){
            response = alternateDelegasiOrPerubahanProsperaTicketUPM(tfr, umtModel, authorization, nik, isMms, isKcKfo, isHO);
        }else if (UPM_TICKET_TYPE_LIMIT_BWMP_PROSPERA.equalsIgnoreCase(umtModel.getType())){
            response = limitBwmpProsperaTicketUPM(tfr, umtModel, authorization, nik);
        }else if (UPM_TICKET_TYPE_PROCESS_EXPIRED_PROSPERA.equalsIgnoreCase(umtModel.getType())){
            response = processExpiredProsperaTicketUPM(tfr, umtModel, authorization, nik, isMms, isKcKfo, isHO);
        }

        return response;
    }

    private ResponseModel<ResUpmManageTicketModel> processProsperaTicketUPM(TrxFuidRequest tfr, UpmManageTicketModel umtModel, String authorization, String nik, boolean isMms, boolean isKcKfo, boolean isHO) throws Exception {
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        MessageValueProspera<ResUpdatePersonnelProspera> resActionUpdateUserData = new MessageValueProspera<>();
        MessageValueProspera<ResRegisterNewPersonnelProspera> resRegisterNewPersonnelProspera = new MessageValueProspera<>();

        ResponseStatus status = FAILED_PROCESSED_BY_PROSPERA;

        if (umtModel.getUpmActionProspera().size() > 0){
            if(umtModel.getUpmActionProspera().size() == 1){
                if (umtModel.getUpmActionProspera().contains(ACTION_REACTIVATE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqReactivatePersonnelProspera(authorization, umtModel);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA)) {
                    reqPersonnelProspera = buildReqUpdateRolePersonnelProspera(tfr.getRole(), authorization, umtModel);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }
            }

            if(umtModel.getUpmActionProspera().size() == 2){
                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA) && umtModel.getUpmActionProspera().contains(ACTION_REACTIVATE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateRolePersonnelProspera(tfr.getRole(), authorization, umtModel);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA) && umtModel.getUpmActionProspera().contains(ACTION_REACTIVATE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateOrganizationPersonnelProspera(tfr.getDataKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA) && umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateRoleAndOrganizationPersonnelProspera(tfr.getRole(), tfr.getDataKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }
            }

            if(umtModel.getUpmActionProspera().size() == 3){
                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA)
                    && umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA)
                    && umtModel.getUpmActionProspera().contains(ACTION_REACTIVATE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateRoleAndOrganizationPersonnelProspera(tfr.getRole(), tfr.getDataKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }
            }
        }else {
            if(isValidProcessNewHireProsperaFUS(tfr, authorization)){
                status = SUCCESS;
            }else {
                reqPersonnelProspera = buildReqPersonnelProspera(tfr, authorization, isMms, isKcKfo, isHO);
                resRegisterNewPersonnelProspera = registerNewPersonnelProspera(tfr, umtModel, reqPersonnelProspera, authorization, isKcKfo);
                if (isSuccessProspera(resRegisterNewPersonnelProspera.getResponseStatus().getResponseCode())){
                    status = SUCCESS;
                }
            }
        }

        if (SUCCESS.equals(status)) {
            TrxFuidApproval tfa = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
            tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesRegisterProspera(tfr, CommonHelper.isTicketFuidSimplifikasi(tfr.getTicketId()) ? CO : getProsperaRoleDesc(tfr.getRole()), reqPersonnelProspera.getCenterCodeOfficer(), reqPersonnelProspera.getUserPassword(), STATUS_DONE));
            trxFuidApprovalService.updateTrxFuidApproval(Mapper.toTrxFuidApproval(umtModel, tfa, UPM_STATUS_VERIFICATION));
            trxAudittrailService.saveTrxAudittrail(Mapper.toTrxAudittrail(umtModel, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM, nik, getUpmNikName(nik)));
        }

        return buildResponse(status, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    private boolean isValidProcessNewHireProsperaFUS(TrxFuidRequest tfr, String authorization) {
        List<UserProspera> userProsperaList = prosperaRepository.getPersonnelProsperaByNik(tfr.getDataNik(), authorization);
        return CommonHelper.isTicketFuidSimplifikasi(tfr.getTicketId()) &&
               userProsperaList.stream().filter(data -> STATUS_PERSONNEL_PROSPERA_ACTIVE.equalsIgnoreCase(data.getStatus()) && data.getRoles() != null
               && data.getRoles().size() > 0 && tfr.getDataNik().equalsIgnoreCase(data.getNik())).collect(Collectors.toList()).size() > 0;
    }

    private ReqPersonnelProspera buildReqPersonnelProspera(TrxFuidRequest tfr, String authorization, boolean isMms, boolean isKcKfo, boolean isHO) {
        ReqPersonnelProspera rpp = new ReqPersonnelProspera();
        String dateNow = DateTimeHelper.getDateToDateStringYYYYMMDD(new Date());
        String generatedenterCodeOfficerUserKcKfo= "", password = "";

        if (isMms){
            MsMMS msMMS = msMmsRepository.findAllByMMSCode(tfr.getDataKodeCabang());
            ResNewCenterCodeOfficer newCenterCodeOfficer = prosperaRepository.getNewCenterCodeOfficer(Integer.valueOf(msMMS.getMmsId()), authorization);

            rpp.setCenterCodeOfficer(newCenterCodeOfficer.getCenterCodeOfficer());
            rpp.setLoginName(newCenterCodeOfficer.getCenterCodeOfficer());
        }else {
            List<UserProspera> userProsperaList = prosperaRepository.getPersonnelProsperaByNik(tfr.getDataNik(), authorization);
            rpp.setLoginName(CommonHelper.generateLoginName(userProsperaList, tfr.getDataNik()));

            if (isKcKfo){
                generatedenterCodeOfficerUserKcKfo = generateCenterCodeOfficerUserKcKfo(tfr.getDataKodeCabang());
                rpp.setCenterCodeOfficer(generatedenterCodeOfficerUserKcKfo);
            }else if (isHO){
                rpp.setCenterCodeOfficer(CommonHelper.concateTwoString(tfr.getDataNik().substring(tfr.getDataNik().length() - 3), RandomStringUtils.randomAlphanumeric(3).toUpperCase()));
            }
        }

        rpp.setLevelId(LEVEL_ID_PROSPERA);
        rpp.setOfficeId(getOfficeId(authorization, tfr.getDataKodeCabang(), isMms, isKcKfo, isHO));
        rpp.setDateOfJoiningMFI(dateNow);
        rpp.setEffectiveDate(dateNow);
        rpp.setNik(tfr.getDataNik());
        rpp.setFirstName(tfr.getDataNamaLengkap().toUpperCase());
        rpp.setEmailId(concateTwoString(tfr.getDataNik(), PREFFIX_EMAIL_BTPNS));

        if (CommonHelper.isTicketFuidSimplifikasi(tfr.getTicketId())){
            rpp.setPersonnelRoles(List.of(PROSPERA_ROLE_CODE_CO));

            rpp.setUserPassword(PSW_TEMPALTE_UPM);
            rpp.setPasswordRepeat(PSW_TEMPALTE_UPM);
        }else {
            rpp.setPersonnelRoles(generateRoles(tfr.getRole()));

            password = CommonHelper.generateUserPassword(tfr.getDataNik(), dateNow);
            rpp.setUserPassword(password);
            rpp.setPasswordRepeat(password);
        }

        if (ALASAN_KEWENANGAN_LIMIT.equalsIgnoreCase(tfr.getAlasan()) && tfr.getNominalTransaksi() != null){
            rpp.setLimit(tfr.getNominalTransaksi());
        }

        return rpp;
    }

    private String generateCenterCodeOfficerUserKcKfo(String kodeCabang) {
        String firstTwoDigits = "";
        String lastCenterCodeOfficer = iMsCenterCodeOfficerProsperaRepository.getLastCenterCodeOfficer(kodeCabang);

        if (lastCenterCodeOfficer == null){
            firstTwoDigits = "A0";
        }else {
            if(lastCenterCodeOfficer.substring(1,2).equalsIgnoreCase("9")){
                firstTwoDigits = CommonHelper.concateTwoString(String.valueOf(CommonHelper.getNextChar(lastCenterCodeOfficer.charAt(0))),"0");
            }else {
                firstTwoDigits = CommonHelper.concateTwoString(lastCenterCodeOfficer.substring(0,1), String.format("%01d", Integer.parseInt(lastCenterCodeOfficer.substring(1,2)) + 1));
            }
        }

        return CommonHelper.concateTwoString(firstTwoDigits, kodeCabang);
    }

    private MsCenterCodeOfficerProspera mapToCenterCodeOfficer(String namaCabang, String kodeCabang, String generatedenterCodeOfficer) {
        MsCenterCodeOfficerProspera msCenterCodeOfficerProspera = new MsCenterCodeOfficerProspera();

        msCenterCodeOfficerProspera.setCabangDesc(namaCabang);
        msCenterCodeOfficerProspera.setCabangId(kodeCabang);
        msCenterCodeOfficerProspera.setGeneratedCenterCodeOfficer(generatedenterCodeOfficer);
        msCenterCodeOfficerProspera.setCreateDateTime(LocalDateTime.now());

        return msCenterCodeOfficerProspera;
    }

    private List<Integer> generateRoles(String role) {
        MsProsperaRole msProsperaRole = prosperaRoleRepository.findAllByTemaRoleCode(role);

        List<Integer> roleList = new ArrayList<>();
        roleList.add(msProsperaRole.getProsperaRoleCode());

        return roleList;
    }

    private List<Integer> generateRolesFromProspera(String role) {
        MsProsperaRole msProsperaRole = prosperaRoleRepository.findAllByRoleDesc(role);

        List<Integer> roleList = new ArrayList<>();
        roleList.add(msProsperaRole.getProsperaRoleCode());

        return roleList;
    }

    private MessageValueProspera<ResRegisterNewPersonnelProspera> registerNewPersonnelProspera(TrxFuidRequest tfr, UpmManageTicketModel umtModel, ReqPersonnelProspera reqPersonnelProspera, String authorization, boolean isKcKfo) {
        MessageValueProspera<ResRegisterNewPersonnelProspera> resRegisterNewPersonnelProspera = prosperaRepository.registerNewPersonnelProspera(reqPersonnelProspera, authorization);

        TrxProsperaRequest trxProsperaRequest = Mapper.toTrxProsperaRequest(umtModel, tfr, reqPersonnelProspera);
        if (isSuccessProspera(resRegisterNewPersonnelProspera.getResponseStatus().getResponseCode())) {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resRegisterNewPersonnelProspera));
            trxProsperaRequest.setStatus(SUCCESS.toString());

            if (isKcKfo){
                iMsCenterCodeOfficerProsperaRepository.save(mapToCenterCodeOfficer(tfr.getDataNamaCabang(), tfr.getDataKodeCabang(), resRegisterNewPersonnelProspera.getData().getCenterCodeOfficer()));
            }
        }else {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resRegisterNewPersonnelProspera));
            trxProsperaRequest.setStatus(FAILED.toString());
        }

        prosperaRequestRepository.save(trxProsperaRequest);

        return resRegisterNewPersonnelProspera;
    }

    private ResponseModel<ResUpmManageTicketModel> deleteProsperaTicketUPM(TrxFuidRequest tfr, UpmManageTicketModel umtModel, String authorization, String nik) {
        ResponseStatus status = FAILED_PROCESSED_BY_PROSPERA;
        if (isValidProcessManualResignProsperaFUS(tfr, authorization)){
           status = SUCCESS;
        }else {
            ReqPersonnelProspera reqPersonnelProspera = buildReqInActivePersonnelProspera(umtModel, authorization);
            MessageValueProspera<ResUpdatePersonnelProspera> resUpdatePersonnelProspera = inActiveUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
            if (isSuccessProspera(resUpdatePersonnelProspera.getResponseStatus().getResponseCode())) {
                status = SUCCESS;
            }else if (resUpdatePersonnelProspera.getDetailErrors() != null
                    && resUpdatePersonnelProspera.getDetailErrors().size() > 0
                    && FAILED_SET_STATUS_PROSPERA_RESPONSE_DESC.equalsIgnoreCase(resUpdatePersonnelProspera.getDetailErrors().get(0).getMessage())) {
                status = FAILED_SET_STATUS_PERSONNEL_PROSPERA;
            }
        }

        if (SUCCESS.equals(status)){
            TrxFuidApproval tfa = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
            tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesDeleteProspera(tfr, STATUS_DONE));
            trxFuidApprovalService.updateTrxFuidApproval(Mapper.toTrxFuidApproval(umtModel, tfa, UPM_STATUS_VERIFICATION));
            trxAudittrailService.saveTrxAudittrail(Mapper.toTrxAudittrail(umtModel, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM, nik, getUpmNikName(nik)));
        }

        return buildResponse(status, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    private boolean isValidProcessManualResignProsperaFUS(TrxFuidRequest tfr, String authorization) {
        List<UserProspera> userProsperaList = prosperaRepository.getPersonnelProsperaByNik(tfr.getDataNik(), authorization);
        return CommonHelper.isTicketFuidSimplifikasi(tfr.getTicketId()) &&
               userProsperaList.stream().filter(data -> STATUS_PERSONNEL_PROSPERA_ACTIVE.equalsIgnoreCase(data.getStatus()) && data.getRoles() != null
               && data.getRoles().size() > 0 && tfr.getDataNik().equalsIgnoreCase(data.getNik())).collect(Collectors.toList()).size() == 0;
    }

    private ReqPersonnelProspera buildReqInActivePersonnelProspera(UpmManageTicketModel umtModel, String authorization) {
        DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        reqPersonnelProspera.setCenterCodeOfficer(detailUserProspera.getCenterCodeOfficer());
        reqPersonnelProspera.setNik(detailUserProspera.getNik());
        reqPersonnelProspera.setOfficeId(getOfficeIdFromProspera(detailUserProspera.getOfficeName(), authorization));
        reqPersonnelProspera.setLevelId(LEVEL_ID_PROSPERA);
        reqPersonnelProspera.setPersonnelRoles(detailUserProspera.getRoleName().isEmpty() ? Collections.EMPTY_LIST : generateRolesFromProspera(detailUserProspera.getRoleName()));
        reqPersonnelProspera.setFirstName(detailUserProspera.getDisplayName());
        reqPersonnelProspera.setLoginName(detailUserProspera.getLoginName());
        reqPersonnelProspera.setStatus(STATUS_PERSONNEL_PROSPERA_INACTIVE_CODE);
        reqPersonnelProspera.setVersionNo(detailUserProspera.getVersionNo());

        return reqPersonnelProspera;
    }

    private MessageValueProspera<ResUpdatePersonnelProspera> inActiveUserProspera(TrxFuidRequest tfr, UpmManageTicketModel umtModel, ReqPersonnelProspera reqPersonnelProspera, String authorization) {
        MessageValueProspera<ResUpdatePersonnelProspera> resUpdatePersonnelProspera = prosperaRepository.updatePersonnelProspera(reqPersonnelProspera, umtModel.getReqProspera().getPersonnelId(), authorization);

        TrxProsperaRequest trxProsperaRequest = Mapper.toTrxProsperaRequest(umtModel, tfr, reqPersonnelProspera);
        if (isSuccessProspera(resUpdatePersonnelProspera.getResponseStatus().getResponseCode())) {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resUpdatePersonnelProspera));
            trxProsperaRequest.setStatus(SUCCESS.toString());
        }else if (resUpdatePersonnelProspera.getDetailErrors() != null && resUpdatePersonnelProspera.getDetailErrors().size() > 0 && FAILED_SET_STATUS_PROSPERA_RESPONSE_DESC.equalsIgnoreCase(resUpdatePersonnelProspera.getDetailErrors().get(0).getMessage())) {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resUpdatePersonnelProspera));
            trxProsperaRequest.setStatus(FAILED.toString());
        }else {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resUpdatePersonnelProspera));
            trxProsperaRequest.setStatus(FAILED.toString());
        }

        prosperaRequestRepository.save(trxProsperaRequest);

        return resUpdatePersonnelProspera;
    }

    private Integer getOfficeId(String authorization, String kodeCabang, boolean isMms, boolean isKcKfo, boolean isHO) {
        Integer officeId = null;

        if (isMms){
            MsMMS msMMS = msMmsRepository.findAllByMMSCode(kodeCabang);
            officeId = Integer.valueOf(msMMS.getMmsId());
        }else if (isKcKfo || isHO){
            ResOfficeDetailProspera resOfficeDetailProspera = prosperaRepository.getOfficeProsperaByOfficeCode(formattingOfficeCode(isKcKfo, isHO, kodeCabang), authorization);
            officeId = resOfficeDetailProspera.getOfficeId();
        }

        return officeId;
    }

    private String formattingOfficeCode(boolean isKcKfo, boolean isHO, String kodeCabang) {
        String officeCode = "";
        if (isHO){
            officeCode = PREFIX_HO;
        }else if (isKcKfo){
            officeCode = CommonHelper.concateTwoString(PREFIX_CABANG, kodeCabang);
        }

        return officeCode;
    }

    private Integer getOfficeIdFromProspera(String officeName, String authorization) {
        Integer officeId = null;

        Map<String, Object> paramMap = buildParamMapGetListOfficeProspera(CommonHelper.getOfficeLevel(officeName));
        List<ResListOfficeProspera> resOfficeDetailProspera = prosperaRepository.getListOfficeProspera(authorization, paramMap);
        for (ResListOfficeProspera data : resOfficeDetailProspera) {
            if (officeName.equalsIgnoreCase(data.getOfficeName())){
                officeId = data.getOfficeId();
            }
        }

        return officeId;
    }

    private Map<String, Object> buildParamMapGetListOfficeProspera(Integer officeLevel) {
        HashMap<String, Object> paramMap = new HashMap<>() {{
            put("officeLevel", officeLevel);
        }};
        return paramMap;
    }

    private ResponseModel<ResUpmManageTicketModel> resubmitDeleteProsperaTicket(TrxFuidRequest tfr, UpmManageTicketModel umtModel, String authorization, String nik) {
        ReqPersonnelProspera reqPersonnelProspera = buildReqResubmitInactivePersonnelProspera(umtModel, authorization);
        MessageValueProspera<ResUpdatePersonnelProspera> resUpdatePersonnelProspera = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);

        ResponseStatus status = FAILED_PROCESSED_BY_PROSPERA;
        if (isSuccessProspera(resUpdatePersonnelProspera.getResponseStatus().getResponseCode())) {
            TrxFuidApproval tfa = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
            tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesDeleteProspera(tfr, STATUS_DONE));
            TrxFuidApproval savedTfa = trxFuidApprovalService.updateTrxFuidApproval(Mapper.toTrxFuidApproval(umtModel, tfa, UPM_STATUS_VERIFICATION));
            TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(Mapper.toTrxAudittrail(umtModel, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM, nik, getUpmNikName(nik)));

            if (savedTfa != null && savedTa != null){
                status = SUCCESS;
            }
        }

        return buildResponse(status, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    private ReqPersonnelProspera buildReqResubmitInactivePersonnelProspera(UpmManageTicketModel umtModel, String authorization) {
        DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        reqPersonnelProspera.setCenterCodeOfficer(detailUserProspera.getCenterCodeOfficer());
        reqPersonnelProspera.setNik(detailUserProspera.getNik());
        reqPersonnelProspera.setOfficeId(getOfficeIdFromProspera(detailUserProspera.getOfficeName(), authorization));
        reqPersonnelProspera.setLevelId(LEVEL_ID_PROSPERA);
        reqPersonnelProspera.setFirstName(detailUserProspera.getDisplayName());
        reqPersonnelProspera.setLoginName(detailUserProspera.getLoginName());
        reqPersonnelProspera.setStatus(STATUS_PERSONNEL_PROSPERA_ACTIVE_CODE);
        reqPersonnelProspera.setVersionNo(detailUserProspera.getVersionNo());

        return reqPersonnelProspera;
    }

    private MessageValueProspera<ResUpdatePersonnelProspera> updateDataUserProspera(TrxFuidRequest tfr, UpmManageTicketModel umtModel, ReqPersonnelProspera reqPersonnelProspera, String authorization) {
        MessageValueProspera<ResUpdatePersonnelProspera> resUpdatePersonnelProspera = prosperaRepository.updatePersonnelProspera(reqPersonnelProspera, umtModel.getReqProspera().getPersonnelId(), authorization);

        TrxProsperaRequest trxProsperaRequest = Mapper.toTrxProsperaRequest(umtModel, tfr, reqPersonnelProspera);
        if (isSuccessProspera(resUpdatePersonnelProspera.getResponseStatus().getResponseCode())) {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resUpdatePersonnelProspera));
            trxProsperaRequest.setStatus(SUCCESS.toString());
        }else {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resUpdatePersonnelProspera));
            trxProsperaRequest.setStatus(FAILED.toString());
        }

        prosperaRequestRepository.save(trxProsperaRequest);

        return resUpdatePersonnelProspera;
    }

    private ResponseModel<ResUpmManageTicketModel> resetPswTicketUPM(TrxFuidRequest tfr, UpmManageTicketModel umtModel, String authorization, String nik) {
        ReqPersonnelProspera reqPersonnelProspera = buildReqResetPswPersonnelProspera(tfr, authorization, umtModel);
        MessageValueProspera<ResUpdatePersonnelProspera> resUpdatePersonnelProspera = prosperaRepository.updatePersonnelProspera(reqPersonnelProspera, umtModel.getReqProspera().getPersonnelId(), authorization);

        ResponseStatus status = FAILED_PROCESSED_BY_PROSPERA;
        TrxProsperaRequest trxProsperaRequest = Mapper.toTrxProsperaRequest(umtModel, tfr, reqPersonnelProspera);
        if (isSuccessProspera(resUpdatePersonnelProspera.getResponseStatus().getResponseCode())) {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resUpdatePersonnelProspera));
            trxProsperaRequest.setStatus(SUCCESS.toString());

            TrxFuidApproval tfa = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
            tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesResetPswProspera(reqPersonnelProspera.getLoginName(), reqPersonnelProspera.getUserPassword(), STATUS_DONE));
            TrxFuidApproval savedTfa = trxFuidApprovalService.updateTrxFuidApproval(Mapper.toTrxFuidApproval(umtModel, tfa, UPM_STATUS_VERIFICATION));

            TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(Mapper.toTrxAudittrail(umtModel, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM, nik, getUpmNikName(nik)));

            if (savedTfa != null && savedTa != null){
                status = SUCCESS;
            }
        }else {
            trxProsperaRequest.setPayloadResponse(new Gson().toJson(resUpdatePersonnelProspera));
            trxProsperaRequest.setStatus(FAILED.toString());
        }

        prosperaRequestRepository.save(trxProsperaRequest);

        return buildResponse(status, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    private ReqPersonnelProspera buildReqResetPswPersonnelProspera(TrxFuidRequest tfr, String authorization, UpmManageTicketModel umtModel) {
        DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();
        String password = "";
        String dateNow = DateTimeHelper.getDateToDateStringYYYYMMDD(new Date());

        reqPersonnelProspera.setNik(detailUserProspera.getNik());
        reqPersonnelProspera.setOfficeId(getOfficeIdFromProspera(detailUserProspera.getOfficeName(), authorization));
        reqPersonnelProspera.setLevelId(LEVEL_ID_PROSPERA);
        reqPersonnelProspera.setPersonnelRoles(generateRolesFromProspera(detailUserProspera.getRoleName()));
        reqPersonnelProspera.setFirstName(detailUserProspera.getDisplayName());
        reqPersonnelProspera.setLoginName(detailUserProspera.getLoginName());
        reqPersonnelProspera.setStatus(STATUS_PERSONNEL_PROSPERA_ACTIVE_CODE);
        reqPersonnelProspera.setVersionNo(detailUserProspera.getVersionNo());

        password = CommonHelper.generateUserPassword(tfr.getDataNik(), dateNow);
        reqPersonnelProspera.setUserPassword(password);
        reqPersonnelProspera.setPasswordRepeat(password);

        return reqPersonnelProspera;
    }

    private boolean isSuccessProspera(String responseCode) {
        return PROSPERA_SUCCESS_RESPONSE_CODE.equalsIgnoreCase(responseCode);
    }


    private ResponseModel<ResUpmManageTicketModel> mutasiProsperaTicketUPM(TrxFuidRequest tfr, UpmManageTicketModel umtModel, String authorization, String nik, boolean isMms, boolean isKcKfo, boolean isHO) throws Exception {
        ReqPersonnelProspera reqRegisterPersonnelProspera = new ReqPersonnelProspera();

        ResponseStatus status = FAILED_PROCESSED_BY_PROSPERA;
        if (tfr.getIsInActivePersonnelProspera() != null && TRUE_FLAG_INT.equals(tfr.getIsInActivePersonnelProspera())){
            DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
            if(detailUserProspera != null){
                ReqPersonnelProspera reqUpdatePersonnelProspera = buildReqInActivePersonnelProspera(umtModel, authorization);
                MessageValueProspera<ResUpdatePersonnelProspera> resUpdatePersonnelProspera = inActiveUserProspera(tfr, umtModel, reqUpdatePersonnelProspera, authorization);

                if (isSuccessProspera(resUpdatePersonnelProspera.getResponseStatus().getResponseCode())){
                    reqRegisterPersonnelProspera = buildReqPersonnelProspera(tfr, authorization, isMms, isKcKfo, isHO);
                    MessageValueProspera<ResRegisterNewPersonnelProspera> resRegisterNewPersonnelProspera = registerNewPersonnelProspera(tfr, umtModel, reqRegisterPersonnelProspera, authorization, isKcKfo);

                    if (isSuccessProspera(resRegisterNewPersonnelProspera.getResponseStatus().getResponseCode())) {
                        status = SUCCESS;
                    } else {
                        status = FAILED_REGISTER_BY_PROSPERA;
                    }
                }else if (resUpdatePersonnelProspera.getDetailErrors() != null && resUpdatePersonnelProspera.getDetailErrors().size() > 0 && FAILED_SET_STATUS_PROSPERA_RESPONSE_DESC.equalsIgnoreCase(resUpdatePersonnelProspera.getDetailErrors().get(0).getMessage())){
                    ReqPersonnelProspera reqResubmitUpdatePersonnelProspera = buildReqResubmitInactivePersonnelProspera(umtModel, authorization);
                    MessageValueProspera<ResUpdatePersonnelProspera> resResubmitPersonnelProspera = updateDataUserProspera(tfr, umtModel, reqResubmitUpdatePersonnelProspera, authorization);

                    if (isSuccessProspera(resResubmitPersonnelProspera.getResponseStatus().getResponseCode())) {
                        reqRegisterPersonnelProspera = buildReqPersonnelProspera(tfr, authorization, isMms, isKcKfo, isHO);
                        MessageValueProspera<ResRegisterNewPersonnelProspera> resRegisterNewPersonnelProspera = registerNewPersonnelProspera(tfr, umtModel, reqRegisterPersonnelProspera, authorization, isKcKfo);

                        if (isSuccessProspera(resRegisterNewPersonnelProspera.getResponseStatus().getResponseCode())) {
                            status = SUCCESS;
                        } else {
                            status = FAILED_REGISTER_BY_PROSPERA;
                        }
                    }
                }
            }else {
                reqRegisterPersonnelProspera = buildReqPersonnelProspera(tfr, authorization, isMms, isKcKfo, isHO);
                MessageValueProspera<ResRegisterNewPersonnelProspera> resRegisterNewPersonnelProspera = registerNewPersonnelProspera(tfr, umtModel, reqRegisterPersonnelProspera, authorization, isKcKfo);

                if (isSuccessProspera(resRegisterNewPersonnelProspera.getResponseStatus().getResponseCode())) {
                    status = SUCCESS;
                } else {
                    status = FAILED_REGISTER_BY_PROSPERA;
                }
            }
        }else {
            reqRegisterPersonnelProspera = buildReqPersonnelProspera(tfr, authorization, isMms, isKcKfo, isHO);
            MessageValueProspera<ResRegisterNewPersonnelProspera> resRegisterNewPersonnelProspera = registerNewPersonnelProspera(tfr, umtModel, reqRegisterPersonnelProspera, authorization, isKcKfo);

            if (isSuccessProspera(resRegisterNewPersonnelProspera.getResponseStatus().getResponseCode())) {
                status = SUCCESS;
            } else {
                status = FAILED_REGISTER_BY_PROSPERA;
            }
        }

        if(SUCCESS.equals(status)){
            TrxFuidApproval tfa = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
            tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesMutasiProspera(tfr, getProsperaRoleDesc(tfr.getRole()), reqRegisterPersonnelProspera.getCenterCodeOfficer(), reqRegisterPersonnelProspera.getLoginName(), reqRegisterPersonnelProspera.getUserPassword(), STATUS_DONE));
            trxFuidApprovalService.updateTrxFuidApproval(Mapper.toTrxFuidApproval(umtModel, tfa, UPM_STATUS_VERIFICATION));
            trxAudittrailService.saveTrxAudittrail(Mapper.toTrxAudittrail(umtModel, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM, nik, getUpmNikName(nik)));
        }

        return buildResponse(status, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    private ResponseModel<ResUpmManageTicketModel> alternateDelegasiOrPerubahanProsperaTicketUPM(TrxFuidRequest tfr, UpmManageTicketModel umtModel, String authorization, String nik, boolean isMms, boolean isKcKfo, boolean isHO) {
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        MessageValueProspera<ResUpdatePersonnelProspera> resActionUpdateUserData = new MessageValueProspera<>();
        MessageValueProspera<ResRegisterNewPersonnelProspera> resActionRegisterUser = new MessageValueProspera<>();

        ResponseStatus status = FAILED_PROCESSED_BY_PROSPERA;

        if (umtModel.getUpmActionProspera().size() > 0){
            if(umtModel.getUpmActionProspera().size() == 1){
                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA)) {
                    reqPersonnelProspera = buildReqUpdateRolePersonnelProspera(tfr.getRole(), authorization, umtModel);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA)) {
                    reqPersonnelProspera = buildReqUpdateOrganizationPersonnelProspera(tfr.getDataKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_REGISTER_USER_PROSPERA)) {
                    reqPersonnelProspera = buildReqPersonnelProspera(tfr, authorization, isMms, isKcKfo, isHO);
                    resActionRegisterUser = registerNewPersonnelProspera(tfr, umtModel, reqPersonnelProspera, authorization, isKcKfo);
                    if (isSuccessProspera(resActionRegisterUser.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_REACTIVATE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqReactivatePersonnelProspera(authorization, umtModel);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }
            }

            if (umtModel.getUpmActionProspera().size() == 2){
                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA) && umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateRoleAndOrganizationPersonnelProspera(tfr.getRole(), tfr.getDataKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA) && umtModel.getUpmActionProspera().contains(ACTION_REACTIVATE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateRolePersonnelProspera(tfr.getRole(), authorization, umtModel);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA) && umtModel.getUpmActionProspera().contains(ACTION_REACTIVATE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateOrganizationPersonnelProspera(tfr.getDataKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_INACTICVE_USER_PROSPERA) && umtModel.getUpmActionProspera().contains(ACTION_REGISTER_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqInActivePersonnelProspera(umtModel, authorization);
                    resActionUpdateUserData = inActiveUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        reqPersonnelProspera = buildReqPersonnelProspera(tfr, authorization, isMms, isKcKfo, isHO);
                        resActionRegisterUser = registerNewPersonnelProspera(tfr, umtModel, reqPersonnelProspera, authorization, isKcKfo);
                        if (isSuccessProspera(resActionRegisterUser.getResponseStatus().getResponseCode())){
                            status = SUCCESS;
                        }
                    }else if (resActionUpdateUserData.getDetailErrors() != null && resActionUpdateUserData.getDetailErrors().size() > 0 && FAILED_SET_STATUS_PROSPERA_RESPONSE_DESC.equalsIgnoreCase(resActionUpdateUserData.getDetailErrors().get(0).getMessage())){
                        reqPersonnelProspera = buildReqResubmitInactivePersonnelProspera(umtModel, authorization);
                        resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                        if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())) {
                            reqPersonnelProspera = buildReqPersonnelProspera(tfr, authorization, isMms, isKcKfo, isHO);
                            resActionRegisterUser = registerNewPersonnelProspera(tfr, umtModel, reqPersonnelProspera, authorization, isKcKfo);
                            if (isSuccessProspera(resActionRegisterUser.getResponseStatus().getResponseCode())){
                                status = SUCCESS;
                            }
                        }
                    }
                }
            }

            if (umtModel.getUpmActionProspera().size() == 3){
                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA)
                    && umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA)
                    && umtModel.getUpmActionProspera().contains(ACTION_REACTIVATE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateRoleAndOrganizationPersonnelProspera(tfr.getRole(), tfr.getDataKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }
            }
        }

        if(SUCCESS.equals(status)){
            TrxFuidApproval tfa = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
            if (TUJUAN_ALTERNATE_DELEGASI_PROSPERA.equalsIgnoreCase(tfr.getTujuan())) {
                if (umtModel.getUpmActionProspera().contains(ACTION_REGISTER_USER_PROSPERA)){
                    tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesAlternateProspera(tfr, getProsperaRoleDesc(tfr.getRole()), reqPersonnelProspera.getCenterCodeOfficer(), reqPersonnelProspera.getLoginName(), reqPersonnelProspera.getUserPassword(), STATUS_DONE, UPM_TICKET_TYPE_PROCESS));
                }else {
                    tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesAlternateProspera(tfr, EMPTY, EMPTY, EMPTY, EMPTY, STATUS_DONE, UPM_TICKET_TYPE_PROCESS));
                }
            }

            if (TUJUAN_PERUBAHAN_PROSPERA.equalsIgnoreCase(tfr.getTujuan())){
                if (umtModel.getUpmActionProspera().contains(ACTION_REGISTER_USER_PROSPERA)){
                    tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesPerubahanProspera(tfr, getProsperaRoleDesc(tfr.getRole()), reqPersonnelProspera.getCenterCodeOfficer(), reqPersonnelProspera.getLoginName(), reqPersonnelProspera.getUserPassword(), STATUS_DONE, UPM_TICKET_TYPE_PROCESS));
                }else {
                    tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesPerubahanProspera(tfr, EMPTY, EMPTY, EMPTY, EMPTY, STATUS_DONE, UPM_TICKET_TYPE_PROCESS));
                }
            }
            trxFuidApprovalService.updateTrxFuidApproval(Mapper.toTrxFuidApproval(umtModel, tfa, UPM_STATUS_VERIFICATION));
            trxAudittrailService.saveTrxAudittrail(Mapper.toTrxAudittrail(umtModel, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM, nik, getUpmNikName(nik)));
        }

        return buildResponse(status, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    private ReqPersonnelProspera buildReqUpdateRoleAndOrganizationPersonnelProspera(String role, String kodeCabang, String authorization, UpmManageTicketModel umtModel, boolean isMms, boolean isKcKfo, boolean isHO) {
        DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        reqPersonnelProspera.setCenterCodeOfficer(detailUserProspera.getCenterCodeOfficer());
        reqPersonnelProspera.setNik(detailUserProspera.getNik());
        reqPersonnelProspera.setOfficeId(getOfficeId(authorization, kodeCabang, isMms, isKcKfo, isHO));
        reqPersonnelProspera.setLevelId(LEVEL_ID_PROSPERA);
        reqPersonnelProspera.setPersonnelRoles(generateRoles(role));
        reqPersonnelProspera.setFirstName(detailUserProspera.getDisplayName());
        reqPersonnelProspera.setLoginName(detailUserProspera.getLoginName());
        reqPersonnelProspera.setStatus(STATUS_PERSONNEL_PROSPERA_ACTIVE_CODE);
        reqPersonnelProspera.setVersionNo(detailUserProspera.getVersionNo());

        return reqPersonnelProspera;
    }

    private ReqPersonnelProspera buildReqUpdateRolePersonnelProspera(String role, String authorization, UpmManageTicketModel umtModel) {
        DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        reqPersonnelProspera.setCenterCodeOfficer(detailUserProspera.getCenterCodeOfficer());
        reqPersonnelProspera.setNik(detailUserProspera.getNik());
        reqPersonnelProspera.setOfficeId(getOfficeIdFromProspera(detailUserProspera.getOfficeName(), authorization));
        reqPersonnelProspera.setLevelId(LEVEL_ID_PROSPERA);
        reqPersonnelProspera.setPersonnelRoles(generateRoles(role));
        reqPersonnelProspera.setFirstName(detailUserProspera.getDisplayName());
        reqPersonnelProspera.setLoginName(detailUserProspera.getLoginName());
        reqPersonnelProspera.setStatus(STATUS_PERSONNEL_PROSPERA_ACTIVE_CODE);
        reqPersonnelProspera.setVersionNo(detailUserProspera.getVersionNo());

        return reqPersonnelProspera;
    }

    private ReqPersonnelProspera buildReqUpdateOrganizationPersonnelProspera(String kodeCabang, String authorization, UpmManageTicketModel umtModel, boolean isMms, boolean isKcKfo, boolean isHO) {
        DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        reqPersonnelProspera.setCenterCodeOfficer(detailUserProspera.getCenterCodeOfficer());
        reqPersonnelProspera.setNik(detailUserProspera.getNik());
        reqPersonnelProspera.setOfficeId(getOfficeId(authorization, kodeCabang, isMms, isKcKfo, isHO));
        reqPersonnelProspera.setLevelId(LEVEL_ID_PROSPERA);
        reqPersonnelProspera.setPersonnelRoles(generateRolesFromProspera(detailUserProspera.getRoleName()));
        reqPersonnelProspera.setFirstName(detailUserProspera.getDisplayName());
        reqPersonnelProspera.setLoginName(detailUserProspera.getLoginName());
        reqPersonnelProspera.setStatus(STATUS_PERSONNEL_PROSPERA_ACTIVE_CODE);
        reqPersonnelProspera.setVersionNo(detailUserProspera.getVersionNo());

        return reqPersonnelProspera;
    }

    private ReqPersonnelProspera buildReqReactivatePersonnelProspera(String authorization, UpmManageTicketModel umtModel) {
        DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        reqPersonnelProspera.setCenterCodeOfficer(detailUserProspera.getCenterCodeOfficer());
        reqPersonnelProspera.setNik(detailUserProspera.getNik());
        reqPersonnelProspera.setOfficeId(getOfficeIdFromProspera(detailUserProspera.getOfficeName(), authorization));
        reqPersonnelProspera.setLevelId(LEVEL_ID_PROSPERA);
        reqPersonnelProspera.setPersonnelRoles(generateRolesFromProspera(detailUserProspera.getRoleName()));
        reqPersonnelProspera.setFirstName(detailUserProspera.getDisplayName());
        reqPersonnelProspera.setLoginName(detailUserProspera.getLoginName());
        reqPersonnelProspera.setStatus(STATUS_PERSONNEL_PROSPERA_ACTIVE_CODE);
        reqPersonnelProspera.setVersionNo(detailUserProspera.getVersionNo());

        return reqPersonnelProspera;
    }

    private ResponseModel<ResUpmManageTicketModel> limitBwmpProsperaTicketUPM(TrxFuidRequest tfr, UpmManageTicketModel umtModel, String authorization, String nik) {
        ResponseStatus status = FAILED_PROCESSED_BY_PROSPERA;

        if (umtModel.getNominalTransaksiUPM() == null || CommonHelper.isValidNominalTransaksiUPM(umtModel.getNominalTransaksiUPM())){
            ReqPersonnelProspera reqPersonnelProspera = buildReqtUpdateLimitPersonnelProspera(tfr, authorization, umtModel);
            MessageValueProspera<ResUpdatePersonnelProspera> resUpdateLimitPersonnelProspera = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
            if (isSuccessProspera(resUpdateLimitPersonnelProspera.getResponseStatus().getResponseCode())) {
                TrxFuidApproval tfa = trxFuidApprovalService.getTrxFuidApprovalByTicketId(umtModel.getTicketId());
                tfa.setUpmInputNotes(CommonHelper.buildTemplateUPMNotesUpdateLimitProspera(STATUS_DONE));
                trxFuidApprovalService.updateTrxFuidApproval(Mapper.toTrxFuidApproval(umtModel, tfa, UPM_STATUS_VERIFICATION));
                trxAudittrailService.saveTrxAudittrail(Mapper.toTrxAudittrail(umtModel, UPM_STATUS_VERIFICATION, TIMELINE_STATUS_DONE_BY_UPM_MAKER, TIMELINE_PIC_UPM, nik, getUpmNikName(nik)));

                status = SUCCESS;
            }
        }else {
            status = FAILED_MAX_DIGIT_REQUIRED;
        }

        return buildResponse(status, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    private ReqPersonnelProspera buildReqtUpdateLimitPersonnelProspera(TrxFuidRequest tfr, String authorization, UpmManageTicketModel umtModel) {
        DetailUserProspera detailUserProspera = prosperaRepository.getPersonnelDetailProspera(umtModel.getReqProspera().getGlobalPersonnelNum(), authorization);
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        reqPersonnelProspera.setNik(detailUserProspera.getNik());
        reqPersonnelProspera.setOfficeId(getOfficeIdFromProspera(detailUserProspera.getOfficeName(), authorization));
        reqPersonnelProspera.setLevelId(LEVEL_ID_PROSPERA);
        reqPersonnelProspera.setPersonnelRoles(generateRolesFromProspera(detailUserProspera.getRoleName()));
        reqPersonnelProspera.setFirstName(detailUserProspera.getDisplayName());
        reqPersonnelProspera.setLoginName(detailUserProspera.getLoginName());
        reqPersonnelProspera.setStatus(STATUS_PERSONNEL_PROSPERA_ACTIVE_CODE);
        reqPersonnelProspera.setVersionNo(detailUserProspera.getVersionNo());
        if(TUJUAN_PENDAFTARAN_BARU_PROSPERA.equalsIgnoreCase(tfr.getTujuan()) || TUJUAN_PERUBAHAN_PROSPERA.equalsIgnoreCase(tfr.getTujuan())){
            if (CommonHelper.isValidNominalTransaksiUPM(umtModel.getNominalTransaksiUPM())){
                reqPersonnelProspera.setLimit(umtModel.getNominalTransaksiUPM());

                tfr.setNominalTransaksiUPM(umtModel.getNominalTransaksiUPM());
                trxFuidRequestRepository.save(tfr);
            }else {
                reqPersonnelProspera.setLimit(tfr.getNominalTransaksi());
            }
        }else if (TUJUAN_PENGHAPUSAN_PROSPERA.equalsIgnoreCase(tfr.getTujuan())){
            reqPersonnelProspera.setLimit(NOL_TYPE_DOUBLE);
        }

        return reqPersonnelProspera;
    }

    private ResponseModel<ResUpmManageTicketModel> processExpiredProsperaTicketUPM(TrxFuidRequest tfr, UpmManageTicketModel umtModel, String authorization, String nik, boolean isMms, boolean isKcKfo, boolean isHO) {
        ReqPersonnelProspera reqPersonnelProspera = new ReqPersonnelProspera();

        MessageValueProspera<ResUpdatePersonnelProspera> resActionUpdateUserData = new MessageValueProspera<>();

        ResponseStatus status = FAILED_PROCESSED_BY_PROSPERA;

        if (umtModel.getUpmActionProspera().size() > 0){
            if(umtModel.getUpmActionProspera().size() == 1){
                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA)) {
                    reqPersonnelProspera = buildReqUpdateRolePersonnelProspera(umtModel.getRole(), authorization, umtModel);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA)) {
                    reqPersonnelProspera = buildReqUpdateOrganizationPersonnelProspera(umtModel.getKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }

                if (umtModel.getUpmActionProspera().contains(ACTION_INACTICVE_USER_PROSPERA)){
                    reqPersonnelProspera = buildReqInActivePersonnelProspera(umtModel, authorization);
                    resActionUpdateUserData = inActiveUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }else if (resActionUpdateUserData.getDetailErrors() != null && resActionUpdateUserData.getDetailErrors().size() > 0 && FAILED_SET_STATUS_PROSPERA_RESPONSE_DESC.equalsIgnoreCase(resActionUpdateUserData.getDetailErrors().get(0).getMessage())){
                        reqPersonnelProspera = buildReqResubmitInactivePersonnelProspera(umtModel, authorization);
                        resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                        if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())) {
                            status = SUCCESS;
                        }
                    }
                }
            }

            if(umtModel.getUpmActionProspera().size() == 2){
                if (umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ROLE_PROSPERA) && umtModel.getUpmActionProspera().contains(ACTION_CHANGE_ORGANIZATION_PROSPERA)){
                    reqPersonnelProspera = buildReqUpdateRoleAndOrganizationPersonnelProspera(umtModel.getRole(), umtModel.getKodeCabang(), authorization, umtModel, isMms, isKcKfo, isHO);
                    resActionUpdateUserData = updateDataUserProspera(tfr, umtModel, reqPersonnelProspera, authorization);
                    if (isSuccessProspera(resActionUpdateUserData.getResponseStatus().getResponseCode())){
                        status = SUCCESS;
                    }
                }
            }
        }

        if (SUCCESS.equals(status)) {
            TrxExpiredFuid tef = iTrxExpiredFuidRepository.findByTicketId(tfr.getTicketId());
            tef.setIsProcessedByUpm(TRUE_FLAG_INT);
            iTrxExpiredFuidRepository.save(tef);
            emailNotificationService.sendUserExpiredTemaNotification(tef);
        }

        return buildResponse(status, buildResUpmManageTicketModel(umtModel.getTicketId(), umtModel.getType()));
    }

    public ResponseModel<ListExpiredFuidModel> getListExpiredFuid(int pageNumMin1, Integer pageNumber, Integer pageSize, Integer isProcessedByUpm) {
        if (!this.isSavedToday()) {
            List<LocalDate> dates = getDatesBasedOnLastSaved();
            List<TrxFuidRequest> trxFuidRequests = trxFuidRequestRepository.findExpiredFuidByStateAndDates(UPM_STATUS_DONE, dates);
            buildAndSaveExpiredFuid(trxFuidRequests);
        }

        Page<TrxExpiredFuid> expiredTickets = this.iTrxExpiredFuidRepository.findExpiredFuidByState(isProcessedByUpm, PageRequest.of(pageNumMin1, pageSize));

        return buildResponse(pageSize, pageNumber, expiredTickets.getContent(), expiredTickets.getTotalPages(), expiredTickets.getTotalElements());
    }

    private List<LocalDate> getDatesBasedOnLastSaved() {
        List<LocalDate> dates = new ArrayList<>();
        Optional<TrxExpiredFuid> optional = this.iTrxExpiredFuidRepository.findFirstByOrderByCreatedDateDesc();
        if (optional.isEmpty()) {
            dates.add(LocalDate.now().minusDays(1));
        } else {
            long dateDifference = ChronoUnit.DAYS.between(new java.sql.Date(optional.get().getCreatedDate().getTime()).toLocalDate(), LocalDate.now());

            for (long index = 1; index <= dateDifference; index++) {
                dates.add(LocalDate.now().minusDays(index));
            }
        }

        return dates;
    }

    private boolean isSavedToday() {
        return iTrxExpiredFuidRepository.countSavedExpiredFuidByCreatedDate(LocalDate.now()) > 0;
    }

    private void buildAndSaveExpiredFuid(List<TrxFuidRequest> trxFuidRequests) {
        List<TrxExpiredFuid> expiredFuidList = new ArrayList<>();
        for (TrxFuidRequest fuidRequest : trxFuidRequests) {
            expiredFuidList.add(this.mapToExpiredFuid(fuidRequest, upmRolesToMap(trxUpmRoleService.getListTrxUpmRole())));
        }
        iTrxExpiredFuidRepository.saveAll(expiredFuidList);
    }

    private Map<String, TrxUpmRole> upmRolesToMap(List<TrxUpmRole> upmRoles) {
        Map<String, TrxUpmRole> ret = new HashMap<>();
        upmRoles.forEach(upmRole -> {
            ret.put(upmRole.getNik(), upmRole);
        });

        return ret;
    }

    private TrxExpiredFuid mapToExpiredFuid(TrxFuidRequest fuidRequest, Map<String, TrxUpmRole> upmRoleMap) {
        TrxExpiredFuid expiredFuidRequest = new TrxExpiredFuid();

        expiredFuidRequest.setTicketId(fuidRequest.getTicketId());
        expiredFuidRequest.setCreatedDate(java.sql.Date.valueOf(LocalDate.now()));
        expiredFuidRequest.setTanggalEfektif(fuidRequest.getTanggalEfektif());
        expiredFuidRequest.setNik(fuidRequest.getDataNik());
        expiredFuidRequest.setNama(fuidRequest.getDataNamaLengkap());
        expiredFuidRequest.setKodeCabamg(fuidRequest.getDataKodeCabang());
        expiredFuidRequest.setNamaCabang(fuidRequest.getDataNamaCabang());
        expiredFuidRequest.setAplikasi(fuidRequest.getAplikasi());
        expiredFuidRequest.setTanggalMasaBerlaku(fuidRequest.getMasaBerlakuSampai());
        expiredFuidRequest.setJenisPengajuan(fuidRequest.getTujuan());
        expiredFuidRequest.setAlasanPengajuan(fuidRequest.getAlasan());
        expiredFuidRequest.setPicProcess(upmRoleMap.get(fuidRequest.getTrxFuidApproval().getUpmInputNIK().toUpperCase()).getNama());
        expiredFuidRequest.setPicApprove(upmRoleMap.get(fuidRequest.getTrxFuidApproval().getUpmCheckerNIK().toUpperCase()).getNama());
        expiredFuidRequest.setIsProcessedByUpm(FALSE_FLAG_INT);

        return expiredFuidRequest;
    }

    private ResponseModel<ListExpiredFuidModel> buildResponse(Integer pageSize, Integer pageNumber, List<TrxExpiredFuid> expiredFuid, int totalPages, long totalItems) {
        ResponseModel<ListExpiredFuidModel> response = new ResponseModel<>();

        response.setType(TYPE_EXPIRED_FUID_GET_LIST);
        response.setStatus(SUCCESS.getCode());
        response.setStatusDesc(SUCCESS.getValue());
        response.setDetails(buildListExpiredFuidModel(pageSize, pageNumber, expiredFuid, totalPages, totalItems));

        return response;
    }

    private ListExpiredFuidModel buildListExpiredFuidModel(Integer pageSize, Integer pageNumber, List<TrxExpiredFuid> expiredFuidList, int totalPages, long totalItems) {
        ListExpiredFuidModel listUpmTicketModel = new ListExpiredFuidModel();

        listUpmTicketModel.setExpiredFuidList(mapToListExpiredFuidModel(expiredFuidList));
        listUpmTicketModel.setPage(pageSize);
        listUpmTicketModel.setLimit(pageNumber);
        listUpmTicketModel.setTotalItems(totalItems);
        listUpmTicketModel.setTotalPages(totalPages);

        return listUpmTicketModel;
    }

    private List<ExpiredFuidModel> mapToListExpiredFuidModel(List<TrxExpiredFuid> expiredFuidList) {
        List<ExpiredFuidModel> expiredFuidModelList = new ArrayList<>();
        Map<String, MsSystemParamDetail> sysParamMap = msSystemParamService.getSystemParamDetailMap();

        for (TrxExpiredFuid expiredFuid : expiredFuidList) {
            expiredFuidModelList.add(buildExpiredFuidModel(expiredFuid, sysParamMap));
        }

        return expiredFuidModelList;
    }

    private ExpiredFuidModel buildExpiredFuidModel(TrxExpiredFuid expiredFuidRequest, Map<String, MsSystemParamDetail> sysParamMap) {
        ExpiredFuidModel expiredFuidModel = new ExpiredFuidModel();

        expiredFuidModel.setTicketId(expiredFuidRequest.getTicketId());
        expiredFuidModel.setTanggalEfektif(DateTimeHelper.getDateToDateStringDDMMYYYY(expiredFuidRequest.getTanggalEfektif()));
        expiredFuidModel.setNik(expiredFuidRequest.getNik());
        expiredFuidModel.setNama(expiredFuidRequest.getNama());
        expiredFuidModel.setKodeCabang(expiredFuidRequest.getKodeCabamg());
        expiredFuidModel.setNamaCabang(expiredFuidRequest.getNamaCabang());
        expiredFuidModel.setAplikasi(mapApplicationNames(expiredFuidRequest.getAplikasi()));
        expiredFuidModel.setJenisPengajuan(expiredFuidRequest.getJenisPengajuan());
        expiredFuidModel.setAlasanPengajuan(sysParamMap.get(expiredFuidRequest.getAlasanPengajuan()).getParamDetailDesc());
        expiredFuidModel.setPicProcess(expiredFuidRequest.getPicProcess());
        expiredFuidModel.setPicApprove(expiredFuidRequest.getPicApprove());
        expiredFuidModel.setIsProcessedByUpm(expiredFuidRequest.getIsProcessedByUpm());

        return expiredFuidModel;
    }

    private String mapApplicationNames(String paramDetailIds) {
        List<String> paramDetailIdList = Arrays.asList(paramDetailIds.split(","));
        List<String> paramDetailDescList = msTemaApplicationService.getMsTemaApplicationList(paramDetailIdList);

        return paramDetailDescList.toString().replaceAll("\\[|\\]", "");
    }

    @Transactional
    public ResponseModel<ResBatchProcess> processExpiredFuidBatch(ReqTicketBatchModel requests) {
        List<TrxExpiredFuid> expiredFuidList = iTrxExpiredFuidRepository.findAllById(requests.getTicketIds());
        for (TrxExpiredFuid expiredFuid : expiredFuidList) {
            expiredFuid.setIsProcessedByUpm(TRUE_FLAG_INT);
        }
        iTrxExpiredFuidRepository.saveAll(expiredFuidList);
        sendUserExpiredTemaNotification(expiredFuidList);

        return buildResponse(expiredFuidList.size());
    }

    private void sendUserExpiredTemaNotification(List<TrxExpiredFuid> expiredFuidList) {
        expiredFuidList.forEach(fuid -> {
            emailNotificationService.sendUserExpiredTemaNotification(fuid);
        });
    }

    private ResponseModel<ResBatchProcess> buildResponse(Integer totalData) {
        ResponseModel<ResBatchProcess> response = new ResponseModel<>();

        response.setType(TYPE_EXPIRED_FUID_PROCESS_BATCH);
        response.setStatus(SUCCESS.getCode());
        response.setStatusDesc(SUCCESS.getValue());
        response.setDetails(Mapper.buildResBatchProcess(totalData, STATUS_PROCESSED));

        return response;
    }

    public List<TrxExpiredFuid> getListUnprocessedExpiredFuid() {
        Pageable pageable = PageRequest.of(0, 50);
        if (!this.isSavedToday()) {
            List<LocalDate> dates = getDatesBasedOnLastSaved();
            List<TrxFuidRequest> trxFuidRequests = trxFuidRequestRepository.findExpiredFuidByStateAndDates(UPM_STATUS_DONE, dates);
            buildAndSaveExpiredFuid(trxFuidRequests);
        }

        return this.iTrxExpiredFuidRepository.findExpiredFuidByState(0, pageable).getContent();
    }

    public ResponseModel<ResUpmReassignPukModel> reassignPUK(UpmReassignPukModel reassignPukModel, Profile profile) throws ParseException {
        String ticketId = reassignPukModel.getTicketId();
        String nikRequester = profile.getPreferred_username();
        String nameRequester = profile.getName();

        boolean isSuccessful = false;

        if (ticketId.startsWith(PREFIX_FU)) {
            isSuccessful = reassignPukFU(reassignPukModel, ticketId, nikRequester, nameRequester, isSuccessful);
        }
        if (ticketId.startsWith(PREFIX_SP)) {
            isSuccessful = reassignPukSP(reassignPukModel, ticketId, nikRequester, nameRequester, isSuccessful);
        }
        if (ticketId.startsWith(PREFIX_UAR)) {
            isSuccessful = trxUPMUARService.reassignPukUAR(reassignPukModel, ticketId, profile);
        }

        ResponseStatus resStatus = isSuccessful ? SUCCESS : FAILED;

        return ResponseModel.<ResUpmReassignPukModel>builder().type(TYPE_UPM_TICKET_REASSIGN_PUK).status(resStatus.getCode()).statusDesc(resStatus.getValue()).details(new ResUpmReassignPukModel(ticketId)).build();
    }

    private boolean reassignPukFU(UpmReassignPukModel reassignPukModel, String ticketId, String nikRequester, String nameRequester, boolean isSuccessful) throws ParseException {
        TrxFuidApproval savedTFA = trxFuidApprovalService.getTrxFuidApprovalByTicketId(ticketId);
        String fromPUK1 = savedTFA.getPuk1NIK();
        String toPUK1 = reassignPukModel.getPuk1();
        String fromPUK2 = savedTFA.getPuk2NIK();
        String toPUK2 = reassignPukModel.getPuk2();

        if (isWaitingPUK1(savedTFA.getCurrentState())) {
            if(isValidReassignPuk1(toPUK1, toPUK2, fromPUK1, fromPUK2)) {
                isSuccessful = executeUpdateFuidApproval(ticketId, nikRequester, nameRequester, savedTFA, fromPUK1, toPUK1, true);
            }
            if(isValidReassignPuk2(toPUK1, toPUK2, fromPUK1, fromPUK2, savedTFA.getCurrentState())) {
                isSuccessful = executeUpdateFuidApproval(ticketId, nikRequester, nameRequester, savedTFA, fromPUK2, toPUK2, false);
            }
        } else if (isWaitingPUK2(savedTFA.getCurrentState())
                && (isValidReassignPuk2(reassignPukModel.getPuk1(), reassignPukModel.getPuk2(), savedTFA.getPuk1NIK(), savedTFA.getPuk2NIK(), savedTFA.getCurrentState()))) {
            isSuccessful = executeUpdateFuidApproval(ticketId, nikRequester, nameRequester, savedTFA, fromPUK2, toPUK2, false);
        }
        return isSuccessful;
    }

    private boolean executeUpdateFuidApproval(String ticketId, String nikRequester, String nameRequester, TrxFuidApproval savedTFA, String fromPUK, String toPUK, boolean isPUK1) throws ParseException {
        boolean isSuccessful = updateDataPukFU(toPUK, savedTFA, isPUK1);

        if (isSuccessful) isSuccessful = insertTrxAudittrail(ticketId, nikRequester, nameRequester, fromPUK, toPUK);
        if (isSuccessful) sendEmailtoDelegatedFU(ticketId, toPUK); sendEmailtoPreviousDelegatedApproval(ticketId, fromPUK, toPUK);

        return isSuccessful;
    }

    private boolean updateDataPukFU(String nikPUK, TrxFuidApproval savedTFA, boolean isPUK1) {
        MsEmployee newPUK = msEmployeeService.getEmployeeByNik(nikPUK);

        if (isPUK1) {
            savedTFA.setPuk1NIK(nikPUK);
            savedTFA.setPuk1Name(newPUK.getFullName());
            savedTFA.setPuk1Occupation(newPUK.getOccupationDesc());
        } else {
            savedTFA.setPuk2NIK(nikPUK);
            savedTFA.setPuk2Name(newPUK.getFullName());
            savedTFA.setPuk2Occupation(newPUK.getOccupationDesc());
        }

        return updatePUKApproval(savedTFA);
    }

    private boolean reassignPukSP(UpmReassignPukModel reassignPukModel, String ticketId, String nikRequester, String nameRequester, boolean isSuccessful) throws ParseException {
        TrxSetupParamApproval savedTSPA = trxSetupParamApprovalService.getTrxSetupParamApprovalByTicketId(ticketId);
        String fromPUK1 = savedTSPA.getPuk1NIK();
        String toPUK1 = reassignPukModel.getPuk1();
        String fromPUK2 = savedTSPA.getPuk2NIK();
        String toPUK2 = reassignPukModel.getPuk2();

        if (isWaitingPUK1(savedTSPA.getCurrentState())) {
            if(isValidReassignPuk1(reassignPukModel.getPuk1(), reassignPukModel.getPuk2(), savedTSPA.getPuk1NIK(), savedTSPA.getPuk2NIK())) {
                isSuccessful = executeUpdateSPApproval(ticketId, nikRequester, nameRequester, savedTSPA, fromPUK1, toPUK1, true);
            }
            if(isValidReassignPuk2(reassignPukModel.getPuk1(), reassignPukModel.getPuk2(), savedTSPA.getPuk1NIK(), savedTSPA.getPuk2NIK(), savedTSPA.getCurrentState())) {
                isSuccessful = executeUpdateSPApproval(ticketId, nikRequester, nameRequester, savedTSPA, fromPUK2, toPUK2, false);
            }
        } else if (isWaitingPUK2(savedTSPA.getCurrentState())
                && (isValidReassignPuk2(reassignPukModel.getPuk1(), reassignPukModel.getPuk2(), savedTSPA.getPuk1NIK(), savedTSPA.getPuk2NIK(), savedTSPA.getCurrentState()))) {
            isSuccessful = executeUpdateSPApproval(ticketId, nikRequester, nameRequester, savedTSPA, fromPUK2, toPUK2, false);
        }
        return isSuccessful;
    }

    private boolean executeUpdateSPApproval(String ticketId, String nikRequester, String nameRequester, TrxSetupParamApproval savedTSPA, String fromPUK, String toPUK, boolean isPUK1) throws ParseException {
        boolean isSuccessful = updateDataPukSP(toPUK, savedTSPA, isPUK1);

        if (isSuccessful) isSuccessful = insertTrxAudittrail(ticketId, nikRequester, nameRequester, fromPUK, toPUK);
        if (isSuccessful) sendEmailtoDelegatedSP(ticketId, toPUK); sendEmailtoPreviousDelegatedApproval(ticketId, fromPUK, toPUK);

        return isSuccessful;
    }

    private boolean updateDataPukSP(String nikPUK, TrxSetupParamApproval savedTSPA, boolean isPUK1) {
        MsEmployee puk = msEmployeeService.getEmployeeByNik(nikPUK);

        if (isPUK1) {
            savedTSPA.setPuk1NIK(nikPUK);
            savedTSPA.setPuk1Name(puk.getFullName());
            savedTSPA.setPuk1Occupation(puk.getOccupationDesc());
        } else {
            savedTSPA.setPuk2NIK(nikPUK);
            savedTSPA.setPuk2Name(puk.getFullName());
            savedTSPA.setPuk2Occupation(puk.getOccupationDesc());
        }

        return updatePUKApproval(savedTSPA);
    }

    private static boolean isWaitingPUK1(String currentState) {
        return currentState.equals(CURR_STATUS_WAITING_PUK1);
    }

    private static boolean isWaitingPUK2(String currentState) {
        return currentState.equals(CURR_STATUS_WAITING_PUK2);
    }

    private boolean updatePUKApproval(TrxFuidApproval savedTFA){
        TrxFuidApproval updatedTFA = trxFuidApprovalService.updateTrxFuidApproval(savedTFA);

        return updatedTFA != null;
    }

    private boolean updatePUKApproval(TrxSetupParamApproval savedTSPA){
        TrxSetupParamApproval updatedTSPA = trxSetupParamApprovalService.updateTrxSetupParamApproval(savedTSPA);

        return updatedTSPA != null;
    }

    private boolean isValidReassignPuk1(String newPuk1, String newPuk2, String existingPuk1, String existingPuk2){
        if(existingPuk2 != null && newPuk1 != null && newPuk2 != null){
            if(!newPuk1.equalsIgnoreCase(existingPuk1) && !newPuk1.equalsIgnoreCase(newPuk2)){
                return true;
            }
        } else if(existingPuk2 == null && newPuk1 != null && (!newPuk1.equalsIgnoreCase(existingPuk1))){
                return true;
        }
        return false;
    }

    private boolean isValidReassignPuk2(String newPuk1, String newPuk2, String existingPuk1, String existingPuk2, String status){
        if(status.equals(CURR_STATUS_WAITING_PUK1)
                && existingPuk2 != null && newPuk1 != null && newPuk2 != null
                && (!newPuk2.equalsIgnoreCase(existingPuk2) && !newPuk1.equalsIgnoreCase(newPuk2))){
                return true;
        }

        if(status.equals(CURR_STATUS_WAITING_PUK2)
                && (newPuk2 != null
                && (!newPuk2.equalsIgnoreCase(existingPuk1) && !newPuk2.equalsIgnoreCase(existingPuk2)))){
            return true;
        }

        return false;
    }

    private boolean insertTrxAudittrail(String ticketId, String nik, String name, String fromPUK, String toPUK) throws ParseException {
        TrxAudittrail audittrail = Mapper.toTrxAudittrailEntity(ticketId);
        audittrail.setNik(nik);
        audittrail.setAction(REASSIGN_PUK_ACTION);
        audittrail.setAdditionalInfo(new Gson().toJson(buildTimelineStatusReassign(nik, name, fromPUK, toPUK, audittrail)));

        return  trxAudittrailService.saveTrxAudittrail(audittrail) != null;
    }

    private static TimelineStatusModel buildTimelineStatusReassign(String nik, String name, String fromPUK, String toPUK, TrxAudittrail ta) {
        TimelineStatusModel timelineStatusModel = new TimelineStatusModel();
        timelineStatusModel.setStatus(TIMELINE_STATUS_REASSIGN + fromPUK + " to " + toPUK);
        timelineStatusModel.setPic(TIMELINE_PIC_UPM + nik + " - " + name);
        timelineStatusModel.setTimestamp(DateTimeHelper.getDateFormater3(ta.getCreateDateTime()));

        return timelineStatusModel;
    }

    private void sendEmailtoDelegatedFU(String ticketId, String pukNik) {
        TrxFuidRequest savedTFR = trxFuidRequestService.getTrxFuidRequestByTicketId(ticketId);
        delegationService.sendEmailtoDelegatedFU(pukNik, savedTFR);
    }

    private void sendEmailtoPreviousDelegatedApproval(String ticketId, String fromPUK, String toPUK) {
        delegationService.sendEmailtoPreviousDelegatedApproval(ticketId, fromPUK, toPUK);
    }

    private void sendEmailtoDelegatedSP(String ticketId, String pukNik) {
        TrxSetupParamRequest savedTSPR = trxSetupParamRequestService.getTrxSetupParamRequestByTicketId(ticketId);
        delegationService.sendEmailtoDelegatedSP(pukNik, savedTSPR);
    }

    public ListUpmTicketModel getUPMTicket(String ticketId, String status, String type, String upmProcess, String searchFlag, List<String> nikList, String name, Integer pageSize, Integer pageNumber) {
        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        int pageNumMin1 = pageNumber - 1;

        ListUpmTicketModel lutm = new ListUpmTicketModel();
        if (CommonHelper.isTicketUAR(ticketId)){
            getListTicketUARUPM(lutm, ticketId, pageNumMin1, pageSize);
        }else {
            getListTicketUserIDAndSetupParamterUPM(lutm, status, type, ticketId, sDateNow, upmProcess, searchFlag, nikList, name, pageNumMin1, pageSize);
        }

        lutm.setPage(pageNumber);
        lutm.setLimit(pageSize);
        lutm.setStatus(status);
        lutm.setType(type);

        return lutm;
    }

    private void getListTicketUARUPM(ListUpmTicketModel lutm, String ticketId, int pageNumMin1, Integer pageSize) {
        Page<TrxUARRequest> pageUAR = iTrxUARRequestRepository.findByTicketIdByAplikasiByYearByQuarterInStatuses(Arrays.asList(UPM_FILTER_TYPE_ALL), UPM_FILTER_TYPE_ALL, -1, UPM_FILTER_TYPE_ALL, ticketId, PageRequest.of(pageNumMin1, pageSize));
        List<UARModel> listUAR = mapper.mapToUARModelList(pageUAR);

        lutm.setTickets(listUAR);
        lutm.setTotalPages(pageUAR.getTotalPages());
        lutm.setTotalItems(pageUAR.getTotalElements());
    }

    private void getListTicketUserIDAndSetupParamterUPM(ListUpmTicketModel lutm, String status, String type, String ticketId, String sDateNow, String upmProcess, String searchFlag, List<String> nikList, String name, int pageNumMin1, Integer pageSize) {
        Page<UpmTicket> pageUT = null;
        if(UPM_STATUS_NEW.equals(status)){
            pageUT = getListTicketStatusNewUPM(type, sDateNow, ticketId, upmProcess, searchFlag, nikList, name, pageNumMin1, pageSize);
        }
        if(UPM_STATUS_PENDING.equals(status)){
            pageUT = getListTicketStatusPendingUPM(type, sDateNow, ticketId, upmProcess, searchFlag, nikList, name, pageNumMin1, pageSize);
        }
        if(UPM_STATUS_ALL.equals(status) || CURR_STATUS_APPROVED.equals(status) || UPM_STATUS_INPROGRESS.equals(status) || UPM_STATUS_VERIFICATION.equals(status) || UPM_STATUS_DONE.equals(status)){
            pageUT = getListTicketAllStatusUPM(type, status, sDateNow, ticketId, upmProcess, searchFlag, nikList, name, pageNumMin1, pageSize);
        }

        mapToListUpmTicketModel(lutm, pageUT, status);
    }

    private Page<UpmTicket> getListTicketStatusNewUPM(String type, String sDateNow, String ticketId, String upmProcess, String searchFlag, List<String> nikList, String name, int pageNumMin1, Integer pageSize) {
        Page<UpmTicket> pageUT = null;

        if(UPM_FILTER_TYPE_ALL.equals(type)) {
            pageUT = getListTicketUPM(pageNumMin1, pageSize, Constants.CURR_STATUS_APPROVED, sDateNow, ticketId, upmProcess, 0, searchFlag, nikList, name);
        } else if(UPM_FILTER_TYPE_USERID.equals(type)){
            pageUT = getListTicketFuidUPM(pageNumMin1, pageSize, Constants.CURR_STATUS_APPROVED, sDateNow, ticketId, upmProcess, 0, searchFlag, nikList, name);
        } else if(UPM_FILTER_TYPE_SETUPPARAM.equals(type)){
            pageUT = getListTicketSetupParamUPM(pageNumMin1, pageSize, Constants.CURR_STATUS_APPROVED, sDateNow, ticketId, upmProcess, 0, searchFlag, nikList, name);
        }

        return pageUT;
    }

    private Page<UpmTicket> getListTicketStatusPendingUPM(String type, String sDateNow, String ticketId, String upmProcess, String searchFlag, List<String> nikList, String name, int pageNumMin1, Integer pageSize) {
        Page<UpmTicket> pageUT = null;

        if(UPM_FILTER_TYPE_ALL.equals(type)) {
            pageUT = getListTicketUPM(pageNumMin1, pageSize, Constants.CURR_STATUS_APPROVED, sDateNow, ticketId, upmProcess, 1, searchFlag, nikList, name);
        } else if(UPM_FILTER_TYPE_USERID.equals(type)){
            pageUT = getListTicketFuidUPM(pageNumMin1, pageSize, Constants.CURR_STATUS_APPROVED, sDateNow, ticketId, upmProcess, 1, searchFlag, nikList, name);
        } else if(UPM_FILTER_TYPE_SETUPPARAM.equals(type)){
            pageUT = getListTicketSetupParamUPM(pageNumMin1, pageSize, Constants.CURR_STATUS_APPROVED, sDateNow, ticketId, upmProcess, 1, searchFlag, nikList, name);
        }

        return pageUT;
    }

    private Page<UpmTicket> getListTicketAllStatusUPM(String type, String status, String sDateNow, String ticketId, String upmProcess, String searchFlag, List<String> nikList, String name, int pageNumMin1, Integer pageSize) {
        Page<UpmTicket> pageUT = null;

        if(UPM_FILTER_TYPE_ALL.equals(type)) {
            pageUT = getListTicketUPM(pageNumMin1, pageSize, status, sDateNow, ticketId, upmProcess, 0, searchFlag, nikList, name);
        } else if(UPM_FILTER_TYPE_USERID.equals(type)){
            pageUT = getListTicketFuidUPM(pageNumMin1, pageSize, status, sDateNow, ticketId, upmProcess, 0, searchFlag, nikList, name);
        } else if(UPM_FILTER_TYPE_SETUPPARAM.equals(type)){
            pageUT = getListTicketSetupParamUPM(pageNumMin1, pageSize, status, sDateNow, ticketId, upmProcess, 0, searchFlag, nikList, name);
        }

        return pageUT;
    }

    private void mapToListUpmTicketModel(ListUpmTicketModel lutm, Page<UpmTicket> pageUT, String status) {
        List<UpmTicket> listUT = pageUT != null ? pageUT.getContent() : null;
        List<UpmTicketModel> listUTM = new ArrayList<>();
        Iterator<UpmTicket> iterator = listUT.iterator();
        while (iterator.hasNext()) {
            UpmTicket ut = iterator.next();
            UpmTicketModel utm = Mapper.toUpmTicketModel(ut);

            String[] splitAplikasi = utm.getAplikasi().split(",");
            List<String> listAplikasi = Arrays.asList(splitAplikasi);
            List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
            utm.setAplikasi(msta.toString().replaceAll("\\[|\\]", ""));

            String ecurrState = "";
            if (UPM_STATUS_ALL.equals(status)) {
                ecurrState = msSystemParamService.getMsSystemParamDetail(utm.getStatus()).getParamDetailDesc();
                utm.setStatusDesc(ecurrState);
            } else {
                ecurrState = msSystemParamService.getMsSystemParamDetail(status).getParamDetailDesc();
                utm.setStatus(status);
                utm.setStatusDesc(ecurrState);
            }

            if (utm.getPicProcess() != null) {
                TrxUpmRole trxUpmRole = trxUpmRoleService.getTrxUpmRole(utm.getPicProcess());
                utm.setPicProcess(trxUpmRole.getNama());
            }

            if (utm.getPicApprove() != null) {
                TrxUpmRole trxUpmRole = trxUpmRoleService.getTrxUpmRole(utm.getPicApprove());
                utm.setPicApprove(trxUpmRole.getNama());
            }

            listUTM.add(utm);
        }
        lutm.setTickets(listUTM);
        lutm.setTotalPages(pageUT.getTotalPages());
        lutm.setTotalItems(pageUT.getTotalElements());
    }
}
