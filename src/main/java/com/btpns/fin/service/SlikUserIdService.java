package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsSPK;
import com.btpns.fin.model.entity.MsSlik;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.ISlikUserIdRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service @Transactional
public class SlikUserIdService {
    private static final Logger logger = LoggerFactory.getLogger(DboRtgsUserIdService.class);

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    ISlikUserIdRepository slikUserIdRepository;

    @Autowired
    ITrxUserIdBatchRepository trxUserIdBatchRepository;

    @Autowired
    MinioService minioService;

    @Autowired
    ExcelHelper excelHelper;

    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResBatchUserId> saveBatchMsSlikUserId(ReqUserIdBatchModel<MsSlik> request, String nikRequester) throws Exception {
        List<MsSlik> msSlikList = slikUserIdRepository.findAll();
        if (!msSlikList.isEmpty() && msSlikList.size() > 0){
            if(deleteMsSlik() > 0){
                saveTrxUserIdBatch(request, nikRequester);
                saveBatchMsSlik(request.getData());
            }
        }else {
            saveTrxUserIdBatch(request, nikRequester);
            saveBatchMsSlik(request.getData());
        }

        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_SLIK, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_SLIK_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsSlik> slikList) {
        return slikList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveBatchMsSlik(List<MsSlik> msSlikList) {
        slikUserIdRepository.saveAll(msSlikList);
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<MsSlik> request, String nikRequester) {
        trxUserIdBatchRepository.save(Mapper.toTrxUserIdBatch(request.getBatchId(), request.getFileName(), request.getTotalData(), request.getType(), nikRequester, msEmployeeService.getEmployeeOrVendor(nikRequester)));
    }

    private int deleteMsSlik() {
        return slikUserIdRepository.deleteAllMsSlik();
    }

    public ResponseModel<ResponseListModel> getListMsSlik(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsSlik> msSlikPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if(SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag)){
                msSlikPageable = slikUserIdRepository.findAllByNikUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag)){
                msSlikPageable = slikUserIdRepository.findAllByNameUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            msSlikPageable = slikUserIdRepository.getListMsSlik(PageRequest.of(pageNumMin1, pageSize));
        }
        ResponseListModel<UserIDModel> responseListModel = Mapper.buildResUserIdListModel(pageSize, pageNumber, mapToUserIDListModel(msSlikPageable.getContent()), msSlikPageable.getTotalPages(), msSlikPageable.getTotalElements());

        return Mapper.buildResponseList(TYPE_MS_SLIK_MANAGEMENT_GET_LIST, SUCCESS, responseListModel);
    }

    private List<UserIDModel> mapToUserIDListModel(List<MsSlik> msSlik) {
        return msSlik.stream().map(MsSlik::toUserIDModel).collect(Collectors.toList());
    }

    public ResponseModel<UserIDModel> getMsSlikByNik(String nik) {
        ResponseModel<UserIDModel> response = new ResponseModel<>();

        MsSlik msSlik = slikUserIdRepository.findByNikUser(nik);
        if (msSlik != null){
            response = ResponseModel.<UserIDModel>builder().type(TYPE_MS_SLIK_MANAGEMENT_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(msSlik.toUserIDModel()).build();
        }

        return response;
    }

    public ResponseModel<ResCUDUserIdModel> saveMsSlikUserId(ReqUserIDModel request) {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        MsSlik msSlik = saveMsSlikData(request.toSlik());
        if (msSlik != null){
            response = Mapper.tobuildResponseCUDUserId(TYPE_MS_SLIK_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, request.getNik()));
        }

        return response;
    }

    private MsSlik saveMsSlikData(MsSlik msSlik) {
        return slikUserIdRepository.save(msSlik);
    }

    public ResponseModel<ResCUDUserIdModel> updateMsSlikUserId(ReqUserIDModel request) {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        MsSlik upadatedMsSlik = updateMsSlikData(request);
        if (upadatedMsSlik != null){
            response = Mapper.tobuildResponseCUDUserId(TYPE_MS_DBO_RTGS_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, request.getNik()));
        }

        return response;
    }

    private MsSlik updateMsSlikData(ReqUserIDModel request) {
        MsSlik foundSlik = slikUserIdRepository.findByNikUser(request.getNik());
        if (foundSlik != null){
            MsSlik newSlik = request.toSlik();
            newSlik.setId(foundSlik.getId());
            newSlik.setNik(foundSlik.getNik());

            foundSlik = slikUserIdRepository.save(newSlik);
        }

        return foundSlik;
    }

    public ResponseModel<ResCUDUserIdModel> deleteMsSlikUserId(String nik) {
        ResponseStatus status = FAILED;
        if (deleteMsSlikByNikUser(nik) > 0) {
            status = SUCCESS;
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_SLIK_MANAGEMENT_DELETE, status, Mapper.toResCUDUserIdModel(DELETE, nik));
    }

    private int deleteMsSlikByNikUser(String nik) {
        return slikUserIdRepository.deleteMsSlikByNik(nik);
    }

    public ResponseModel<ResUploadModel> generateExcelMsSlikUserId() throws Exception {
        List<MsSlik> slikList = slikUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(slikList), SLIK_USER_ID_FILE_NAME);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(SLIK_USER_ID_FILE_NAME, XLSX_EXTENSION), SLIK_USER_ID_FILE_NAME);

        return Mapper.buildResponse(TYPE_MS_SLIK_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsSlikUserId() throws Exception {
        List<MsSlik> slikList = slikUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(slikList), SLIK_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(SLIK_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
