package com.btpns.fin.service;

import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.ApplicationTypeDataModel;
import com.btpns.fin.model.entity.MsSystemParam;
import com.btpns.fin.model.entity.MsSystemParamDetail;
import com.btpns.fin.model.entity.MsUserIDApplication;
import com.btpns.fin.model.request.ReqApplicationTypeModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMsSystemParamDetailRepository;
import com.btpns.fin.repository.IMsSystemParamRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service @Transactional
public class MsSystemParamService {
    @Autowired
    IMsSystemParamRepository iMsSystemParamRepository;

    @Autowired
    IMsSystemParamDetailRepository iMsSystemParamDetailRepository;

    public MsSystemParam getMsSystemParam(String paramId){
        MsSystemParam msp = iMsSystemParamRepository.getMsSystemParam(paramId);
        return msp;
    }

    public MsSystemParamDetail getMsSystemParamDetail(String paramDetailId){
        MsSystemParamDetail mspd = iMsSystemParamDetailRepository.getMsSystemParamDetail(paramDetailId);
        return mspd;
    }

    public Map<String, MsSystemParamDetail> getMsSystemParamDetailMap(List<String> paramIds) {
        if (paramIds.size() > 0) {
            return iMsSystemParamDetailRepository.getMsSystemParamDetailByParamId(paramIds)
                    .stream()
                    .collect(Collectors.toMap(MsSystemParamDetail::getParamDetailId, Function.identity()));
        }
        return Collections.emptyMap();
    }

    public Map<String, MsSystemParamDetail> getMsSystemParamDetailWithParamDetailIdDescMap(List<String> paramIds) {
        if (paramIds.size() > 0) {
            return iMsSystemParamDetailRepository.getMsSystemParamDetailByParamId(paramIds)
                    .stream()
                    .collect(Collectors.toMap(MsSystemParamDetail::getParamDetailDesc, Function.identity()));
        }
        return Collections.emptyMap();
    }

    public ResponseModel<ResApplicationTypeDataListModel> getListSPCategory(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsSystemParamDetail> categorySPDataPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if (SEARCH_FLAG_PARAM_DETAIL_ID.equalsIgnoreCase(searchFlag)){
                categorySPDataPageable = iMsSystemParamDetailRepository.findAllByParamDetailIdPageable(KODE_KATEGORI_PARAM, searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_PARAM_DETAIL_DESC.equalsIgnoreCase(searchFlag)){
                categorySPDataPageable = iMsSystemParamDetailRepository.findAllByParamDetailDescPageable(KODE_KATEGORI_PARAM, searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            categorySPDataPageable = iMsSystemParamDetailRepository.findAllSPCategoryDataPageable(KODE_KATEGORI_PARAM, PageRequest.of(pageNumMin1, pageSize));
        }

        return buildResponse(TYPE_SP_CATEGORY_MANAGEMENT_GET_LIST, SUCCESS, pageSize, pageNumber, categorySPDataPageable.getContent(), categorySPDataPageable.getTotalPages(), categorySPDataPageable.getTotalElements());
    }

    public ResponseModel<ApplicationTypeDataModel> getSPCategoryByParamDetailId(String paramDetailId) {
        ResponseModel<ApplicationTypeDataModel> response = new ResponseModel<>();

        MsSystemParamDetail msSystemParamDetail = iMsSystemParamDetailRepository.findByParamDetailId(Arrays.asList(KODE_KATEGORI_PARAM), paramDetailId);
        if (msSystemParamDetail != null){
            response = buildResponse(TYPE_SP_CATEGORY_MANAGEMENT_GET, SUCCESS, mapToApplicationTypeDataModel(msSystemParamDetail));
        }

        return response;
    }

    public ResponseModel<ResApplicationTypeDataModel> updateSPCategory(ReqApplicationTypeModel request) {
        ResponseModel<ResApplicationTypeDataModel> response = new ResponseModel<>();

        MsSystemParamDetail updateSPCategoryData = updateSPCategoryData(request);
        if (updateSPCategoryData != null){
            response = buildResponseAddorEdit(TYPE_SP_CATEGORY_MANAGEMENT_ADD_EDIT, SUCCESS, buildResApplicationTypeDataModel(request.getParamDetailId(), request.getStatus(), EDIT));
        }

        return response;
    }

    private MsSystemParamDetail updateSPCategoryData(ReqApplicationTypeModel request) {
        MsSystemParamDetail msSystemParamDetail = iMsSystemParamDetailRepository.findByParamDetailId(Arrays.asList(KODE_KATEGORI_PARAM), request.getParamDetailId());

        if (msSystemParamDetail != null){
            msSystemParamDetail = iMsSystemParamDetailRepository.save(mapToMsSystemParamDetail(msSystemParamDetail, request));
        }

        return msSystemParamDetail;
    }

    private MsSystemParamDetail mapToMsSystemParamDetail(MsSystemParamDetail msSystemParamDetail, ReqApplicationTypeModel request) {
        MsSystemParamDetail updatedData = msSystemParamDetail;

        updatedData.setParamDetailId(request.getParamDetailId());
        updatedData.setParamDetailDesc(request.getParamDetailDesc());
        updatedData.setStatus(request.getStatus());

        return updatedData;
    }

    public ResponseModel<ResApplicationTypeDataModel> saveSPCategory(ReqApplicationTypeModel request) {
        ResponseModel<ResApplicationTypeDataModel> response = new ResponseModel<>();

        MsSystemParamDetail saveSPCategoryData = saveSPCategoryData(request);
        if (saveSPCategoryData != null){
            response = buildResponseAddorEdit(TYPE_SP_CATEGORY_MANAGEMENT_ADD_EDIT, SUCCESS, buildResApplicationTypeDataModel(request.getParamDetailId(), request.getStatus(), ADD));
        }

        return response;
    }

    private MsSystemParamDetail saveSPCategoryData(ReqApplicationTypeModel request) {
        return iMsSystemParamDetailRepository.save(mapToMsSystemParamDetail(request));
    }

    private ResponseModel<ResApplicationTypeDataListModel> buildResponse(String type, ResponseStatus status, Integer pageSize, Integer pageNumber, List<MsSystemParamDetail> msSystemParamDetailList, int totalPages, long totalItems) {
        ResponseModel<ResApplicationTypeDataListModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(buildResApplicationTypeDataListModel(pageSize, pageNumber, msSystemParamDetailList, totalPages, totalItems));

        return response;
    }

    private ResApplicationTypeDataListModel buildResApplicationTypeDataListModel(Integer pageSize, Integer pageNumber, List<MsSystemParamDetail> msSystemParamDetailList, int totalPages, long totalItems) {
        ResApplicationTypeDataListModel resApplicationTypeDataListModel = new ResApplicationTypeDataListModel();

        resApplicationTypeDataListModel.setApplicationTypeDetail(mapToApplicationTypeDataModelList(msSystemParamDetailList));
        resApplicationTypeDataListModel.setLimit(pageSize);
        resApplicationTypeDataListModel.setPage(pageNumber);
        resApplicationTypeDataListModel.setTotalPages(totalPages);
        resApplicationTypeDataListModel.setTotalItems(totalItems);

        return resApplicationTypeDataListModel;
    }

    private List<ApplicationTypeDataModel> mapToApplicationTypeDataModelList(List<MsSystemParamDetail> msSystemParamDetailList) {
        List<ApplicationTypeDataModel> applicationTypeDataModelList = new ArrayList<>();

        msSystemParamDetailList.forEach(data -> {
            applicationTypeDataModelList.add(mapToApplicationTypeDataModel(data));
        });

        return applicationTypeDataModelList;
    }

    private ApplicationTypeDataModel mapToApplicationTypeDataModel(MsSystemParamDetail msSystemParamDetail) {
        ApplicationTypeDataModel applicationTypeDataModel = new ApplicationTypeDataModel();

        applicationTypeDataModel.setParamId(msSystemParamDetail.getParamId());
        applicationTypeDataModel.setParamDetailId(msSystemParamDetail.getParamDetailId());
        applicationTypeDataModel.setParamDetailDesc(msSystemParamDetail.getParamDetailDesc());
        applicationTypeDataModel.setStatus(msSystemParamDetail.getStatus());

        return applicationTypeDataModel;
    }

    private ResponseModel<ApplicationTypeDataModel> buildResponse(String type, ResponseStatus status, ApplicationTypeDataModel applicationTypeDataModel) {
        ResponseModel<ApplicationTypeDataModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(applicationTypeDataModel);

        return response;
    }

    private ResponseModel<ResApplicationTypeDataModel> buildResponseAddorEdit(String type, ResponseStatus status, ResApplicationTypeDataModel resApplicationTypeDataModel) {
        ResponseModel<ResApplicationTypeDataModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resApplicationTypeDataModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    private ResApplicationTypeDataModel buildResApplicationTypeDataModel(String paramDetailId, String status, String type) {
        ResApplicationTypeDataModel resApplicationTypeDataModel = new ResApplicationTypeDataModel();

        resApplicationTypeDataModel.setType(type);
        resApplicationTypeDataModel.setParamDetailId(paramDetailId);
        resApplicationTypeDataModel.setStatus(status);

        return resApplicationTypeDataModel;
    }

    private MsSystemParamDetail mapToMsSystemParamDetail(ReqApplicationTypeModel request) {
        MsSystemParamDetail msSystemParamDetail = new MsSystemParamDetail();

        msSystemParamDetail.setParamId(request.getParamId());
        msSystemParamDetail.setStatus(request.getStatus());
        msSystemParamDetail.setParamDetailId(request.getParamDetailId());
        msSystemParamDetail.setParamDetailDesc(request.getParamDetailDesc());

        return msSystemParamDetail;
    }

    public List<MsSystemParamDetail> getAllSystemParamDetail() {
        return iMsSystemParamDetailRepository.findAll();
    }

    public Map<String, MsSystemParamDetail> getSystemParamDetailMap() {
        Map<String, MsSystemParamDetail> ret = new HashMap<>();
        List<MsSystemParamDetail> systemParamDetails = getAllSystemParamDetail();
        systemParamDetails.forEach(msSystemParamDetail -> {
            ret.put(msSystemParamDetail.getParamDetailId(), msSystemParamDetail);
        });

        return ret;
    }
}
