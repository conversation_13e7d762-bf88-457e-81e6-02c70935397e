package com.btpns.fin.service;

import com.btpns.fin.model.MsUserIDOwnershipModel;
import com.btpns.fin.model.entity.MsOfficerNROwnership;
import com.btpns.fin.model.entity.MsUserIDOwnership;
import com.btpns.fin.model.entity.MsUserIDOwnershipDetail;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsOfficerNROwnershipRepository;
import com.btpns.fin.repository.IUserIDOwnershipDetailRepository;
import com.btpns.fin.repository.IUserIDOwnershipRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class UserIDOwnershipService {
    @Autowired
    IUserIDOwnershipDetailRepository userIDOwnershipDetailRepository;
    @Autowired
    IUserIDOwnershipRepository userIDOwnershipRepository;

    @Autowired
    IMsOfficerNROwnershipRepository officerNROwnershipRepository;

    public ResponseModel<ResponseListModel<MsUserIDOwnershipModel>> getListUserIDOwnership(Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsUserIDOwnership> userIDOwnershipPageble = new PageImpl<>(new ArrayList<>());
        Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if(SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag)){
                userIDOwnershipPageble = userIDOwnershipRepository.findUserIDOwnershipByNIK(Character.isLetter(searchData.charAt(searchData.length() - 1)) ? searchData.substring(0, searchData.length()-1): searchData, pageable);
            }
            if (SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag)){
                userIDOwnershipPageble = userIDOwnershipRepository.findUserIDOwnershipByName(searchData, pageable);
            }
        }else {
            userIDOwnershipPageble = userIDOwnershipRepository.findAllUserIDOwnership(pageable);
        }

        List<MsUserIDOwnershipModel> data = userIDOwnershipPageble.getContent().stream().map(MsUserIDOwnership::toMsUserIDOwnershipModel).collect(Collectors.toList());

        ResponseListModel<MsUserIDOwnershipModel> responseDetails = ResponseListModel.<MsUserIDOwnershipModel>builder()
                .data(data)
                .page(pageNumber)
                .limit(pageSize)
                .totalPages(userIDOwnershipPageble.getTotalPages())
                .totalItems(userIDOwnershipPageble.getTotalElements())
                .build();

        return ResponseModel.<ResponseListModel<MsUserIDOwnershipModel>>builder().type(TYPE_MS_USERID_OWNERSHIP_MANAGEMENT_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    public ResponseModel<List<MsUserIDOwnershipDetail>> getDetailUserIDOwnership(String nikRequester) {
        List<MsUserIDOwnershipDetail> userIDOwnershipDetails = getMsUserIDOwnershipDetails(nikRequester);

        return ResponseModel.<List<MsUserIDOwnershipDetail>>builder().type(TYPE_MS_USERID_OWNERSHIP_GET_DETAIL).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(userIDOwnershipDetails).build();
    }

    private List<MsUserIDOwnershipDetail> getMsUserIDOwnershipDetails(String nik) {
        List<MsOfficerNROwnership> msOfficerNROwnerships = officerNROwnershipRepository.findOfficerNROwnershipByNIK(nik);
        List<MsUserIDOwnershipDetail> userIDOwnershipDetails = msOfficerNROwnerships.stream().map(MsOfficerNROwnership::toUserIDOwnershipDetail).collect(Collectors.toList());

        userIDOwnershipDetails.addAll(userIDOwnershipDetailRepository.findAllOwnedActiveUserID(nik));

        return userIDOwnershipDetails;
    }
}
