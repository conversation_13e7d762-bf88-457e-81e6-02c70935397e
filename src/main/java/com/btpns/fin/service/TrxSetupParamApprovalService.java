package com.btpns.fin.service;

import com.btpns.fin.model.entity.TrxSetupParamApproval;
import com.btpns.fin.repository.ITrxSetupParamApprovalRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

@Service
public class TrxSetupParamApprovalService {
    @Autowired
    ITrxSetupParamApprovalRepository trxSetupParamApprovalRepository;

    @Transactional
    public TrxSetupParamApproval saveTrxSetupParamApproval(TrxSetupParamApproval trxSetupParamApproval){
        return trxSetupParamApprovalRepository.save(trxSetupParamApproval);
    }

    @Transactional
    public TrxSetupParamApproval updateTrxSetupParamApproval(TrxSetupParamApproval trxSetupParamApproval) {
        Optional<TrxSetupParamApproval> existTrxSetupParamApproval = trxSetupParamApprovalRepository.getTrxSetupParamApprovalByTicketId(trxSetupParamApproval.getTicketId());
        if (existTrxSetupParamApproval.isPresent()) {
            /*trxSetupParamApproval.setCurrentState(existTrxSetupParamApproval.get().getCurrentState());
            trxSetupParamApproval.setCurrentStateDT(existTrxSetupParamApproval.get().getCurrentStateDT());*/
            trxSetupParamApprovalRepository.deleteById(existTrxSetupParamApproval.get().getId());
        }
        return trxSetupParamApprovalRepository.save(trxSetupParamApproval);
    }

    public TrxSetupParamApproval getTrxSetupParamApprovalByTicketId(String ticketId) {
        Optional<TrxSetupParamApproval> existTspa = trxSetupParamApprovalRepository.getTrxSetupParamApprovalByTicketId(ticketId);
        TrxSetupParamApproval tspa = new TrxSetupParamApproval();
        if (existTspa.isPresent()) {
            tspa.setId(existTspa.get().getId());
            tspa.setTicketId(existTspa.get().getTicketId());
            tspa.setCurrentState(existTspa.get().getCurrentState());
            tspa.setCurrentStateDT(existTspa.get().getCurrentStateDT());
            String upmInputAttachment = "[]";
            if(existTspa.get().getUpmInputAttachment() != null){
                upmInputAttachment = existTspa.get().getUpmInputAttachment();
            }
            tspa.setUpmInputAttachment(upmInputAttachment);
            if(existTspa.get().getPuk1NIK() != null){
                tspa.setPuk1NIK(existTspa.get().getPuk1NIK());
                tspa.setPuk1Name(existTspa.get().getPuk1Name());
                tspa.setPuk1Occupation(existTspa.get().getPuk1Occupation());
                tspa.setPuk1Notes(existTspa.get().getPuk1Notes());
                tspa.setPuk1Dt(existTspa.get().getPuk1Dt());
                tspa.setPuk1Status(existTspa.get().getPuk1Status());
                tspa.setPuk1DelegationId(existTspa.get().getPuk1DelegationId());
            }
            if(existTspa.get().getPuk2NIK() != null){
                tspa.setPuk2NIK(existTspa.get().getPuk2NIK());
                tspa.setPuk2Name(existTspa.get().getPuk2Name());
                tspa.setPuk2Occupation(existTspa.get().getPuk2Occupation());
                tspa.setPuk2Notes(existTspa.get().getPuk2Notes());
                tspa.setPuk2Dt(existTspa.get().getPuk2Dt());
                tspa.setPuk2Status(existTspa.get().getPuk2Status());
                tspa.setPuk2DelegationId(existTspa.get().getPuk2DelegationId());
            }
            if(existTspa.get().getUpmInputNIK() != null){
                tspa.setUpmInputNIK(existTspa.get().getUpmInputNIK());
                tspa.setUpmInputNotes(existTspa.get().getUpmInputNotes());
                tspa.setUpmInputDt(existTspa.get().getUpmInputDt());
                tspa.setUpmInputStatus(existTspa.get().getUpmInputStatus());
            }
            if(existTspa.get().getUpmCheckerNIK() != null){
                tspa.setUpmCheckerNIK(existTspa.get().getUpmCheckerNIK());
                tspa.setUpmCheckerNotes(existTspa.get().getUpmCheckerNotes());
                tspa.setUpmCheckerDt(existTspa.get().getUpmCheckerDt());
                tspa.setUpmCheckerStatus(existTspa.get().getUpmCheckerStatus());
            }
        }
        return tspa;
    }

    @Transactional
    public int updateTrxSetupParamApprovalPUK1ByTicketId(String ticketId, String puk1Nik, String puk1DelegationId){
        return trxSetupParamApprovalRepository.updateTrxSetupParamApprovalPUK1ByTicketId(ticketId, puk1Nik, puk1DelegationId);
    }

    @Transactional
    public int updateTrxSetupParamApprovalPUK2ByTicketId(String ticketId, String puk2Nik, String puk2DelegationId){
        return trxSetupParamApprovalRepository.updateTrxSetupParamApprovalPUK2ByTicketId(ticketId, puk2Nik, puk2DelegationId);
    }
}
