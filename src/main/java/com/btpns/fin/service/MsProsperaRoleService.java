package com.btpns.fin.service;

import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.ProsperaRoleModel;
import com.btpns.fin.model.entity.MsProsperaRole;
import com.btpns.fin.model.response.ResRolesProspera;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsProsperaRoleRepository;
import com.btpns.fin.repository.ProsperaRepository;
import java.util.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class MsProsperaRoleService {
    @Autowired
    private IMsProsperaRoleRepository prosperaRoleRepository;

    @Autowired
    private ProsperaRepository prosperaRepository;

    @Autowired
    private MsSystemParamService systemParamService;

    public ResponseModel<ResponseListModel<ProsperaRoleModel>> getListProsperaRole(Integer pageNumber, Integer pageSize) {
        Page<MsProsperaRole> temaProsperaRoles = prosperaRoleRepository.findAll(PageRequest.of(pageNumber - 1, pageSize));
        List<ProsperaRoleModel> prosperaRoleModels = temaProsperaRoles.getContent().stream().map(MsProsperaRole::toProsperaRoleModel).collect(Collectors.toList());
        ResponseListModel<ProsperaRoleModel> responseDetail = ResponseListModel.<ProsperaRoleModel>builder()
                .data(prosperaRoleModels)
                .page(pageNumber)
                .limit(pageSize)
                .totalItems(temaProsperaRoles.getTotalElements())
                .totalPages(temaProsperaRoles.getTotalPages())
                .build();

        return ResponseModel.<ResponseListModel<ProsperaRoleModel>>builder().type(TYPE_PROSPERA_ROLE_MANAGEMENT_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetail).build();
    }

    public ResponseModel<ProsperaRoleModel> getDetailProsperaRole(String temaRoleCode) {
        MsProsperaRole prosperaRole = prosperaRoleRepository.findAllByTemaRoleCode(temaRoleCode);

        if (prosperaRole == null) {
            return buildResponse(TYPE_PROSPERA_ROLE_MANAGEMENT_GET, NOT_FOUND, null);
        }

        return buildResponse(TYPE_PROSPERA_ROLE_MANAGEMENT_GET, SUCCESS, prosperaRole.toProsperaRoleModel());
    }

    public ResponseModel<ProsperaRoleModel> addOrEditProsperaRole(String authorization, String requestType, ProsperaRoleModel requestedProsperaRole) {
        MsProsperaRole existingProsperaRole = prosperaRoleRepository.findByProsperaRoleCode(requestedProsperaRole.getProsperaRoleCode());
        if (ADD.equalsIgnoreCase(requestType) && existingProsperaRole != null) {
            return buildResponse(TYPE_PROSPERA_ROLE_MANAGEMENT_ADD_EDIT, ALREADY_EXIST, requestedProsperaRole);
        }
        if (EDIT.equalsIgnoreCase(requestType) && existingProsperaRole == null) {
            return buildResponse(TYPE_PROSPERA_ROLE_MANAGEMENT_ADD_EDIT, NOT_FOUND, requestedProsperaRole);
        }

        Map<Integer, String> prosperaRoleMap = getProsperaRoleMapFromProspera(authorization);
        if (prosperaRoleMap.isEmpty()) {
            return buildResponse(TYPE_PROSPERA_ROLE_MANAGEMENT_ADD_EDIT, FAILED_GET_DATA_FROM_PROSPERA, requestedProsperaRole);
        }

        if (!isValidProsperaRoleCodeOrName(requestedProsperaRole, prosperaRoleMap)) {
            return buildResponse(TYPE_PROSPERA_ROLE_MANAGEMENT_ADD_EDIT, FAILED_SET_PROSPERA_ROLE_ATTRIBUTES, requestedProsperaRole);
        }

        MsProsperaRole savedProsperaRole = processProsperaRoleRequest(requestType, requestedProsperaRole, existingProsperaRole);
        return buildResponse(TYPE_PROSPERA_ROLE_MANAGEMENT_ADD_EDIT, SUCCESS, savedProsperaRole.toProsperaRoleModel());
    }

    private MsProsperaRole saveProsperaRole(MsProsperaRole prosperaRole) {
        return prosperaRoleRepository.save(prosperaRole);
    }

    public MsProsperaRole processProsperaRoleRequest(String requestType, ProsperaRoleModel prosperaRoleModel, MsProsperaRole prosperaRole) {
        if (EDIT.equalsIgnoreCase(requestType)) {
            return updateDataProsperaRole(prosperaRoleModel, prosperaRole);
        } else {
            return saveProsperaRole(prosperaRoleModel.toMsProsperaRole());
        }
    }

    private static ResponseModel<ProsperaRoleModel> buildResponse(String type, ResponseStatus responseStatus, ProsperaRoleModel prosperaRoleModel) {
        return ResponseModel.<ProsperaRoleModel>builder().type(type).status(responseStatus.getCode()).statusDesc(responseStatus.getValue()).details(prosperaRoleModel).build();
    }

    private MsProsperaRole updateDataProsperaRole(ProsperaRoleModel prosperaRoleModel, MsProsperaRole prosperaRole) {
        prosperaRole.setRoleDesc(prosperaRoleModel.getRoleName());
        prosperaRole.setActive(prosperaRoleModel.isActive());
        prosperaRole.setSystemParamId(prosperaRoleModel.getOfficeLevel());

        return saveProsperaRole(prosperaRole);
    }

    private static boolean isValidProsperaRoleCodeOrName(ProsperaRoleModel prosperaRoleModel, Map<Integer, String> prosperaRoleMap) {
        return prosperaRoleMap.containsKey(prosperaRoleModel.getProsperaRoleCode())
                && prosperaRoleModel.getRoleName().equalsIgnoreCase(prosperaRoleMap.get(prosperaRoleModel.getProsperaRoleCode()));
    }

    private Map<Integer, String> getProsperaRoleMapFromProspera(String authorization) {
        List<ResRolesProspera> prosperaRoles = prosperaRepository.getListRoleProspera(authorization);
        if (prosperaRoles != null) {
            return prosperaRoles.stream().collect(Collectors.toMap(ResRolesProspera::getId, ResRolesProspera::getName));
        }
        return Collections.emptyMap();
    }
}
