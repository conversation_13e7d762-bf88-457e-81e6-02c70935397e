package com.btpns.fin.service;

import com.btpns.fin.model.StatusModel;
import com.btpns.fin.model.entity.TrxFuidApproval;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.ITrxFuidApprovalRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class FilterService {
    @Autowired
    ITrxFuidApprovalRepository iTrxFuidApprovalRepository;

    public ResponseModel<List<StatusModel>> getFilterStatus(String nikRequester, Integer isUser) {
        List<StatusModel> statusModelList = new ArrayList<>();
        if (isUser == 0){
            List<TrxFuidApproval> getPendingPUK2 = iTrxFuidApprovalRepository.getPendingPUK2ByNik(nikRequester);
            if (getPendingPUK2.size() > 0){
                statusModelList.add(new StatusModel(CURR_STATUS_WAITING_PUK2, CURR_STATUS_WAITING_PUK2_DESC));
            }
            statusModelList.add(new StatusModel(CURR_STATUS_APPROVED, CURR_STATUS_APPROVED_DESC));
            statusModelList.add(new StatusModel(CURR_STATUS_REJECTED, CURR_STATUS_REJECTED_DESC));
            statusModelList.add(new StatusModel(UPM_STATUS_INPROGRESS, CURR_STATUS_INPROGRESS_DESC));
            statusModelList.add(new StatusModel(UPM_STATUS_VERIFICATION, CURR_STATUS_VERIFICATION_DESC));
            statusModelList.add(new StatusModel(UPM_STATUS_DONE, CURR_STATUS_DONE_DESC));
        }else {
            statusModelList.add(new StatusModel(CURR_STATUS_APPROVED, CURR_STATUS_APPROVED_DESC));
            statusModelList.add(new StatusModel(CURR_STATUS_REJECTED, CURR_STATUS_REJECTED_DESC));
            statusModelList.add(new StatusModel(CURR_STATUS_WAITING_PUK1, CURR_STATUS_WAITING_PUK1_DESC));
            statusModelList.add(new StatusModel(CURR_STATUS_WAITING_PUK2, CURR_STATUS_WAITING_PUK2_DESC));
            statusModelList.add(new StatusModel(UPM_STATUS_INPROGRESS, CURR_STATUS_INPROGRESS_DESC));
            statusModelList.add(new StatusModel(UPM_STATUS_VERIFICATION, CURR_STATUS_VERIFICATION_DESC));
            statusModelList.add(new StatusModel(UPM_STATUS_DONE, CURR_STATUS_DONE_DESC));
        }

        return ResponseModel.<List<StatusModel>>builder().type(TYPE_FILTER_STATUS_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(statusModelList).build();
    }
}
