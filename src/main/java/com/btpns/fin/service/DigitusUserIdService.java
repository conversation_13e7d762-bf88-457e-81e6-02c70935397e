package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.MsDigitusModel;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsDboRTGS;
import com.btpns.fin.model.entity.MsDigitus;
import com.btpns.fin.model.request.ReqMsDigitus;
import com.btpns.fin.model.request.ReqMsDigitusModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IDigitusUserIdRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service @Transactional
public class DigitusUserIdService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    ITrxUserIdBatchRepository trxUserIdBatchRepository;

    @Autowired
    IDigitusUserIdRepository digitusUserIdRepository;

    @Autowired
    MinioService minioService;

    @Autowired
    ExcelHelper excelHelper;

    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResBatchUserId> saveBatchMsDigitusUserId(ReqUserIdBatchModel<ReqMsDigitus> request, String nikRequester) throws Exception {
        List<MsDigitus> msDigitusList = digitusUserIdRepository.findAll();
        if (!msDigitusList.isEmpty() && msDigitusList.size() > 0){
            if(deleteAllMsDigitus() > 0){
                saveTrxUserIdBatch(request, nikRequester);
                saveBatchMsDigitus(request.getData());
            }
        }else {
            saveTrxUserIdBatch(request, nikRequester);
            saveBatchMsDigitus(request.getData());
        }
        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_DIGITUS, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_DIGITUS_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<ReqMsDigitus> msDigitusList) {
        return msDigitusList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveBatchMsDigitus(List<ReqMsDigitus> reqMsDigitusList) throws Exception {
        List<MsDigitus> digitusList = new ArrayList<>();
        for (ReqMsDigitus msDigitus : reqMsDigitusList) {
            digitusList.add(mapToMsDigitus(msDigitus));
        }

        digitusUserIdRepository.saveAll(digitusList);
    }

    private MsDigitus mapToMsDigitus(ReqMsDigitus request) throws Exception {
        MsDigitus msDigitus = new MsDigitus();

        msDigitus.setNik(request.getNik());
        msDigitus.setNamaUser(request.getNamaUser());
        msDigitus.setKewenangan(request.getKewenangan());
        msDigitus.setJabatan(request.getJabatan());

        return msDigitus;
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<ReqMsDigitus> request, String nikRequester) {
        trxUserIdBatchRepository.save(Mapper.toTrxUserIdBatch(request.getBatchId(), request.getFileName(), request.getTotalData(), request.getType(), nikRequester, msEmployeeService.getEmployeeOrVendor(nikRequester)));
    }

    private int deleteAllMsDigitus() {
        return digitusUserIdRepository.deleteAllMsDigitus();
    }

    public ResponseModel<ResponseListModel> getListMsDigitus(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsDigitus> msDigitusPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if(SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag)){
                msDigitusPageable = digitusUserIdRepository.findAllByNikUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag)){
                msDigitusPageable = digitusUserIdRepository.findAllByNameUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            msDigitusPageable = digitusUserIdRepository.getListMsDigitus(PageRequest.of(pageNumMin1, pageSize));
        }

        return Mapper.buildResponseList(TYPE_MS_DIGITUS_MANAGEMENT_GET_LIST, SUCCESS, buildResUserIdListModel(pageSize, pageNumber, msDigitusPageable.getContent(), msDigitusPageable.getTotalPages(), msDigitusPageable.getTotalElements()));
    }

    private ResponseListModel buildResUserIdListModel(Integer pageSize, Integer pageNumber, List<MsDigitus> msDigitus, int totalPages, long totalItems) {
        ResponseListModel responseListModel = new ResponseListModel();

        responseListModel.setData(mapToMsDigitusListModel(msDigitus));
        responseListModel.setLimit(pageSize);
        responseListModel.setPage(pageNumber);
        responseListModel.setTotalPages(totalPages);
        responseListModel.setTotalItems(totalItems);

        return responseListModel;
    }

    private List<MsDigitusModel> mapToMsDigitusListModel(List<MsDigitus> msDigitus) {
        List<MsDigitusModel> msDigitusModelList = new ArrayList<>();

        msDigitus.forEach(data -> {
            msDigitusModelList.add(mapToMsDigitusModel(data));
        });

        return msDigitusModelList;
    }

    public ResponseModel<MsDigitusModel> getMsDigitusByNik(String nik) {
        ResponseModel<MsDigitusModel> response = new ResponseModel<>();

        MsDigitus msDigitus = digitusUserIdRepository.findByNikUser(nik);
        if (msDigitus != null){
            response = buildResponse(TYPE_MS_DIGITUS_MANAGEMENT_GET, SUCCESS, mapToMsDigitusModel(msDigitus));
        }

        return response;
    }

    private MsDigitusModel mapToMsDigitusModel(MsDigitus msDigitus) {
        MsDigitusModel msDigitusModel = new MsDigitusModel();

        msDigitusModel.setNik(msDigitus.getNik());
        msDigitusModel.setNamaUser(msDigitus.getNamaUser());
        msDigitusModel.setKewenangan(msDigitus.getKewenangan());
        msDigitusModel.setJabatan(msDigitus.getJabatan());

        return msDigitusModel;
    }

    private ResponseModel<MsDigitusModel> buildResponse(String type, ResponseStatus status, MsDigitusModel msDigitusModel) {
        ResponseModel<MsDigitusModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(msDigitusModel);

        return response;
    }

    public ResponseModel<ResCUDUserIdModel> updateMsDigitusUserId(ReqMsDigitusModel request) throws Exception {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        MsDigitus upadatedMsDigitus = updateMsDigitusData(request);
        if (upadatedMsDigitus != null){
            response = Mapper.tobuildResponseCUDUserId(TYPE_MS_DIGITUS_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, request.getNik()));
        }

        return response;
    }

    private MsDigitus updateMsDigitusData(ReqMsDigitusModel request) {
        MsDigitus msDigitus = digitusUserIdRepository.findByNikUser(request.getNik());
        if (msDigitus != null){
            msDigitus = digitusUserIdRepository.save(mapNewMsDigitusData(msDigitus, request));
        }

        return msDigitus;
    }

    private MsDigitus mapNewMsDigitusData(MsDigitus savedMsDigitus, ReqMsDigitusModel request) {
        MsDigitus msDigitus = savedMsDigitus;

        msDigitus.setNamaUser(request.getNamaUser().isEmpty() ? savedMsDigitus.getNamaUser() : request.getNamaUser());
        msDigitus.setKewenangan(request.getKewenangan().isEmpty() ? savedMsDigitus.getKewenangan() : request.getKewenangan());
        msDigitus.setJabatan(request.getJabatan().isEmpty() ? savedMsDigitus.getJabatan() : request.getJabatan());

        return msDigitus;
    }

    public ResponseModel<ResCUDUserIdModel> saveMsDigitusUserId(ReqMsDigitusModel request) {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        MsDigitus msDigitus = saveMsDigitusData(mapToMsDigitus(request));
        if (msDigitus != null){
            response = Mapper.tobuildResponseCUDUserId(TYPE_MS_DIGITUS_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, request.getNik()));
        }

        return response;
    }

    private MsDigitus mapToMsDigitus(ReqMsDigitusModel request) {
        MsDigitus msDigitus = new MsDigitus();

        msDigitus.setNik(request.getNik());
        msDigitus.setNamaUser(request.getNamaUser());
        msDigitus.setKewenangan(request.getKewenangan());
        msDigitus.setJabatan(request.getJabatan());

        return msDigitus;
    }

    private MsDigitus saveMsDigitusData(MsDigitus msDigitus) {
        return digitusUserIdRepository.save(msDigitus);
    }

    public ResponseModel<ResCUDUserIdModel> deleteMsDigitusUserId(String nik) {ResponseStatus status = FAILED;
        if (deleteMsDigitusByNikUser(nik) > 0) {
            status = SUCCESS;
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_DIGITUS_MANAGEMENT_DELETE, status, Mapper.toResCUDUserIdModel(DELETE, nik));
    }

    private int deleteMsDigitusByNikUser(String nik) {
        return digitusUserIdRepository.deleteMsDigitusByNik(nik);
    }

    public ResponseModel<ResUploadModel> genereteExcelMsDigitusUserId() throws Exception {
        List<MsDigitus> msDigitusList = digitusUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelDigitus(msDigitusList, DIGITUS_USER_ID_FILE_NAME);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(DIGITUS_USER_ID_FILE_NAME, XLSX_EXTENSION), DIGITUS_USER_ID_FILE_NAME);

        return Mapper.buildResponse(TYPE_MS_DIGITUS_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsDigitusUserId() throws Exception {
        List<MsDigitus> msDigitusList = digitusUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelDigitus(msDigitusList, DIGITUS_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(DIGITUS_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
