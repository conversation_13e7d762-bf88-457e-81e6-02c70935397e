package com.btpns.fin.service;

import com.btpns.fin.model.entity.MsHolidayList;
import com.btpns.fin.repository.IMsHolidayListRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MsHolidayListService {

    @Autowired
    IMsHolidayListRepository iMsHolidayList;

    public List<MsHolidayList> getHolidayList(String period) {
        return iMsHolidayList.getMsHolidayList(period);
    }

    public Map<String, MsHolidayList> getHolidayMap(String period) {
        return getHolidayList(period)
                .stream()
                .collect(Collectors.toMap(MsHolidayList::getHolidayDate, Function.identity()));
    }

    public Page<MsHolidayList> getHolidayListAll(int pageNumMin1, Integer pageSize, String period) {
        return iMsHolidayList.getMsHolidayListAll(period, PageRequest.of(pageNumMin1, pageSize, Sort.by("holidayDate").ascending()));
    }

    //insert
    @Transactional
    public MsHolidayList saveMsHolidayList(MsHolidayList msHolidayList) {
        LocalDateTime dateTimeNow = LocalDateTime.now();
        msHolidayList.setCreateDatetime(dateTimeNow);
        msHolidayList.setUpdateDatetime(dateTimeNow);
        return iMsHolidayList.save(msHolidayList);
    }

    //update
    @Transactional
    public MsHolidayList updateMsHolidayList(MsHolidayList msHolidayList) {
        Optional<MsHolidayList> existMsHolidayList = iMsHolidayList.findById(msHolidayList.getId());
        if(existMsHolidayList.isPresent()){
            msHolidayList.setCreateDatetime(existMsHolidayList.get().getCreateDatetime());
        }
        LocalDateTime dateTimeNow = LocalDateTime.now();
        msHolidayList.setUpdateDatetime(dateTimeNow);
        return iMsHolidayList.save(msHolidayList);
    }

    public MsHolidayList getMsHolidayById(String id) {
        return iMsHolidayList.getMsHolidayById(id);
    }

    //delete
    @Transactional
    public int deleteMsHolidayById(String id) {
        return iMsHolidayList.deleteMsHolidayById(id);
    }
}
