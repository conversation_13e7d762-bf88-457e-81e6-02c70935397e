package com.btpns.fin.service;

import com.btpns.fin.controller.MsFAQTemaController;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.MsFAQTemaModel;
import com.btpns.fin.model.request.ReqMsFAQTemaModel;
import com.btpns.fin.model.entity.MsFAQTema;
import com.btpns.fin.model.response.ResMsFAQTemaModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsFAQTemaRepository;
import io.micrometer.core.instrument.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.CommonHelper.getHttpStatusDetail;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.responseBadRequest;
import static com.btpns.fin.helper.ResponseHelper.responseFailed;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
@Transactional
public class MsFAQTemaService {
    private static final Logger logger = LoggerFactory.getLogger(MsFAQTemaService.class);
    @Autowired
    IMsFAQTemaRepository iMsFAQTemaRepository;

    public ResponseModel<ResponseListModel<MsFAQTemaModel>> getListMsFAQTema(Integer pageSize, Integer pageNumber) {
        Page<MsFAQTema> msFAQTemaPageable = iMsFAQTemaRepository.getListMsFAQTemaPageable(PageRequest.of(pageNumber - 1, pageSize));
        List<MsFAQTemaModel> msFAQTemaModelList = msFAQTemaPageable.getContent().stream().map(MsFAQTema::toMsFAQTemaModel).collect(Collectors.toList());
        ResponseListModel<MsFAQTemaModel> details = ResponseListModel.<MsFAQTemaModel>builder()
                .data(msFAQTemaModelList)
                .page(pageNumber).limit(pageSize)
                .totalPages(msFAQTemaPageable.getTotalPages())
                .totalItems(msFAQTemaPageable.getTotalElements())
                .build();
        return ResponseModel.<ResponseListModel<MsFAQTemaModel>>builder().type(TYPE_MS_FAQ_MANAGEMENT_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(details).build();
    }

    public ResponseModel<MsFAQTemaModel> getMsFAQTemaByFaqId(String faqId) {
        ResponseModel<MsFAQTemaModel> response = new ResponseModel<>();
        if(StringUtils.isNotBlank(faqId)) {
            MsFAQTema msFAQTema = iMsFAQTemaRepository.findMsFAQTemaByFaqId(faqId);
            if (msFAQTema != null){
                response = ResponseModel.<MsFAQTemaModel>builder().type(TYPE_MS_FAQ_MANAGEMENT_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(msFAQTema.toMsFAQTemaModel()).build();
            }
        }
        return response;
    }

    public ResponseModel<ResMsFAQTemaModel> addOrEditMsFAQTema(ReqMsFAQTemaModel request, String nikRequester) {
        ResponseModel<ResMsFAQTemaModel> response = new ResponseModel<>();
        if (iMsFAQTemaRepository.findMsFAQTemaByFaqId(request.getFaqId()) != null && EDIT.equalsIgnoreCase(request.getType())){
            response = updateMsFAQTema(request, nikRequester);
        }else {
            response = saveMsFAQTema(request, nikRequester);
        }
        return response;
    }

    public ResponseModel<ResMsFAQTemaModel> saveMsFAQTema(ReqMsFAQTemaModel request, String nikRequester) {
        ResponseModel<ResMsFAQTemaModel> response = new ResponseModel<>();
        String faqId = generateFaqId();
        Integer contentOrder = iMsFAQTemaRepository.getLastContentOrder();
        MsFAQTema msFAQTema = iMsFAQTemaRepository.save(mapToMsFAQTema(request, faqId, contentOrder, nikRequester));
        if (msFAQTema != null){
            response = buildResponseModel(TYPE_MS_FAQ_MANAGEMENT_ADD_EDIT, SUCCESS, mapToResMsFAQTemaModel(ADD, faqId));
        }
        return response;
    }

    private String generateFaqId() {
        String faqId = "";

        String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());
        String lastFaqId = iMsFAQTemaRepository.getLastFaqId();

        if (lastFaqId == null) {
            faqId = "FAQ" + currDtTicket + "0001";
        } else {
            String strlastDate = lastFaqId.substring(3, 9);
            if (strlastDate.equals(currDtTicket)) {
                faqId = "FAQ" + currDtTicket + String.format("%04d", Integer.parseInt(lastFaqId.substring(9, 13)) + 1);
            } else {
                faqId = "FAQ" + currDtTicket + "0001";
            }
        }
        return faqId;
    }

    private MsFAQTema mapToMsFAQTema(ReqMsFAQTemaModel request, String lastFaqId, Integer contentOrder, String nikRequester) {
        MsFAQTema msFAQTema = new MsFAQTema();
        msFAQTema.setFaqId(lastFaqId);
        msFAQTema.setContentOrder(contentOrder);
        msFAQTema.setContentTitle(request.getContentTitle());
        msFAQTema.setContentDesc(request.getContentDesc());
        msFAQTema.setVisible(request.getVisible());
        msFAQTema.setCreatedAt(LocalDateTime.now());
        msFAQTema.setUpdatedAt(LocalDateTime.now());
        msFAQTema.setCreatedBy(nikRequester);
        return msFAQTema;
    }

    private ResMsFAQTemaModel mapToResMsFAQTemaModel(String type, String faqId) {
        ResMsFAQTemaModel result = new ResMsFAQTemaModel();
        result.setType(type);
        result.setFaqId(faqId);
        return result;
    }

    private ResponseModel<ResMsFAQTemaModel> buildResponseModel(String type, ResponseStatus status, ResMsFAQTemaModel resMsFAQTemaModel) {
        ResponseModel<ResMsFAQTemaModel> response = new ResponseModel<>();
        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(resMsFAQTemaModel);
        return response;
    }

    public ResponseModel<ResMsFAQTemaModel> updateMsFAQTema(ReqMsFAQTemaModel request, String nikRequester) {
        ResponseModel<ResMsFAQTemaModel> response = new ResponseModel<>();
        MsFAQTema updatedMsFaqTema = updateMsFaqTema(request, nikRequester);
        if (updatedMsFaqTema != null){
            response = buildResponseModel(TYPE_MS_FAQ_MANAGEMENT_ADD_EDIT, SUCCESS, mapToResMsFAQTemaModel(EDIT, request.getFaqId()));
        }
        return response;
    }

    private MsFAQTema updateMsFaqTema(ReqMsFAQTemaModel request, String nikRequester) {
        MsFAQTema existingMsFaqTema = iMsFAQTemaRepository.findMsFAQTemaByFaqId(request.getFaqId());
        if (existingMsFaqTema != null){
            iMsFAQTemaRepository.save(mapToMsFAQTemaModel(existingMsFaqTema, request, nikRequester));
        }
        return existingMsFaqTema;
    }

    private MsFAQTema mapToMsFAQTemaModel(MsFAQTema existingMsFaqTema, ReqMsFAQTemaModel request, String nikRequester) {
        MsFAQTema msFAQTema = existingMsFaqTema;
        msFAQTema.setContentTitle(!request.getContentTitle().isEmpty() ? request.getContentTitle() : existingMsFaqTema.getContentTitle());
        msFAQTema.setContentDesc(!request.getContentDesc().isEmpty() ? request.getContentDesc() : existingMsFaqTema.getContentDesc());
        msFAQTema.setContentOrder(request.getContentOrder() != null ? request.getContentOrder() : existingMsFaqTema.getContentOrder());
        msFAQTema.setVisible(request.getVisible() != null ? request.getVisible() : existingMsFaqTema.getVisible());
        msFAQTema.setUpdatedAt(LocalDateTime.now());
        msFAQTema.setCreatedBy(nikRequester);
        return msFAQTema;
    }

    public ResponseModel<ResMsFAQTemaModel> deleteMsFAQTema(String faqId) {
        ResponseModel<ResMsFAQTemaModel> response = new ResponseModel<>();
        if(StringUtils.isNotBlank(faqId) && iMsFAQTemaRepository.findMsFAQTemaByFaqId(faqId) != null) {
            ResponseStatus status = FAILED;
            if (deleteMsFAQTemaByFaqId(faqId) > 0) {
                status = SUCCESS;
            }
            response = buildResponseModel(TYPE_MS_FAQ_MANAGEMENT_DELETE, status, mapToResMsFAQTemaModel(DELETE, faqId));
        }
        return response;
    }

    private int deleteMsFAQTemaByFaqId(String faqId) {
        return iMsFAQTemaRepository.deleteMsFAQTemaByFaqId(faqId);
    }
}
