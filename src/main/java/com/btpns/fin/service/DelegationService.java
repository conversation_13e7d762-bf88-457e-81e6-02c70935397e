package com.btpns.fin.service;

import com.btpns.fin.model.ContentEmail;
import com.btpns.fin.model.entity.MsEmployee;
import com.btpns.fin.model.entity.TrxFuidRequest;
import com.btpns.fin.model.entity.TrxSetupParamRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

import static com.btpns.fin.helper.Constants.*;

@Service
public class DelegationService {
    @Autowired
    UpmTicketService upmTicketService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    EmailNotificationService emailNotificationService;

    public void sendEmailtoDelegatedFU(String pukNik, TrxFuidRequest savedTFR) {
        ContentEmail contentEmail = upmTicketService.buildFuidContentEmail(savedTFR, null, false);

        String ecurrState = msSystemParamService.getMsSystemParamDetail(savedTFR.getTrxFuidApproval().getCurrentState()).getParamDetailDesc();
        contentEmail.setStatusTiket(ecurrState);

        contentEmail.setAlasanPengajuan(savedTFR.getAlasanPengajuan() == null ? "" : savedTFR.getAlasanPengajuan());
        contentEmail.setInfoTambahan(savedTFR.getInfoTambahan() == null ? "" : savedTFR.getInfoTambahan());

        MsEmployee waitingApprovalPUK = msEmployeeService.getMsEmployeeByNik(pukNik);
        HashMap<String, String> mapPUKDirector = msEmployeeDirectorService.isPukDirectorByNikOptima(pukNik);

        emailNotificationService.sendCreateDelegatedFU(pukNik, contentEmail, EMPTY, waitingApprovalPUK, mapPUKDirector);
    }

    public void sendEmailtoDelegatedSP(String pukNik, TrxSetupParamRequest savedTSPR) {
        ContentEmail contentEmail = upmTicketService.buildSPContentEmail(savedTSPR, null, null, false);

        String ecurrState = msSystemParamService.getMsSystemParamDetail(savedTSPR.getTrxSetupParamApproval().getCurrentState()).getParamDetailDesc();
        contentEmail.setStatusTiket(ecurrState);

        MsEmployee waitingApprovalPUK = msEmployeeService.getMsEmployeeByNik(pukNik);
        HashMap<String, String> mapPUKDirector = msEmployeeDirectorService.isPukDirectorByNikOptima(pukNik);

        emailNotificationService.sendCreateDelegatedSP(pukNik, contentEmail, EMPTY, waitingApprovalPUK, mapPUKDirector);
    }

    public void sendEmailtoPreviousDelegatedApproval(String ticketId, String fromPUK, String toPUK) {
        MsEmployee waitingApprovalPUK = msEmployeeService.getMsEmployeeByNik(toPUK);
        HashMap<String, String> mapPUKDirector = msEmployeeDirectorService.isPukDirectorByNikOptima(fromPUK);

        emailNotificationService.sendCreatePreviousDelegatedApproval(ticketId, fromPUK, waitingApprovalPUK, mapPUKDirector);
    }
}
