package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.TimelineStatusModel;
import com.btpns.fin.model.TimelineTicketModel;
import com.btpns.fin.model.entity.TrxAudittrail;
import com.btpns.fin.model.entity.TrxUARAudittrail;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.ITrxUARAudittrailRepository;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class TicketTimelineService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    ITrxUARAudittrailRepository trxUARAudittrailRepository;

    public ResponseModel getTicketTimeline(String ticketId) {
        List<TimelineStatusModel> listTSM = new ArrayList<>();

        if (CommonHelper.isTicketUAR(ticketId)){
            List<TrxUARAudittrail> uarAudittrailList = trxUARAudittrailRepository.findAllByTicketId(ticketId);
            for (TrxUARAudittrail trxUARAudittrail : uarAudittrailList) {
                listTSM.add(mapToTimelineStatusModel(trxUARAudittrail.getAdditionalInfo()));
            }
        }else {
            List<TrxAudittrail> audittrailList = trxAudittrailService.getTrxAudittrailByTicketId(ticketId);
            for (TrxAudittrail trxAudittrail : audittrailList) {
                listTSM.add(mapToTimelineStatusModel(trxAudittrail.getAdditionalInfo()));
            }
        }

        return buildResponse(TYPE_TICKET_TIMELINE_GET, SUCCESS, buildTimelineTicketModel(ticketId, listTSM));
    }

    private TimelineStatusModel mapToTimelineStatusModel(String additionalInfo) {
        return gson.fromJson(additionalInfo, TimelineStatusModel.class);
    }

    private TimelineTicketModel buildTimelineTicketModel(String ticketId, List<TimelineStatusModel> listTSM) {
        TimelineTicketModel timelineTicketModel = new TimelineTicketModel();

        timelineTicketModel.setTicketId(ticketId);
        timelineTicketModel.setTimeline(listTSM);

        return timelineTicketModel;
    }

    private ResponseModel buildResponse(String type, ResponseStatus status, TimelineTicketModel timelineTicketModel) {
        ResponseModel<TimelineTicketModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(timelineTicketModel);

        return response;
    }
}
