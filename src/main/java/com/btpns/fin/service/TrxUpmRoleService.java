package com.btpns.fin.service;

import com.btpns.fin.model.entity.TrxUpmRole;
import com.btpns.fin.repository.ITrxUpmRoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TrxUpmRoleService {
    @Autowired
    ITrxUpmRoleRepository iTrxUpmRoleRepository;

    public TrxUpmRole saveTrxUpmRole(TrxUpmRole trxUpmRole) {
        return iTrxUpmRoleRepository.save(trxUpmRole);
    }

    public TrxUpmRole getTrxUpmRole(String nik){
        return iTrxUpmRoleRepository.getTrxUpmRole(nik);
    }

    public List<TrxUpmRole> getTrxUpmRoleMakerAll(){
        return iTrxUpmRoleRepository.getTrxUpmRoleMakerAll();
    }

    public List<TrxUpmRole> getTrxUpmRoleCheckerAll(){
        return iTrxUpmRoleRepository.getTrxUpmRoleCheckerAll();
    }

    public List<TrxUpmRole> getTrxUpmRoleCheckerAndAdminAll(){
        return iTrxUpmRoleRepository.getTrxUpmRoleCheckerAndAdminAll();
    }

    public Page<TrxUpmRole> getListTrxUpmRole(int pageNumMin1, int pageSize) {
        Pageable pageable = PageRequest.of(pageNumMin1, pageSize);
        return iTrxUpmRoleRepository.getListTrxUpmRole(pageable);
    }

    @Transactional
    public int deleteTrxUpmRole(String nik) {
        return iTrxUpmRoleRepository.deleteTrxUpmRole(nik);
    }

    public List<TrxUpmRole> getListTrxUpmRole() {
        return iTrxUpmRoleRepository.findAll();
    }

    public Map<String, TrxUpmRole> getMapUpmRoles() {
        List<TrxUpmRole> listUpmRole = getListTrxUpmRole();
        Map<String, TrxUpmRole> ret = new HashMap<>();
        listUpmRole.forEach(upmRole -> {
            ret.putIfAbsent(upmRole.getNik().toUpperCase(), upmRole);
        });
        return ret;
    }
}
