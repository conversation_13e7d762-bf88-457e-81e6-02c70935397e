package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.AttachmentModel;
import com.btpns.fin.model.ReportAplikasiPerTiketModel;
import com.btpns.fin.model.entity.MsEmployee;
import com.btpns.fin.model.entity.MsTemaApplication;
import com.btpns.fin.model.entity.ReportAplikasiPerTiket;
import com.btpns.fin.model.entity.TrxUpmRole;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IReportRepository;
import com.google.gson.Gson;
import liquibase.util.csv.opencsv.CSVWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class ReportUPMService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    MsEmployeeService msEmployeeService;
    @Autowired
    MsTemaApplicationService msTemaApplicationService;
    @Autowired
    EmailNotificationService emailNotificationService;
    @Autowired
    TrxUpmRoleService trxUpmRoleService;
    @Autowired
    MinioService minioService;
    @Autowired
    IReportRepository iReportRepository;
    @Autowired
    CommonHelper commonHelper;
    @Value("${query.limit}")
    private Integer queryLimit;
    private final Logger log = LoggerFactory.getLogger(ReportUPMService.class);

    public Page<ReportAplikasiPerTiket> getListReportPermohonanPerTicket(String status, String type, Integer pageNumber, Integer pageSize, List<String> upmProcess, List<String> upmChecker, String dateFlag, String startDate, String endDate, String tipeKewenanganLimit){
        Page<ReportAplikasiPerTiket> listRAPT = new PageImpl<>(new ArrayList<>());

        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey();
        endDate = period.getValue();
        HashMap<String, String> dateMap = commonHelper.getDateByDateFlag(startDate, endDate, dateFlag);
        upmProcess = getUpmData(upmProcess);
        upmChecker = getUpmData(upmChecker);
        if(pageSize == null){
            pageSize = 50;
        }
        int pageNumMin1 = pageNumber - 1;
        Integer isPending = 0;
        if(status.equals(Constants.UPM_STATUS_NEW) || status.equals(Constants.UPM_STATUS_PENDING)){
            if(status.equals(Constants.UPM_STATUS_PENDING)){
                isPending = 1;
            }
            status = CURR_STATUS_APPROVED;
        }

        List<String> typeList = getTicketTypeList(tipeKewenanganLimit, type);
        if (TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit) && UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
            listRAPT = new PageImpl<>(new ArrayList<>());
        }else {
            listRAPT = iReportRepository.getListReportPermohonanPerTiket(PageRequest.of(pageNumMin1, pageSize), typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);
        }

        return listRAPT;
    }

    private List<String> getUpmData(List<String> upmList) {
        if(CollectionUtils.isEmpty(upmList)){
            upmList = null;
        }else {
            TrxUpmRole upm = trxUpmRoleService.getTrxUpmRole(upmList.get(0));
            upmList = upm != null ? Arrays.asList(upm.getNama()) : null;
        }
        return upmList;
    }

    private List<String> getTicketTypeList(String tipeKewenanganLimit, String type) {
        List<String> typeList = new ArrayList<>();
        if (TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit)){
            typeList.add(PREFFIX_TICKET_FUID);
        }else {
            if (UPM_STATUS_ALL.equalsIgnoreCase(type)){
                typeList.add(PREFFIX_TICKET_FUID);
                typeList.add(PREFFIX_TICKET_SP);
            }else {
                if (UPM_FILTER_TYPE_USERID.equalsIgnoreCase(type)){
                    typeList.add(PREFFIX_TICKET_FUID);
                }
                if (UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
                    typeList.add(PREFFIX_TICKET_SP);
                }
            }
        }
        return typeList;
    }

    public Page<ReportAplikasiPerTiket> resolvePUKNameAndOccupation(Page<ReportAplikasiPerTiket> listRAPT) {
        if (listRAPT != null) {
            List<ReportAplikasiPerTiket> reportList = listRAPT.getContent();
            List<String> nikPukList = getAllNikPUK(reportList);

            Map<String, MsEmployee> pukMap = msEmployeeService.getMsEmployeeMap(nikPukList);
            for (ReportAplikasiPerTiket report : reportList) {
                if (report.getPuk1NIK() != null) {
                    updatePUK1Info(pukMap, report);
                }
                if (report.getPuk2NIK() != null) {
                    updatePUK2Info(pukMap, report);
                }
            }
        }

        return listRAPT;
    }

    private static List<String> getAllNikPUK(List<ReportAplikasiPerTiket> reportList) {
        return Stream.concat(reportList.stream().map(ReportAplikasiPerTiket::getPuk1NIK).filter(Objects::nonNull),
                             reportList.stream().map(ReportAplikasiPerTiket::getPuk2NIK).filter(Objects::nonNull))
                     .map(nikPUK -> nikPUK.split("-")[0]).collect(Collectors.toList());
    }

    private static void updatePUK1Info(Map<String, MsEmployee> pukMap, ReportAplikasiPerTiket report) {
        if (report.getPuk1Nama() == null) {
            MsEmployee puk = pukMap.get(report.getPuk1NIK().toLowerCase());
            report.setPuk1Nama(puk != null ? puk.getFullName() : EMPTY);
        }
        if (report.getPuk1Jabatan() == null) {
            MsEmployee puk = pukMap.get(report.getPuk1NIK().toLowerCase());
            report.setPuk1Jabatan(puk != null ? puk.getOccupation() : EMPTY);
        }
    }

    private static void updatePUK2Info(Map<String, MsEmployee> pukMap, ReportAplikasiPerTiket report) {
        if (report.getPuk2Nama() == null) {
            MsEmployee puk = pukMap.get(report.getPuk2NIK().toLowerCase());
            report.setPuk2Nama(puk != null ? puk.getFullName() : EMPTY);
        }
        if (report.getPuk2Jabatan() == null) {
            MsEmployee puk = pukMap.get(report.getPuk2NIK().toLowerCase());
            report.setPuk2Jabatan(puk != null ? puk.getOccupation() : EMPTY);
        }
    }

    public ResponseModel<ResUploadModel> generateReportPermohonanPerTiketCSV(String status, String type, List<String> upmProcess, List<String> upmChecker, String dateFlag, String startDate, String endDate, String tipeKewenanganLimit) throws Exception {
        Page<ReportAplikasiPerTiket> listRAPT = new PageImpl<>(new ArrayList<>());

        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey();
        endDate = period.getValue();
        HashMap<String, String> dateMap = commonHelper.getDateByDateFlag(startDate, endDate, dateFlag);
        upmProcess = getUpmData(upmProcess);
        upmChecker = getUpmData(upmChecker);
        Integer isPending = 0;
        if(status.equals(Constants.UPM_STATUS_NEW) || status.equals(Constants.UPM_STATUS_PENDING)){
            if(status.equals(Constants.UPM_STATUS_PENDING)){
                isPending = 1;
            }
            status = CURR_STATUS_APPROVED;
        }
        List<String> typeList = getTicketTypeList(tipeKewenanganLimit, type);
        PageRequest pageRequest = PageRequest.of(0, 10000);

        if (TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit) && UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
            listRAPT = new PageImpl<>(new ArrayList<>());
        }else {
            listRAPT = iReportRepository.getListReportPermohonanPerTiket(pageRequest, typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);
        }

        List<ReportAplikasiPerTiketModel> listRAPTM = new ArrayList<ReportAplikasiPerTiketModel>();
        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM));
        listRAPT.getContent().forEach(rapt -> {
            ReportAplikasiPerTiketModel raptm = Mapper.toReportAplikasiPerTiketModel(rapt);
            if (!StringUtils.isEmpty(raptm.getAplikasi())) {
                String[] split = raptm.getAplikasi().split(",");
                List<String> collect = Arrays.stream(split).map(data -> {
                    return msTemaApplicationMap.get(data).getParamDetailDesc();
                }).collect(Collectors.toList());
                raptm.setAplikasi(String.join(",", collect));
            }
            listRAPTM.add(raptm);
        });

        byte[] byteCSV = makeCsv(listRAPTM);
        Map<String, String> result = minioService.uploadReportFile(byteCSV, DocumentHelper.generateReportFilePath(Constants.REPORT_FILE_NAME, Constants.CSV_EXTENSION), Constants.REPORT_FILE_NAME);
        ResUploadModel rum = new ResUploadModel(result);
        ResponseModel<ResUploadModel> response = buildResponseDownloadCsv(SUCCESS, rum, TYPE_REPORT_PER_TIKET);

        return response;
    }

    public ResFileDownload directDownloadReportPermohonanPerTiketCSV(String status, String type, List<String> upmProcess, List<String> upmChecker, String dateFlag, String startDate, String endDate, String tipeKewenanganLimit) throws Exception {
        Page<ReportAplikasiPerTiket> listRAPT = new PageImpl<>(new ArrayList<>());

        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey();
        endDate = period.getValue();
        HashMap<String, String> dateMap = commonHelper.getDateByDateFlag(startDate, endDate, dateFlag);
        upmProcess = getUpmData(upmProcess);
        upmChecker = getUpmData(upmChecker);
        Integer isPending = 0;
        if(status.equals(Constants.UPM_STATUS_NEW) || status.equals(Constants.UPM_STATUS_PENDING)){
            if(status.equals(Constants.UPM_STATUS_PENDING)){
                isPending = 1;
            }
            status = CURR_STATUS_APPROVED;
        }
        List<String> typeList = getTicketTypeList(tipeKewenanganLimit, type);
        PageRequest pageRequest = PageRequest.of(0, 10000);

        if (TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit) && UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
            listRAPT = new PageImpl<>(new ArrayList<>());
        }else {
            listRAPT = iReportRepository.getListReportPermohonanPerTiket(pageRequest, typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);
        }

        List<ReportAplikasiPerTiketModel> listRAPTM = new ArrayList<ReportAplikasiPerTiketModel>();
        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM));
        listRAPT.getContent().forEach(rapt -> {
            ReportAplikasiPerTiketModel raptm = Mapper.toReportAplikasiPerTiketModel(rapt);
            if (!StringUtils.isEmpty(raptm.getAplikasi())) {
                String[] split = raptm.getAplikasi().split(",");
                List<String> collect = Arrays.stream(split).map(data -> {
                    return msTemaApplicationMap.get(data).getParamDetailDesc();
                }).collect(Collectors.toList());
                raptm.setAplikasi(String.join(",", collect));
            }
            listRAPTM.add(raptm);
        });

        byte[] byteCSV = makeCsv(listRAPTM);

        return new ResFileDownload(byteCSV, commonHelper.concateTwoString(REPORT_FILE_NAME, CSV_EXTENSION));
    }

    public byte[] makeCsv(List<ReportAplikasiPerTiketModel> listRAPTM) throws UnsupportedEncodingException {
        StringBuffer csv= new StringBuffer();
        csv.append("\""+TICKET_ID_COL_NAME+"\",\""+CREATE_DATE_COL_NAME+"\",\""+TANGGAL_EFEKTIF_COL_NAME+"\",\""
                +NIK_COL_NAME+"\",\""+NAMA_COL_NAME+"\",\""+JABATAN_COL_NAME+"\",\""+USER_ID_COL_NAME+"\",\""
                +KODE_CABANG_COL_NAME+"\",\""+NAMA_CABANG_COL_NAME+"\",\""+LOCATION_COL_NAME+"\",\""+EMAIL_COL_NAME+"\",\""+TELEPON_COL_NAME+"\",\""
                +JENIS_APLIKASI_COL_NAME+"\",\""+JENIS_TINGKATAN_USER_COL_NAME+"\",\""+MASA_BERLAKU_COL_NAME+"\",\""
                +JENIS_PENGAJUAN_COL_NAME+"\",\""+DESKRIPSI_ALASAN_COL_NAME+"\",\""+KETERANGAN_COL_NAME+"\",\""+INFO_TAMBAHAN_COL_NAME+"\",\""+STATUS_LAMPIRAN_COL_NAME+"\",\""
                +PUK1_NIK_COL_NAME+"\",\"" +PUK1_NAMA_COL_NAME+"\",\""+PUK1_JABATAN_COL_NAME+"\",\""+PUK1_TGL_APPROVE_COL_NAME+"\",\""
                +PUK2_NIK_COL_NAME+"\",\"" +PUK2_NAMA_COL_NAME+"\",\""+PUK2_JABATAN_COL_NAME+"\",\""+PUK2_TGL_APPROVE_COL_NAME+"\",\""
                +STATUS_COL_NAME+"\",\"" +PIC_PROCESS_COL_NAME+"\",\""+TGL_PROCESS_COL_NAME+"\",\""+PIC_APPROVE_COL_NAME+"\",\""+TGL_APPROVE_COL_NAME+"\",\""+KATEGORI_COL_NAME+"\""
                +"\r\n");

        for(int i=0; i<listRAPTM.size(); i++){
            ReportAplikasiPerTiketModel raptm = listRAPTM.get(i);
            csv.append("\""+raptm.getTicketId()+"\",\""+raptm.getCreateDate()+"\",\""+raptm.getTanggalEfektif()+"\",\""
                    +"\'"+raptm.getDataNIK()+"\",\""+raptm.getDataNama()+"\",\""+raptm.getDataJabatan()+"\",\""+"\'"+raptm.getDataUserId()+"\",\""
                    +"\'"+raptm.getDataKodeCabang()+"\",\""+raptm.getDataNamaCabang()+"\",\""+raptm.getLocation()+"\",\""+raptm.getDataEmail()+"\",\""+"\'"+raptm.getDataTelepon()+"\",\""
                    +raptm.getAplikasi()+"\",\""+raptm.getTingkatanUser()+"\",\""+raptm.getMasaBerlaku()+"\",\""
                    +raptm.getJenisPengajuan()+"\",\""+raptm.getDeskripsi().replaceAll("\n"," ")+"\",\""+raptm.getAlasanPengajuan().replaceAll("\n"," ")+"\",\""+raptm.getInfoTambahan().replaceAll("\n"," ")+"\",\""+raptm.getAttachment()+"\",\""
                    +"\'"+raptm.getPuk1NIK()+"\",\""+raptm.getPuk1Nama()+"\",\""+raptm.getPuk1Jabatan()+"\",\""+raptm.getPuk1ApproveDate()+"\",\""
                    +"\'" +raptm.getPuk2NIK()+"\",\""+raptm.getPuk2Nama()+"\",\""+raptm.getPuk2Jabatan()+"\",\""+raptm.getPuk2ApproveDate()+"\",\""
                    +raptm.getStatus()+"\",\""+raptm.getPicProcess()+"\",\""+raptm.getCurrentStateDate()+"\",\""+raptm.getPicApprove()+"\",\""+raptm.getDoneDate()+"\",\""+raptm.getKategori()+"\""
                    +"\r\n");
        }

        return csv.toString().getBytes("windows-1252");
    }

    @Async("asyncExecutor")
    public void sendEmailReportPermohonanPerTiketCSV(String nikRequester, String status, String type, List<String> upmProcess, List<String> upmChecker, String dateFlag, String startDate, String endDate, String tipeKewenanganLimit) throws Exception {
        Page<ReportAplikasiPerTiket> listRAPT = new PageImpl<>(new ArrayList<>());

        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey();
        endDate = period.getValue();
        HashMap<String, String> dateMap = commonHelper.getDateByDateFlag(startDate, endDate, dateFlag);
        upmProcess = getUpmData(upmProcess);
        upmChecker = getUpmData(upmChecker);
        Integer isPending = 0;
        if(status.equals(Constants.UPM_STATUS_NEW) || status.equals(Constants.UPM_STATUS_PENDING)){
            if(status.equals(Constants.UPM_STATUS_PENDING)){
                isPending = 1;
            }
            status = CURR_STATUS_APPROVED;
        }
        List<String> typeList = getTicketTypeList(tipeKewenanganLimit, type);

        if (!TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit) && !UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
            listRAPT = iReportRepository.getListReportPermohonanPerTiket(PageRequest.of(0, queryLimit), typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);

            String csvFileName = CommonHelper.concateTwoString(DocumentHelper.generateReportDetailUPMFileName(TIKET, startDate, endDate), CSV_EXTENSION);
            String csvFilePath = CommonHelper.concateTwoString(TMP_FOLDER, csvFileName);
            String gzipFileName = CommonHelper.concateTwoString(csvFileName, GZIP_EXTENSION);
            String gzipFilePath = CommonHelper.concateTwoString(csvFilePath, GZIP_EXTENSION);
            File csvOutputFile = new File(csvFilePath);
            try {
                FileWriter writer = new FileWriter(csvOutputFile);
                CSVWriter csvWriter = new CSVWriter(writer);
                csvWriter.writeNext(DocumentHelper.createHeaderCsvreportDetailUPM());

                for (int i = 0; i <= listRAPT.getTotalPages(); i++) {
                    listRAPT = iReportRepository.getListReportPermohonanPerTiket(PageRequest.of(i, queryLimit), typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);

                    if (listRAPT.hasContent()){
                        listRAPT.getContent().forEach(rapt -> {
                            Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM));

                            ReportAplikasiPerTiketModel raptm = Mapper.toReportAplikasiPerTiketModel(rapt);
                            if (!StringUtils.isEmpty(raptm.getAplikasi())) {
                                String[] split = raptm.getAplikasi().split(",");
                                List<String> collect = Arrays.stream(split).map(data -> {
                                    return msTemaApplicationMap.get(data).getParamDetailDesc();
                                }).collect(Collectors.toList());
                                raptm.setAplikasi(String.join(",", collect));
                            }

                            csvWriter.writeNext(DocumentHelper.createBodyCsvreportDetailUPM(raptm));
                        });
                    }
                }
                writer.close();
                log.info("Done creating CSV in {}", csvFilePath);


                CommonHelper.gzipFile(csvFilePath, gzipFilePath);
                log.info("Done creating Gzip in {}", gzipFilePath);
                File gzipOutputFile = new File(gzipFilePath);

                Map<String, String> result = minioService.uploadReportFileAsGzip(DocumentHelper.getFileBase64(gzipOutputFile), DocumentHelper.generateReportFilePath(csvFileName, GZIP_EXTENSION), gzipFileName);
                emailNotificationService.sendReportDetailUPMCsvToEmail(gson.fromJson(gson.toJson(result), AttachmentModel.class), nikRequester, TIKET, startDate, endDate);
            } catch (Exception ex) {
                log.error("Failed Generate report detail UPM ", ex);
            } finally {
                if(csvOutputFile.exists()) {
                    csvOutputFile.delete();
                }
                File gzip = new File(gzipFilePath);
                if(gzip.exists()) {
                    gzip.delete();
                }
            }
        }
    }

    private ResponseModel<ResUploadModel> buildResponseDownloadCsv(ResponseStatus status, ResUploadModel result, String typeReport) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        if(typeReport.equals(TYPE_REPORT_PER_APLIKASI)) {
            response.setType(TYPE_REPORT_PER_APLIKASI_DOWNLOAD);
        }
        if(typeReport.equals(TYPE_REPORT_PER_TIKET)){
            response.setType(TYPE_REPORT_PER_TIKET_DOWNLOAD);
        }
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }

    public Page<ReportAplikasiPerTiket> getListReportPermohonanPerAplikasi(String status, String type, Integer pageNumber, Integer pageSize, List<String> upmProcess, List<String> upmChecker, String dateFlag, String startDate, String endDate, String tipeKewenanganLimit) {
        Page<ReportAplikasiPerTiket> listRAPT = new PageImpl<>(new ArrayList<>());

        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey();
        endDate = period.getValue();
        HashMap<String, String> dateMap = commonHelper.getDateByDateFlag(startDate, endDate, dateFlag);
        upmProcess = getUpmData(upmProcess);
        upmChecker = getUpmData(upmChecker);
        //default pageSize
        if(pageSize == null){
            pageSize = 50;
        }
        int pageNumMin1 = pageNumber - 1;
        Integer isPending = 0;
        if(status.equals(Constants.UPM_STATUS_NEW) || status.equals(Constants.UPM_STATUS_PENDING)){
            if(status.equals(Constants.UPM_STATUS_PENDING)){
                isPending = 1;
            }
            status = CURR_STATUS_APPROVED;
        }
        List<String> typeList = getTicketTypeList(tipeKewenanganLimit, type);

        if (TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit) && UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
            listRAPT = new PageImpl<>(new ArrayList<>());
        }else {
            listRAPT = iReportRepository.getListReportPermohonanPerAplikasi(PageRequest.of(pageNumMin1, pageSize), typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);
        }

        return listRAPT;
    }

    public ResponseModel<ResUploadModel> generateReportPermohonanPerAplikasiCSV(String status, String type, List<String> upmProcess, List<String> upmChecker, String dateFlag, String startDate, String endDate, String tipeKewenanganLimit) throws Exception {
        Page<ReportAplikasiPerTiket> listRAPT = new PageImpl<>(new ArrayList<>());

        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey();
        endDate = period.getValue();
        HashMap<String, String> dateMap = commonHelper.getDateByDateFlag(startDate, endDate, dateFlag);
        upmProcess = getUpmData(upmProcess);
        upmChecker = getUpmData(upmChecker);
        Integer isPending = 0;
        if(status.equals(Constants.UPM_STATUS_NEW) || status.equals(Constants.UPM_STATUS_PENDING)){
            if(status.equals(Constants.UPM_STATUS_PENDING)){
                isPending = 1;
            }
            status = CURR_STATUS_APPROVED;
        }
        List<String> typeList = getTicketTypeList(tipeKewenanganLimit, type);
        PageRequest pageRequest = PageRequest.of(0, 10000);

        if (TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit) && UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
            listRAPT = new PageImpl<>(new ArrayList<>());
        }else {
            listRAPT = iReportRepository.getListReportPermohonanPerAplikasi(pageRequest, typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);
        }

        List<ReportAplikasiPerTiketModel> listRAPTM = new ArrayList<ReportAplikasiPerTiketModel>();
        listRAPT.getContent().forEach(rapt -> {
            ReportAplikasiPerTiketModel raptm = Mapper.toReportAplikasiPerTiketModel(rapt);
            listRAPTM.add(raptm);
        });

        byte[] byteCSV = makeCsv(listRAPTM);
        Map<String, String> result = minioService.uploadReportFile(byteCSV, DocumentHelper.generateReportFilePath(Constants.REPORT_FILE_NAME, Constants.CSV_EXTENSION), Constants.REPORT_FILE_NAME);
        ResUploadModel rum = new ResUploadModel(result);
        ResponseModel<ResUploadModel> response = buildResponseDownloadCsv(SUCCESS, rum, TYPE_REPORT_PER_APLIKASI);

        return response;
    }

    public ResFileDownload directDownloadReportPermohonanPerAplikasiCSV(String status, String type, List<String> upmProcess, List<String> upmChecker, String dateFlag, String startDate, String endDate, String tipeKewenanganLimit) throws Exception {
        Page<ReportAplikasiPerTiket> listRAPT = new PageImpl<>(new ArrayList<>());

        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey();
        endDate = period.getValue();
        HashMap<String, String> dateMap = commonHelper.getDateByDateFlag(startDate, endDate, dateFlag);
        upmProcess = getUpmData(upmProcess);
        upmChecker = getUpmData(upmChecker);
        Integer isPending = 0;
        if(status.equals(Constants.UPM_STATUS_NEW) || status.equals(Constants.UPM_STATUS_PENDING)){
            if(status.equals(Constants.UPM_STATUS_PENDING)){
                isPending = 1;
            }
            status = CURR_STATUS_APPROVED;
        }
        List<String> typeList = getTicketTypeList(tipeKewenanganLimit, type);
        PageRequest pageRequest = PageRequest.of(0, 10000);

        if (TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit) && UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
            listRAPT = new PageImpl<>(new ArrayList<>());
        }else {
            listRAPT = iReportRepository.getListReportPermohonanPerAplikasi(pageRequest, typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);
        }

        List<ReportAplikasiPerTiketModel> listRAPTM = new ArrayList<ReportAplikasiPerTiketModel>();
        listRAPT.getContent().forEach(rapt -> {
            ReportAplikasiPerTiketModel raptm = Mapper.toReportAplikasiPerTiketModel(rapt);
            listRAPTM.add(raptm);
        });

        byte[] byteCSV = makeCsv(listRAPTM);

        return new ResFileDownload(byteCSV, commonHelper.concateTwoString(REPORT_FILE_NAME, CSV_EXTENSION));
    }

    @Async("asyncExecutor")
    public void sendEmailReportPermohonanPerAplikasiCSV(String nikRequester, String status, String type, List<String> upmProcess, List<String> upmChecker, String dateFlag, String startDate, String endDate, String tipeKewenanganLimit) throws Exception {
        Page<ReportAplikasiPerTiket> listRAPT = new PageImpl<>(new ArrayList<>());

        String sDateNow = DateTimeHelper.getDateEfektif(new Date());
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        startDate = period.getKey();
        endDate = period.getValue();
        HashMap<String, String> dateMap = commonHelper.getDateByDateFlag(startDate, endDate, dateFlag);
        upmProcess = getUpmData(upmProcess);
        upmChecker = getUpmData(upmChecker);
        Integer isPending = 0;
        if(status.equals(Constants.UPM_STATUS_NEW) || status.equals(Constants.UPM_STATUS_PENDING)){
            if(status.equals(Constants.UPM_STATUS_PENDING)){
                isPending = 1;
            }
            status = CURR_STATUS_APPROVED;
        }
        List<String> typeList = getTicketTypeList(tipeKewenanganLimit, type);

        if (!TIPE_KEWENANGAN_LIMIT_TRANSAKSI.equalsIgnoreCase(tipeKewenanganLimit) && !UPM_FILTER_TYPE_SETUPPARAM.equalsIgnoreCase(type)){
            listRAPT = iReportRepository.getListReportPermohonanPerAplikasi(PageRequest.of(0, queryLimit), typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);

            String csvFileName = CommonHelper.concateTwoString(DocumentHelper.generateReportDetailUPMFileName(APLIKASI, startDate, endDate), CSV_EXTENSION);
            String csvFilePath = CommonHelper.concateTwoString(TMP_FOLDER, csvFileName);
            String gzipFileName = CommonHelper.concateTwoString(csvFileName, GZIP_EXTENSION);
            String gzipFilePath = CommonHelper.concateTwoString(csvFilePath, GZIP_EXTENSION);
            File csvOutputFile = new File(TMP_FOLDER + csvFileName);
            try {
                FileWriter writer = new FileWriter(csvOutputFile);
                CSVWriter csvWriter = new CSVWriter(writer);
                csvWriter.writeNext(DocumentHelper.createHeaderCsvreportDetailUPM());

                for (int i = 0; i <= listRAPT.getTotalPages(); i++) {
                    listRAPT = iReportRepository.getListReportPermohonanPerAplikasi(PageRequest.of(i,queryLimit), typeList, status, sDateNow, upmProcess, upmChecker, isPending, dateMap.get(EFFECTIVE_DT_START), dateMap.get(EFFECTIVE_DT_END), dateMap.get(CREATE_DT_START), dateMap.get(CREATE_DT_END), dateMap.get(CURRENT_STATE_DT_START), dateMap.get(CURRENT_STATE_DT_END), tipeKewenanganLimit);

                    if (listRAPT.hasContent()){
                        listRAPT.getContent().forEach(rapt -> {
                            ReportAplikasiPerTiketModel raptm = Mapper.toReportAplikasiPerTiketModel(rapt);

                            csvWriter.writeNext(DocumentHelper.createBodyCsvreportDetailUPM(raptm));
                        });
                    }
                }
                writer.close();
                log.info("Done creating CSV in {}", csvFilePath);

                CommonHelper.gzipFile(csvFilePath, gzipFilePath);
                log.info("Done creating Gzip in {}", gzipFilePath);
                File gzipOutputFile = new File(gzipFilePath);

                Map<String, String> result = minioService.uploadReportFileAsGzip(DocumentHelper.getFileBase64(gzipOutputFile), DocumentHelper.generateReportFilePath(csvFileName, GZIP_EXTENSION), gzipFileName);
                emailNotificationService.sendReportDetailUPMCsvToEmail(gson.fromJson(gson.toJson(result), AttachmentModel.class), nikRequester, APLIKASI, startDate, endDate);
            } catch (Exception ex) {
                log.error("Failed Generate report detail UPM ", ex);
            } finally {
                if(csvOutputFile.exists()) {
                    csvOutputFile.delete();
                }
                File gzip = new File(gzipFilePath);
                if(gzip.exists()) {
                    gzip.delete();
                }
            }
        }
    }
}
