package com.btpns.fin.service;

import com.btpns.fin.model.entity.AnalyticCount;
import com.btpns.fin.repository.IAnalyticCountRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AnalyticCountService {
    @Autowired
    IAnalyticCountRepository analyticCountRepository;

    public List<AnalyticCount> getCountAnalyticMaker(String startDate, String endDate, String role){
        return analyticCountRepository.getCountAnalyticMaker(startDate, endDate, role);
    }
}
