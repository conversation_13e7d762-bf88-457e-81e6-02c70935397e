package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.helper.Mapper;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.DataUserPerApp;
import com.btpns.fin.model.UARPendingPukData;
import com.btpns.fin.model.UARUserModel;
import com.btpns.fin.model.entity.MsUserIDApplication;
import com.btpns.fin.model.entity.TrxUARRequest;
import com.btpns.fin.model.request.ReqApprovalBatch;
import com.btpns.fin.model.response.*;
import com.btpns.fin.model.request.ReqConfirmUARModel;
import com.btpns.fin.repository.IReportUARUserRepository;
import com.btpns.fin.repository.ITrxUARAudittrailRepository;
import com.btpns.fin.repository.ITrxUARRequestRepository;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
@Transactional
public class TrxUserUARService {
    @Autowired
    private ITrxUARRequestRepository iTrxUARRequestRepository;

    @Autowired
    private ITrxUARAudittrailRepository iTrxUARAudittrailRepository;

    @Autowired
    private IReportUARUserRepository iReportUARUserRepository;

    @Autowired
    private MsUserIDApplicationService msUserIDApplicationService;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private MsEmployeeService msEmployeeService;

    @Autowired
    private MsSystemParamService systemParamService;

    @Autowired
    private TrxDelegationService trxDelegationService;

    @Autowired
    private EmailNotificationService emailNotificationService;

    @Autowired
    private MsSystemParamService msSystemParamService;

    @Autowired
    private MinioService minioService;

    @Autowired
    private Mapper mapper;

    public ResponseModel<List<DataUserPerApp>> getListUARRequests(String nikRequester) {
        List<TrxUARRequest> uarRequests = iTrxUARRequestRepository.findAllUARByNikInStatuses(Arrays.asList(STATUS_PENDING_USER, CURR_STATUS_REJECTED), nikRequester);
        Map<String, MsUserIDApplication> userIDAppMap = msUserIDApplicationService.getUserIDAppMap();

        List<DataUserPerApp> details = buildDataUserPerAppList(uarRequests, userIDAppMap);

        return buildResponse(TYPE_UAR_USER_CONFIRM_GET_LIST, SUCCESS, details);
    }

    private List<DataUserPerApp> buildDataUserPerAppList(List<TrxUARRequest> uarRequests, Map<String, MsUserIDApplication> userIDAppMap) {
        List<DataUserPerApp> data = new ArrayList<>();
        for (TrxUARRequest uarRequest : uarRequests) {
            UARUserModel dataUser = mapToDataUser(uarRequest);

            DataUserPerApp dataAplikasi = buildDataUserPerApp(userIDAppMap, uarRequest, dataUser);

            data.add(dataAplikasi);
        }
        return data;
    }

    private UARUserModel mapToDataUser(TrxUARRequest uarRequest) {
        UARUserModel dataUser = new UARUserModel();

        dataUser.setTicketId(uarRequest.getTicketId());
        dataUser.setNik(uarRequest.getNik());
        dataUser.setNamaUser(uarRequest.getNamaUser());
        dataUser.setKewenangan(uarRequest.getKewenangan());
        dataUser.setJabatan(uarRequest.getJabatan() != null ? uarRequest.getJabatan() : EMPTY);
        dataUser.setUnitKerja(uarRequest.getUnitKerja());
        dataUser.setStatus(buildStatusModel(uarRequest.getTrxUARApproval().getCurrentState()));
        return dataUser;
    }

    private static DataUserPerApp buildDataUserPerApp(Map<String, MsUserIDApplication> userIDAppMap, TrxUARRequest uarRequest, UARUserModel dataUser) {
        DataUserPerApp dataAplikasi = new DataUserPerApp();

        dataAplikasi.setAplikasi(userIDAppMap.get(uarRequest.getAplikasi()).getParamDetailDesc());
        dataAplikasi.setDataUser(dataUser);
        return dataAplikasi;
    }

    private static ResponseModel<List<DataUserPerApp>> buildResponse(String type, ResponseStatus status, List<DataUserPerApp> data) {
        ResponseModel<List<DataUserPerApp>> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(data);

        return response;
    }

    public boolean isValidGetDetailRequester(String ticketId, String nikRequester) {
        TrxUARRequest foundUAR = iTrxUARRequestRepository.getOne(ticketId);

        return CommonHelper.validateGetDetail(nikRequester, foundUAR.getNik(), foundUAR.getTrxUARApproval().getPukNik(), foundUAR.getTrxUARApproval().getPukNik(), trxUpmRoleService.getTrxUpmRole(nikRequester));
    }

    public ResponseModel<UARDetailModel> getUARDetail(String ticketId) {
        TrxUARRequest foundUARRequest = iTrxUARRequestRepository.getOne(ticketId);

        return buildResponse(TYPE_DETAIL_UAR_GET, SUCCESS, mapToUARDetailModel(foundUARRequest));
    }

    private ResponseModel<UARDetailModel> buildResponse(String type, ResponseStatus status, UARDetailModel detail) {
        ResponseModel<UARDetailModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(detail);

        return response;
    }

    private UARDetailModel mapToUARDetailModel(TrxUARRequest uarRequest) {
        UARDetailModel detail = new UARDetailModel();

        detail.setTicketId(uarRequest.getTicketId());
        detail.setAplikasi(msUserIDApplicationService.getAppDesc(uarRequest.getAplikasi()));
        detail.setPeriodeTahun(uarRequest.getPeriodYear());
        detail.setPeriodeTriwulan(uarRequest.getPeriodQuarter());
        detail.setKonfirmasiAkses(uarRequest.getTrxUARApproval().getUserConfirmation() != null ? Mapper.getConfirmationStatusValue(uarRequest.getTrxUARApproval().getUserConfirmation()) : EMPTY);
        detail.setData(buildDataUserID(uarRequest));
        detail.setStatus(buildStatusModel(uarRequest.getTrxUARApproval().getCurrentState()));
        detail.setProgress(buildProgressMap(uarRequest));
        detail.setManualConfirmation(Boolean.TRUE.equals(uarRequest.isManualConfirmation()) ? uarRequest.isManualConfirmation() : FALSE_FLAG_BOOL);
        detail.setIsReminded(uarRequest.getReminder() != null && uarRequest.getReminder() > 0 ? Boolean.TRUE : Boolean.FALSE);

        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        detail.setAttachment(new Gson().fromJson(uarRequest.getTrxUARApproval().getUpmMakerAttachment(), attachmentListType));

        return detail;
    }

    private StatusModel buildStatusModel(String statusKey) {
        StatusModel status = new StatusModel();

        status.setKeyStatus(statusKey);
        status.setValueStatus(systemParamService.getMsSystemParamDetail(statusKey).getParamDetailDesc());

        return status;
    }

    private DataUserIDModel buildDataUserID(TrxUARRequest uarRequest) {
        DataUserIDModel data = new DataUserIDModel();

        data.setNik(uarRequest.getNik());
        data.setNamaUser(uarRequest.getNamaUser());
        data.setKewenangan(uarRequest.getKewenangan());
        data.setJabatan(uarRequest.getJabatan() != null ? uarRequest.getJabatan() : EMPTY);
        data.setUnitKerja(uarRequest.getUnitKerja());
        data.setEmail(uarRequest.getEmail() != null ? uarRequest.getEmail() : EMPTY);

        return data;
    }

    private LinkedHashMap<String, Object> buildProgressMap(TrxUARRequest uarRequest) {
        LinkedHashMap<String, Object> progress = new LinkedHashMap<>();
        TrxUARApproval uarApproval = uarRequest.getTrxUARApproval();

        if (uarApproval.getUserNik() != null) {
            DataProgressModel userProgress = buildDataProgressModel(KEY_USER, uarRequest, uarRequest.getNik(), uarApproval.getUserResponseDT(), uarApproval.getUserNikStatus(), uarApproval.getUserNikNotes());

            progress.put(KEY_USER, userProgress);
        }

        if (uarApproval.getPukNik() != null) {
            DataProgressModel pukProgress = buildDataProgressModel(KEY_PUK, uarRequest, uarApproval.getPukNik(), uarApproval.getPukApprovalDT(), uarApproval.getPukStatus(), uarApproval.getPukNotes());

            progress.put(KEY_PUK, pukProgress);
        }

        if (uarApproval.getUpmMakerNik() != null) {
            DataProgressModel upmMakerProgress = buildDataProgressModel(UPM_PROCESS, uarRequest, uarApproval.getUpmMakerNik(), uarApproval.getUpmMakerProcessDT(), uarApproval.getUpmMakerStatus(), uarApproval.getUpmMakerNotes());

            progress.put(UPM_PROCESS, upmMakerProgress);
        }

        if (uarApproval.getUpmCheckerNik() != null) {
            DataProgressModel upmCheckerProgress = buildDataProgressModel(UPM_CHECKER, uarRequest, uarApproval.getUpmCheckerNik(), uarApproval.getUpmCheckerApprovalDT(), uarApproval.getUpmCheckerStatus(), uarApproval.getUpmCheckerNotes());

            progress.put(UPM_CHECKER, upmCheckerProgress);
        }

        return progress;
    }

    private DataProgressModel buildDataProgressModel(String progressKey, TrxUARRequest uarRequest, String nik, LocalDateTime dateTime, String statusKey, String notes) {
        DataProgressModel progressModel = new DataProgressModel();
        if (KEY_USER.equalsIgnoreCase(progressKey)) {
            mapProgressData(progressModel, uarRequest.getNik(), uarRequest.getNamaUser(), uarRequest.getJabatan());
        } else if (KEY_PUK.equalsIgnoreCase(progressKey) || UPM_PROCESS.equalsIgnoreCase(progressKey) || UPM_CHECKER.equalsIgnoreCase(progressKey)) {
            MsEmployee foundEmployee = msEmployeeService.getEmployeeByNik(nik);
            String name = uarRequest.getTrxUARApproval().getPukName() != null ? uarRequest.getTrxUARApproval().getPukName() : foundEmployee.getFullName();
            String occupation = uarRequest.getTrxUARApproval().getPukOccupation() != null ? uarRequest.getTrxUARApproval().getPukOccupation() : foundEmployee.getOccupationDesc();

            mapProgressData(progressModel, nik, name, occupation);
        } else {
            TrxUpmRole foundUpm = trxUpmRoleService.getTrxUpmRole(nik);
            mapProgressData(progressModel, foundUpm.getNik(), foundUpm.getNama(), foundUpm.getRole());
        }

        progressModel.setDateTime(dateTime != null ? DateTimeHelper.getFullDate(dateTime) : EMPTY);
        progressModel.setStatus(buildStatusModel(statusKey));
        progressModel.setNotes(notes != null ? notes : EMPTY);

        return progressModel;
    }

    private static void mapProgressData(DataProgressModel progressModel, String nik, String namaUser, String jabatan) {
        progressModel.setNik(nik);
        progressModel.setNama(namaUser);
        progressModel.setJabatan(jabatan != null ? jabatan : EMPTY);
    }

    public ResponseModel<ResponseListModel> getListUARApprovals(int pageNumMin1, Integer pageNumber, Integer pageSize, String nik) {
        Page<TrxUARRequest> trxUARRequestsPageble = iTrxUARRequestRepository.findAllPageableUARByStatusByPukNik(STATUS_PENDING_PUK, nik, PageRequest.of(pageNumMin1, pageSize));

        return Mapper.buildResponseList(TYPE_UAR_PUK_CONFIRM_GET_LIST, SUCCESS, buildResUserIdListModel(pageSize, pageNumber, trxUARRequestsPageble.getContent(), trxUARRequestsPageble.getTotalPages(), trxUARRequestsPageble.getTotalElements()));
    }

    private ResponseListModel buildResUserIdListModel(Integer pageSize, Integer pageNumber, List<TrxUARRequest> trxUARRequest, int totalPages, long totalItems) {
        Map<String, MsUserIDApplication> userIDAppMap = msUserIDApplicationService.getUserIDAppMap();

        ResponseListModel responseListModel = new ResponseListModel();

        responseListModel.setData(mapToListDataUARPerApp(trxUARRequest, userIDAppMap));
        responseListModel.setLimit(pageSize);
        responseListModel.setPage(pageNumber);
        responseListModel.setTotalPages(totalPages);
        responseListModel.setTotalItems(totalItems);

        return responseListModel;
    }

    private List<UARPendingPukData> mapToListDataUARPerApp(List<TrxUARRequest> trxUARRequest, Map<String, MsUserIDApplication> userIDAppMap) {
        List<UARPendingPukData> UARPendingPukData = new ArrayList<>();

        for (TrxUARRequest uarRequest : trxUARRequest) {
            UARPendingPukData.add(mapToUARPendingPukData(uarRequest, userIDAppMap));
        }

        return UARPendingPukData;
    }

    private UARPendingPukData mapToUARPendingPukData(TrxUARRequest uarRequest, Map<String, MsUserIDApplication> userIDAppMap) {
        UARPendingPukData uarPendingPukData = new UARPendingPukData();

        uarPendingPukData.setTicketId(uarRequest.getTicketId());
        uarPendingPukData.setAplikasi(userIDAppMap.get(uarRequest.getAplikasi()).getParamDetailDesc());
        uarPendingPukData.setNik(uarRequest.getNik());
        uarPendingPukData.setNamaUser(uarRequest.getNamaUser());
        uarPendingPukData.setKewenangan(uarRequest.getKewenangan());
        uarPendingPukData.setJabatan(uarRequest.getJabatan() != null ? uarRequest.getJabatan() : EMPTY);
        uarPendingPukData.setUnitKerja(uarRequest.getUnitKerja());
        uarPendingPukData.setKonfirmasiAkses(Mapper.getConfirmationStatusValue(uarRequest.getTrxUARApproval().getUserConfirmation()));
        uarPendingPukData.setNotes(!uarRequest.getTrxUARApproval().getUserNikNotes().isEmpty() ? uarRequest.getTrxUARApproval().getUserNikNotes() : EMPTY);

        return uarPendingPukData;
    }

    public ResponseModel<ResUARModel> updateConfirmationUAR(ReqConfirmUARModel request, Profile profile) {
        TrxUARRequest existingUARRequest = findByTicketId(request.getTicketId());

        String timelineStatus = TIMELINE_STATUS_CONFIRM_TICKET;
        if (TYPE_RESUBMIT.equalsIgnoreCase(request.getType())) {
            timelineStatus = TIMELINE_STATUS_RESUBMIT_TICKET;
        }
        existingUARRequest.setTrxUARApproval(updateUARConfirmation(request, existingUARRequest));
        existingUARRequest.setManualConfirmation(FALSE_FLAG_BOOL);
        existingUARRequest.setReminder(0);
        TrxUARRequest updatedUARRequest = iTrxUARRequestRepository.save(existingUARRequest);

        TrxUARAudittrail uarAudittrail = Mapper.buildUARAudittrail(profile, updatedUARRequest, updatedUARRequest.getTrxUARApproval().getCurrentState(), timelineStatus, TIMELINE_PIC_USER, request.getNotes());
        iTrxUARAudittrailRepository.save(uarAudittrail);
        if (updatedUARRequest != null) {
            sendUARNotificationByType(request.getType(), updatedUARRequest, request.getNotes());
        }

        return buildResponse(TYPE_UAR_USER_CONFIRM, SUCCESS, new ResUARModel(request.getTicketId(), request.getType()));
    }

    private TrxUARApproval updateUARConfirmation(ReqConfirmUARModel request, TrxUARRequest existingUARRequest) {
        TrxUARApproval existingUARApproval = existingUARRequest.getTrxUARApproval();

        existingUARApproval.setUserConfirmation(request.getConfirmation());
        existingUARApproval.setUserNikStatus(STATUS_CONFIRMED);
        existingUARApproval.setUserNikNotes(request.getNotes());
        existingUARApproval.setUserResponseDT(LocalDateTime.now());

        MsEmployee puk = msEmployeeService.getEmployeeByNik(request.getPuk());
        existingUARApproval.setPukNik(request.getPuk());
        existingUARApproval.setPukName(puk.getFullName());
        existingUARApproval.setPukOccupation(puk.getOccupationDesc());
        existingUARApproval.setPukStatus(STATUS_WAITING);
        existingUARApproval.setCurrentState(STATUS_PENDING_PUK);

        return existingUARApproval;
    }

    private ResponseModel<ResUARModel> buildResponse(String type, ResponseStatus status, ResUARModel detail) {
        ResponseModel<ResUARModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(detail);

        return response;
    }

    public boolean isValidRequestTypeByTicketStatus(ReqConfirmUARModel request, TrxUARRequest uarRequest) {
        return (TYPE_CONFIRM.equalsIgnoreCase(request.getType()) && STATUS_PENDING_USER.equalsIgnoreCase(uarRequest.getTrxUARApproval().getCurrentState()))
                || (TYPE_RESUBMIT.equalsIgnoreCase(request.getType()) && CURR_STATUS_REJECTED.equalsIgnoreCase(uarRequest.getTrxUARApproval().getCurrentState()));
    }

    public TrxUARRequest findByTicketId(String ticketId) {
        return iTrxUARRequestRepository.findByTicketId(ticketId);
    }

    public ResponseModel<ResBatchProcess> updateApprovalUAR(ReqApprovalBatch request, List<TrxUARRequest> existingUARs, Profile profile) {
        String uarStatus = "";
        String timelineStatus = "";
        String pukStatus = "";
        if (TYPE_APPROVE.equalsIgnoreCase(request.getType())) {
            uarStatus = UPM_STATUS_INPROGRESS;
            timelineStatus = TIMELINE_STATUS_APPROVE_TICKET;
            pukStatus = CURR_STATUS_APPROVED;
        }
        if (UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(request.getType())) {
            uarStatus = CURR_STATUS_REJECTED;
            timelineStatus = TIMELINE_STATUS_REJECT_TICKET;
            pukStatus = CURR_STATUS_REJECTED;
        }

        List<TrxUARRequest> updatedUARs = updateAllUARApproval(request, existingUARs, uarStatus, pukStatus);

        saveAllUARAudittrail(profile, timelineStatus, updatedUARs);

        if (updatedUARs.size() > 0) {
            updatedUARs.forEach(data -> {
                sendUARNotificationByType(request.getType(), data, data.getTrxUARApproval().getPukNotes());
            });
        }

        return buildResponse(request.getType(), SUCCESS, Mapper.buildResBatchProcess(updatedUARs.size(), request.getType().equals(TYPE_APPROVE) ? CURR_STATUS_APPROVED : CURR_STATUS_REJECTED));
    }

    private List<TrxUARRequest> updateAllUARApproval(ReqApprovalBatch request, List<TrxUARRequest> existingUARs, String uarStatus, String pukStatus) {
        List<ApprovalDataModel> approvalDataList = request.getData();
        for (int index = 0; index < existingUARs.size(); index++) {
            TrxUARApproval updatedUARApproval = updatePukApproval(existingUARs.get(index).getTrxUARApproval(), pukStatus, approvalDataList.get(index).getNotes(), uarStatus);
            existingUARs.get(index).setTrxUARApproval(updatedUARApproval);
            existingUARs.get(index).setReminder(0);
        }

        return iTrxUARRequestRepository.saveAll(existingUARs);
    }

    private TrxUARApproval updatePukApproval(TrxUARApproval existingUARApproval, String pukStatus, String notes, String currentStatus) {
        existingUARApproval.setPukStatus(pukStatus);
        existingUARApproval.setPukApprovalDT(LocalDateTime.now());
        existingUARApproval.setPukNotes(notes);
        existingUARApproval.setCurrentState(currentStatus);

        return existingUARApproval;
    }

    private void saveAllUARAudittrail(Profile profile, String timelineStatus, List<TrxUARRequest> updatedUARs) {
        List<TrxUARAudittrail> audittrailList = new ArrayList<>();
        for (TrxUARRequest updatedUAR : updatedUARs) {
            TrxUARAudittrail newUARAudittrail = Mapper.buildUARAudittrail(profile, updatedUAR, updatedUAR.getTrxUARApproval().getCurrentState(), timelineStatus, TIMELINE_PIC_PUK, updatedUAR.getTrxUARApproval().getPukNotes());
            audittrailList.add(newUARAudittrail);
        }
        iTrxUARAudittrailRepository.saveAll(audittrailList);
    }

    private ResponseModel<ResBatchProcess> buildResponse(String type, ResponseStatus status, ResBatchProcess details) {
        ResponseModel<ResBatchProcess> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(details);

        return response;
    }

    public boolean isValidPukApproval(ReqApprovalBatch request, List<TrxUARRequest> existingUARs) {
        if (!(TYPE_APPROVE.equalsIgnoreCase(request.getType()) || UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(request.getType()))) {
            return false;
        }
        for (TrxUARRequest foundUAR : existingUARs) {
            if (!STATUS_PENDING_PUK.equals(foundUAR.getTrxUARApproval().getCurrentState())) {
                return false;
            }
        }

        return true;
    }

    private void sendUARNotificationByType(String type, TrxUARRequest uarRequest, String notes) {
        String appDesc = msUserIDApplicationService.getAppDesc(uarRequest.getAplikasi());
        String currentStatusDesc = systemParamService.getMsSystemParamDetail(uarRequest.getTrxUARApproval().getCurrentState()).getParamDetailDesc();
        if (TYPE_CONFIRM.equalsIgnoreCase(type) || TYPE_RESUBMIT.equalsIgnoreCase(type)) {
            emailNotificationService.sendPukUARApprovalNotification(uarRequest, appDesc, currentStatusDesc);
        }
        if (TYPE_APPROVE.equalsIgnoreCase(type)) {
            emailNotificationService.sendUserUARApprovedNotification(uarRequest, appDesc, currentStatusDesc);
        }
        if (UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(type)) {
            emailNotificationService.sendUserUARRejectedNotification(uarRequest, appDesc, currentStatusDesc, KEY_PUK.toUpperCase(), notes);
        }
    }

    public List<String> getListTicketIdFromApprovalRequests(List<ApprovalDataModel> request) {
        return request.stream()
                .map(ApprovalDataModel::getTicketId)
                .collect(Collectors.toList());
    }

    public List<TrxUARRequest> getListUARFromTicketIds(List<String> ticketIds) {
        return iTrxUARRequestRepository.findAllById(ticketIds);
    }

    public boolean isValidPUK(List<TrxUARRequest> uarList, String nikRequester) {
        List<String> uarNikPUKs = uarList.stream()
                .map(uarRequest -> uarRequest.getTrxUARApproval().getPukNik())
                .collect(Collectors.toList());

        return uarNikPUKs.stream().allMatch(nik -> nik.equalsIgnoreCase(nikRequester));
    }

    public ResponseModel<ResponseListModel> getReportUARUser(String nikRequester, String triwulan, String tahun, String aplikasi, int pageNumMin1, Integer pageNumber, Integer pageSize) {
        Page<ResReportUARUser> reportUARUserPageable = new PageImpl<>(new ArrayList<>());
        if (!tahun.isEmpty() && !triwulan.isEmpty() && !aplikasi.isEmpty()){
            reportUARUserPageable = iReportUARUserRepository.getReportUARUser(nikRequester, triwulan, tahun, aplikasi, Arrays.asList(STATUS_PENDING_USER, CURR_STATUS_REJECTED), PageRequest.of(pageNumMin1, pageSize));
        }

        return Mapper.buildResponseList(TYPE_REPORT_UAR_USER, SUCCESS, buildResListModel(pageSize, pageNumber, reportUARUserPageable.getContent(), reportUARUserPageable.getTotalPages(), reportUARUserPageable.getTotalElements()));
    }

    private ResponseListModel<ResReportUARUser> buildResListModel(Integer pageSize, Integer pageNumber, List<ResReportUARUser> data, int totalPages, long totalItems) {
        ResponseListModel<ResReportUARUser> responseListModel = new ResponseListModel<>();

        responseListModel.setData(buildResReportUARUserList(data));
        responseListModel.setLimit(pageSize);
        responseListModel.setPage(pageNumber);
        responseListModel.setTotalPages(totalPages);
        responseListModel.setTotalItems(totalItems);

        return responseListModel;
    }

    private List<ResReportUARUser> buildResReportUARUserList(List<ResReportUARUser> resReportUARUserList) {
        Map<String, MsSystemParamDetail> systemParamDetailMap = systemParamService.getSystemParamDetailMap();
        List<ResReportUARUser> result = new ArrayList<>();

        for (ResReportUARUser resReportUARUser : resReportUARUserList) {
            result.add(buildResReportUARUser(resReportUARUser, systemParamDetailMap));
        }

        return result;
    }

    private ResReportUARUser buildResReportUARUser(ResReportUARUser resReportUARUser, Map<String, MsSystemParamDetail> systemParamDetailMap) {
        ResReportUARUser result = new ResReportUARUser();

        result.setTicketId(resReportUARUser.getTicketId());
        result.setNik(resReportUARUser.getNik());
        result.setNama(resReportUARUser.getNama());
        result.setKewenangan(resReportUARUser.getKewenangan());
        result.setJabatan(resReportUARUser.getJabatan() == null ? EMPTY : resReportUARUser.getJabatan());
        result.setUnitKerja(resReportUARUser.getUnitKerja());
        result.setStatus(resReportUARUser.getStatus().equals(NOL) ? UAR_CONFIRMATION_DELETE : UAR_CONFIRMATION_ACTIVE);
        result.setKeterangan(resReportUARUser.getKeterangan() == null ? EMPTY : resReportUARUser.getKeterangan());
        result.setStatusTiket(systemParamDetailMap.get(resReportUARUser.getStatusTiket()).getParamDetailDesc());

        return result;
    }

    public ResponseModel<ResUploadModel> getDetailUARAsPDF(String ticketId) throws Exception {
        TrxUARRequest foundUAR = findByTicketId(ticketId);

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(mapToDataSourceList(foundUAR));
        Map<String, Object> parameters = mapper.toParameterMap(getTimeLineModelList(ticketId));
        JasperReport jasperReport = CommonHelper.compileJasperReportTemplate("classpath:pdf_detail_uar.jrxml");

        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);
        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

        Map<String, String> result = minioService.uploadReportFilePDF(pdfReportStream.toByteArray(), DocumentHelper.generateReportFilePath(DETAIL_UAR_FILE_NAME, PDF_EXTENSION), DETAIL_UAR_FILE_NAME);

        return ResponseModel.<ResUploadModel>builder().type(TYPE_DETAIL_UAR_DOWNLOAD).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(new ResUploadModel(result)).build();
    }

    public ResFileDownload directDownloadPdfDetailUAR(String ticketId) throws Exception {
        TrxUARRequest foundUAR = findByTicketId(ticketId);

        JasperReport jasperReport = CommonHelper.compileJasperReportTemplate("classpath:pdf_detail_uar.jrxml");

        Map<String, Object> parameters = mapper.toParameterMap(getTimeLineModelList(ticketId));

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(mapToDataSourceList(foundUAR));

        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);
        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

        return new ResFileDownload(pdfReportStream.toByteArray(), DETAIL_UAR_FILE_NAME.concat(PDF_EXTENSION));
    }


    private Collection<?> mapToDataSourceList(TrxUARRequest foundUAR) {
        List<Map<String, String>> dataSourceList = new ArrayList<>();
        Map<String, String> dataSourceMap = new HashMap<>();

        TrxUARApproval foundUARApproval = foundUAR.getTrxUARApproval();

        dataSourceMap.put("ticketId", foundUAR.getTicketId());
        dataSourceMap.put("aplikasi", msUserIDApplicationService.getAppDesc(foundUAR.getAplikasi()));
        dataSourceMap.put("periode", Mapper.getRangePeriod(foundUAR.getPeriodQuarter(), String.valueOf(foundUAR.getPeriodYear())));
        dataSourceMap.put("nik", foundUAR.getNik());
        dataSourceMap.put("nama", foundUAR.getNamaUser());
        dataSourceMap.put("kewenangan", foundUAR.getKewenangan());
        dataSourceMap.put("jabatan", foundUAR.getJabatan() != null ? foundUAR.getJabatan() : EMPTY);
        dataSourceMap.put("unitKerja", foundUAR.getUnitKerja());
        dataSourceMap.put("statusKonfirmasi", foundUARApproval.getUserConfirmation() != null ? Mapper.getConfirmationStatusValue(foundUARApproval.getUserConfirmation()) : EMPTY);
        dataSourceMap.put("keterangan", foundUARApproval.getUserNikNotes());

        if (isApprovedByPUK(foundUARApproval)){
            dataSourceMap.put("nikPUK", foundUARApproval.getPukNik());
            dataSourceMap.put("namaPUK", foundUARApproval.getPukName());
            dataSourceMap.put("jabatanPUK", foundUARApproval.getPukOccupation());
        }

        if(isRejectedByUPM(foundUARApproval)){
            dataSourceMap.put("catatan", foundUARApproval.getUpmMakerNotes());
        }

        dataSourceMap.put("statusTiket", msSystemParamService.getMsSystemParamDetail(foundUARApproval.getCurrentState()).getParamDetailDesc());

        dataSourceList.add(dataSourceMap);

        return dataSourceList;
    }

    private boolean isApprovedByPUK(TrxUARApproval uarApproval) {
        return CURR_STATUS_APPROVED.equals(uarApproval.getPukStatus());
    }

    private boolean isRejectedByUPM(TrxUARApproval uarApproval) {
        return CURR_STATUS_REJECTED.equals(uarApproval.getUpmMakerStatus())
                && CURR_STATUS_REJECTED.equals(uarApproval.getCurrentState());
    }

    private List<TimelineModel> getTimeLineModelList(String ticketId) {
        List<TimelineModel> timelineModelList = new ArrayList<>();
        Map<String, TrxUpmRole> upmRoleMap = trxUpmRoleService.getMapUpmRoles();

        List<TrxUARAudittrail> uarAudittrails = iTrxUARAudittrailRepository.findAllByTicketId(ticketId);
        uarAudittrails.stream().map(data -> timelineModelList.add(buildTimelineModel(upmRoleMap, data))).collect(Collectors.toList());

        return timelineModelList;
    }

    private static TimelineModel buildTimelineModel(Map<String, TrxUpmRole> upmRoleMap, TrxUARAudittrail uarAudittrail) {
        Gson gson = new Gson();
        TimelineModel timelineModel = gson.fromJson(uarAudittrail.getAdditionalInfo(), TimelineModel.class);

        timelineModel.setAction(uarAudittrail.getAction());
        timelineModel.setNik(uarAudittrail.getNik());
        if (uarAudittrail.getAction().equalsIgnoreCase(REASSIGN_UPM_ACTION)) {
            timelineModel.setName(upmRoleMap.get(timelineModel.getNik().toUpperCase()).getNama());
        }
        return timelineModel;
    }


}
