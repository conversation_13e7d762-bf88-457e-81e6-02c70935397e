package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.helper.Mapper;
import com.btpns.fin.model.UserTicketModel;
import com.btpns.fin.model.entity.UserTicket;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IUserTicketRepository;
import io.micrometer.core.instrument.util.StringUtils;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.*;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class UserTicketService {
    @Autowired
    IUserTicketRepository iUserTicketRepository;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    MinioService minioService;

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    Mapper mapper;

    public ResponseModel<ResponseListModel<UserTicketModel>> getListUserTicket(Integer page, Integer limit, String status, String type, String nikRequester, Integer isUser, Integer isWaitingApproval, String startDate, String endDate) {
        List<String> statusList = getStatusList(status, isUser, isWaitingApproval);
        List<String> typeList = geTypeList(type);
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)){
            Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
            startDate = period.getKey();
            endDate = period.getValue();
        }else {
            startDate = null;
            endDate = null;
        }
        Page<UserTicket> userTicketPageable = iUserTicketRepository.getListUserTicket(statusList, typeList, nikRequester, isUser, isWaitingApproval, startDate, endDate, PageRequest.of(page - 1, limit));

        List<UserTicketModel> userTicketModelList = buildUserTicketModelList(userTicketPageable.getContent());
        ResponseListModel<UserTicketModel> responseDetails = ResponseListModel.<UserTicketModel>builder().data(userTicketModelList).page(page).limit(limit).totalPages(userTicketPageable.getTotalPages()).totalItems(userTicketPageable.getTotalElements()).build();

        return ResponseModel.<ResponseListModel<UserTicketModel>>builder().type(TYPE_USER_TICKET_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    private List<String> getStatusList(String status, Integer isUser, Integer isWaitingApproval) {
        List<String> statusList = new ArrayList<>();
        if (FILTER_ALL.equalsIgnoreCase(status)){
            if (isUser == 0){
                if (isWaitingApproval != null && isWaitingApproval == 1){
                    statusList.add(CURR_STATUS_WAITING_PUK1);
                    statusList.add(CURR_STATUS_WAITING_PUK2);
                }else {
                    statusList.add(CURR_STATUS_APPROVED);
                    statusList.add(CURR_STATUS_REJECTED);
                    statusList.add(UPM_STATUS_INPROGRESS);
                    statusList.add(UPM_STATUS_VERIFICATION);
                    statusList.add(UPM_STATUS_DONE);
                }
            }else {
                statusList.add(CURR_STATUS_APPROVED);
                statusList.add(CURR_STATUS_REJECTED);
                statusList.add(CURR_STATUS_WAITING_PUK1);
                statusList.add(CURR_STATUS_WAITING_PUK2);
                statusList.add(UPM_STATUS_INPROGRESS);
                statusList.add(UPM_STATUS_VERIFICATION);
                statusList.add(UPM_STATUS_DONE);
            }
        }else {
            statusList.add(status);
        }
        return statusList;
    }

    private List<String> geTypeList(String type) {
        List<String> typeList = new ArrayList<>();
        if (FILTER_ALL.equalsIgnoreCase(type)){
            typeList.add(PREFFIX_TICKET_FUID);
            typeList.add(PREFFIX_TICKET_SP);
        }else {
            typeList.add(type);
        }
        return typeList;
    }

    private List<UserTicketModel> buildUserTicketModelList(List<UserTicket> userTicketList) {
        List<UserTicketModel> userTicketModelList = new ArrayList<>();
        userTicketList.forEach(data->{
            userTicketModelList.add(buildUserTicketModel(data));
        });
        return userTicketModelList;
    }

    private UserTicketModel buildUserTicketModel(UserTicket userTicket) {
        UserTicketModel userTicketModel = new UserTicketModel();
        userTicketModel.setTicketId(userTicket.getTicketId());
        userTicketModel.setTanggalEfektif(DateTimeHelper.getDateEfektif(userTicket.getTanggalEfektif()));
        userTicketModel.setNik(userTicket.getDataNik());
        userTicketModel.setNama(userTicket.getDataNamaLengkap());
        userTicketModel.setJabatan(userTicket.getDataJabatan());
        userTicketModel.setKodeNamaCabang(commonHelper.generateKodeNamaCabang(userTicket.getDataKodeCabang(), userTicket.getDataNamaCabang()));
        userTicketModel.setAplikasi(commonHelper.getApplicationDesc(userTicket.getAplikasi()));
        userTicketModel.setJenisPengajuan(userTicket.getJenisPengajuan());
        userTicketModel.setDeskripsiAlasan(userTicket.getDeskripsiAlasan());
        userTicketModel.setStatus(userTicket.getCurrentStateDesc());
        return userTicketModel;
    }

    public ResponseModel<ResUploadModel> generateReportTicketUserPdf(String status, String type, String nikRequester, Integer isUser, Integer isWaitingApproval, String startDate, String endDate) throws Exception {
        List<String> statusList = getStatusList(status, isUser, isWaitingApproval);
        List<String> typeList = geTypeList(type);
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)){
            Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
            startDate = period.getKey();
            endDate = period.getValue();
        }else {
            startDate = null;
            endDate = null;
        }
        String fullName = msEmployeeService.getEmployeeByNik(nikRequester).getFullName();
        List<UserTicket> userTicketList= iUserTicketRepository.getListUserTicket(statusList, typeList, nikRequester, isUser, isWaitingApproval, startDate, endDate, Pageable.unpaged()).getContent();

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(buildUserTicketModelList(userTicketList));
        File file = ResourceUtils.getFile(userTicketList.size() > 0 ? "classpath:pdf_report_permohonan_ticket_user.jrxml" : "classpath:pdf_default_report_permohonan_ticket_user.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, mapper.generateReportTicketUserParameters(nikRequester, fullName, startDate, endDate, isUser), dataSource);
        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

        Map<String, String> result = minioService.uploadReportFilePDF(pdfReportStream.toByteArray(), DocumentHelper.generateReportFilePath(REPORT_TICKET_USER_FILE_NAME, PDF_EXTENSION), REPORT_TICKET_USER_FILE_NAME);

        return ResponseModel.<ResUploadModel>builder().type(TYPE_USER_TICKET_DOWNLOAD).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(new ResUploadModel(result)).build();
    }

    public ResFileDownload directDownloadReportTicketUserPdf(String status, String type, String nikRequester, Integer isUser, Integer isWaitingApproval, String startDate, String endDate) throws Exception {
        List<String> statusList = getStatusList(status, isUser, isWaitingApproval);
        List<String> typeList = geTypeList(type);
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)){
            Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
            startDate = period.getKey();
            endDate = period.getValue();
        }else {
            startDate = null;
            endDate = null;
        }
        String fullName = msEmployeeService.getEmployeeByNik(nikRequester).getFullName();
        List<UserTicket> userTicketList= iUserTicketRepository.getListUserTicket(statusList, typeList, nikRequester, isUser, isWaitingApproval, startDate, endDate, Pageable.unpaged()).getContent();

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(buildUserTicketModelList(userTicketList));
        File file = ResourceUtils.getFile(userTicketList.size() > 0 ? "classpath:pdf_report_permohonan_ticket_user.jrxml" : "classpath:pdf_default_report_permohonan_ticket_user.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, mapper.generateReportTicketUserParameters(nikRequester, fullName, startDate, endDate, isUser), dataSource);
        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

        return new ResFileDownload(pdfReportStream.toByteArray(), REPORT_TICKET_USER_FILE_NAME.concat(PDF_EXTENSION));
    }
}
