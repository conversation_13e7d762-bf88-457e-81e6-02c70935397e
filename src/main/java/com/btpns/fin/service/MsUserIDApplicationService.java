package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.model.DataAplikasiModel;
import com.btpns.fin.model.UserIDAppDetailModel;
import com.btpns.fin.model.UserIDAppModel;
import com.btpns.fin.model.entity.MsUserIDApplication;
import com.btpns.fin.model.request.ReqUserIDApplication;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsUserIDApplicationRepository;
import com.btpns.fin.repository.ITrxUARRequestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
public class MsUserIDApplicationService {
    @Autowired
    private IMsUserIDApplicationRepository iMsUserIDApplicationRepository;

    @Autowired
    private ITrxUARRequestRepository iTrxUARRequestRepository;

    public ResponseModel<ResponseListModel<UserIDAppDetailModel>> getUserIDApplications(String searchFlag, String searchData, Integer pageNumber, Integer pageSize) {
        Page<MsUserIDApplication> userIDApplications = new PageImpl<>(new ArrayList<>());
        Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);

        if (SEARCH_FLAG_PARAM_DETAIL_ID.equals(searchFlag)) {
            userIDApplications = iMsUserIDApplicationRepository.findAllUserIDApplicationsByCode(searchData, pageable);
        }
        if (SEARCH_FLAG_PARAM_DETAIL_DESC.equals(searchFlag)) {
            userIDApplications = iMsUserIDApplicationRepository.findAllUserIDApplicationsByDesc(searchData, pageable);
        }

        ResponseListModel<UserIDAppDetailModel> details = ResponseListModel.<UserIDAppDetailModel>builder()
                .data(userIDApplications.getContent().stream().map(MsUserIDApplication::toUserIDAppDetailModel).collect(Collectors.toList()))
                .page(pageNumber)
                .limit(pageSize)
                .totalPages(userIDApplications.getTotalPages())
                .totalItems(userIDApplications.getTotalElements())
                .build();

        return ResponseModel.<ResponseListModel<UserIDAppDetailModel>>builder().type(TYPE_USER_ID_APPLICATION_UPM_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(details).build();
    }

    private List<UserIDAppModel> mapToUserIDAppModelList(List<MsUserIDApplication> userIDApplications) {
        if (!userIDApplications.isEmpty()) {
            return userIDApplications.stream().map(MsUserIDApplication::toUserIDAppModel).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public Map<String, MsUserIDApplication> getUserIDAppMap() {
        List<MsUserIDApplication> listUserIDApp = iMsUserIDApplicationRepository.findAll();
        Map<String, MsUserIDApplication> ret = new HashMap<>();
        listUserIDApp.forEach(app -> ret.putIfAbsent(app.getParamDetailId(), app));

        return ret;
    }

    public Optional<MsUserIDApplication> findById(String paramDetailId) {
        return iMsUserIDApplicationRepository.findById(paramDetailId);
    }

    public String getAppDesc(String paramDetailId) {
        Optional<MsUserIDApplication> optional = findById(paramDetailId);
        if (optional.isPresent()) {
            String paramDetailDesc = optional.get().getParamDetailDesc();
            paramDetailDesc = CommonHelper.resolveParamDetailDescS4(paramDetailDesc);
            return paramDetailDesc;
        }

        return paramDetailId;
    }

    public ResponseModel<List<UserIDAppModel>> getAllAvaiableUserIDApplications(String nik, Boolean isVisible, Boolean isUAR) {
        List<MsUserIDApplication> userIDApplicationList = new ArrayList<>();

        if (nik != null && !nik.isEmpty()) {
            List<String> paramDetailIds = iTrxUARRequestRepository.findDistinctApplicationIdByNik(nik, STATUS_PENDING_USER);
            if (!paramDetailIds.isEmpty()) {
                userIDApplicationList = iMsUserIDApplicationRepository.findUserIDApplicationInParamDetailIds(paramDetailIds);
            }
        } else {
            userIDApplicationList = iMsUserIDApplicationRepository.findUARUserIDApplications(isVisible, isUAR);
        }

        List<UserIDAppModel> userIDAppModels = mapToUserIDAppModelList(userIDApplicationList);

        return ResponseModel.<List<UserIDAppModel>>builder().type(TYPE_USER_ID_APPLICATION_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(userIDAppModels).build();
    }

    public ResponseModel<UserIDAppDetailModel> getDetailUserIDApplication(String paramDetailId) {
        UserIDAppDetailModel detail = new UserIDAppDetailModel();
        Optional<MsUserIDApplication> optional = findById(paramDetailId);
        if (optional.isPresent()) {
            detail = optional.get().toUserIDAppDetailModel();
        }

        return ResponseModel.<UserIDAppDetailModel>builder().type(TYPE_USER_ID_APPLICATION_UPM_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(detail).build();
    }

    public ResponseModel<UserIDAppDetailModel> saveUserIDApplication(ReqUserIDApplication request) {
        MsUserIDApplication newUserIDApplication = request.toMsUserIDApplication();

        MsUserIDApplication savedUserIDApplication = iMsUserIDApplicationRepository.save(newUserIDApplication);

        return ResponseModel.<UserIDAppDetailModel>builder().type(TYPE_USER_ID_APPLICATION_UPM_ADD_EDIT).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(savedUserIDApplication.toUserIDAppDetailModel()).build();
    }

    public ResponseModel<UserIDAppDetailModel> updateUserIDApplication(MsUserIDApplication msUserIDApplication, ReqUserIDApplication request) {
        MsUserIDApplication updatedUserIDApplication = iMsUserIDApplicationRepository.save(updateDataUserIDApplication(msUserIDApplication, request));

        return ResponseModel.<UserIDAppDetailModel>builder().type(TYPE_USER_ID_APPLICATION_UPM_ADD_EDIT).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(updatedUserIDApplication.toUserIDAppDetailModel()).build();
    }

    private MsUserIDApplication updateDataUserIDApplication(MsUserIDApplication msUserIDApplication, ReqUserIDApplication request) {
        msUserIDApplication.setParamDetailDesc(request.getDesc());
        msUserIDApplication.setIsUar(request.isUar());
        msUserIDApplication.setIsVisible(request.isVisible());

        return msUserIDApplication;
    }

    public List<UserIDAppModel> getAllActiveUserIDApplications() {
        List<MsUserIDApplication> userIDApplicationList = iMsUserIDApplicationRepository.findUARUserIDApplications(true, null);
        if (userIDApplicationList.isEmpty()) {
            return Collections.emptyList();
        }

        return mapToUserIDAppModelList(userIDApplicationList);
    }

    public DataAplikasiModel getDataAplikasiUserID(String paramDetailId) {
        Optional<MsUserIDApplication> optional = findById(paramDetailId);

        return optional.map(MsUserIDApplication::toDataAplikasiModel).orElse(null);
    }
}
