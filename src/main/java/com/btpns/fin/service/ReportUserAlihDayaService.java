package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.ReportAlihDayaUser;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IReportUserAlihDayaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class ReportUserAlihDayaService {
    @Autowired
    IReportUserAlihDayaRepository reportUserAlihDayaRepository;

    @Autowired
    MinioService minioService;

    public ResponseModel<ResUploadModel> generateUserAlihDayaCsv() throws Exception {
        List<ReportAlihDayaUser> listRADU = reportUserAlihDayaRepository.getListReportUserAlihDaya();
        byte[] byteCSV = makeCsv(listRADU);
        Map<String, String> result = minioService.uploadReportFile(byteCSV, DocumentHelper.generateReportFilePath(REPORT_USER_ALIH_DAYA_FILE_NAME, CSV_EXTENSION), REPORT_USER_ALIH_DAYA_FILE_NAME);

        return buildResponse(SUCCESS, new ResUploadModel(result));
    }

    public ResFileDownload directDownloadAlihDayaCSV() throws Exception {
        List<ReportAlihDayaUser> listRADU = reportUserAlihDayaRepository.getListReportUserAlihDaya();
        byte[] byteCSV = makeCsv(listRADU);

        return new ResFileDownload(byteCSV, CommonHelper.concateTwoString(REPORT_USER_ALIH_DAYA_FILE_NAME, CSV_EXTENSION));
    }

    private byte[] makeCsv(List<ReportAlihDayaUser> listRADU) throws UnsupportedEncodingException {
        StringBuffer csv = new StringBuffer();

        csv.append("\""+NIK_VENDOR_COL_NAME+"\",\""+NAMA_VENDOR_COL_NAME+"\",\""+JABATAN_VENDOR_COL_NAME+"\",\""+NIK_PUK_VENDOR_COL_NAME+"\",\""+NAMA_PUK_VENDOR_COL_NAME+"\",\""+MASA_BERLAKU_COL_NAME+"\""+"\r\n");
        listRADU.forEach(data -> {
            csv.append("\""+"\'"+data.getNikVendor()+"\",\""+data.getNameVendor()+"\",\""+data.getOccupationVendor()+"\",\""+"\'"+data.getNikPUK()+"\",\""+data.getNamePUK()+"\",\""+DateTimeHelper.getDateToDateStringDDMMYYYY(data.getMasaBerlakuSampai())+"\""+"\r\n");
        });

        return csv.toString().getBytes("windows-1252");
    }

    private ResponseModel<ResUploadModel> buildResponse(ResponseStatus status, ResUploadModel result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(TYPE_USER_ALIH_DAYA_DOWNLOAD);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }
}
