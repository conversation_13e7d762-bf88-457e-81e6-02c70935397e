package com.btpns.fin.service;

import com.btpns.fin.model.entity.ReportProductivity;
import com.btpns.fin.repository.IReportProductivityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReportProductivityService {
    @Autowired
    IReportProductivityRepository reportProductivityRepository;

    public List<ReportProductivity> getListReportProductivity(String startDate, String endDate, List<String> upmProcess){
        return reportProductivityRepository.getListReportProductivity(startDate, endDate, upmProcess);
    };
}
