package com.btpns.fin.service;

import com.btpns.fin.model.entity.TrxAudittrail;
import com.btpns.fin.repository.ITrxAudittrailRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
public class TrxAudittrailService {
    @Autowired
    ITrxAudittrailRepository trxAudittrailRepository;

    @Transactional
    public TrxAudittrail saveTrxAudittrail(TrxAudittrail trxAudittrail){
        return  trxAudittrailRepository.save(trxAudittrail);
    }

    public List<TrxAudittrail> getTrxAudittrailByTicketId(String ticketId){
        List<TrxAudittrail> listTa = trxAudittrailRepository.getTrxAudittrailByTicketId(ticketId);
        return listTa;
    }
}
