package com.btpns.fin.service;

import com.btpns.fin.helper.Mapper;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.MsEmployeeDirectorModel;
import com.btpns.fin.model.entity.MsEmployeeDirector;
import com.btpns.fin.model.request.ReqMsEmployeeDirectorModel;
import com.btpns.fin.model.response.ResMsEmployeeDirectorModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class MsEmployeeDirectorService {
    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    public MsEmployeeDirector getMsEmployeeDirectorByNIKLdap(String nikLdap){
        return iMsEmployeeDirectorRepository.getMsEmployeeDirectorByNIKLdap(nikLdap);
    }

    public ResponseModel<MsEmployeeDirectorModel> getMsEmployeeDirectorByNIKOptima(String nikOptima){
        ResponseModel<MsEmployeeDirectorModel> response = new ResponseModel<>();

        MsEmployeeDirector employeeDirector = iMsEmployeeDirectorRepository.getMsEmployeeDirectorByNIKOptima(nikOptima);
        if(employeeDirector != null){
            response = ResponseModel.<MsEmployeeDirectorModel>builder().type(TYPE_DIRECTOR_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(employeeDirector.toMsEmployeeDirectorModel()).build();
        }

        return response;
    }

    public ResponseModel<ResponseListModel<MsEmployeeDirectorModel>> getMsEmployeeDirectorList(int pageNumber, Integer pageSize){
        Page<MsEmployeeDirector> msEmployeeDirectorPageable = iMsEmployeeDirectorRepository.getMsEmployeeDirectorList(PageRequest.of(pageNumber - 1, pageSize, Sort.by("nikOptima").ascending()));
        List<MsEmployeeDirectorModel> msEmployeeDirectorModelList = msEmployeeDirectorPageable.getContent().stream().map(MsEmployeeDirector::toMsEmployeeDirectorModel).collect(Collectors.toList());

        ResponseListModel<MsEmployeeDirectorModel> details = ResponseListModel.<MsEmployeeDirectorModel>builder()
                .data(msEmployeeDirectorModelList)
                .page(pageNumber).limit(pageSize)
                .totalPages(msEmployeeDirectorPageable.getTotalPages())
                .totalItems(msEmployeeDirectorPageable.getTotalElements())
                .build();

        return ResponseModel.<ResponseListModel<MsEmployeeDirectorModel>>builder().type(TYPE_DIRECTOR_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(details).build();
    }

    @Transactional
    public ResponseModel<ResMsEmployeeDirectorModel> saveMsEmployeeDirector(ReqMsEmployeeDirectorModel request) {
        ResponseModel<ResMsEmployeeDirectorModel> response = new ResponseModel<>();

        MsEmployeeDirector saveMsEmployeeDirector = iMsEmployeeDirectorRepository.save(Mapper.toMsEmployeeDirectorEntity(request));
        if(saveMsEmployeeDirector != null){
            response = buildResponse(TYPE_DIRECTOR_ADD_EDIT, SUCCESS, buildResMsEmployeeDirectorModel(saveMsEmployeeDirector.getNikOptima(), ADD));
        }

        return response;
    }

    @Transactional
    public ResponseModel<ResMsEmployeeDirectorModel> updateMsEmployeeDirector(ReqMsEmployeeDirectorModel request) {
        Optional<MsEmployeeDirector> existMsEmployeeDirector = iMsEmployeeDirectorRepository.findById(request.getNikOptima());

        if (existMsEmployeeDirector.isPresent()){
            iMsEmployeeDirectorRepository.save(mapToMsEmployeeDirector(existMsEmployeeDirector.get(), request));
        }

        return buildResponse(TYPE_DIRECTOR_ADD_EDIT, SUCCESS, buildResMsEmployeeDirectorModel(request.getNikOptima(), EDIT));
    }

    private MsEmployeeDirector mapToMsEmployeeDirector(MsEmployeeDirector existMsEmployeeDirector, ReqMsEmployeeDirectorModel request) {
        MsEmployeeDirector employeeDirector = existMsEmployeeDirector;

        employeeDirector.setNikLdap(!request.getNikLdap().isEmpty() ? request.getNikLdap() : existMsEmployeeDirector.getNikLdap());
        employeeDirector.setNamaLengkap(!request.getNamaLengkap().isEmpty() ? request.getNamaLengkap() : existMsEmployeeDirector.getNamaLengkap());
        employeeDirector.setEmail(!request.getEmail().isEmpty() ? request.getEmail() : existMsEmployeeDirector.getEmail());
        employeeDirector.setKeterangan(!request.getKeterangan().isEmpty() ? request.getKeterangan() : existMsEmployeeDirector.getKeterangan());
        employeeDirector.setUpdateDatetime(LocalDateTime.now());

        return employeeDirector;
    }

    @Transactional
    public ResponseModel<ResMsEmployeeDirectorModel> deleteMsEmployeeDirectorByNIKOptima(String nikOptima) {
        ResponseStatus status = FAILED;

        if(iMsEmployeeDirectorRepository.deleteMsEmployeeDirectorByNIKOptima(nikOptima) > 0){
            status = SUCCESS;
        }

        return buildResponse(TYPE_DIRECTOR_DELETE, status, buildResMsEmployeeDirectorModel(nikOptima, DELETE));
    }

    private ResponseModel<ResMsEmployeeDirectorModel> buildResponse(String type, ResponseStatus status, ResMsEmployeeDirectorModel resMsEmployeeDirectorModel) {
        ResponseModel<ResMsEmployeeDirectorModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resMsEmployeeDirectorModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    private ResMsEmployeeDirectorModel buildResMsEmployeeDirectorModel(String nikOptima, String type) {
        ResMsEmployeeDirectorModel resMsEmployeeDirectorModel = new ResMsEmployeeDirectorModel();

        resMsEmployeeDirectorModel.setType(type);
        resMsEmployeeDirectorModel.setNikOptima(nikOptima);

        return resMsEmployeeDirectorModel;
    }

    public HashMap<String, String> isPukDirectorByNikOptima(String nikOptima){
        HashMap<String, String> output = new HashMap<>();
        boolean isPukDirector = false;
        String emailPukDirector = "";
        MsEmployeeDirector msEmployeeDirector = iMsEmployeeDirectorRepository.getMsEmployeeDirectorByNIKOptima(nikOptima);
        if (msEmployeeDirector != null) {
            isPukDirector = true;
            emailPukDirector = msEmployeeDirector.getEmail().trim();
        }
        output.put(KEY_IS_PUK_DIRECTOR, String.valueOf(isPukDirector));
        output.put(KEY_EMAIL_PUK_DIRECTOR, emailPukDirector);
        return output;
    }
}
