package com.btpns.fin.service;

import com.btpns.fin.helper.Constants;
import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.model.ReportPermohonanModel;
import com.btpns.fin.model.entity.MsTemaApplication;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.entity.MsSystemParamDetail;
import com.btpns.fin.model.entity.PermohonanDetail;
import com.btpns.fin.repository.IReportPermohonanRepository;
import com.btpns.fin.repository.ITrxPUKVendorRepository;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;

@Service
public class ReportPemohonService {

    @Autowired
    MinioService minioService;

    @Autowired
    IReportPermohonanRepository iReportPermohonanRepository;

    @Autowired
    ITrxPUKVendorRepository trxPUKVendorRepository;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsEmployeeService msEmployeeService;

    public ResUploadModel getReportPermohonanPdf(String startPeriod, String endPeriod, String nikRequester, int isUser) throws Exception {
        List<PermohonanDetail> listReportPermohonan = getListReportPermohonanPdf(startPeriod, endPeriod, nikRequester, 0, 10000, isUser);
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(listReportPermohonan);

        File file = ResourceUtils.getFile("classpath:report_permohonan.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

        String fullName = msEmployeeService.getEmployeeOrVendor(nikRequester);
        Map<String, Object> parameters = new HashMap<>() {{
            put("pemohon", nikRequester.concat(" - ").concat(fullName));
            String strStartPeriod = convertYYYYMMDDtoDDMMYYYY(startPeriod.substring(0, 10));
            String strEndPeriod = convertYYYYMMDDtoDDMMYYYY(endPeriod.substring(0, 10));
            put("period", strStartPeriod.concat(" s/d ").concat(strEndPeriod));
        }};
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

        ByteArrayOutputStream pdfReportStream = new ByteArrayOutputStream();
        JRPdfExporter exporter = new JRPdfExporter();
        exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
        exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(pdfReportStream));
        exporter.exportReport();

        Map<String, String> result = minioService.uploadReportFilePDF(pdfReportStream.toByteArray(), DocumentHelper.generateReportFilePath(Constants.REPORT_FILE_NAME, Constants.PDF_EXTENSION), REPORT_FILE_NAME);
        return new ResUploadModel(result);
    }

    public ReportPermohonanModel getListReportPermohonan(String startDate, String endDate, String nikRequester, int pageNumber, int limit, int isUser){
        ReportPermohonanModel reportPermohonan = new ReportPermohonanModel();

        Pageable pageable = PageRequest.of(pageNumber - 1, limit);
        Page<PermohonanDetail> pagePD = iReportPermohonanRepository.getReportPermohonan(startDate, endDate, nikRequester, Constants.UPM_STATUS_DONE, pageable, isUser);
        List<PermohonanDetail> details = pagePD.getContent();
        List<PermohonanDetail> listReportPermohonan = mapPermohonanDetail(details);
        reportPermohonan.setStartDate(startDate);
        reportPermohonan.setEndDate(endDate);
        reportPermohonan.setPage(pageNumber);
        reportPermohonan.setLimit(limit);
        reportPermohonan.setData(listReportPermohonan);
        reportPermohonan.setTotalPages(pagePD.getTotalPages());
        reportPermohonan.setTotalItems(pagePD.getTotalElements());

        return reportPermohonan;
    }

    public List<PermohonanDetail> getListReportPermohonanPdf(String startDate, String endDate, String nikRequester, int offset, int limit, int isUser){
        Pageable pageable = PageRequest.of(offset, limit);
        Page<PermohonanDetail> pagePD = iReportPermohonanRepository.getReportPermohonan(startDate, endDate, nikRequester, Constants.UPM_STATUS_DONE, pageable, isUser);
        List<PermohonanDetail> details = pagePD.getContent();
        return mapPermohonanDetail(details);
    };

    private List<PermohonanDetail> mapPermohonanDetail(List<PermohonanDetail> details) {
        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM));
        return details.stream()
                .peek(detail -> {
                    if(!StringUtils.isEmpty(detail.getCreateDatetime()) && detail.getCreateDatetime().length() > 10){
                        detail.setCreateDatetime(convertYYYYMMDDTimetoDDMMYYYYTime(detail.getCreateDatetime().substring(0, 19)));
                    }
                    if (!StringUtils.isEmpty(detail.getTanggalEfektif()) && detail.getTanggalEfektif().length() == 10) {
                        detail.setTanggalEfektif(convertYYYYMMDDtoDDMMYYYY(detail.getTanggalEfektif()));
                    }
                    if (StringUtils.isEmpty(detail.getMasaBerlaku())) {
                        detail.setMasaBerlaku("Tetap");
                    } else if (detail.getMasaBerlaku().equals("1900-01-01")) {
                        detail.setMasaBerlaku("-");
                    } else {
                        detail.setMasaBerlaku(convertYYYYMMDDtoDDMMYYYY(detail.getMasaBerlaku()));
                    }
                    if (!StringUtils.isEmpty(detail.getAplikasi())) {
                        String[] split = detail.getAplikasi().split(",");
                        List<String> collect = Arrays.stream(split).map(data -> {
                            return msTemaApplicationMap.get(data).getParamDetailDesc();
                        }).collect(Collectors.toList());
                        detail.setAplikasi(String.join(",", collect));
                    }
                    if (!StringUtils.isEmpty(detail.getTanggalSelesai()) && detail.getTanggalSelesai().length() > 10) {
                        detail.setTanggalSelesai(convertYYYYMMDDtoDDMMYYYY(detail.getTanggalSelesai().substring(0, 10)));
                    }
                }).collect(Collectors.toList());
    }

    private String convertYYYYMMDDtoDDMMYYYY(String strDate){
        Date dtDate = null;
        try {
            dtDate = DateTimeHelper.getDateYYYYMMDD(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return DateTimeHelper.getDateDDMMYYYYAsString(dtDate);
    }

    private String convertYYYYMMDDTimetoDDMMYYYYTime(String strDateTime){
        LocalDateTime dateTime = DateTimeHelper.getDateCreateDateAsDate(strDateTime);
        return DateTimeHelper.getFullDate(dateTime);
    }
}
