package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.TotalActivity;
import com.btpns.fin.model.entity.TotalTujuanPerBranchType;
import com.btpns.fin.model.response.ResAnalyticVolumeTrxUser;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.ITotalActivityRepository;
import com.btpns.fin.repository.ITotalTujuanPerBranchTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
public class SummaryReportUPMService {
    @Autowired
    private ITotalActivityRepository totalActivityRepository;

    @Autowired
    private ITotalTujuanPerBranchTypeRepository totalTujuanPerBranchTypeRepository;

    @Autowired
    private AnalyticThreeMonthsService analyticThreeMonthsService;

    @Autowired
    private MinioService minioService;

    @Autowired
    private ExcelHelper excelHelper;

    public ResponseModel<SummaryReportUPM> getSummaryReportUPM(Integer month, Integer year) {
        SummaryReportUPM summaryReportUPM = generateSummaryReportUPM(month, year);

        return ResponseModel.<SummaryReportUPM>builder().type(TYPE_REPORT_SUMMARY_UPM_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(summaryReportUPM).build();
    }

    private SummaryReportUPM generateSummaryReportUPM(Integer month, Integer year) {
        ResAnalyticVolumeTrxUser analyticVolumeTrxUPM = getAnalyticVolumeTrxLast3Months();
        List<TotalActivity> totalActivity = totalActivityRepository.getTotalActivityByMonth(month, year);
        List<TotalTujuanPerBranchType> totalFUID = totalTujuanPerBranchTypeRepository.getTotalTujuanFUIDPerBranchTypeByMonth(month, year);
        List<TotalTujuanPerBranchType> totalSP = totalTujuanPerBranchTypeRepository.getTotalTujuanSPPerBranchTypeByMonth(month, year);
        List<TotalTujuanPerBranchType> totalActivityCombined = Stream.concat(totalFUID.stream(), totalSP.stream())
                                                                    .sorted((activity1, activity2) -> Integer.compare(activity2.getGrandTotal(), activity1.getGrandTotal()))
                                                                    .collect(Collectors.toList());

        TotalTujuanPerKategori totalTujuanPerKategori = buildTotalTujuanPerKategori(totalFUID, totalSP, totalActivityCombined);

        return buildSummaryReportUPM(analyticVolumeTrxUPM, totalActivity, totalActivityCombined, totalTujuanPerKategori);
    }

    private ResAnalyticVolumeTrxUser getAnalyticVolumeTrxLast3Months() {
        LocalDate dateNow = LocalDate.now();
        String period1 = DateTimeHelper.getDatePeriodMonth(dateNow.minusMonths(3));
        String period2 = DateTimeHelper.getDatePeriodMonth(dateNow.minusMonths(2));
        String period3 = DateTimeHelper.getDatePeriodMonth(dateNow.minusMonths(1));

        return analyticThreeMonthsService.getAnalyticVolumeTrxUsers(period1, period2, period3);
    }

    private SummaryReportUPM buildSummaryReportUPM(ResAnalyticVolumeTrxUser analyticVolumeTrxUPM, List<TotalActivity> totalActivity, List<TotalTujuanPerBranchType> totalActivityCombined, TotalTujuanPerKategori totalTujuanPerKategori) {
        SummaryReportUPM summaryReportUPM = new SummaryReportUPM();
        summaryReportUPM.setVolumeTrxUPMLast3Months(analyticVolumeTrxUPM);
        summaryReportUPM.setTotalActivity(totalActivity);
        summaryReportUPM.setTotalJenisPengajuanPerKategori(totalTujuanPerKategori);
        summaryReportUPM.setTop5Activity(getTop5Activity(totalActivityCombined));

        return summaryReportUPM;
    }

    private TotalTujuanPerKategori buildTotalTujuanPerKategori(List<TotalTujuanPerBranchType> totalFUID, List<TotalTujuanPerBranchType> totalSP, List<TotalTujuanPerBranchType> totalActivityCombined) {
        TotalTujuanPerKategori totalTujuanPerKategori = new TotalTujuanPerKategori();
        totalTujuanPerKategori.setUserIDMaintenance(totalFUID);
        totalTujuanPerKategori.setParameterMaintenance(totalSP);
        totalTujuanPerKategori.setGrandTotal(buildGrandTotalPerBranchType(totalActivityCombined));

        return totalTujuanPerKategori;
    }

    private GrandTotalPerBranchType buildGrandTotalPerBranchType(List<TotalTujuanPerBranchType> totalActivityCombined) {
        GrandTotalPerBranchType grandTotal = new GrandTotalPerBranchType(0, 0, 0, 0);

        for (TotalTujuanPerBranchType activity : totalActivityCombined) {
            grandTotal.setHo(grandTotal.getHo() + activity.getHo());
            grandTotal.setKcKfo(grandTotal.getKcKfo() + activity.getKcKfo());
            grandTotal.setMms(grandTotal.getMms() + activity.getMms());
            grandTotal.setGrandTotal(grandTotal.getGrandTotal() + activity.getGrandTotal());
        }

        return grandTotal;
    }

    private Top5Activity getTop5Activity(List<TotalTujuanPerBranchType> totalActivityCombined) {
        List<TopActivity> details = new ArrayList<>();
        int totalNonMMS = 0;
        int totalMMS = 0;

        for (TotalTujuanPerBranchType data : totalActivityCombined) {
            TopActivity topActivity = data.toTopActivity();
            details.add(topActivity);

            totalNonMMS = totalNonMMS + data.getKcKfo() + data.getHo();
            totalMMS = totalMMS + data.getMms();

            if (details.size() == 5) break;
        }

        return new Top5Activity(details, totalNonMMS, totalMMS, totalMMS + totalNonMMS);
    }

    public ResponseModel<ResUploadModel> generateExcelSummaryReportUPM(int month, int year) throws Exception {
        SummaryReportUPM summaryReportUPM = generateSummaryReportUPM(month, year);

        byte[] excelByte = excelHelper.exportExcelSummaryReportUPM(summaryReportUPM, REPORT_SUMMARY_UPM_FILE_NAME, month, year);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(REPORT_SUMMARY_UPM_FILE_NAME, XLSX_EXTENSION), REPORT_SUMMARY_UPM_FILE_NAME);

        return Mapper.buildResponse(TYPE_REPORT_SUMMARY_UPM_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadSummaryReportUPM(Integer month, Integer year) throws IOException {
        SummaryReportUPM summaryReportUPM = generateSummaryReportUPM(month, year);
        byte[] excelByte = excelHelper.exportExcelSummaryReportUPM(summaryReportUPM, REPORT_SUMMARY_UPM_FILE_NAME, month, year);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(REPORT_SUMMARY_UPM_FILE_NAME, XLSX_EXTENSION));
    }
}
