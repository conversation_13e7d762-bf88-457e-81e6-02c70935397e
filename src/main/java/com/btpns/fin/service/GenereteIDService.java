package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.GenereteIDModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsProsperaRoleRepository;
import com.btpns.fin.repository.IMsTemaApplicationRepository;
import com.btpns.fin.repository.IMsSystemParamDetailRepository;
import com.btpns.fin.repository.IMsUserIDApplicationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class GenereteIDService {
    @Autowired
    IMsSystemParamDetailRepository msSystemParamDetailRepository;

    @Autowired
    IMsTemaApplicationRepository msTemaApplicationRepository;

    @Autowired
    IMsUserIDApplicationRepository msUserIDApplicationRepository;

    @Autowired
    IMsProsperaRoleRepository msProsperaRoleRepository;

    public ResponseModel<GenereteIDModel> genereteID(String paramId) {
        ResponseModel<GenereteIDModel> response = new ResponseModel<>();

        String lastParamDetailId = "";
        if (KODE_APLIKASI_FUID.equalsIgnoreCase(paramId) || KODE_APLIKASI_SETUP_PARAM.equalsIgnoreCase(paramId)){
            lastParamDetailId = msTemaApplicationRepository.getLastParamDetailIdByParamId(paramId);
        } else if (KODE_APLIKASI_USER_ID.equalsIgnoreCase(paramId)) {
            lastParamDetailId = msUserIDApplicationRepository.getLastParamDetailId();
        } else if (paramId.startsWith("R")) {
            lastParamDetailId = msProsperaRoleRepository.getLastTemaRoleCodeByParamId(paramId);
        } else {
            lastParamDetailId = msSystemParamDetailRepository.getLastParamDetailIdByParamId(paramId);
        }

        if (lastParamDetailId != null){
            response = buildResponse(TYPE_GENERETE_ID, SUCCESS, mapToGenereteIDModel(paramId, lastParamDetailId));
        }

        return response;
    }

    private ResponseModel<GenereteIDModel> buildResponse(String type, ResponseStatus status, GenereteIDModel genereteIDModel) {
        ResponseModel<GenereteIDModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(genereteIDModel);

        return response;
    }

    private GenereteIDModel mapToGenereteIDModel(String paramId, String lastParamDetailId) {
        GenereteIDModel genereteIDModel = new GenereteIDModel();

        genereteIDModel.setParamId(paramId);
        genereteIDModel.setGenerateId(CommonHelper.genereteNewParamDetailId(lastParamDetailId));

        return genereteIDModel;
    }
}
