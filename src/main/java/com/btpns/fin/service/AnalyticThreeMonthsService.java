package com.btpns.fin.service;

import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.AnalyticVolumeTrxUser;
import com.btpns.fin.model.response.ResAnalyticVolumeTrxUser;
import com.btpns.fin.model.response.ResAnalyticVolumeTrxUserDetail;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IAnalyticThreeMonthsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.btpns.fin.helper.Constants.*;

@Service
public class AnalyticThreeMonthsService {
    private static final Logger logger = LoggerFactory.getLogger(AnalyticThreeMonthsService.class);

    @Autowired
    IAnalyticThreeMonthsRepository analyticThreeMonthsRepository;

    public ResponseModel<ResAnalyticVolumeTrxUser> getCountAnalyticVolumeTrxUsers(String period1, String period2, String period3) {
        ResAnalyticVolumeTrxUser response = getAnalyticVolumeTrxUsers(period1, period2, period3);

        return buildRestResponse(ResponseStatus.SUCCESS, response);
    }

    public ResAnalyticVolumeTrxUser getAnalyticVolumeTrxUsers(String period1, String period2, String period3) {
        String startEffectiveDTPeriod1 = getStartAndEndPeriod(period1, period1).getKey().split(" ") [0];
        String endEffectiveDTPeriod1 = getStartAndEndPeriod(period1, period1).getValue().split(" ") [0];
        String startEffectiveDTPeriod2 = getStartAndEndPeriod(period2, period2).getKey().split(" ") [0];
        String endEffectiveDTPeriod2 = getStartAndEndPeriod(period2, period2).getValue().split(" ") [0];
        String startEffectiveDTPeriod3 = getStartAndEndPeriod(period3, period3).getKey().split(" ") [0];
        String endEffectiveDTPeriod3 = getStartAndEndPeriod(period3, period3).getValue().split(" ") [0];

        List<AnalyticVolumeTrxUser> analyticVolumeTrxUsers = analyticThreeMonthsRepository.getCountAnalyticVolumeTrxUsers(startEffectiveDTPeriod1, endEffectiveDTPeriod1, startEffectiveDTPeriod2, endEffectiveDTPeriod2, startEffectiveDTPeriod3, endEffectiveDTPeriod3);
        List<ResAnalyticVolumeTrxUserDetail> analyticVolumeTrxUserDetails = mapToResAnalyticVolumeTrxUserDetail(analyticVolumeTrxUsers, getFormattedPeriod(period1), getFormattedPeriod(period2), getFormattedPeriod(period3));

        return maptoResAnalyticVolumeTrxUser(analyticVolumeTrxUserDetails);
    }

    private List<ResAnalyticVolumeTrxUserDetail> mapToResAnalyticVolumeTrxUserDetail(List<AnalyticVolumeTrxUser> analyticVolumeTrxUsers, String period1, String period2, String period3) {
        List<ResAnalyticVolumeTrxUserDetail> analyticVolumeTrxUserDetails = new ArrayList<>();
        ResAnalyticVolumeTrxUserDetail fuid = mapResAnalyticDetail(USER_ID_MAINTENANCE, period1, period2, period3);
        ResAnalyticVolumeTrxUserDetail sp = mapResAnalyticDetail(PARAMETER_MAINTENANCE, period1, period2, period3);

        Integer period1Fuid = 0, period2Fuid = 0, period3Fuid = 0;
        Integer period1SP = 0, period2SP = 0, period3SP = 0;

        for (AnalyticVolumeTrxUser data : analyticVolumeTrxUsers) {
            if (USER_ID_MAINTENANCE.equalsIgnoreCase(data.getType())) {
                if (period1.equalsIgnoreCase(data.getPeriodMonth())) {
                    period1Fuid = period1Fuid + data.getTotal();
                } else if (period2.equalsIgnoreCase(data.getPeriodMonth())) {
                    period2Fuid = period2Fuid + data.getTotal();
                } else {
                    period3Fuid = period3Fuid + data.getTotal();
                }
            } else if (PARAMETER_MAINTENANCE.equalsIgnoreCase(data.getType())) {
                if (period1.equalsIgnoreCase(data.getPeriodMonth())) {
                    period1SP = period1SP + data.getTotal();
                } else if (period2.equalsIgnoreCase(data.getPeriodMonth())) {
                    period2SP = period2SP + data.getTotal();
                } else {
                    period3SP = period3SP + data.getTotal();
                }
            } else {
                logger.info("Type Not Valid");
            }
        }

        fuid.setTotal1(period1Fuid);
        fuid.setTotal2(period2Fuid);
        fuid.setTotal3(period3Fuid);
        sp.setTotal1(period1SP);
        sp.setTotal2(period2SP);
        sp.setTotal3(period3SP);

        analyticVolumeTrxUserDetails.add(fuid);
        analyticVolumeTrxUserDetails.add(sp);

        return analyticVolumeTrxUserDetails;
    }

    private Map.Entry<String, String> getStartAndEndPeriod(String startDate, String endDate) {
        startDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getBeginningofMonth(startDate));
        endDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getEndofMonth(endDate));

        return Map.entry(startDate, endDate);
    }

    private String getFormattedPeriod(String period) {
        String[] split = period.split("-");
        return split[1] + split[0];
    }

    private ResAnalyticVolumeTrxUserDetail mapResAnalyticDetail(String type, String period1, String period2, String period3) {
        ResAnalyticVolumeTrxUserDetail detail = new ResAnalyticVolumeTrxUserDetail();

        detail.setType(type);
        detail.setPeriodMonth1(period1);
        detail.setPeriodMonth2(period2);
        detail.setPeriodMonth3(period3);

        return detail;
    }

    private ResponseModel<ResAnalyticVolumeTrxUser> buildRestResponse(ResponseStatus status, ResAnalyticVolumeTrxUser analyticVolumeTrxUser) {
        ResponseModel<ResAnalyticVolumeTrxUser> response = new ResponseModel<>();

        response.setType(TYPE_ANALYTIC_TRX_VOLUME_USER);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(analyticVolumeTrxUser);

        return response;
    }

    private ResAnalyticVolumeTrxUser maptoResAnalyticVolumeTrxUser(List<ResAnalyticVolumeTrxUserDetail> analyticVolumeTrxUserDetails) {
        ResAnalyticVolumeTrxUser resAnalyticVolumeTrxUser = new ResAnalyticVolumeTrxUser();

        Integer totalPeriodMonth1 = 0;
        Integer totalPeriodMonth2 = 0;
        Integer totalPeriodMonth3 = 0;

        for (ResAnalyticVolumeTrxUserDetail data : analyticVolumeTrxUserDetails) {
            totalPeriodMonth1 = totalPeriodMonth1 + data.getTotal1();
            totalPeriodMonth2 = totalPeriodMonth2 + data.getTotal2();
            totalPeriodMonth3 = totalPeriodMonth3 + data.getTotal3();
        }

        resAnalyticVolumeTrxUser.setData(analyticVolumeTrxUserDetails);
        resAnalyticVolumeTrxUser.setTotalPeriodMonth1(totalPeriodMonth1);
        resAnalyticVolumeTrxUser.setTotalPeriodMonth2(totalPeriodMonth2);
        resAnalyticVolumeTrxUser.setTotalPeriodMonth3(totalPeriodMonth3);

        return resAnalyticVolumeTrxUser;
    }
}
