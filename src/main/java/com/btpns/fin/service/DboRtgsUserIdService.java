package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsDboRTGS;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IDboRtgsUserIdRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service @Transactional
public class DboRtgsUserIdService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    IDboRtgsUserIdRepository dboRtgsUserIdRepository;

    @Autowired
    ITrxUserIdBatchRepository trxUserIdBatchRepository;

    @Autowired
    MinioService minioService;

    @Autowired
    ExcelHelper excelHelper;

    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResBatchUserId> saveBatchMsDboRtgsUserId(ReqUserIdBatchModel<MsDboRTGS> request, String nikRequester) throws Exception {
        List<MsDboRTGS> msDboRTGSList = dboRtgsUserIdRepository.findAll();
        if (!msDboRTGSList.isEmpty() && msDboRTGSList.size() > 0){
            if(deleteMsDboRTGS() > 0){
                saveTrxUserIdBatch(request, nikRequester);
                saveBatchMsDboRtgs(request.getData());
            }
        }else {
            saveTrxUserIdBatch(request, nikRequester);
            saveBatchMsDboRtgs(request.getData());
        }
        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_DBO_RTGS, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_DBO_RTGS_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsDboRTGS> msDboRTGSList) {
        return msDboRTGSList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveBatchMsDboRtgs(List<MsDboRTGS> msDboRTGSList) {
        dboRtgsUserIdRepository.saveAll(msDboRTGSList);
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<MsDboRTGS> request, String nikRequester) {
        trxUserIdBatchRepository.save(Mapper.toTrxUserIdBatch(request.getBatchId(), request.getFileName(), request.getTotalData(), request.getType(), nikRequester, msEmployeeService.getEmployeeOrVendor(nikRequester)));
    }

    private int deleteMsDboRTGS() {
        return dboRtgsUserIdRepository.deleteAllMsDboRTGS();
    }

    public ResponseModel<ResponseListModel> getListMsDboRTGS(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<MsDboRTGS> msDboRTGSPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if(SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag)){
                msDboRTGSPageable = dboRtgsUserIdRepository.findAllByNikUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag)){
                msDboRTGSPageable = dboRtgsUserIdRepository.findAllByNameUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            msDboRTGSPageable = dboRtgsUserIdRepository.getListMsDboRTGS(PageRequest.of(pageNumMin1, pageSize));
        }

        ResponseListModel<UserIDModel> responseListModel = Mapper.buildResUserIdListModel(pageSize, pageNumber, mapToUserIDListModel(msDboRTGSPageable.getContent()), msDboRTGSPageable.getTotalPages(), msDboRTGSPageable.getTotalElements());

        return Mapper.buildResponseList(TYPE_MS_DBO_RTGS_MANAGEMENT_GET_LIST, SUCCESS, responseListModel);
    }

    private List<UserIDModel> mapToUserIDListModel(List<MsDboRTGS> msDboRTGS) {
        return msDboRTGS.stream().map(MsDboRTGS::toUserIDModel).collect(Collectors.toList());
    }

    public ResponseModel<UserIDModel> getMsDboRTGSByNik(String nik) {
        ResponseModel<UserIDModel> response = new ResponseModel<>();

        MsDboRTGS msDboRTGS = dboRtgsUserIdRepository.findByNikUser(nik);
        if (msDboRTGS != null){
            response = ResponseModel.<UserIDModel>builder().type(TYPE_MS_DBO_RTGS_MANAGEMENT_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(msDboRTGS.toUserIDModel()).build();
        }

        return response;
    }

    public ResponseModel<ResCUDUserIdModel> saveMsDboRTGSUserId(ReqUserIDModel request) {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        MsDboRTGS msDboRTGS = saveMsDboRTGSData(request.toDboRTGS());
        if (msDboRTGS != null){
            response = Mapper.tobuildResponseCUDUserId(TYPE_MS_DBO_RTGS_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, request.getNik()));
        }

        return response;
    }

    private MsDboRTGS saveMsDboRTGSData(MsDboRTGS msDboRTGS) {
        return dboRtgsUserIdRepository.save(msDboRTGS);
    }

    public ResponseModel<ResCUDUserIdModel> updateMsDboRTGSUserId(ReqUserIDModel request) {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        MsDboRTGS upadatedMsDboRTGS = updateMsDboRTGSData(request);
        if (upadatedMsDboRTGS != null){
            response = Mapper.tobuildResponseCUDUserId(TYPE_MS_DBO_RTGS_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, request.getNik()));
        }

        return response;
    }

    private MsDboRTGS updateMsDboRTGSData(ReqUserIDModel request) {
        MsDboRTGS foundDboRTGS = dboRtgsUserIdRepository.findByNikUser(request.getNik());
        if (foundDboRTGS != null) {
            MsDboRTGS newDboRTGS = request.toDboRTGS();
            newDboRTGS.setId(foundDboRTGS.getId());
            newDboRTGS.setNik(foundDboRTGS.getNik());

            foundDboRTGS = dboRtgsUserIdRepository.save(newDboRTGS);
        }

        return foundDboRTGS;
    }

    public ResponseModel<ResCUDUserIdModel> deleteMsDboRTGSUserId(String nik) {
        ResponseStatus status = FAILED;
        if (deleteMsDboRTGSByNikUser(nik) > 0) {
            status = SUCCESS;
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_DBO_RTGS_MANAGEMENT_DELETE, status, Mapper.toResCUDUserIdModel(DELETE, nik));
    }

    private int deleteMsDboRTGSByNikUser(String nik) {
        return dboRtgsUserIdRepository.deleteMsDboRTGSByNik(nik);
    }

    public ResponseModel<ResUploadModel> genereteExcelMsDboRTGSUserId() throws Exception {
        List<MsDboRTGS> msDboRTGSList = dboRtgsUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(msDboRTGSList), DBORTGS_USER_ID_FILE_NAME);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(DBORTGS_USER_ID_FILE_NAME, XLSX_EXTENSION), DBORTGS_USER_ID_FILE_NAME);

        return Mapper.buildResponse(TYPE_MS_DBO_RTGS_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsDboRTGSUserId() throws Exception {
        List<MsDboRTGS> msDboRTGSList = dboRtgsUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(msDboRTGSList), DBORTGS_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(DBORTGS_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
