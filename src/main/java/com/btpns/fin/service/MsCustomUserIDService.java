package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsCustomUserID;
import com.btpns.fin.model.CustomUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.ICustomUserIDRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.google.gson.Gson;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
@Transactional
public class MsCustomUserIDService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private ICustomUserIDRepository customUserIDRepository;

    @Autowired
    private MsUserIDApplicationService userIDApplicationService;

    @Autowired
    private MsEmployeeService employeeService;

    @Autowired
    private ITrxUserIdBatchRepository userIdBatchRepository;

    @Autowired
    private MinioService minioService;

    @Autowired
    private ExcelHelper excelHelper;

    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResponseListCustomUserID> getAllByApplication(Integer pageNumber, Integer pageSize, String searchFlag, String searchData, String paramDetailId) {
        Page<MsCustomUserID> customUserIDs = findAllByApplication(searchFlag, searchData, paramDetailId, PageRequest.of((pageNumber - 1), pageSize));
        List<UserIDModel> data = mapToUserIDModelList(customUserIDs.getContent());

        ResponseListCustomUserID responseDetails = new ResponseListCustomUserID.Builder()
                .aplikasi(userIDApplicationService.getDataAplikasiUserID(paramDetailId))
                .data(data)
                .page(pageNumber)
                .limit(pageSize)
                .totalPages(customUserIDs.getTotalPages())
                .totalItems(customUserIDs.getTotalElements())
                .build();

        return ResponseModel.<ResponseListCustomUserID>builder().type(TYPE_MS_CUSTOM_USERID_MANAGEMENT_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    private List<UserIDModel> mapToUserIDModelList(List<MsCustomUserID> customUserIDs) {
        return customUserIDs.stream().map(MsCustomUserID::toUserIDModel).collect(Collectors.toList());
    }

    private Page<MsCustomUserID> findAllByApplication(String searchFlag, String searchData, String paramDetailId, Pageable pageable) {
        if (searchData.isBlank()) {
            return customUserIDRepository.findAllByParamDetailId(paramDetailId, pageable);
        }
        if (SEARCH_FLAG_NIK.equals(searchFlag)) {
            return customUserIDRepository.findAllByParamDetailIdByNik(paramDetailId, searchData, pageable);
        }
        if (SEARCH_FLAG_NAME.equals(searchFlag)) {
            return customUserIDRepository.findAllByParamDetailIdByNamaUser(paramDetailId, searchData, pageable);
        }

        return Page.empty();
    }

    public ResponseModel<CustomUserIDModel> getCustomUserIDByNikAndParamDetailId(String nik, String paramDetailId) {
        CustomUserIDModel customUserIDModel = new CustomUserIDModel();

        Optional<MsCustomUserID> optional = this.findByNikAndParamDetailId(nik, paramDetailId);
        ResponseStatus status = NOT_FOUND;
        if (optional.isPresent()) {
            MsCustomUserID foundCustomUserID = optional.get();
            customUserIDModel = toCustomUserIDModel(foundCustomUserID);

            status = SUCCESS;
        }

        return ResponseModel.<CustomUserIDModel>builder().type(TYPE_MS_CUSTOM_USERID_MANAGEMENT_GET).status(status.getCode()).statusDesc(status.getValue()).details(customUserIDModel).build();
    }

    private CustomUserIDModel toCustomUserIDModel(MsCustomUserID foundCustomUserID) {
        CustomUserIDModel customUserIDModel = new CustomUserIDModel();

        customUserIDModel.setAplikasi(userIDApplicationService.getDataAplikasiUserID(foundCustomUserID.getParamDetailId()));
        customUserIDModel.setNik(foundCustomUserID.getNik());
        customUserIDModel.setNamaUser(foundCustomUserID.getNamaUser());
        customUserIDModel.setKewenangan(foundCustomUserID.getKewenangan());
        customUserIDModel.setJabatan(foundCustomUserID.getJabatan());
        customUserIDModel.setUnitKerja(foundCustomUserID.getUnitKerja());

        return customUserIDModel;
    }

    public Optional<MsCustomUserID> findByNikAndParamDetailId(String nik, String paramDetailId) {
        return customUserIDRepository.findByNikAndParamDetailId(nik, paramDetailId);
    }

    public ResponseModel<ResCUDUserIdModel> saveCustomUserID(UserIDModel userIDModel, String paramDetailId) {
        MsCustomUserID newCustomUserID = userIDModel.toMsCustomUserID(paramDetailId);
        MsCustomUserID savedCustomUserID = customUserIDRepository.save(newCustomUserID);

        ResCUDUserIdModel responseDetails = Mapper.toResCUDUserIdModel(ADD, savedCustomUserID.getNik());

        return ResponseModel.<ResCUDUserIdModel>builder().type(TYPE_MS_CUSTOM_USERID_MANAGEMENT_ADD_EDIT).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    public ResponseModel<ResCUDUserIdModel> updateCustomUserID(MsCustomUserID msCustomUserID, UserIDModel details) {
        MsCustomUserID updatedCustomUserID = updateDataCustomUserID(msCustomUserID, details);
        MsCustomUserID savedCustomUserID = customUserIDRepository.save(updatedCustomUserID);

        ResCUDUserIdModel responseDetails = Mapper.toResCUDUserIdModel(EDIT, savedCustomUserID.getNik());

        return ResponseModel.<ResCUDUserIdModel>builder().type(TYPE_MS_CUSTOM_USERID_MANAGEMENT_ADD_EDIT).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    private MsCustomUserID updateDataCustomUserID(MsCustomUserID msCustomUserID, UserIDModel userIDModel) {
        msCustomUserID.setNamaUser(userIDModel.getNamaUser());
        msCustomUserID.setKewenangan(userIDModel.getKewenangan());
        msCustomUserID.setJabatan(userIDModel.getJabatan());
        msCustomUserID.setUnitKerja(userIDModel.getUnitKerja());

        return msCustomUserID;
    }

    public ResponseModel<ResCUDUserIdModel> deleteCustomUserID(MsCustomUserID foundCustomUserID) {
        customUserIDRepository.delete(foundCustomUserID);

        ResCUDUserIdModel response = Mapper.toResCUDUserIdModel(DELETE, foundCustomUserID.getNik());

        return ResponseModel.<ResCUDUserIdModel>builder().type(TYPE_MS_CUSTOM_USERID_MANAGEMENT_DELETE).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(response).build();
    }

    public ResponseModel<ResBatchUserId> saveBatchCustomUserID(ReqUserIdBatchModel<MsCustomUserID> request, String paramDetailId, String nikRequester) throws Exception {
        deleteAllCustomUserIDByParamDetailId(paramDetailId);

        List<MsCustomUserID> customUserIDs = setParamDetailIdToAll(request.getData(), paramDetailId);
        customUserIDRepository.saveAll(customUserIDs);

        saveTrxUserIDBatch(request, nikRequester);

        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(paramDetailId, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_CUSTOM_USERID_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsCustomUserID> msCustomUserIDList) {
        return msCustomUserIDList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveTrxUserIDBatch(ReqUserIdBatchModel<MsCustomUserID> request, String nikRequester) {
        userIdBatchRepository.save(Mapper.toTrxUserIdBatch(request.getBatchId(), request.getFileName(), request.getTotalData(), request.getType(), nikRequester, employeeService.getEmployeeOrVendor(nikRequester)));
    }

    private static List<MsCustomUserID> setParamDetailIdToAll(List<MsCustomUserID> customUserIDs, String paramDetailId) {
        for (MsCustomUserID customUserID : customUserIDs) {
            customUserID.setParamDetailId(paramDetailId);
        }
        return customUserIDs;
    }

    private void deleteAllCustomUserIDByParamDetailId(String paramDetailId) {
        customUserIDRepository.deleteAllByParamDetailId(paramDetailId);
    }

    public ResponseModel<ResUploadModel> generateExcelMsCustomUserID(String paramDetailId) throws Exception {
        List<MsCustomUserID> customUserIDs = customUserIDRepository.findAllByParamDetailId(paramDetailId, Pageable.unpaged()).getContent();

        String customUserIDFileName = generateUserIDFileName(paramDetailId);
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDModelList(customUserIDs), customUserIDFileName);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(customUserIDFileName, XLSX_EXTENSION), customUserIDFileName);

        return Mapper.buildResponse(TYPE_MS_CUSTOM_USERID_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    private String generateUserIDFileName(String paramDetailId) {
        return userIDApplicationService.getAppDesc(paramDetailId).toLowerCase();
    }

    public ResFileDownload directDownloadExcelMsCustomSUserId(String paramDetailId) throws Exception {
        List<MsCustomUserID> customUserIDs = customUserIDRepository.findAllByParamDetailId(paramDetailId, Pageable.unpaged()).getContent();

        String customUserIDFileName = generateUserIDFileName(paramDetailId);
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDModelList(customUserIDs), customUserIDFileName);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(customUserIDFileName, XLSX_EXTENSION));
    }
}
