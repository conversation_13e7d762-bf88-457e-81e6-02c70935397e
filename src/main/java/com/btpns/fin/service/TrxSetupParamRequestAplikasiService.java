package com.btpns.fin.service;

import com.btpns.fin.model.entity.TrxSetupParamRequestAplikasi;
import com.btpns.fin.repository.ITrxSetupParamRequestAplikasiRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
public class TrxSetupParamRequestAplikasiService {
    @Autowired
    ITrxSetupParamRequestAplikasiRepository iTrxSetupParamRequestAplikasiRepository;

    @Transactional
    public TrxSetupParamRequestAplikasi saveTrxSetupParamRequestAplikasi (TrxSetupParamRequestAplikasi tsprap){
        return iTrxSetupParamRequestAplikasiRepository.save(tsprap);
    }

    @Transactional
    public int deleteTrxSetupParamRequestAplikasiByTicketId (String ticketId){
        return iTrxSetupParamRequestAplikasiRepository.deleteTrxSetupParamRequestAplikasiByTicketId(ticketId);
    }

    @Transactional
    public int updateTrxFuidRequestAplikasiByTicketId(String ticketId, String periodDateDone, String periodMonthDone) {
        return iTrxSetupParamRequestAplikasiRepository.updateTrxSetupParamRequestRequestAplikasiByTicketId(ticketId, periodDateDone, periodMonthDone);
    }
}
