package com.btpns.fin.service;

import com.btpns.fin.model.entity.TrxUserIdBatch;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class TrxUserIdBatchService {
    @Autowired
    ITrxUserIdBatchRepository trxUserIdBatchRepository;

    public boolean existInInterval(String nik, String type, int interval) {
        return trxUserIdBatchRepository.checkRequestInterval(LocalDateTime.now().minusSeconds(interval), LocalDateTime.now(), nik, type) > 0;
    }

    public TrxUserIdBatch getUserIdBatchByBatchId(String batchId) {
        return trxUserIdBatchRepository.getTrxUserIdBatchByBatchId(batchId);
    }
}
