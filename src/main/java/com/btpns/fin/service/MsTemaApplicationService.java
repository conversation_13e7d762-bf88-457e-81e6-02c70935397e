package com.btpns.fin.service;

import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.MsTemaApplicationModel;
import com.btpns.fin.model.entity.MsTemaApplication;
import com.btpns.fin.model.request.ReqTemaApplicationModel;
import com.btpns.fin.model.response.ResTemaApplicationDataListModel;
import com.btpns.fin.model.response.ResTemaApplicationDataModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsTemaApplicationRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class MsTemaApplicationService {
    @Autowired
    IMsTemaApplicationRepository iMsTemaApplicationRepository;

    public MsTemaApplication getMsTemaApplication(String paramDetailId){
        return iMsTemaApplicationRepository.getMsTemaApplication(paramDetailId);
    }

    public List<String> getMsTemaApplicationList(List<String> paramDetailIds) {
        return iMsTemaApplicationRepository.getMsTemaApplicationList(paramDetailIds);
    }

    public Map<String, MsTemaApplication> getMsTemaApplicationMap(List<String> paramIds) {
        if (paramIds.size() > 0) {
            return iMsTemaApplicationRepository.getMsTemaApplicationByParamId(paramIds)
                    .stream()
                    .collect(Collectors.toMap(MsTemaApplication::getParamDetailId, Function.identity()));
        }
        return Collections.emptyMap();
    }

    public Map<String, MsTemaApplication> getMsTemaApplicationWithParamDetailIdDescKeyMap(List<String> paramIds) {
        if (paramIds.size() > 0) {
            return iMsTemaApplicationRepository.getMsTemaApplicationByParamId(paramIds)
                    .stream()
                    .collect(Collectors.toMap(MsTemaApplication::getParamDetailDesc, Function.identity()));
        }
        return Collections.emptyMap();
    }

    public ResponseModel<ResTemaApplicationDataListModel> getListTemaApplication(int pageNumMin1, Integer pageSize, Integer pageNumber, String applicationType, String searchFlag, String searchData) {
        Page<MsTemaApplication> temaApplicationDataPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(applicationType)){
            if (SEARCH_FLAG_APLIKASI_FUID.equalsIgnoreCase(applicationType)){
                temaApplicationDataPageable = getApplicationTypePageable(KODE_APLIKASI_FUID, searchFlag, searchData, pageNumMin1, pageSize);
            }else if (SEARCH_FLAG_APLIKASI_SETUP_PARAMETER.equalsIgnoreCase(applicationType)){
                temaApplicationDataPageable = getApplicationTypePageable(KODE_APLIKASI_SETUP_PARAM, searchFlag, searchData, pageNumMin1, pageSize);
            }
        }else {
            temaApplicationDataPageable = iMsTemaApplicationRepository.findAllApplicationTypePageable(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM), PageRequest.of(pageNumMin1, pageSize));
        }

        return buildResponse(TYPE_TEMA_APPLICATON_MANAGEMENT_GET_LIST, SUCCESS, pageSize, pageNumber, temaApplicationDataPageable.getContent(), temaApplicationDataPageable.getTotalPages(), temaApplicationDataPageable.getTotalElements());
    }

    private Page<MsTemaApplication> getApplicationTypePageable(String applicationCode, String searchFlag, String searchData, int pageNumMin1, Integer pageSize) {
        Page<MsTemaApplication> temaApplicationDataPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if (SEARCH_FLAG_PARAM_DETAIL_ID.equalsIgnoreCase(searchFlag)){
                temaApplicationDataPageable = iMsTemaApplicationRepository.findAllByParamDetailIdPageable(applicationCode, searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_PARAM_DETAIL_DESC.equalsIgnoreCase(searchFlag)){
                temaApplicationDataPageable = iMsTemaApplicationRepository.findAllByParamDetailDescPageable(applicationCode, searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            temaApplicationDataPageable = iMsTemaApplicationRepository.findAllApplicationTypePageable(Arrays.asList(applicationCode), PageRequest.of(pageNumMin1, pageSize));
        }

        return temaApplicationDataPageable;
    }

    private ResponseModel<ResTemaApplicationDataListModel> buildResponse(String type, ResponseStatus status, Integer pageSize, Integer pageNumber, List<MsTemaApplication> msTemaApplicationList, int totalPages, long totalItems) {
        ResponseModel<ResTemaApplicationDataListModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(buildResTemaApplicationDataListModel(pageSize, pageNumber, msTemaApplicationList, totalPages, totalItems));

        return response;
    }

    private ResTemaApplicationDataListModel buildResTemaApplicationDataListModel(Integer pageSize, Integer pageNumber, List<MsTemaApplication> msTemaApplicationList, int totalPages, long totalItems) {
        ResTemaApplicationDataListModel resTemaApplicationDataListModel = new ResTemaApplicationDataListModel();

        resTemaApplicationDataListModel.setApplicationTemaDetail(mapToMsTemaApplicationDataModelList(msTemaApplicationList));
        resTemaApplicationDataListModel.setLimit(pageSize);
        resTemaApplicationDataListModel.setPage(pageNumber);
        resTemaApplicationDataListModel.setTotalPages(totalPages);
        resTemaApplicationDataListModel.setTotalItems(totalItems);

        return resTemaApplicationDataListModel;
    }

    private List<MsTemaApplicationModel> mapToMsTemaApplicationDataModelList(List<MsTemaApplication> msTemaApplicationList) {
        List<MsTemaApplicationModel> msTemaApplicationDataModelList = new ArrayList<>();

        msTemaApplicationList.forEach(data -> {
            msTemaApplicationDataModelList.add(mapToMsTemaApplicationModel(data));
        });

        return msTemaApplicationDataModelList;
    }

    private MsTemaApplicationModel mapToMsTemaApplicationModel(MsTemaApplication msTemaApplication) {
        MsTemaApplicationModel msTemaApplicationModel = new MsTemaApplicationModel();

        msTemaApplicationModel.setParamId(msTemaApplication.getParamId());
        msTemaApplicationModel.setParamDetailId(msTemaApplication.getParamDetailId());
        msTemaApplicationModel.setParamDetailDesc(msTemaApplication.getParamDetailDesc());
        msTemaApplicationModel.setHoApplicationStatus(msTemaApplication.getHoApplicationStatus());
        msTemaApplicationModel.setBranchApplicationStatus(msTemaApplication.getBranchApplicationStatus());
        msTemaApplicationModel.setMmsApplicationStatus(msTemaApplication.getMmsApplicationStatus());

        return msTemaApplicationModel;
    }

    public ResponseModel<MsTemaApplicationModel> getTemaApplicationByParamDetailId(String paramDetailId) {
        ResponseModel<MsTemaApplicationModel> response = new ResponseModel<>();

        MsTemaApplication msTemaApplication = iMsTemaApplicationRepository.findByParamDetailId(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM), paramDetailId);
        if (msTemaApplication != null){
            response = buildResponse(TYPE_TEMA_APPLICATON_MANAGEMENT_GET, SUCCESS, mapToMsTemaApplicationModel(msTemaApplication));
        }

        return response;
    }

    private ResponseModel<MsTemaApplicationModel> buildResponse(String type, ResponseStatus status, MsTemaApplicationModel msTemaApplicationModel) {
        ResponseModel<MsTemaApplicationModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(msTemaApplicationModel);

        return response;
    }

    public ResponseModel<ResTemaApplicationDataModel> updateTemaApplication(ReqTemaApplicationModel request) {
        ResponseModel<ResTemaApplicationDataModel> response = new ResponseModel<>();

        MsTemaApplication updatedMsTemaApplication = updateApplicationTypeData(request);
        if (updatedMsTemaApplication != null){
            response = buildResponseAddorEdit(TYPE_TEMA_APPLICATON_MANAGEMENT_ADD_EDIT, SUCCESS, buildResTemaApplicationDataModel(updatedMsTemaApplication, EDIT));
        }

        return response;
    }

    private MsTemaApplication updateApplicationTypeData(ReqTemaApplicationModel request) {
        MsTemaApplication msTemaApplication = iMsTemaApplicationRepository.findByParamDetailId(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM), request.getParamDetailId());

        if (msTemaApplication != null){
            msTemaApplication = iMsTemaApplicationRepository.save(mapToMsTemaApplicationModel(msTemaApplication, request));
        }

        return msTemaApplication;
    }

    private ResponseModel<ResTemaApplicationDataModel> buildResponseAddorEdit(String type, ResponseStatus status, ResTemaApplicationDataModel resTemaApplicationDataModel) {
        ResponseModel<ResTemaApplicationDataModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resTemaApplicationDataModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    private ResTemaApplicationDataModel buildResTemaApplicationDataModel(MsTemaApplication msTemaApplication, String type) {
        ResTemaApplicationDataModel resTemaApplicationDataModel = new ResTemaApplicationDataModel();

        resTemaApplicationDataModel.setType(type);
        resTemaApplicationDataModel.setParamDetailId(msTemaApplication.getParamDetailId());
        resTemaApplicationDataModel.setHoApplicationStatus(msTemaApplication.getHoApplicationStatus());
        resTemaApplicationDataModel.setBranchApplicationStatus(msTemaApplication.getBranchApplicationStatus());
        resTemaApplicationDataModel.setMmsApplicationStatus(msTemaApplication.getMmsApplicationStatus());

        return resTemaApplicationDataModel;
    }

    private MsTemaApplication mapToMsTemaApplicationModel(MsTemaApplication msTemaApplication, ReqTemaApplicationModel request) {
        MsTemaApplication updatedData = msTemaApplication;

        updatedData.setParamDetailDesc(request.getParamDetailDesc());
        updatedData.setHoApplicationStatus(getStatusApplication(request.getIsHo()));
        updatedData.setBranchApplicationStatus(getStatusApplication(request.getIsBranch()));
        if (!KODE_APLIKASI_SETUP_PARAM.equalsIgnoreCase(request.getParamId())){
            updatedData.setMmsApplicationStatus(getStatusApplication(request.getIsMms()));
        }

        return updatedData;
    }

    private String getStatusApplication(Integer isChecked) {
        String applicationStatus = STATUS_APPLICATION_INACTIVE;
        if (TRUE_FLAG_INT.equals(isChecked)){
            applicationStatus = STATUS_APPLICATION_ACTIVE;
        }
        return applicationStatus;
    }

    public ResponseModel<ResTemaApplicationDataModel> saveTemaApplication(ReqTemaApplicationModel request) {
        ResponseModel<ResTemaApplicationDataModel> response = new ResponseModel<>();

        MsTemaApplication savedMsTemaApplication = saveTemaApplicationData(request);
        if (savedMsTemaApplication != null){
            response = buildResponseAddorEdit(TYPE_TEMA_APPLICATON_MANAGEMENT_ADD_EDIT, SUCCESS, buildResTemaApplicationDataModel(savedMsTemaApplication, ADD));
        }

        return response;
    }

    private MsTemaApplication saveTemaApplicationData(ReqTemaApplicationModel request) {
        return iMsTemaApplicationRepository.save(mapToMsTemaApplication(request));
    }

    private MsTemaApplication mapToMsTemaApplication(ReqTemaApplicationModel request) {
        MsTemaApplication msTemaApplication = new MsTemaApplication();

        msTemaApplication.setParamId(request.getParamId());
        msTemaApplication.setParamDetailId(request.getParamDetailId());
        msTemaApplication.setParamDetailDesc(request.getParamDetailDesc());
        msTemaApplication.setHoApplicationStatus(getStatusApplication(request.getIsHo()));
        msTemaApplication.setBranchApplicationStatus(getStatusApplication(request.getIsBranch()));
        msTemaApplication.setMmsApplicationStatus(getStatusApplication(request.getIsMms()));
        if (KODE_APLIKASI_SETUP_PARAM.equalsIgnoreCase(request.getParamId())){
            msTemaApplication.setMmsApplicationStatus(STATUS_APPLICATION_INACTIVE);
        }

        return msTemaApplication;
    }
}
