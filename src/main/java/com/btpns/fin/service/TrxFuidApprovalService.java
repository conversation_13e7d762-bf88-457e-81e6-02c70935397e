package com.btpns.fin.service;

import com.btpns.fin.model.entity.TrxFuidApproval;
import com.btpns.fin.repository.ITrxFuidApprovalRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

@Service
public class TrxFuidApprovalService {
    @Autowired
    ITrxFuidApprovalRepository trxFuidApprovalRepository;

    @Transactional
    public TrxFuidApproval saveTrxFuidApproval(TrxFuidApproval trxFuidApproval) {
        TrxFuidApproval tfa = null;
        Optional<TrxFuidApproval> existTrxFuidApproval = trxFuidApprovalRepository.getTrxFuidApprovalByTicketId(trxFuidApproval.getTicketId());
        if (!existTrxFuidApproval.isPresent()) {
            tfa = trxFuidApprovalRepository.save(trxFuidApproval);
        }
        return tfa;
    }

    @Transactional
    public TrxFuidApproval updateTrxFuidApproval(TrxFuidApproval trxFuidApproval) {
        Optional<TrxFuidApproval> existTrxFuidApproval = trxFuidApprovalRepository.getTrxFuidApprovalByTicketId(trxFuidApproval.getTicketId());
        if (existTrxFuidApproval.isPresent()) {
            /*trxFuidApproval.setCurrentState(existTrxFuidApproval.get().getCurrentState());
            trxFuidApproval.setCurrentStateDT(existTrxFuidApproval.get().getCurrentStateDT());*/
            trxFuidApprovalRepository.deleteById(existTrxFuidApproval.get().getId());
        }
        return trxFuidApprovalRepository.save(trxFuidApproval);
    }

    public TrxFuidApproval getTrxFuidApprovalByTicketId(String ticketId){
        Optional<TrxFuidApproval> existTfa = trxFuidApprovalRepository.getTrxFuidApprovalByTicketId(ticketId);
        TrxFuidApproval tfa = new TrxFuidApproval();
        if(existTfa.isPresent()){
            tfa.setId(existTfa.get().getId());
            tfa.setTicketId(existTfa.get().getTicketId());
            tfa.setCurrentState(existTfa.get().getCurrentState());
            tfa.setCurrentStateDT(existTfa.get().getCurrentStateDT());
            String upmInputAttachment = "[]";
            if(existTfa.get().getUpmInputAttachment() != null){
                upmInputAttachment = existTfa.get().getUpmInputAttachment();
            }
            tfa.setUpmInputAttachment(upmInputAttachment);
            if(existTfa.get().getPuk1NIK() != null){
                tfa.setPuk1NIK(existTfa.get().getPuk1NIK());
                tfa.setPuk1Name(existTfa.get().getPuk1Name());
                tfa.setPuk1Occupation(existTfa.get().getPuk1Occupation());
                tfa.setPuk1Notes(existTfa.get().getPuk1Notes());
                tfa.setPuk1Dt(existTfa.get().getPuk1Dt());
                tfa.setPuk1Status(existTfa.get().getPuk1Status());
                tfa.setPuk1DelegationId(existTfa.get().getPuk1DelegationId());
            }
            if(existTfa.get().getPuk2NIK() != null){
                tfa.setPuk2NIK(existTfa.get().getPuk2NIK());
                tfa.setPuk2Name(existTfa.get().getPuk2Name());
                tfa.setPuk2Occupation(existTfa.get().getPuk2Occupation());
                tfa.setPuk2Notes(existTfa.get().getPuk2Notes());
                tfa.setPuk2Dt(existTfa.get().getPuk2Dt());
                tfa.setPuk2Status(existTfa.get().getPuk2Status());
                tfa.setPuk2DelegationId(existTfa.get().getPuk2DelegationId());
            }
            if(existTfa.get().getUpmInputNIK() != null){
                tfa.setUpmInputNIK(existTfa.get().getUpmInputNIK());
                tfa.setUpmInputNotes(existTfa.get().getUpmInputNotes());
                tfa.setUpmInputDt(existTfa.get().getUpmInputDt());
                tfa.setUpmInputStatus(existTfa.get().getUpmInputStatus());
            }
            if(existTfa.get().getUpmCheckerNIK() != null){
                tfa.setUpmCheckerNIK(existTfa.get().getUpmCheckerNIK());
                tfa.setUpmCheckerNotes(existTfa.get().getUpmCheckerNotes());
                tfa.setUpmCheckerDt(existTfa.get().getUpmCheckerDt());
                tfa.setUpmCheckerStatus(existTfa.get().getUpmCheckerStatus());
            }
        }
        return tfa;
    }

    @Transactional
    public int updateTrxFuidApprovalPUK1ByTicketId(String ticketId, String puk1Nik, String puk1DelegationId){
        return trxFuidApprovalRepository.updateTrxFuidApprovalPUK1ByTicketId(ticketId, puk1Nik, puk1DelegationId);
    }

    @Transactional
    public int updateTrxFuidApprovalPUK2ByTicketId(String ticketId, String puk2Nik, String puk2DelegationId){
        return trxFuidApprovalRepository.updateTrxFuidApprovalPUK2ByTicketId(ticketId, puk2Nik, puk2DelegationId);
    }
}
