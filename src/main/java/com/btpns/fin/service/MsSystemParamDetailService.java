package com.btpns.fin.service;

import com.btpns.fin.model.AplikasiModel;
import com.btpns.fin.model.DataAplikasiModel;
import com.btpns.fin.model.entity.MsProsperaRole;
import com.btpns.fin.model.entity.MsSystemParamDetail;
import com.btpns.fin.model.entity.MsTemaApplication;
import com.btpns.fin.repository.IMsProsperaRoleRepository;
import com.btpns.fin.repository.IMsSystemParamDetailRepository;
import com.btpns.fin.repository.IMsTemaApplicationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;

@Service
public class MsSystemParamDetailService {
    @Autowired
    IMsSystemParamDetailRepository iMsSystemParamDetailRepository;

    @Autowired
    IMsTemaApplicationRepository iMsTemaApplicationRepository;

    @Autowired
    private IMsProsperaRoleRepository iMsProsperaRoleRepository;

    public AplikasiModel getMsSystemParamData(String data, Boolean isHo, Boolean isBranch, Boolean isMms, Boolean isKewenanganLimit, String status) {
        AplikasiModel result = new AplikasiModel();

        List<String> paramIds = getParamId(data, isHo, isBranch, isMms);
        if (status.isBlank() || STATUS_APPLICATION_ALL.equalsIgnoreCase(status)){
            result = buildAplikasModel(paramIds);
        }else if (STATUS_APPLICATION_ACTIVE.equalsIgnoreCase(status)){
            if (paramIds.contains(KODE_APLIKASI_FUID) || paramIds.contains(KODE_APLIKASI_SETUP_PARAM)){
                result = buildAplikasModelActiveByOfficeType(paramIds, isHo, isBranch, isMms);
            } else if (TRUE_FLAG_BOOL.equals(isKewenanganLimit)){
                result = buildAplikasModelActiveByKewenanganLimit(paramIds);
            } else {
                result = buildAplikasModelActive(paramIds);
            }
        }

        return result;
    }

    private AplikasiModel buildAplikasModel(List<String> paramIds) {
        AplikasiModel am = new AplikasiModel();
        List<DataAplikasiModel> dams = new ArrayList<DataAplikasiModel>();

        if (paramIds.contains(KODE_APLIKASI_FUID) || paramIds.contains(KODE_APLIKASI_SETUP_PARAM)){
            List<MsTemaApplication> msTemaApplicationList = iMsTemaApplicationRepository.getMsTemaApplicationByParamId(paramIds);

            msTemaApplicationList.forEach(data -> {
                dams.add(buildDataAplikasiModel(data));
            });
        } else if (paramIds.contains(KODE_ROLE_HO) && paramIds.contains(KODE_ROLE_CABANG) && paramIds.contains(KODE_ROLE_MMS) && paramIds.size() == 3) {
            List<MsProsperaRole> msProsperaRoleList = this.iMsProsperaRoleRepository.findAll();

            buildThenAddEachModel(dams, msProsperaRoleList);
        } else if (paramIds.contains(KODE_ROLE_MMS) || paramIds.contains(KODE_ROLE_CABANG) || paramIds.contains(KODE_ROLE_HO)) {
            List<MsProsperaRole> msProsperaRoleList = this.iMsProsperaRoleRepository.findBySystemParamIdIn(paramIds);

            buildThenAddEachModel(dams, msProsperaRoleList);
        } else {
            List<MsSystemParamDetail> msSystemParamDetailList = iMsSystemParamDetailRepository.getMsSystemParamDetailByParamId(paramIds);

            msSystemParamDetailList.forEach(data -> {
                dams.add(buildDataAplikasiModel(data));
            });
        }
        am.setAplikasi(dams);

        return am;
    }

    private static void buildThenAddEachModel(List<DataAplikasiModel> dams, List<MsProsperaRole> msProsperaRoleList) {
        msProsperaRoleList.forEach(data -> {
            dams.add(buildDataAplikasiModel(data));
        });
    }

    private AplikasiModel buildAplikasModelActiveByOfficeType(List<String> paramIds, Boolean isHo, Boolean isBranch, Boolean isMms) {
        AplikasiModel am = new AplikasiModel();
        List<DataAplikasiModel> dams = new ArrayList<DataAplikasiModel>();
        List<MsTemaApplication> msTemaApplicationList = iMsTemaApplicationRepository.getMsTemaApplicationByParamId(paramIds);

        msTemaApplicationList.forEach(data -> {
            if (TRUE_FLAG_BOOL.equals(isHo) && STATUS_APPLICATION_ACTIVE.equalsIgnoreCase(data.getHoApplicationStatus())){
                dams.add(buildDataAplikasiModel(data));
            }else if (TRUE_FLAG_BOOL.equals(isBranch) && STATUS_APPLICATION_ACTIVE.equalsIgnoreCase(data.getBranchApplicationStatus())){
                dams.add(buildDataAplikasiModel(data));
            }else if (TRUE_FLAG_BOOL.equals(isMms) && STATUS_APPLICATION_ACTIVE.equalsIgnoreCase(data.getMmsApplicationStatus())){
                dams.add(buildDataAplikasiModel(data));
            }
        });
        am.setAplikasi(dams);

        return am;
    }

    private AplikasiModel buildAplikasModelActiveByKewenanganLimit(List<String> paramIds) {
        AplikasiModel am = new AplikasiModel();
        List<DataAplikasiModel> dams = new ArrayList<DataAplikasiModel>();
        List<MsProsperaRole> msProsperaRoleList = iMsProsperaRoleRepository.findActiveBySystemParamIdIn(paramIds);

        msProsperaRoleList.forEach(data -> {
            if (WHITELIST_ROLE_KEWENANGAN_LIMIT_PROSPERA.contains(data.getRoleDesc())){
                dams.add(buildDataAplikasiModel(data));
            }
        });
        am.setAplikasi(dams);

        return am;
    }

    private AplikasiModel buildAplikasModelActive(List<String> paramIds) {
        AplikasiModel am = new AplikasiModel();
        List<DataAplikasiModel> dams = new ArrayList<DataAplikasiModel>();
        if (isParamIdsContainsRoleCode(paramIds)) {
            if (paramIds.size() == 3 && paramIds.contains(KODE_ROLE_HO) && paramIds.contains(KODE_ROLE_CABANG) && paramIds.contains(KODE_ROLE_MMS)){
                List<MsProsperaRole> msProsperaRoleList = this.iMsProsperaRoleRepository.findAll();
                buildThenAddEachModel(dams, msProsperaRoleList);
            }

            if (paramIds.size() == 2 && paramIds.contains(KODE_ROLE_CABANG) && paramIds.contains(KODE_ROLE_MMS)){
                List<MsProsperaRole> msProsperaRoleList = this.iMsProsperaRoleRepository.findActiveBySystemParamIdIn(paramIds);
                msProsperaRoleList.forEach(data -> {
                    if (WHITELIST_ROLE_EXPIRED_TICKET_PROSPERA_HOKCKFO.contains(data.getRoleDesc())){
                        dams.add(buildDataAplikasiModel(data));
                    }
                });
            }

            if (paramIds.size() == 1 && isParamIdsContainsRoleCode(paramIds)){
                List<MsProsperaRole> msProsperaRoleList = this.iMsProsperaRoleRepository.findActiveBySystemParamIdIn(paramIds);
                buildThenAddEachModel(dams, msProsperaRoleList);
            }
        } else {
            List<MsSystemParamDetail> msSystemParamDetailList = iMsSystemParamDetailRepository.getMsSystemParamDetailByParamId(paramIds);
            msSystemParamDetailList.forEach(data -> {
                if (STATUS_APPLICATION_ACTIVE.equalsIgnoreCase(data.getStatus())){
                    dams.add(buildDataAplikasiModel(data));
                }
            });
        }

        am.setAplikasi(dams);

        return am;
    }

    private boolean isParamIdsContainsRoleCode(List<String> paramIds) {
        return TEMA_ROLE_CODE.stream().anyMatch(paramIds::contains);
    }

    private static DataAplikasiModel buildDataAplikasiModel(MsSystemParamDetail data) {
        DataAplikasiModel dam = new DataAplikasiModel();

        dam.setCode(data.getParamDetailId());
        dam.setDesc(data.getParamDetailDesc());

        return dam;
    }

    private static DataAplikasiModel buildDataAplikasiModel(MsTemaApplication data) {
        DataAplikasiModel dam = new DataAplikasiModel();

        dam.setCode(data.getParamDetailId());
        dam.setDesc(data.getParamDetailDesc());

        return dam;
    }

    private static DataAplikasiModel buildDataAplikasiModel(MsProsperaRole data) {
        DataAplikasiModel dam = new DataAplikasiModel();

        dam.setCode(data.getTemaRoleCode());
        dam.setDesc(data.getRoleDesc());

        return dam;
    }

    private List<String> getParamId(String data, Boolean isHo, Boolean isBranch, Boolean isMms) {
        List<String> paramId = new ArrayList<>();

        if(data.equals(TYPE_SYS_PARAM_APLIKASI_SETUP_PARAM)){
            paramId.add(KODE_APLIKASI_SETUP_PARAM);
        }else if(data.equals(TYPE_SYS_PARAM_KATEGORI_PARAM)){
            paramId.add(KODE_KATEGORI_PARAM);
        }else if (data.equals(TYPE_SYS_PARAM_ROLE)){
            if (TRUE_FLAG_BOOL.equals(isHo) && TRUE_FLAG_BOOL.equals(isBranch) && TRUE_FLAG_BOOL.equals(isMms)){
                paramId.add(KODE_ROLE_HO);
                paramId.add(KODE_ROLE_CABANG);
                paramId.add(KODE_ROLE_MMS);
            }else if (TRUE_FLAG_BOOL.equals(isHo) && TRUE_FLAG_BOOL.equals(isBranch)){
                paramId.add(KODE_ROLE_HO);
                paramId.add(KODE_ROLE_CABANG);
            }else if (TRUE_FLAG_BOOL.equals(isHo)){
                paramId.add(KODE_ROLE_HO);
            }else if (TRUE_FLAG_BOOL.equals(isBranch)){
                paramId.add(KODE_ROLE_CABANG);
            }else if (TRUE_FLAG_BOOL.equals(isMms)){
                paramId.add(KODE_ROLE_MMS);
            }
        }else {
            paramId.add(KODE_APLIKASI_FUID);
        }

        return paramId;
    }
}
