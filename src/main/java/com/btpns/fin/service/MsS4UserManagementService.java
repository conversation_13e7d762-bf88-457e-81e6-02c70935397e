package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsDboRTGS;
import com.btpns.fin.model.entity.MsS4;
import com.btpns.fin.model.entity.TrxUserIdBatch;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMsS4UserManagementRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
@Transactional
public class MsS4UserManagementService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    private IMsS4UserManagementRepository iMsS4UserManagementRepository;

    @Autowired
    private ITrxUserIdBatchRepository iTrxUserIdBatchRepository;

    @Autowired
    private MsEmployeeService msEmployeeService;

    @Autowired
    MinioService minioService;

    @Autowired
    ExcelHelper excelHelper;


    @Autowired
    Mapper mapper;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResponseListModel> getListS4Users(int pageNumMin1, Integer pageNumber, Integer pageSize, String searchFlag, String searchData) {
        Page<MsS4> msS4Users = new PageImpl<>(new ArrayList<>());
        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)) {
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NIK)) {
                msS4Users = this.iMsS4UserManagementRepository.findAllByNik(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NAME)) {
                msS4Users = this.iMsS4UserManagementRepository.findAllByNamaUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        } else {
            msS4Users = this.iMsS4UserManagementRepository.findAll(PageRequest.of(pageNumMin1, pageSize));
        }
        
        return Mapper.buildResponseList(TYPE_MS_S4_MANAGEMENT_GET_LIST, SUCCESS, buildResUserIdListModel(pageSize, pageNumber, msS4Users.getContent(), msS4Users.getTotalPages(), msS4Users.getTotalElements()));
    }

    private ResponseListModel buildResUserIdListModel(Integer pageSize, Integer pageNumber, List<MsS4> msS4, int totalPages, long totalItems) {
        ResponseListModel responseListModel = new ResponseListModel();

        responseListModel.setData(mapToUserIDModelList(msS4));
        responseListModel.setLimit(pageSize);
        responseListModel.setPage(pageNumber);
        responseListModel.setTotalPages(totalPages);
        responseListModel.setTotalItems(totalItems);

        return responseListModel;
    }

    private List<UserIDModel> mapToUserIDModelList(List<MsS4> msS4Users) {
        return msS4Users.stream().map(MsS4::toUserIDModel).collect(Collectors.toList());
    }

    public Optional<MsS4> findByNik(String nik) {
        return this.iMsS4UserManagementRepository.findByNik(nik);
    }

    public ResponseModel<ResCUDUserIdModel> saveS4User(ReqUserIDModel reqUserIDModel) {
        MsS4 msS4User = this.iMsS4UserManagementRepository.save(reqUserIDModel.toS4());

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_S4_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, msS4User.getNik()));
    }

    public ResponseModel<UserIDModel> getS4UserByNik(String nik) {
        UserIDModel userIDModel = new UserIDModel();
        Optional<MsS4> optionalMsS4 = this.findByNik(nik);
        if (optionalMsS4.isPresent()) {
            MsS4 msS4 = optionalMsS4.get();
            userIDModel = msS4.toUserIDModel();
        }

        return ResponseModel.<UserIDModel>builder().type(TYPE_MS_S4_MANAGEMENT_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(userIDModel).build();
    }

    public ResponseModel<ResCUDUserIdModel> updateS4User(ReqUserIDModel reqUserIDModel) {
        MsS4 savedMsS4User = new MsS4();
        Optional<MsS4> optionalMsS4 = this.findByNik(reqUserIDModel.getNik());
        if (optionalMsS4.isPresent()) {
            MsS4 newMsS4User = reqUserIDModel.toS4();
            newMsS4User.setId(optionalMsS4.get().getId());
            newMsS4User.setNik(optionalMsS4.get().getNik());

            savedMsS4User = this.iMsS4UserManagementRepository.save(newMsS4User);

            return Mapper.tobuildResponseCUDUserId(TYPE_MS_S4_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, savedMsS4User.getNik()));
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_S4_MANAGEMENT_ADD_EDIT, FAILED, Mapper.toResCUDUserIdModel(EDIT, reqUserIDModel.getNik()));
    }

    public ResponseModel<ResCUDUserIdModel> deleteS4User(String nik) {
        ResCUDUserIdModel response = Mapper.toResCUDUserIdModel(DELETE, nik);

        ResponseStatus status = FAILED;
        if (this.iMsS4UserManagementRepository.deleteMsS4User(nik) > 0) {
            status = SUCCESS;
        }
        return Mapper.tobuildResponseCUDUserId(TYPE_MS_S4_MANAGEMENT_DELETE, status, response);
    }

    public ResponseModel<ResBatchUserId> saveBatchMsS4User(ReqUserIdBatchModel<MsS4> request, String nikRequester) throws Exception {
        this.iMsS4UserManagementRepository.deleteAll();
        this.saveTrxUserIdBatch(request, nikRequester);
        this.saveBatchMsS4(request.getData());
        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_S4, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_S4_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsS4> msS4List) {
        return msS4List.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<MsS4> request, String nikRequester) {
        TrxUserIdBatch trxUserIdBatch = Mapper.toTrxUserIdBatch(request.getBatchId(),
                request.getFileName(),
                request.getTotalData(),
                request.getType(),
                nikRequester,
                msEmployeeService.getEmployeeOrVendor(nikRequester));
        this.iTrxUserIdBatchRepository.save(trxUserIdBatch);
    }

    private void saveBatchMsS4(List<MsS4> msS4List) {
        this.iMsS4UserManagementRepository.saveAll(msS4List);
    }

    public ResponseModel<ResUploadModel> genereteExcelMsS4UserId() throws Exception {
        List<MsS4> msS4List = iMsS4UserManagementRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDModelList(msS4List), S4_USER_ID_FILE_NAME);

        Map<String, String> result = minioService.uploadFileExcel(excelByte, DocumentHelper.generateReportFilePath(S4_USER_ID_FILE_NAME, XLSX_EXTENSION), S4_USER_ID_FILE_NAME);

        return Mapper.buildResponse(TYPE_MS_S4_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsS4UserId() throws Exception {
        List<MsS4> msS4List = iMsS4UserManagementRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDModelList(msS4List), S4_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(S4_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
