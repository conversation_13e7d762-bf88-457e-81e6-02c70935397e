package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.UserIDModel;
import com.btpns.fin.model.entity.MsCMS;
import com.btpns.fin.model.entity.TrxUploadUserIdAudittrail;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.ICMSUserIdRepository;
import com.btpns.fin.repository.ITrxUserIdBatchRepository;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
@Transactional
public class CMSUserIdService {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    ITrxUserIdBatchRepository trxUserIdBatchRepository;

    @Autowired
    ICMSUserIdRepository icmsUserIdRepository;

    @Autowired
    ExcelHelper excelHelper;

    @Autowired
    Mapper mapper;

    @Autowired
    MinioService minioService;

    @Autowired
    TrxUploadUserIdAudittrailService trxUploadUserIdAudittrailService;

    public ResponseModel<ResBatchUserId> saveBatchMsCMSUserId(ReqUserIdBatchModel<MsCMS> request, String nikRequester) throws Exception {
        List<MsCMS> msCMSList = icmsUserIdRepository.findAll();
        if (!msCMSList.isEmpty() && msCMSList.size() > 0){
            if(deleteMsCMS() > 0){
                saveTrxUserIdBatch(request, nikRequester);
                saveBatchMsCMS(request.getData());
            }
        }else {
            saveTrxUserIdBatch(request, nikRequester);
            saveBatchMsCMS(request.getData());
        }
        trxUploadUserIdAudittrailService.saveTrxUploadUserIdAudittrail(mapper.toTrxUploadUserIdAudittrail(KODE_APLIKASI_USER_ID_CMS, gson.toJson(request.getData())));

        return Mapper.toResBatchUserIdResponseModel(TYPE_MS_CMS_MANAGEMENT_BATCH, SUCCESS, Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()));
    }

    private List<UserIDModel> buildUserIdModel(List<MsCMS> msCMSList) {
        return msCMSList.stream().map(data -> data.toUserIDModel()).collect(Collectors.toList());
    }

    private void saveBatchMsCMS(List<MsCMS> msCMSList) {
        icmsUserIdRepository.saveAll(msCMSList);
    }

    private void saveTrxUserIdBatch(ReqUserIdBatchModel<MsCMS> request, String nikRequester) {
        trxUserIdBatchRepository.save(Mapper.toTrxUserIdBatch(request.getBatchId(), request.getFileName(), request.getTotalData(), request.getType(), nikRequester, msEmployeeService.getEmployeeOrVendor(nikRequester)));
    }

    private int deleteMsCMS() {
        return icmsUserIdRepository.deleteAllMsCMS();
    }

    public ResponseModel<ResponseListModel> getListCMSUsers(int pageNumMin1, Integer pageNumber, Integer pageSize, String searchFlag, String searchData) {
        Page<MsCMS> msCMSUsers = new PageImpl<>(new ArrayList<>());
        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)) {
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NIK)) {
                msCMSUsers = this.icmsUserIdRepository.findAllByNik(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
            if (searchFlag.equalsIgnoreCase(SEARCH_FLAG_NAME)) {
                msCMSUsers = this.icmsUserIdRepository.findAllByNamaUser(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        } else {
            msCMSUsers = this.icmsUserIdRepository.findAll(PageRequest.of(pageNumMin1, pageSize));
        }

        ResponseListModel<UserIDModel> responseListModel = Mapper.buildResUserIdListModel(pageSize, pageNumber, mapToUserIDListModel(msCMSUsers.getContent()), msCMSUsers.getTotalPages(), msCMSUsers.getTotalElements());

        return Mapper.buildResponseList(TYPE_MS_CMS_MANAGEMENT_GET_LIST, SUCCESS, responseListModel);
    }

    private List<UserIDModel> mapToUserIDListModel(List<MsCMS> msCMSUsers) {
        return msCMSUsers.stream().map(MsCMS::toUserIDModel).collect(Collectors.toList());
    }

    public Optional<MsCMS> findByNik(String nik) {
        return this.icmsUserIdRepository.findByNik(nik);
    }

    public ResponseModel<ResCUDUserIdModel> saveCMSUser(ReqUserIDModel reqUserIDModel) {
        MsCMS msCMSUser = this.icmsUserIdRepository.save(reqUserIDModel.toCMS());

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_CMS_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(ADD, msCMSUser.getNik()));
    }

    public ResponseModel<UserIDModel> getCMSUserByNik(String nik) {
        UserIDModel userIDModel = new UserIDModel();
        Optional<MsCMS> optionalMsCMS = this.findByNik(nik);
        if (optionalMsCMS.isPresent()) {
            userIDModel = optionalMsCMS.get().toUserIDModel();
        }

        return ResponseModel.<UserIDModel>builder().type(TYPE_MS_CMS_MANAGEMENT_GET).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(userIDModel).build();
    }

    public ResponseModel<ResCUDUserIdModel> updateCMSUser(ReqUserIDModel reqUserIDModel) {
        MsCMS savedMsCMSUser = new MsCMS();
        Optional<MsCMS> optionalMsCMS = this.findByNik(reqUserIDModel.getNik());
        if (optionalMsCMS.isPresent()) {
            MsCMS newMsCMSUser = reqUserIDModel.toCMS();
            newMsCMSUser.setId(optionalMsCMS.get().getId());
            newMsCMSUser.setNik(optionalMsCMS.get().getNik());

            savedMsCMSUser = this.icmsUserIdRepository.save(newMsCMSUser);

            return Mapper.tobuildResponseCUDUserId(TYPE_MS_CMS_MANAGEMENT_ADD_EDIT, SUCCESS, Mapper.toResCUDUserIdModel(EDIT, savedMsCMSUser.getNik()));
        }

        return Mapper.tobuildResponseCUDUserId(TYPE_MS_CMS_MANAGEMENT_ADD_EDIT, FAILED, Mapper.toResCUDUserIdModel(EDIT, reqUserIDModel.getNik()));
    }

    public ResponseModel<ResCUDUserIdModel> deleteCMSUser(String nik) {
        ResCUDUserIdModel response = Mapper.toResCUDUserIdModel(DELETE, nik);

        ResponseStatus status = FAILED;
        if (this.icmsUserIdRepository.deleteMsCMSUser(nik) > 0) {
            status = SUCCESS;
        }
        return Mapper.tobuildResponseCUDUserId(TYPE_MS_CMS_MANAGEMENT_DELETE, status, response);
    }

    public ResponseModel<ResUploadModel> generateExcelMsCMSUserId() throws Exception {
        List<MsCMS> msCMSList = icmsUserIdRepository.findAll();
        byte[] excelBytes = excelHelper.exportExcelUserID(mapToUserIDListModel(msCMSList), CMS_USER_ID_FILE_NAME);

        Map<String, String> result = minioService.uploadFileExcel(excelBytes, DocumentHelper.generateReportFilePath(CMS_USER_ID_FILE_NAME, XLSX_EXTENSION), CMS_USER_ID_FILE_NAME);
        return Mapper.buildResponse(TYPE_MS_CMS_MANAGEMENT_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadExcelMsCMSUserId() throws Exception {
        List<MsCMS> msCMSList = icmsUserIdRepository.findAll();
        byte[] excelByte = excelHelper.exportExcelUserID(mapToUserIDListModel(msCMSList), CMS_USER_ID_FILE_NAME);

        return new ResFileDownload(excelByte, CommonHelper.concateTwoString(CMS_USER_ID_FILE_NAME, XLSX_EXTENSION));
    }
}
