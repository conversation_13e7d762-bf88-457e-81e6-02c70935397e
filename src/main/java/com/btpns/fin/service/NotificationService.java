package com.btpns.fin.service;

import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.helper.Triwulan;
import com.btpns.fin.model.NotificationMessage;
import com.btpns.fin.model.NotificationModel;
import com.btpns.fin.model.entity.TrxComment;
import com.btpns.fin.model.entity.TrxExpiredFuid;
import com.btpns.fin.model.entity.TrxUARRequest;
import com.btpns.fin.model.entity.TrxUPMReminder;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.model.response.ResponseUpdateReminder;
import com.btpns.fin.repository.ITrxCommentRepository;
import com.btpns.fin.repository.ITrxUARRequestRepository;
import com.btpns.fin.repository.ITrxUPMReminderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static com.btpns.fin.helper.Constants.*;

@Service
public class NotificationService {
    @Autowired
    private UpmTicketService upmTicketService;

    @Autowired
    private ITrxCommentRepository commentRepository;

    @Autowired
    private ITrxUARRequestRepository iTrxUARRequestRepository;

    @Autowired
    private ITrxUPMReminderRepository upmReminderRepository;

    public ResponseModel<NotificationModel> getNotificationStatus(String userType, String nik) {
        NotificationModel notificationModel = buildNotificationModel(userType, nik);

        return buildResponse(ResponseStatus.SUCCESS, notificationModel);
    }

    public NotificationModel buildNotificationModel(String userType, String nik) {
        NotificationModel notificationModel = new NotificationModel();

        if (USER.equalsIgnoreCase(userType)){
            List<TrxUARRequest> trxUAR = iTrxUARRequestRepository.findAllUARByStatusByUserNikOrPukNik(nik);
            notificationModel.setUnconfirmedUAR(trxUAR.size() > 0 ? TRUE_FLAG_BOOL : FALSE_FLAG_BOOL);
        }

        if (UPM.equalsIgnoreCase(userType)){
            List<TrxComment> trxComment = commentRepository.findAllCommentByStatus(COMMENT_STATUS_DELIVER);
            notificationModel.setCommentUnread(trxComment.size() > 0 ? TRUE_FLAG_BOOL : FALSE_FLAG_BOOL );

            List<TrxExpiredFuid> expiredFuids = upmTicketService.getListUnprocessedExpiredFuid();
            notificationModel.setUnprocessedFuid(expiredFuids.size() > 0 ? TRUE_FLAG_BOOL : FALSE_FLAG_BOOL);

            notificationModel.setMonthlyReminder(getReminder(nik,LocalDate.now(), TYPE_MONTHLY_REMINDER, MONTHLY_REMINDER_MESSAGE));
            notificationModel.setQuarterlyReminder(getReminder(nik,LocalDate.now(), TYPE_UAR_REMINDER, getUARReminderMessage(LocalDate.now().getMonthValue())));
        }
        return notificationModel;
    }

    private NotificationMessage getReminder(String nik, LocalDate currentDate, String schedulerKey, String message) {
        int currentMonth = currentDate.getMonthValue();
        int currentYear = currentDate.getYear();

        TrxUPMReminder reminder = findUnnoticedByTypeAndMonthAndYear(nik, schedulerKey, currentMonth, currentYear);
        if (reminder != null) {
            return new NotificationMessage(reminder.getId(), reminder.getType(), message, reminder.isNotified());
        }

        return null;
    }

    private String getUARReminderMessage(int currentMonth) {
        Triwulan triwulan = Triwulan.getTriwulan(currentMonth);

        return "Saatnya Melakukan User Access Review (UAR) untuk Periode " + triwulan.getMonthRange();
    }

    private TrxUPMReminder findUnnoticedByTypeAndMonthAndYear(String nik, String type, int month, int year) {
        Optional<TrxUPMReminder> optionalReminder = upmReminderRepository.findUnnoticedReminderByMonthAndYear(nik, type, month, year);

        return optionalReminder.orElse(null);
    }

    private ResponseModel<NotificationModel> buildResponse(ResponseStatus status, NotificationModel notificationModel) {
        ResponseModel<NotificationModel> response = new ResponseModel<>();

        response.setType(TYPE_NOTIFICATOIN_STATUS_GET);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(notificationModel);

        return response;
    }

    public ResponseModel<ResponseUpdateReminder> updateReminderFlag(BigInteger id) {
        ResponseStatus responseStatus = ResponseStatus.FAILED;

        TrxUPMReminder updatedUPMReminder = new TrxUPMReminder();
        Optional<TrxUPMReminder> optionalReminder = upmReminderRepository.findById(id);
        if (optionalReminder.isPresent()) {
            updatedUPMReminder = updateUPMReminder(optionalReminder.get());

            responseStatus = ResponseStatus.SUCCESS;
        }

        return ResponseModel.<ResponseUpdateReminder>builder().
                type(TYPE_NOTIFICATION_REMINDER_UPDATE)
                .status(responseStatus.getCode())
                .statusDesc(responseStatus.getValue())
                .details(new ResponseUpdateReminder(updatedUPMReminder.getType(), updatedUPMReminder.isNotified()))
                .build();
    }

    private TrxUPMReminder updateUPMReminder(TrxUPMReminder existingUPMReminder) {
        existingUPMReminder.setNotified(true);

        return upmReminderRepository.save(existingUPMReminder);
    }
}
