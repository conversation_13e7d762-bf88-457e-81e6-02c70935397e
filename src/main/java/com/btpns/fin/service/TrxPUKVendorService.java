package com.btpns.fin.service;

import com.btpns.fin.helper.DateTimeHelper;
import com.btpns.fin.helper.Mapper;
import com.btpns.fin.helper.Profile;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.AlihDayaUserModel;
import com.btpns.fin.model.DetailPUKModel;
import com.btpns.fin.model.entity.MsEmployeeHierarchy;
import com.btpns.fin.model.entity.TrxPUKVendorBatch;
import com.btpns.fin.model.request.ReqUserIdBatchModel;
import com.btpns.fin.model.response.*;
import com.btpns.fin.model.entity.MsEmployee;
import com.btpns.fin.model.request.ReqMsDataAlihDayaModel;
import com.btpns.fin.model.entity.TrxPUKVendor;
import com.btpns.fin.repository.IMsEmployeeHierarchyRepository;
import com.btpns.fin.repository.IMsEmployeeRepository;
import com.btpns.fin.repository.ITrxPUKVendorBatchRepository;
import com.btpns.fin.repository.ITrxPUKVendorRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service @Transactional
public class TrxPUKVendorService {
    @Autowired
    ITrxPUKVendorRepository trxPUKVendorRepository;

    @Autowired
    IMsEmployeeRepository iMsEmployeeRepository;

    @Autowired
    IMsEmployeeHierarchyRepository iMsEmployeeHierarchyRepository;

    @Autowired
    ITrxPUKVendorBatchRepository iTrxPUKVendorBatchRepository;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Transactional
    public TrxPUKVendor saveTrxPUKVendor(TrxPUKVendor trxPUKVendor){
        return trxPUKVendorRepository.save(trxPUKVendor);
    }

    @Transactional
    public TrxPUKVendor updateTrxPUKVendor(TrxPUKVendor trxPUKVendor){
        TrxPUKVendor savedTPV = trxPUKVendorRepository.findByNikVendor(trxPUKVendor.getNikVendor());
        if(savedTPV != null){
            trxPUKVendor.setCreateDatetime(savedTPV.getCreateDatetime());
            trxPUKVendorRepository.deleteTrxPUKVendor(Collections.singletonList(trxPUKVendor.getNikVendor()));
        }
        return trxPUKVendorRepository.save(trxPUKVendor);
    }

    public TrxPUKVendor findByNikVendor(String nikVendor){
        return trxPUKVendorRepository.findByNikVendor(nikVendor);
    }

    public List<TrxPUKVendor> getTrxPUKVendor(List<String> nik) {
        return trxPUKVendorRepository.getTrxPUKVendor(nik);
    }

    public List<MsEmployee> getListPUKVendor(@Param("nik") String nik){
        List<MsEmployee> listPUK = new ArrayList<MsEmployee>();

        TrxPUKVendor trxPUKVendor = trxPUKVendorRepository.findByNikVendor(nik);
        nik = trxPUKVendor.getNikPUK();

        while(!nik.equals("0")){
            MsEmployee msEmployee = iMsEmployeeRepository.getMsEmployeeByNik(nik);
            if(msEmployee != null) {
                listPUK.add(msEmployee);
                nik = msEmployee.getDirectSupervisorNIK();
            } else {
                nik = "0";
            }
        }
        return listPUK;
    }

    public ResponseModel<ResponseListModel<AlihDayaUserModel>> getListTrxPUKVendor(int pageNumMin1, Integer pageSize, Integer pageNumber, String searchFlag, String searchData) {
        Page<TrxPUKVendor> trxPUKVendorPageable = new PageImpl<>(new ArrayList<>());

        if (StringUtils.isNotBlank(searchFlag) && StringUtils.isNotBlank(searchData)){
            if(SEARCH_FLAG_NIK.equalsIgnoreCase(searchFlag)){
                trxPUKVendorPageable = trxPUKVendorRepository.findAllByNikVendor(searchData, PageRequest.of(pageNumMin1, pageSize));
            }else if (SEARCH_FLAG_NAME.equalsIgnoreCase(searchFlag)){
                trxPUKVendorPageable = trxPUKVendorRepository.findAllByNameVendor(searchData, PageRequest.of(pageNumMin1, pageSize));
            }
        }else {
            trxPUKVendorPageable = trxPUKVendorRepository.getListTrxPUKVendor(PageRequest.of(pageNumMin1, pageSize));
        }

        return buildResponse(SUCCESS, pageSize, pageNumber, trxPUKVendorPageable.getContent(), trxPUKVendorPageable.getTotalPages(), trxPUKVendorPageable.getTotalElements());
    }

    private ResponseModel<ResponseListModel<AlihDayaUserModel>> buildResponse(ResponseStatus status, Integer pageSize, Integer pageNumber, List<TrxPUKVendor> trxPUKVendor, int totalPages, long totalItems) {
        ResponseModel<ResponseListModel<AlihDayaUserModel>> response = new ResponseModel<>();

        response.setType(TYPE_ALIH_DAYA_MANAGEMENT_USER_GET_LIST);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(buildResAlihDayaUserListModel(pageSize, pageNumber, trxPUKVendor, totalPages, totalItems));

        return response;
    }

    private ResponseListModel<AlihDayaUserModel> buildResAlihDayaUserListModel(Integer pageSize, Integer pageNumber, List<TrxPUKVendor> trxPUKVendor, int totalPages, long totalItems) {
        return ResponseListModel.<AlihDayaUserModel>builder()
                .data(mapToAlihDayaUserModel(trxPUKVendor))
                .limit(pageSize)
                .page(pageNumber)
                .totalPages(totalPages)
                .totalItems(totalItems)
                .build();
    }

    private List<AlihDayaUserModel> mapToAlihDayaUserModel(List<TrxPUKVendor> trxPUKVendor) {
        List<AlihDayaUserModel> alihDayaUserModelList = new ArrayList<>();

        trxPUKVendor.forEach(tpv -> {
            AlihDayaUserModel alihDayaUserModel = new AlihDayaUserModel();

            alihDayaUserModel.setNik(tpv.getNikVendor());
            alihDayaUserModel.setNama(tpv.getNameVendor());
            alihDayaUserModel.setJabatan(tpv.getOccupationVendor());
            alihDayaUserModel.setMasaBerlakuSampai(DateTimeHelper.getDateToDateStringDDMMYYYY(tpv.getMasaBerlakuSampai()));
            alihDayaUserModel.setPukVendor(mapToDetailPUKModel(tpv.getNikPUK()));

            alihDayaUserModelList.add(alihDayaUserModel);
        });

        return alihDayaUserModelList;
    }

    private DetailPUKModel mapToDetailPUKModel(String nikPUK) {
        MsEmployee msEmployee = iMsEmployeeRepository.getMsEmployeeByNik(nikPUK);

        DetailPUKModel detailPUKModel = new DetailPUKModel();

        if(msEmployee != null){
            detailPUKModel.setNikPUK(msEmployee.getNik());
            detailPUKModel.setNamaPUK(msEmployee.getFullName());
            detailPUKModel.setJabatanPUK(msEmployee.getOccupation());
        }else {
            MsEmployeeHierarchy msEmployeeHierarchy = iMsEmployeeHierarchyRepository.getMsEmployeeHierarchyByNik(nikPUK);

            detailPUKModel.setNikPUK(msEmployeeHierarchy.getNik());
            detailPUKModel.setNamaPUK(msEmployeeHierarchy.getFullName());
            detailPUKModel.setJabatanPUK(msEmployeeHierarchy.getOccupationDesc());
        }

        return detailPUKModel;
    }

    public ResponseModel<AlihDayaUserModel> getTrxPUKVendorByNik(String nik) {
        TrxPUKVendor trxPUKVendor = trxPUKVendorRepository.findByNikVendor(nik);
        AlihDayaUserModel alihDayaUserModel = mapToAlihDayaUserModel(trxPUKVendor, nik);

        return buildResponse(SUCCESS, alihDayaUserModel);
    }

    private AlihDayaUserModel mapToAlihDayaUserModel(TrxPUKVendor trxPUKVendor, String nik) {
        AlihDayaUserModel alihDayaUserModel = new AlihDayaUserModel();

        if (trxPUKVendor == null){
            alihDayaUserModel.setNik(nik);
        }

        alihDayaUserModel.setNik(trxPUKVendor.getNikVendor());
        alihDayaUserModel.setNama(trxPUKVendor.getNameVendor());
        alihDayaUserModel.setJabatan(trxPUKVendor.getOccupationVendor());
        alihDayaUserModel.setMasaBerlakuSampai(DateTimeHelper.getDateToDateStringDDMMYYYY(trxPUKVendor.getMasaBerlakuSampai()));
        alihDayaUserModel.setPukVendor(mapToDetailPUKModel(trxPUKVendor.getNikPUK()));

        return alihDayaUserModel;
    }

    private ResponseModel<AlihDayaUserModel> buildResponse(ResponseStatus status, AlihDayaUserModel alihDayaUserModel) {
        ResponseModel<AlihDayaUserModel> response = new ResponseModel<>();

        response.setType(TYPE_ALIH_DAYA_MANAGEMENT_USER_GET);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(alihDayaUserModel);

        return response;
    }

    public ResponseModel<ResAlihDayaUserModel> updatePUKVendor(ReqMsDataAlihDayaModel request) throws ParseException {
        ResponseModel<ResAlihDayaUserModel> response = new ResponseModel<>();

        request.setUpdateDateTime(LocalDateTime.now());
        TrxPUKVendor updatePUKVendor = updateTrxPUKVendor(mapToTrxPUKVendor(request));

        if (updatePUKVendor != null){
            response = buildResponse(TYPE_ALIH_DAYA_MANAGEMENT_USER_ADD_EDIT, SUCCESS, buildResponse(request.getNik(), EDIT));
        }

        return response;
    }

    private TrxPUKVendor mapToTrxPUKVendor(ReqMsDataAlihDayaModel request) throws ParseException {
        MsEmployee msEmployee = iMsEmployeeRepository.getMsEmployeeByNik(request.getNikPUK());
        TrxPUKVendor trxPUKVendor = new TrxPUKVendor();

        trxPUKVendor.setNikVendor(request.getNik());
        trxPUKVendor.setNameVendor(request.getNama());
        trxPUKVendor.setNikPUK(request.getNikPUK());
        if (msEmployee != null){
            trxPUKVendor.setNamePUK(msEmployee.getFullName());
        }else {
            trxPUKVendor.setNamePUK(iMsEmployeeHierarchyRepository.getMsEmployeeHierarchyByNik(request.getNikPUK()).getFullName());
        }
        trxPUKVendor.setCreateDatetime(request.getCreateDatetime());
        trxPUKVendor.setUpdateDateTime(request.getUpdateDateTime());
        trxPUKVendor.setMasaBerlakuSampai(getFormattedString(request.getMasaBerlakuSampai()));
        trxPUKVendor.setOccupationVendor(request.getJabatanVendor());
        trxPUKVendor.setOccupationDescVendor(request.getJabatanVendor());

        return trxPUKVendor;
    }

    private Date getFormattedString(String date) throws ParseException {
        String[] split = date.split("-");
        return DateTimeHelper.getStringToDateYYYYMMDD(split[2] + "-" + split[1] + "-" + split[0]);
    }

    public ResponseModel<ResAlihDayaUserModel> savePUKVendor(ReqMsDataAlihDayaModel request) throws ParseException {
        ResponseModel<ResAlihDayaUserModel> response = new ResponseModel<>();

        request.setCreateDatetime(LocalDateTime.now());
        request.setUpdateDateTime(LocalDateTime.now());
        TrxPUKVendor savePUKVendor = saveTrxPUKVendor(mapToTrxPUKVendor(request));

        if (savePUKVendor != null){
            response = buildResponse(TYPE_ALIH_DAYA_MANAGEMENT_USER_ADD_EDIT, SUCCESS, buildResponse(request.getNik(), ADD));
        }

        return response;
    }

    public ResponseModel<ResBatchProcess> deleteTrxPUKVendor(List<String> nikList){
        ResponseStatus status = FAILED;
        int deletedAlihDaya = deleteTrxPUKVendorByNik(nikList);
        if (deletedAlihDaya > 0) {
            status = SUCCESS;
        }
        ResBatchProcess details = Mapper.buildResBatchProcess(deletedAlihDaya, STATUS_DELETED);

        return ResponseModel.<ResBatchProcess>builder().type(TYPE_ALIH_DAYA_MANAGEMENT_USER_DELETE).status(status.getCode()).statusDesc(status.getValue()).details(details).build();
    }

    private ResAlihDayaUserModel buildResponse(String nik, String type) {
        ResAlihDayaUserModel response = new ResAlihDayaUserModel();

        response.setNik(nik);
        response.setType(type);

        return response;
    }

    private int deleteTrxPUKVendorByNik(List<String> nikList) {
        return trxPUKVendorRepository.deleteTrxPUKVendor(nikList);
    }

    private ResponseModel<ResAlihDayaUserModel> buildResponse(String type, ResponseStatus status, ResAlihDayaUserModel resAlihDayaUserModel) {
        ResponseModel<ResAlihDayaUserModel> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resAlihDayaUserModel);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    public ResponseModel<ResBatchUserId> updateDataALihDayaInBulk(ReqUserIdBatchModel<AlihDayaUserModel> request, Profile profile) {
        List<TrxPUKVendor> existingPUKVendors = trxPUKVendorRepository.findAll();
        Map<String, TrxPUKVendor> existingPUKVendorMap = streamToMap(existingPUKVendors);

        Map<String, MsEmployee> pukMap = getPUKMap(request);

        List<TrxPUKVendor> newPUKVendors = request.getData().stream().map(AlihDayaUserModel::toTrxPUKVendor).collect(Collectors.toList());
        newPUKVendors.forEach(newTrxPUKVendor -> updateExistingAndNewEntriesData(existingPUKVendorMap, newTrxPUKVendor, pukMap));

        trxPUKVendorRepository.saveAll(newPUKVendors);
        iTrxPUKVendorBatchRepository.save(buildTrxPUKVendorBatch(request, profile));

        return ResponseModel.<ResBatchUserId>builder()
                .type(TYPE_ALIH_DAYA_MANAGEMENT_BULK_ADD_EDIT)
                .status(SUCCESS.getCode())
                .statusDesc(SUCCESS.getValue())
                .details(Mapper.toResBatchUserIdModel(request.getBatchId(), request.getTotalData()))
                .build();
    }

    private Map<String, MsEmployee> getPUKMap(ReqUserIdBatchModel<AlihDayaUserModel> request) {
        List<String> nikPUKList = request.getData().stream().map(data -> data.getPukVendor().getNikPUK()).collect(Collectors.toList());
        return msEmployeeService.getMsEmployeeMap(nikPUKList);
    }

    private static void updateExistingAndNewEntriesData(Map<String, TrxPUKVendor> existingPUKVendorMap, TrxPUKVendor newPUKVendor, Map<String, MsEmployee> pukMap) {
        TrxPUKVendor existingPUKVendor = existingPUKVendorMap.get(newPUKVendor.getNikVendor());
        if (existingPUKVendor != null) {
            newPUKVendor.setId(existingPUKVendor.getId());
            newPUKVendor.setCreateDatetime(existingPUKVendor.getCreateDatetime());
            newPUKVendor.setMasaBerlakuSampai(existingPUKVendor.getMasaBerlakuSampai());
        } else {
            newPUKVendor.setCreateDatetime(LocalDateTime.now());
            newPUKVendor.setMasaBerlakuSampai(java.sql.Date.valueOf(LocalDate.of(9999, 12, 28)));
        }

        newPUKVendor.setNamePUK(resolvePUKName(newPUKVendor, pukMap));
    }

    private static String resolvePUKName(TrxPUKVendor newPUKVendor, Map<String, MsEmployee> pukMap) {
        if (pukMap != null && !pukMap.isEmpty()) {
            return pukMap.get(newPUKVendor.getNikPUK()).getFullName();
        }
        return EMPTY;
    }

    private static Map<String, TrxPUKVendor> streamToMap(List<TrxPUKVendor> trxPUKVendors) {
        return trxPUKVendors.stream().collect(Collectors.toMap(trxPUKVendor -> trxPUKVendor.getNikVendor().toUpperCase(), trxPUKVendor -> trxPUKVendor));
    }

    private static TrxPUKVendorBatch buildTrxPUKVendorBatch(ReqUserIdBatchModel<AlihDayaUserModel> request, Profile profile) {
        TrxPUKVendorBatch trxPUKVendorBatch = new TrxPUKVendorBatch();

        trxPUKVendorBatch.setBatchId(request.getBatchId());
        trxPUKVendorBatch.setBatchFileName(request.getFileName());
        trxPUKVendorBatch.setTotalData(request.getTotalData());
        trxPUKVendorBatch.setUploaderNIK(profile.getPreferred_username());
        trxPUKVendorBatch.setUploaderName(profile.getName());

        return trxPUKVendorBatch;
    }
}
