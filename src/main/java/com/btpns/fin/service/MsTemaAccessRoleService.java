package com.btpns.fin.service;

import com.btpns.fin.model.TemaAccessRoleModel;
import com.btpns.fin.model.entity.MsTemaAccessRole;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsTemaAccessRoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class MsTemaAccessRoleService {
    @Autowired
    IMsTemaAccessRoleRepository msTemaAccessRoleRepository;

    public ResponseModel<ResponseListModel<TemaAccessRoleModel>> getListTemaAccessRole(Integer pageNumber, Integer pageSize) {
        Page<MsTemaAccessRole> temaAccessRoles = msTemaAccessRoleRepository.findAll(PageRequest.of(pageNumber - 1, pageSize));
        List<TemaAccessRoleModel> temaAccessRoleModelList = temaAccessRoles.getContent().stream().map(MsTemaAccessRole::toTemaAccessRoleModel).collect(Collectors.toList());
        ResponseListModel<TemaAccessRoleModel> responseDetail = ResponseListModel.<TemaAccessRoleModel>builder()
                .data(temaAccessRoleModelList)
                .page(pageNumber)
                .limit(pageSize)
                .totalItems(temaAccessRoles.getTotalElements())
                .totalPages(temaAccessRoles.getTotalPages())
                .build();
        return ResponseModel.<ResponseListModel<TemaAccessRoleModel>>builder().type(TYPE_ACCESS_ROLE_MANAGEMENT_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetail).build();
    }
}
