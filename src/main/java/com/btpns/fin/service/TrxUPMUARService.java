package com.btpns.fin.service;

import com.btpns.fin.helper.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.*;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.*;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class TrxUPMUARService {
    @Autowired
    private ITrxUARRequestRepository iTrxUARRequestRepository;

    @Autowired
    private ITrxUARAudittrailRepository iTrxUARAudittrailRepository;
    
    @Autowired
    private IDboRtgsUserIdRepository iDboRtgsUserIdRepository;

    @Autowired
    private IMsS4UserManagementRepository iS4UserManagementRepository;

    @Autowired
    private IMsSPKUserManagementRepository iSPKUserManagementRepository;

    @Autowired
    private ISlikUserIdRepository iSlikUserIdRepository;

    @Autowired
    private ICMSUserIdRepository iCMSUserIdRepository;

    @Autowired
    private ITepatMBankingCorporateUserIdRepository iTepatMBankingCorporateRepository;

    @Autowired
    private IMsTepatMBankingIndividuRepository iTepatMBankingIndividuRepository;

    @Autowired
    private ICustomUserIDRepository iCustomUserIDRepository;

    @Autowired
    private ITrxUARSummaryRepository iTrxUARSummaryRepository;

    @Autowired
    private TrxUpmRoleService trxUpmRoleService;

    @Autowired
    private MsUserIDApplicationService msUserIDApplicationService;

    @Autowired
    private EmailNotificationService emailNotificationService;

    @Autowired
    private MsSystemParamService msSystemParamService;

    @Autowired
    private MsHolidayListService msHolidayListService;

    @Autowired
    private MsEmployeeService msEmployeeService;

    @Autowired
    IReportUARUPMRepository iReportUARUPMRepository;

    @Autowired
    private MinioService minioService;

    @Autowired
    DelegationService delegationService;

    @Autowired
    MsSystemParamDetailService msSystemParamDetailService;

    @Autowired
    IMsSOPNumberRepository iMsSOPNumberRepository;

    @Autowired
    private Mapper mapper;

    public ResponseModel<ResBatchUARModel> saveUARRequest(RequestModel<UARRequestModel> request, Profile profile) {
        String aplikasi = request.getDetails().getAplikasi();
        List<TrxUARRequest> uarRequests = new ArrayList<>();
        List<TrxUARAudittrail> uarAudittrails = new ArrayList<>();

        enrichDataByAplikasi(request, profile, aplikasi, uarRequests, uarAudittrails);

        this.iTrxUARRequestRepository.saveAll(uarRequests);
        this.iTrxUARAudittrailRepository.saveAll(uarAudittrails);

        sendEmailUARRequestInBatch(uarRequests);

        return buildResponse(request, uarRequests, SUCCESS);
    }

    private void sendEmailUARRequestInBatch(List<TrxUARRequest> uarRequests) {
        Map<String, MsUserIDApplication> userIDAppMap = msUserIDApplicationService.getUserIDAppMap();
        Map<String, MsSystemParamDetail> paramDetailMap = msSystemParamService.getSystemParamDetailMap();
        Map<String, MsHolidayList> holidayMap = msHolidayListService.getHolidayMap(String.valueOf(LocalDate.now().getYear()));
        uarRequests.forEach(uarRequest -> {
            emailNotificationService.sendUserUARRequestNotification(uarRequest, userIDAppMap.get(uarRequest.getAplikasi()).getParamDetailDesc(), paramDetailMap.get(uarRequest.getTrxUARApproval().getCurrentState()).getParamDetailDesc(), holidayMap);
        });
    }

    private void enrichDataByAplikasi(RequestModel<UARRequestModel> request, Profile profile, String aplikasi, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails) {
        if (KODE_APLIKASI_USER_ID_DBO_RTGS.equalsIgnoreCase(aplikasi)) {
            enrichDataDboRTGS(request, uarRequests, uarAudittrails, profile);
        } else if (KODE_APLIKASI_USER_ID_S4.equalsIgnoreCase(aplikasi)) {
            enrichDataS4(request, uarRequests, uarAudittrails, profile);
        } else if (KODE_APLIKASI_USER_ID_SPK.equalsIgnoreCase(aplikasi)) {
            enrichDataSPK(request, uarRequests, uarAudittrails, profile);
        } else if (KODE_APLIKASI_USER_ID_CMS.equalsIgnoreCase(aplikasi)) {
            enrichDataCMS(request, uarRequests, uarAudittrails, profile);
        } else if (KODE_APLIKASI_USER_ID_SLIK.equalsIgnoreCase(aplikasi)) {
            enrichDataSlik(request, uarRequests, uarAudittrails, profile);
        } else if (KODE_APLIKASI_USER_ID_TEPAT_MBANKING_CORP.equalsIgnoreCase(aplikasi)) {
            enrichDataTepatMBankingCorporate(request, uarRequests, uarAudittrails, profile);
        } else if (KODE_APLIKASI_USER_ID_TEPAT_MBANKING_INDIVIDU.equalsIgnoreCase(aplikasi)) {
            enrichDataTepatMBankingIndividu(request, uarRequests, uarAudittrails, profile);
        } else {
            enrichDataCustomUserID(request, uarRequests, uarAudittrails, profile);
        }

    }

    private void enrichDataCustomUserID(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails, Profile profile) {
        List<MsCustomUserID> customUserIDs = iCustomUserIDRepository.findAllByParamDetailId(request.getDetails().getAplikasi(), Pageable.unpaged()).getContent();
        Optional<TrxUARRequest> latestUARRequest = getLatestTicket();
        int counter = 0;
        for (MsCustomUserID customUserID : customUserIDs) {
            counter++;
            TrxUARRequest uarRequest = Mapper.buildTrxUARRequest(request.getDetails(), generateUarTicketId(latestUARRequest, counter), customUserID.getNik(), customUserID.getNamaUser(), customUserID.getKewenangan(), customUserID.getJabatan(), customUserID.getUnitKerja());
            uarRequest.setTrxUARApproval(buildTrxUARApproval(uarRequest));

            uarRequests.add(uarRequest);
            uarAudittrails.add(Mapper.buildUARAudittrail(profile, uarRequest, uarRequest.getTrxUARApproval().getCurrentState(), TIMELINE_STATUS_CREATE_TICKET, TIMELINE_PIC_UPM, EMPTY));
        }
    }

    private void enrichDataDboRTGS(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails, Profile profile) {
        List<MsDboRTGS> dboRTGSList = iDboRtgsUserIdRepository.findAll();
        Optional<TrxUARRequest> latestUARRequest = getLatestTicket();
        int counter = 0;
        for (MsDboRTGS dboRTGSUser : dboRTGSList) {
            counter++;
            TrxUARRequest uarRequest = Mapper.buildTrxUARRequest(request.getDetails(),generateUarTicketId(latestUARRequest, counter), dboRTGSUser.getNik(), dboRTGSUser.getNamaUser(), dboRTGSUser.getKewenangan(), dboRTGSUser.getJabatan(), dboRTGSUser.getUnitKerja());
            uarRequest.setTrxUARApproval(buildTrxUARApproval(uarRequest));

            uarRequests.add(uarRequest);
            uarAudittrails.add(Mapper.buildUARAudittrail(profile, uarRequest, uarRequest.getTrxUARApproval().getCurrentState(), TIMELINE_STATUS_CREATE_TICKET, TIMELINE_PIC_UPM, EMPTY));

        }
    }

    private void enrichDataS4(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails, Profile profile) {
        List<MsS4> s4List = iS4UserManagementRepository.findAll();

        Optional<TrxUARRequest> latestUARRequest = getLatestTicket();
        int counter = 0;
        for (MsS4 s4User : s4List) {
            counter++;
            TrxUARRequest uarRequest = Mapper.buildTrxUARRequest(request.getDetails(), generateUarTicketId(latestUARRequest, counter), s4User.getNik(), s4User.getNamaUser(), s4User.getKewenangan(), s4User.getJabatan(), s4User.getUnitKerja());
            uarRequest.setTrxUARApproval(buildTrxUARApproval(uarRequest));

            uarRequests.add(uarRequest);
            uarAudittrails.add(Mapper.buildUARAudittrail(profile, uarRequest, uarRequest.getTrxUARApproval().getCurrentState(), TIMELINE_STATUS_CREATE_TICKET, TIMELINE_PIC_UPM, EMPTY));
        }
    }

    private void enrichDataSPK(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails, Profile profile) {
        List<MsSPK> spkList = iSPKUserManagementRepository.findAll();

        Optional<TrxUARRequest> latestUARRequest = getLatestTicket();
        int counter = 0;
        for (MsSPK spkUser : spkList) {
            counter++;
            TrxUARRequest uarRequest = Mapper.buildTrxUARRequest(request.getDetails(),generateUarTicketId(latestUARRequest, counter), spkUser.getNik(), spkUser.getNamaUser(), spkUser.getKewenangan(), spkUser.getJabatan(), spkUser.getUnitKerja());
            uarRequest.setTrxUARApproval(buildTrxUARApproval(uarRequest));

            uarRequests.add(uarRequest);
            uarAudittrails.add(Mapper.buildUARAudittrail(profile, uarRequest, uarRequest.getTrxUARApproval().getCurrentState(), TIMELINE_STATUS_CREATE_TICKET, TIMELINE_PIC_UPM, EMPTY));
        }
    }

    private void enrichDataCMS(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails, Profile profile) {
        List<MsCMS> cmsList = iCMSUserIdRepository.findAll();

        Optional<TrxUARRequest> latestUARRequest = getLatestTicket();
        int counter = 0;
        for (MsCMS cmsUser : cmsList) {
            counter++;
            TrxUARRequest uarRequest = Mapper.buildTrxUARRequest(request.getDetails(),generateUarTicketId(latestUARRequest, counter), cmsUser.getNik(), cmsUser.getNamaUser(), cmsUser.getKewenangan(), cmsUser.getJabatan(), cmsUser.getUnitKerja());
            uarRequest.setTrxUARApproval(buildTrxUARApproval(uarRequest));

            uarRequests.add(uarRequest);
            uarAudittrails.add(Mapper.buildUARAudittrail(profile, uarRequest, uarRequest.getTrxUARApproval().getCurrentState(), TIMELINE_STATUS_CREATE_TICKET, TIMELINE_PIC_UPM, EMPTY));
        }
    }

    private void enrichDataSlik(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails, Profile profile) {
        List<MsSlik> slikList = iSlikUserIdRepository.findAll();

        Optional<TrxUARRequest> latestUARRequest = getLatestTicket();
        int counter = 0;
        for (MsSlik slikUser : slikList) {
            counter++;
            TrxUARRequest uarRequest = Mapper.buildTrxUARRequest(request.getDetails(),generateUarTicketId(latestUARRequest, counter), slikUser.getNik(), slikUser.getNamaUser(), slikUser.getKewenangan(), slikUser.getJabatan(), slikUser.getUnitKerja());
            uarRequest.setTrxUARApproval(buildTrxUARApproval(uarRequest));

            uarRequests.add(uarRequest);
            uarAudittrails.add(Mapper.buildUARAudittrail(profile, uarRequest, uarRequest.getTrxUARApproval().getCurrentState(), TIMELINE_STATUS_CREATE_TICKET, TIMELINE_PIC_UPM, EMPTY));
        }
    }

    private void enrichDataTepatMBankingIndividu(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails, Profile profile) {
        List<MsTepatMBankingIndividu> tepatMBankingIndividuList = iTepatMBankingIndividuRepository.findAll();

        Optional<TrxUARRequest> latestUARRequest = getLatestTicket();
        int counter = 0;
        for (MsTepatMBankingIndividu tepatMBankingIndividuUser : tepatMBankingIndividuList) {
            counter++;
            TrxUARRequest uarRequest = Mapper.buildTrxUARRequest(request.getDetails(),generateUarTicketId(latestUARRequest, counter), tepatMBankingIndividuUser.getNik(), tepatMBankingIndividuUser.getNamaUser(), tepatMBankingIndividuUser.getKewenangan(), tepatMBankingIndividuUser.getJabatan(), tepatMBankingIndividuUser.getUnitKerja());
            uarRequest.setTrxUARApproval(buildTrxUARApproval(uarRequest));

            uarRequests.add(uarRequest);
            uarAudittrails.add(Mapper.buildUARAudittrail(profile, uarRequest, uarRequest.getTrxUARApproval().getCurrentState(), TIMELINE_STATUS_CREATE_TICKET, TIMELINE_PIC_UPM, EMPTY));
        }
    }

    private void enrichDataTepatMBankingCorporate(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, List<TrxUARAudittrail> uarAudittrails, Profile profile) {
        List<MsTepatMBankingCorporate> tepatMBankingCorporateList = iTepatMBankingCorporateRepository.findAll();

        Optional<TrxUARRequest> latestUARRequest = getLatestTicket();
        int counter = 0;
        for (MsTepatMBankingCorporate tepatMBankingCorpUser : tepatMBankingCorporateList) {
            counter++;
            TrxUARRequest uarRequest = Mapper.buildTrxUARRequest(request.getDetails(),generateUarTicketId(latestUARRequest, counter), tepatMBankingCorpUser.getNik(), tepatMBankingCorpUser.getNamaUser(), tepatMBankingCorpUser.getKewenangan(), tepatMBankingCorpUser.getJabatan(), tepatMBankingCorpUser.getUnitKerja());
            uarRequest.setTrxUARApproval(buildTrxUARApproval(uarRequest));

            uarRequests.add(uarRequest);
            uarAudittrails.add(Mapper.buildUARAudittrail(profile, uarRequest, uarRequest.getTrxUARApproval().getCurrentState(), TIMELINE_STATUS_CREATE_TICKET, TIMELINE_PIC_UPM, EMPTY));
        }
    }

    private TrxUARApproval buildTrxUARApproval(TrxUARRequest uarRequest) {
        TrxUARApproval uarApproval = new TrxUARApproval();

        uarApproval.setTicketId(uarRequest.getTicketId());
        uarApproval.setCurrentState(STATUS_PENDING_USER);
        uarApproval.setUserNik(uarRequest.getNik());
        uarApproval.setUserNikStatus(STATUS_WAITING);

        return uarApproval;
    }

    private ResponseModel<ResBatchUARModel> buildResponse(RequestModel<UARRequestModel> request, List<TrxUARRequest> uarRequests, ResponseStatus status) {
        ResponseModel<ResBatchUARModel> response = new ResponseModel<>();

        response.setType(request.getType());
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(buildResBatchUARModel(request.getDetails(), uarRequests));

        return response;
    }

    private ResBatchUARModel buildResBatchUARModel(UARRequestModel uarRequestModel, List<TrxUARRequest> uarRequests) {
        ResBatchUARModel resBatchUARModel = new ResBatchUARModel();

        Optional<MsUserIDApplication> optional = msUserIDApplicationService.findById(uarRequestModel.getAplikasi());
        optional.ifPresent(msUserIDApplication -> resBatchUARModel.setAplikasi(msUserIDApplication.getParamDetailDesc()));
        resBatchUARModel.setTotalData(uarRequests.size());

        return resBatchUARModel;
    }

    private synchronized String generateUarTicketId(Optional<TrxUARRequest> latestUARRequest, int counter) {
        String currentDate = new SimpleDateFormat("yyMMdd").format(new Date());
        StringBuilder sbTicketId = new StringBuilder();

        if (latestUARRequest.isPresent()) {
            String lastTicketId = latestUARRequest.get().getTicketId();
            String lastDate = lastTicketId.substring(3, 9);

            if (lastDate.equals(currentDate)) {
                Integer ticketNumber = counter + Integer.parseInt(lastTicketId.substring(9, 13));
                sbTicketId.append(PREFIX_UAR).append(currentDate).append(String.format("%04d", ticketNumber));
            } else {
                sbTicketId.append(PREFIX_UAR).append(currentDate).append(String.format("%04d", counter));
            }
        } else {
            sbTicketId.append(PREFIX_UAR).append(currentDate).append(String.format("%04d", counter));
        }
        return sbTicketId.toString();
    }

    public Optional<TrxUARRequest> getLatestTicket(){
        return iTrxUARRequestRepository.findLatestTrxUARRequest();
    }

    public ResponseModel<ResponseListModel> getListUAR(String status, String aplikasi, Integer periodYear, String periodQuarter, String searchFlag, String searchData, Integer pageNumber, int pageSize) {
        Page<TrxUARRequest> uars = findUARBySearchFlagByAplikasiByYearByQuarterInStatuses(buildStatusList(status), aplikasi, periodYear, periodQuarter, searchFlag, searchData, PageRequest.of(pageNumber - 1, pageSize));

        List<UARModel> uarModels = mapper.mapToUARModelList(uars);
        ResponseListModel<UARModel> data = buildResponseListModel(pageSize, pageNumber, uarModels, uars.getTotalPages(), uars.getTotalElements());

        return Mapper.buildResponseList(TYPE_UAR_GET_LIST, SUCCESS, data);
    }

    private Page<TrxUARRequest> findUARBySearchFlagByAplikasiByYearByQuarterInStatuses(List<String> statuses, String aplikasi, Integer periodYear, String periodQuarter, String searchFlag, String searchData, Pageable pageable) {
        if (StringUtils.isBlank(searchData)) {
            return iTrxUARRequestRepository.findByAplikasiByYearByQuarterInStatuses(statuses, aplikasi, periodYear, periodQuarter, pageable);
        }
        if (SEARCH_FLAG_TICKET_ID.equals(searchFlag)) {
            return iTrxUARRequestRepository.findByTicketIdByAplikasiByYearByQuarterInStatuses(statuses, aplikasi, periodYear, periodQuarter, searchData, pageable);
        }
        if (SEARCH_FLAG_NIK.equals(searchFlag)) {
            return iTrxUARRequestRepository.findByNIKByAplikasiByYearByQuarterInStatuses(statuses, aplikasi, periodYear, periodQuarter, searchData, pageable);
        }
        if (SEARCH_FLAG_NAMA_USER.equals(searchFlag)) {
            return iTrxUARRequestRepository.findByNameByAplikasiByYearByQuarterInStatuses(statuses, aplikasi, periodYear, periodQuarter, searchData, pageable);
        }
        return new PageImpl<>(Collections.emptyList());
    }

    private static List<String> buildStatusList(String status) {
        List<String> statuses = new ArrayList<>();
        statuses.add(status);
        if (STATUS_PENDING_USER.equalsIgnoreCase(status)) {
            statuses.add(CURR_STATUS_REJECTED);
        }
        return statuses;
    }

    private List<UARModel> mapToUARModelList(Page<TrxUARRequest> uars) {
        Map<String, TrxUpmRole> upmRoleMap = trxUpmRoleService.getMapUpmRoles();
        Map<String, MsUserIDApplication> userIDAppMap = msUserIDApplicationService.getUserIDAppMap();

        List<UARModel> uarModels = new ArrayList<>();
        for (TrxUARRequest uarRequest : uars.getContent()) {
            UARModel uarModel = new UARModel();

            uarModel.setTicketId(uarRequest.getTicketId());
            uarModel.setAplikasi(userIDAppMap.get(uarRequest.getAplikasi()).getParamDetailDesc());
            uarModel.setPeriodeTahun(uarRequest.getPeriodYear());
            uarModel.setPeriodeTriwulan(uarRequest.getPeriodQuarter());
            uarModel.setNik(uarRequest.getNik());
            uarModel.setNamaUser(uarRequest.getNamaUser());
            uarModel.setKewenangan(uarRequest.getKewenangan());
            uarModel.setJabatan(uarRequest.getJabatan() != null ? uarRequest.getJabatan() : EMPTY);
            uarModel.setUnitKerja(uarRequest.getUnitKerja());
            uarModel.setKonfirmasiAkses(uarRequest.getTrxUARApproval().getUserConfirmation() != null ? Mapper.getConfirmationStatusValue(uarRequest.getTrxUARApproval().getUserConfirmation()) : EMPTY);
            uarModel.setKeterangan(uarRequest.getTrxUARApproval().getUserNikNotes() != null ? uarRequest.getTrxUARApproval().getUserNikNotes() : EMPTY);
            uarModel.setReminder(Optional.ofNullable(uarRequest.getReminder()).orElse(0) != 0 ? CommonHelper.concateTwoString(REMINDER, uarRequest.getReminder().toString()) : EMPTY);
            uarModel.setPicProcess(uarRequest.getTrxUARApproval().getUpmMakerNik() != null ? upmRoleMap.get(uarRequest.getTrxUARApproval().getUpmMakerNik().toUpperCase()).getNama() : EMPTY);
            uarModel.setPicApprove(uarRequest.getTrxUARApproval().getUpmCheckerNik() != null ? upmRoleMap.get(uarRequest.getTrxUARApproval().getUpmCheckerNik().toUpperCase()).getNama() : EMPTY);

            uarModels.add(uarModel);
        }
        return uarModels;
    }

    private ResponseListModel<UARModel> buildResponseListModel(Integer pageSize, Integer pageNumber, List<UARModel> uarModels, int totalPages, long totalItems) {
        ResponseListModel<UARModel> data = new ResponseListModel<>();
        data.setPage(pageNumber);
        data.setLimit(pageSize);
        data.setTotalPages(totalPages);
        data.setTotalItems(totalItems);
        data.setData(uarModels);

        return data;
    }

    public boolean isValidRequestByRole(String ticketId, String type, String role) {
        String currentState = findByTicketId(ticketId).getTrxUARApproval().getCurrentState();

        if (UPM_ROLE_MAKER.equalsIgnoreCase(role)) {
            return isValidMakerProcess(type, role, currentState) ||
                    isValidMakerManualConfirmation(role, currentState);
        } else if (UPM_ROLE_CHECKER.equalsIgnoreCase(role) || UPM_ROLE_ADMIN.equalsIgnoreCase(role)) {
            return isValidCheckerOrAdminVerification(type, role, currentState) ||
                    isValidCheckerOrAdminReject(type, role, currentState);
        } else {
            return false;
        }
    }

    private static boolean isValidMakerProcess(String type, String role, String currentState) {
        return UPM_ROLE_MAKER.equalsIgnoreCase(role) &&
                UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) &&
                (UPM_TICKET_TYPE_PROCESS.equalsIgnoreCase(type) || UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(type));
    }

    private static boolean isValidMakerManualConfirmation(String role, String currentState) {
        return UPM_ROLE_MAKER.equalsIgnoreCase(role) &&
                (STATUS_PENDING_USER.equalsIgnoreCase(currentState) || CURR_STATUS_REJECTED.equalsIgnoreCase(currentState) || STATUS_PENDING_PUK.equals(currentState));
    }

    private static boolean isValidCheckerOrAdminVerification(String type, String role, String currentState) {
        return (UPM_ROLE_CHECKER.equalsIgnoreCase(role) || UPM_ROLE_ADMIN.equalsIgnoreCase(role)) &&
                UPM_STATUS_VERIFICATION.equalsIgnoreCase(currentState) &&
                UPM_TICKET_TYPE_CHECKER_VERIFY.equalsIgnoreCase(type);
    }

    private static boolean isValidCheckerOrAdminReject(String type, String role, String currentState) {
        return (UPM_ROLE_CHECKER.equalsIgnoreCase(role) || UPM_ROLE_ADMIN.equalsIgnoreCase(role)) &&
                (UPM_STATUS_INPROGRESS.equalsIgnoreCase(currentState) || UPM_STATUS_VERIFICATION.equalsIgnoreCase(currentState)) &&
                UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(type);
    }

    public TrxUARRequest findByTicketId(String ticketId) {
        return iTrxUARRequestRepository.findByTicketId(ticketId);
    }

    public ResponseModel<ResUARModel> updateApprovalUpmUAR(ReqApprovalModel request, Profile profile, TrxUpmRole upmRole) {
        TrxUARRequest existingUARRequest = findByTicketId(request.getTicketId());

        String uarStatus = "";
        String timelineStatus = "";
        if (UPM_ROLE_MAKER.equalsIgnoreCase(upmRole.getRole())) {
            if (UPM_TICKET_TYPE_PROCESS.equalsIgnoreCase(request.getType())) {
                uarStatus = UPM_STATUS_VERIFICATION;
                timelineStatus = TIMELINE_STATUS_DONE_BY_UPM_MAKER;
            }
            if (UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(request.getType())) {
                uarStatus = UPM_STATUS_REJECTED;
                timelineStatus = TIMELINE_STATUS_REJECT_TICKET;
            }
            if (UPM_TICKET_TYPE_PICK.equalsIgnoreCase(request.getType())) {
                uarStatus = UPM_STATUS_INPROGRESS;
                timelineStatus = TIMELINE_STATUS_MANUAL_CONFIRM_UPM;
            }
            processUarAsMaker(profile.getPreferred_username(), request, uarStatus, existingUARRequest);
        }
        if (UPM_ROLE_CHECKER.equalsIgnoreCase(upmRole.getRole()) || UPM_ROLE_ADMIN.equalsIgnoreCase(upmRole.getRole())) {
            if (UPM_TICKET_TYPE_CHECKER_VERIFY.equalsIgnoreCase(request.getType())) {
                uarStatus = UPM_STATUS_DONE;
                timelineStatus = TIMELINE_STATUS_DONE;
            }
            if (UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(request.getType())) {
                timelineStatus = TIMELINE_STATUS_REJECT_TICKET;
                if (UPM_STATUS_INPROGRESS.equalsIgnoreCase(existingUARRequest.getTrxUARApproval().getCurrentState())) {
                    uarStatus = UPM_STATUS_REJECTED;
                } else {
                    uarStatus = UPM_STATUS_INPROGRESS;
                }
            }
            processUarAsChecker(profile.getPreferred_username(), request, uarStatus, existingUARRequest.getTrxUARApproval());
        }
        TrxUARRequest updatedUARRequest = iTrxUARRequestRepository.save(existingUARRequest);
        saveUARAudittrail(profile, timelineStatus, updatedUARRequest, uarStatus, request.getNotes());
        sendUARNotificationByUPM(request.getType(), updatedUARRequest, request.getNotes());

        return ResponseModel.<ResUARModel>builder()
                .type(TYPE_UAR_UPM_PROCESS)
                .status(SUCCESS.getCode())
                .statusDesc(SUCCESS.getValue())
                .details(new ResUARModel(existingUARRequest.getTicketId(), request.getType()))
                .build();
    }

    private void saveUARAudittrail(Profile profile, String timelineStatus, TrxUARRequest updatedUARRequest, String action, String notes) {
        TrxUARAudittrail uarAudittrail = Mapper.buildUARAudittrail(profile, updatedUARRequest, action, timelineStatus, TIMELINE_PIC_UPM, notes);
        iTrxUARAudittrailRepository.save(uarAudittrail);
    }

    private void processUarAsMaker(String nikRequester, ReqApprovalModel request, String uarStatus, TrxUARRequest uarRequest) {
        TrxUARApproval uarApproval = uarRequest.getTrxUARApproval();

        uarApproval.setCurrentState(uarStatus);
        if (UPM_TICKET_TYPE_PICK.equals(request.getType())) {
            uarApproval.setUserConfirmation(request.getConfirmation());
            uarApproval.setUserNikNotes(request.getNotes());
            uarApproval.setUserResponseDT(LocalDateTime.now());
            uarApproval.setUserNikStatus(STATUS_CONFIRMED);
            uarRequest.setManualConfirmation(TRUE_FLAG_BOOL);
        } else {
            uarApproval.setUpmMakerNik(nikRequester);
            uarApproval.setUpmMakerStatus(uarStatus);
            uarApproval.setUpmMakerProcessDT(LocalDateTime.now());
            uarApproval.setUpmMakerNotes(request.getNotes());
            uarApproval.setUpmMakerAttachment(resolveAttachment(request));
        }
    }

    private String resolveAttachment(ReqApprovalModel request) {
        List<AttachmentModel> attachmentList =  new ArrayList<>();
        if (request.getAttachment() != null && !request.getAttachment().isEmpty()) {
            attachmentList = request.getAttachment();
        }
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();

        return new Gson().toJson(attachmentList, attachmentListType);
    }

    private void processUarAsChecker(String nikRequester, ReqApprovalModel request, String uarStatus, TrxUARApproval uarApproval) {
        uarApproval.setUpmCheckerNik(nikRequester);
        uarApproval.setUpmCheckerStatus(uarStatus);
        uarApproval.setUpmCheckerApprovalDT(LocalDateTime.now());
        uarApproval.setUpmCheckerNotes(request.getNotes());
        uarApproval.setCurrentState(uarStatus);
    }

    public void sendUARNotificationByUPM(String type, TrxUARRequest uarRequest, String notes) {
        if (uarRequest != null) {
            String appDesc = msUserIDApplicationService.getAppDesc(uarRequest.getAplikasi());
            String currentStatusDesc = msSystemParamService.getMsSystemParamDetail(uarRequest.getTrxUARApproval().getCurrentState()).getParamDetailDesc();
            if (UPM_TICKET_TYPE_CHECKER_VERIFY.equalsIgnoreCase(type)) {
                emailNotificationService.sendUserUARDoneNotification(uarRequest, appDesc, currentStatusDesc);
            }
            if (UPM_TICKET_TYPE_REJECT.equalsIgnoreCase(type) && UPM_STATUS_REJECTED.equalsIgnoreCase(uarRequest.getTrxUARApproval().getCurrentState())) {
                emailNotificationService.sendUserUARRejectedNotification(uarRequest, appDesc, currentStatusDesc, UPM.toUpperCase(), notes);
            }
        }
    }

    public ResponseModel<ResponseListModel> getReportUARUPM(String triwulan, String tahun, String aplikasi, int pageNumMin1, Integer pageNumber, Integer pageSize) {
        Page<ResReportUARUPM> reportUARUPMPageable = new PageImpl<>(new ArrayList<>());
        if (!tahun.isEmpty() && !triwulan.isEmpty() && !aplikasi.isEmpty()){
            reportUARUPMPageable = iReportUARUPMRepository.getReportUARUPM(triwulan, tahun, aplikasi, UPM_STATUS_DONE, PageRequest.of(pageNumMin1, pageSize));
        }

        return Mapper.buildResponseList(TYPE_REPORT_UAR_UPM, SUCCESS, buildResUserIdListModel(pageSize, pageNumber, reportUARUPMPageable.getContent(), reportUARUPMPageable.getTotalPages(), reportUARUPMPageable.getTotalElements()));
    }

    private ResponseListModel buildResUserIdListModel(Integer pageSize, Integer pageNumber, List<ResReportUARUPM> resReportUARUPMList, int totalPages, long totalItems) {
        ResponseListModel responseListModel = new ResponseListModel();

        responseListModel.setData(buildResReportUARUPMList(resReportUARUPMList));
        responseListModel.setLimit(pageSize);
        responseListModel.setPage(pageNumber);
        responseListModel.setTotalPages(totalPages);
        responseListModel.setTotalItems(totalItems);

        return responseListModel;
    }

    private List buildResReportUARUPMList(List<ResReportUARUPM> resReportUARUPMList) {
        List<ResReportUARUPM> result = new ArrayList<>();

        for (ResReportUARUPM resReportUARUPM : resReportUARUPMList) {
            result.add(buildResReportUARUPM(resReportUARUPM));
        }

        return result;
    }

    private ResReportUARUPM buildResReportUARUPM(ResReportUARUPM resReportUARUPM) {
        ResReportUARUPM result = new ResReportUARUPM();

        result.setTicketId(resReportUARUPM.getTicketId());
        result.setNik(resReportUARUPM.getNik());
        result.setNama(resReportUARUPM.getNama());
        result.setKewenangan(resReportUARUPM.getKewenangan());
        result.setJabatan(resReportUARUPM.getJabatan() == null ? EMPTY : resReportUARUPM.getJabatan());
        result.setUnitKerja(resReportUARUPM.getUnitKerja());
        result.setStatus(resReportUARUPM.getStatus().equals(NOL) ? UAR_CONFIRMATION_DELETE : UAR_CONFIRMATION_ACTIVE);
        result.setKeterangan(resReportUARUPM.getKeterangan() == null ? EMPTY : resReportUARUPM.getKeterangan());

        return result;
    }

    public ResponseModel<ResUploadModel> generateReportUARAsPDF(String triwulan, String tahun, String aplikasi) throws Exception {
        Page<ResReportUARUPM> reportUARUPMPageable = iReportUARUPMRepository.getReportUARUPM(triwulan, tahun, aplikasi, UPM_STATUS_DONE, Pageable.unpaged());
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(buildResReportUARUPMList(reportUARUPMPageable.getContent()));

        File file = ResourceUtils.getFile("classpath:report_konfirmasi_userid.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

        String appDesc = msUserIDApplicationService.getAppDesc(aplikasi);
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, mapper.generateReportParameters(triwulan, tahun, appDesc), dataSource);
        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

        Map<String, String> result = minioService.uploadReportFilePDF(pdfReportStream.toByteArray(), DocumentHelper.generateReportFilePath(REPORT_FILE_NAME, PDF_EXTENSION), REPORT_FILE_NAME);

        return buildResponse(TYPE_REPORT_UAR_UPM_DOWNLOAD, SUCCESS, result);
    }

    public ResFileDownload directDownloadReportUARUPMAsPDF(String triwulan, String tahun, String aplikasi) throws Exception {
        Page<ResReportUARUPM> reportUARUPMPageable = iReportUARUPMRepository.getReportUARUPM(triwulan, tahun, aplikasi, UPM_STATUS_DONE, Pageable.unpaged());
        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(buildResReportUARUPMList(reportUARUPMPageable.getContent()));

        File file = ResourceUtils.getFile("classpath:report_konfirmasi_userid.jrxml");
        JasperReport jasperReport = JasperCompileManager.compileReport(file.getAbsolutePath());

        String appDesc = msUserIDApplicationService.getAppDesc(aplikasi);
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, mapper.generateReportParameters(triwulan, tahun, appDesc), dataSource);
        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Collections.singletonList(jasperPrint));

        return new ResFileDownload(pdfReportStream.toByteArray(), CommonHelper.concateTwoString(REPORT_FILE_NAME, PDF_EXTENSION));
    }

    private static ResponseModel<ResUploadModel> buildResponse(String type, ResponseStatus status, Map<String, String> result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(new ResUploadModel(result));
        return response;
    }

    public ResponseModel<ResBatchProcess> sendReminderUAR(ReqTicketBatchModel request, Profile profile) {
        List<TrxUARRequest> trxUARRequestList = iTrxUARRequestRepository.findAllById(request.getTicketIds());
        List<TrxUARAudittrail> trxUARAudittrailList = new ArrayList<>();
        for (TrxUARRequest uar : trxUARRequestList) {
            uar.setReminder(resolveReminder(uar.getReminder()));
            trxUARAudittrailList.add(buildUARAudittrailReminder(profile, uar, buildReminderTimelineStatus(uar), TIMELINE_PIC_UPM));
        }
        iTrxUARRequestRepository.saveAll(trxUARRequestList);
        iTrxUARAudittrailRepository.saveAll(trxUARAudittrailList);
        sendUARReminderNotification(trxUARRequestList);

        return ResponseModel.<ResBatchProcess>builder().type(TYPE_UAR_UPM_SEND_REMINDER).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(Mapper.buildResBatchProcess(request.getTicketIds().size(), STATUS_REMINDED)).build();
    }

    private boolean isValidSendReminderRequest(TrxUARRequest uarRequest) {
        return uarRequest != null
                && (STATUS_PENDING_USER.equals(uarRequest.getTrxUARApproval().getCurrentState())
                || STATUS_PENDING_PUK.equals(uarRequest.getTrxUARApproval().getCurrentState())
                || CURR_STATUS_REJECTED.equals(uarRequest.getTrxUARApproval().getCurrentState()));
    }

    private String buildReminderTimelineStatus(TrxUARRequest uarRequest) {
        String subjectNIK = STATUS_PENDING_USER.equals(uarRequest.getTrxUARApproval().getCurrentState()) ? uarRequest.getNik() : uarRequest.getTrxUARApproval().getPukNik();

        return TIMELINE_STATUS_SEND_REMINDER + uarRequest.getReminder() + " to " + subjectNIK;
    }

    public static TrxUARAudittrail buildUARAudittrailReminder(Profile profile, TrxUARRequest uarRequest, String status, String timelinePIC) {
        TrxUARAudittrail uarAudittrail = new TrxUARAudittrail();

        uarAudittrail.setNik(profile.getPreferred_username());
        uarAudittrail.setTicketId(uarRequest.getTicketId());
        uarAudittrail.setAction(UPM_STATUS_SEND_REMINDER);
        uarAudittrail.setAdditionalInfo(new Gson().toJson(Mapper.buildTimelineStatus(profile, status, timelinePIC, EMPTY)));
        return uarAudittrail;
    }

    private Integer resolveReminder(Integer reminder) {
        if (reminder == null) {
            reminder = 1;
        } else {
            reminder++;
        }

        return reminder;
    }

    private void sendUARReminderNotification(List<TrxUARRequest> trxUARRequestList) {
        trxUARRequestList.forEach(uar -> {
            String appDesc = msUserIDApplicationService.getAppDesc(uar.getAplikasi());
            String currentStatusDesc = msSystemParamService.getMsSystemParamDetail(uar.getTrxUARApproval().getCurrentState()).getParamDetailDesc();
            Map<String, MsHolidayList> holidayMap = msHolidayListService.getHolidayMap(String.valueOf(LocalDate.now().getYear()));

            emailNotificationService.sendUARReminderNotification(uar, appDesc, currentStatusDesc, holidayMap);
        });
    }

    public ResFileDownload directDownloadBeritaAcaraPDF(String refNumber, String triwulan, String tahun, String aplikasi, Integer isNewBA) throws Exception {
        Page<TrxUARRequest> uarRequests = iTrxUARRequestRepository.findByAplikasiByYearByQuarterInStatuses(List.of(UPM_STATUS_DONE), aplikasi, Integer.valueOf(tahun), triwulan, Pageable.unpaged());

        List<UserIDModel> previouslyActiveUserIDs = new ArrayList<>();
        List<UserIDModel> previouslyInactiveUserIDs = new ArrayList<>();
        populateUserIDList(uarRequests.getContent(), previouslyActiveUserIDs, previouslyInactiveUserIDs);

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(previouslyInactiveUserIDs);

        JasperReport mainReport = null;
        if (KODE_APLIKASI_USER_ID_TEPAT_MBANKING_INDIVIDU.equalsIgnoreCase(aplikasi) || KODE_APLIKASI_USER_ID_TEPAT_MBANKING_CORP.equalsIgnoreCase(aplikasi)){
            mainReport = CommonHelper.compileJasperReportTemplate("classpath:pdf_berita_acara_uar_mbanking_mainreport.jrxml");
        }else {
            mainReport = CommonHelper.compileJasperReportTemplate("classpath:pdf_berita_acara_uar_mainreport.jrxml");
        }
        JasperReport subReport = CommonHelper.compileJasperReportTemplate("classpath:pdf_berita_acara_uar_subreport.jrxml");

        String appDesc = msUserIDApplicationService.getAppDesc(aplikasi);
        String noSOP = iMsSOPNumberRepository.findAllMsSOPNumberData().getSopNumber();
        Map<String, Object> mainReportParams = mapper.generateUARSummaryParameters(refNumber, triwulan, tahun, appDesc, uarRequests.getContent().size(), previouslyActiveUserIDs.size(), previouslyInactiveUserIDs.size(), noSOP);

        JasperPrint jasperPrintMain = JasperFillManager.fillReport(mainReport, mainReportParams, new JREmptyDataSource());
        JasperPrint jasperPrintSub = JasperFillManager.fillReport(subReport, mainReportParams, dataSource);
        ByteArrayOutputStream pdfReportStream = CommonHelper.exportJasperReports(Arrays.asList(jasperPrintMain, jasperPrintSub));

        if (TRUE_FLAG_INT.equals(isNewBA)){
            saveUARReportSummaryMetadata(refNumber, aplikasi, Integer.valueOf(tahun), triwulan);
        }

        return new ResFileDownload(pdfReportStream.toByteArray(), refNumber.concat(PDF_EXTENSION));
    }

    private static void populateUserIDList(List<TrxUARRequest> uarRequestList, List<UserIDModel> previouslyActiveuserIDs, List<UserIDModel> previouslyInactiveuserIDs) {
        for (TrxUARRequest uarRequest : uarRequestList) {
            UserIDModel userId = new UserIDModel();
            userId.setNik(uarRequest.getNik());
            userId.setNamaUser(uarRequest.getNamaUser());
            userId.setKewenangan(uarRequest.getKewenangan());
            userId.setJabatan(uarRequest.getJabatan() != null ? uarRequest.getJabatan() : EMPTY);
            userId.setUnitKerja(uarRequest.getUnitKerja());

            if (uarRequest.getTrxUARApproval().getUserConfirmation() == 1) {
                previouslyActiveuserIDs.add(userId);
            } else {
                previouslyInactiveuserIDs.add(userId);
            }
        }
    }

    public boolean reassignPukUAR(UpmReassignPukModel reassignPukModel, String ticketId, Profile profile) {
        TrxUARRequest existingUARRequest = iTrxUARRequestRepository.findByTicketId(ticketId);
        String toPUK = reassignPukModel.getPuk1();

        boolean isSuccessful = false;
        if (existingUARRequest != null && isValidReassignPUK(existingUARRequest.getTrxUARApproval(), toPUK)) {
            String fromPUK = existingUARRequest.getTrxUARApproval().getPukNik();
            TrxUARRequest updatedUARRequest = updateDataPukUAR(existingUARRequest, toPUK);

            String timelineStatus = TIMELINE_STATUS_REASSIGN + fromPUK + " to " + toPUK;
            saveUARAudittrail(profile, timelineStatus, updatedUARRequest, REASSIGN_PUK_ACTION, EMPTY);

            isSuccessful = true;
            if (isSuccessful) sendEmailtoDelegatedUAR(ticketId); delegationService.sendEmailtoPreviousDelegatedApproval(ticketId, fromPUK, toPUK);
        }

        return isSuccessful;
    }

    private void sendEmailtoDelegatedUAR(String ticketId) {
        TrxUARRequest uarRequest = iTrxUARRequestRepository.findByTicketId(ticketId);
        String appDesc = msUserIDApplicationService.getAppDesc(uarRequest.getAplikasi());
        String currentStatusDesc = msSystemParamService.getMsSystemParamDetail(uarRequest.getTrxUARApproval().getCurrentState()).getParamDetailDesc();

        emailNotificationService.sendPukUARApprovalNotification(uarRequest, appDesc, currentStatusDesc);
    }

    private boolean isValidReassignPUK(TrxUARApproval trxUARApproval, String toPUK) {
        return STATUS_PENDING_PUK.equals(trxUARApproval.getCurrentState())
                && !trxUARApproval.getPukNik().equalsIgnoreCase(toPUK);
    }

    private TrxUARRequest updateDataPukUAR(TrxUARRequest existingUARRequest, String toPUK) {
        MsEmployee newPUK = msEmployeeService.getEmployeeByNik(toPUK);

        existingUARRequest.getTrxUARApproval().setPukNik(toPUK);
        existingUARRequest.getTrxUARApproval().setPukName(newPUK.getFullName());
        existingUARRequest.getTrxUARApproval().setPukOccupation(newPUK.getOccupationDesc());
        existingUARRequest.setReminder(0);

        return iTrxUARRequestRepository.save(existingUARRequest);
    }

    private void saveUARReportSummaryMetadata(String refNumber, String aplikasi, Integer periodYear, String periodQuarter) {
        TrxUARSummary newUARSummary = buildTrxUARSummary(refNumber, aplikasi, periodYear, periodQuarter);
        iTrxUARSummaryRepository.save(newUARSummary);
    }

    private TrxUARSummary buildTrxUARSummary(String refNumber, String aplikasi, Integer periodYear, String periodQuarter) {
        TrxUARSummary newUARReport = new TrxUARSummary();

        newUARReport.setAplikasi(aplikasi);
        newUARReport.setPeriodYear(periodYear);
        newUARReport.setPeriodQuarter(periodQuarter);
        newUARReport.setRefNumber(refNumber);

        return newUARReport;
    }

    public ResponseModel<ResponseListModel<UARSummaryModel>> getListUARSummary(String refNumber, String triwulan, String tahun, String aplikasi, Integer pageNumber, Integer pageSize) {
        Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
        Page<TrxUARSummary> uarSummaryPageable = iTrxUARSummaryRepository.findByRefNumberByTriwulanByTahunByAplikasi(refNumber, triwulan, Integer.valueOf(tahun), aplikasi, pageable);

        Map<String, MsUserIDApplication> applicationMap = msUserIDApplicationService.getUserIDAppMap();
        List<UARSummaryModel> uarSummaries = uarSummaryPageable.getContent().stream()
                .map(data -> data.toUARSummaryModel(applicationMap))
                .collect(Collectors.toList());

        ResponseListModel<UARSummaryModel> responseDetails = ResponseListModel.<UARSummaryModel>builder()
                .data(uarSummaries)
                .page(pageNumber)
                .limit(pageSize)
                .totalPages(uarSummaryPageable.getTotalPages())
                .totalItems(uarSummaryPageable.getTotalElements())
                .build();

        return ResponseModel.<ResponseListModel<UARSummaryModel>>builder().type(TYPE_UAR_UPM_SUMMARY_GET_LIST).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(responseDetails).build();
    }

    public ResponseModel<ResUploadModel> getListUARAsCSV(String status, String aplikasi, Integer periodYear, String periodQuarter, String searchFlag, String searchData) throws Exception {
        Page<TrxUARRequest> uars = findUARBySearchFlagByAplikasiByYearByQuarterInStatuses(buildStatusList(status), aplikasi, periodYear, periodQuarter, searchFlag, searchData, Pageable.unpaged());
        List<UARModel> uarModels = mapper.mapToUARModelList(uars);

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("\""+TICKET_ID_COL_NAME+"\",\""+JENIS_APLIKASI_COL_NAME+"\",\""+NIK_COL_NAME+"\",\""+NAMA_USER_COL_NAME+"\",\""+KEWENANGAN_COL_NAME+"\",\""+JABATAN_COL_NAME+"\",\""+UNIT_KERJA_COL_NAME+"\",\""+KONFIRMASI_AKSES_COL_NAME+"\",\""+KETERANGAN_COL_NAME+"\",\""+PIC_PROCESS_COL_NAME+"\",\""+PIC_APPROVE_COL_NAME+"\",\""+TAHUN_COL_NAME+"\",\""+TRIWULAN_COL_NAME+"\""+"\r\n");
        uarModels.forEach(data -> stringBuffer.append("\""+data.getTicketId()+"\",\""+data.getAplikasi()+"\",\""+"'"+data.getNik()+"\",\""+data.getNamaUser()+"\",\""+data.getKewenangan()+"\",\""+data.getJabatan()+"\",\""+data.getUnitKerja()+"\",\""+data.getKonfirmasiAkses()+"\",\""+data.getKeterangan()+"\",\""+data.getPicProcess()+"\",\""+data.getPicApprove()+"\",\""+data.getPeriodeTahun()+"\",\""+data.getPeriodeTriwulan()+"\""+"\r\n"));

        Map<String, String> result = minioService.uploadReportFile(stringBuffer.toString().getBytes("windows-1252"), DocumentHelper.generateReportFilePath(UAR_FILE_NAME, CSV_EXTENSION), UAR_FILE_NAME);

        return ResponseModel.<ResUploadModel>builder().type(TYPE_UAR_DOWNLOAD).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(new ResUploadModel(result)).build();
    }

    public ResFileDownload directDownloadUARAsCSV(String status, String aplikasi, Integer periodYear, String periodQuarter, String searchFlag, String searchData) throws Exception {
        Page<TrxUARRequest> uars = findUARBySearchFlagByAplikasiByYearByQuarterInStatuses(buildStatusList(status), aplikasi, periodYear, periodQuarter, searchFlag, searchData, Pageable.unpaged());
        List<UARModel> uarModels = mapper.mapToUARModelList(uars);

        StringBuffer csv = new StringBuffer();
        csv.append("\""+TICKET_ID_COL_NAME+"\",\""+JENIS_APLIKASI_COL_NAME+"\",\""+NIK_COL_NAME+"\",\""+NAMA_USER_COL_NAME+"\",\""+KEWENANGAN_COL_NAME+"\",\""+JABATAN_COL_NAME+"\",\""+UNIT_KERJA_COL_NAME+"\",\""+KONFIRMASI_AKSES_COL_NAME+"\",\""+KETERANGAN_COL_NAME+"\",\""+PIC_PROCESS_COL_NAME+"\",\""+PIC_APPROVE_COL_NAME+"\",\""+TAHUN_COL_NAME+"\",\""+TRIWULAN_COL_NAME+"\""+"\r\n");
        uarModels.forEach(data -> csv.append("\""+data.getTicketId()+"\",\""+data.getAplikasi()+"\",\""+"'"+data.getNik()+"\",\""+data.getNamaUser()+"\",\""+data.getKewenangan()+"\",\""+data.getJabatan()+"\",\""+data.getUnitKerja()+"\",\""+data.getKonfirmasiAkses()+"\",\""+data.getKeterangan()+"\",\""+data.getPicProcess()+"\",\""+data.getPicApprove()+"\",\""+data.getPeriodeTahun()+"\",\""+data.getPeriodeTriwulan()+"\""+"\r\n"));

        return new ResFileDownload(csv.toString().getBytes("windows-1252"), CommonHelper.concateTwoString(UAR_FILE_NAME, CSV_EXTENSION));
    }
}
