package com.btpns.fin.service;

import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.NotificationModel;
import com.btpns.fin.model.entity.TrxComment;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.ITrxCommentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class TrxCommentService {
    @Autowired
    ITrxCommentRepository trxCommentRepository;

    public TrxComment getLastCommentByTicketId(String ticketId){
        return trxCommentRepository.getLastCommentByTicketId(ticketId);
    }

    public List<TrxComment> getListCommentByTicketId(String ticketId){
        return trxCommentRepository.getListCommentByTicketId(ticketId);
    }

    public Page<TrxComment> getListCommentByStatus(String status, int pageNumMin1, int pageSize){
        String order = status.equals(COMMENT_STATUS_DELIVER) ? "createDateTime" : "readDateTime";
        Pageable sortedByCommentId = PageRequest.of(pageNumMin1, pageSize, Sort.by(order).descending());
        return trxCommentRepository.getListCommentByStatus(status, sortedByCommentId);
    }

    public TrxComment getCommentByTicketIdAndCommentId(String ticketId,String commentId){
        return  trxCommentRepository.getCommentByTicketIdAndCommentId(ticketId, commentId);
    }

    @Transactional
    public TrxComment updateTrxComment(TrxComment trxComment, String status){
        Optional<TrxComment> existTrxComment = trxCommentRepository.findById(trxComment.getCommentId());
        if(existTrxComment.isPresent()){
            LocalDateTime dateNow = LocalDateTime.now();
            trxComment.setStatus(status);
            trxComment.setReadDateTime(dateNow);
        }
        return trxCommentRepository.save(trxComment);
    }

    @Transactional
    public TrxComment saveTrxComment(TrxComment trxComment){
        return trxCommentRepository.save(trxComment);
    }

    public ResponseModel<NotificationModel> getCommentStatus() {
        NotificationModel notificationModel = new NotificationModel();
        notificationModel.setCommentUnread(FALSE_FLAG_BOOL);

        List<TrxComment> trxComment = trxCommentRepository.findAllCommentByStatus(COMMENT_STATUS_DELIVER);
        if (trxComment.size() > 0){
            notificationModel.setCommentUnread(TRUE_FLAG_BOOL);
        }

        return buildResponse(SUCCESS, notificationModel);
    }

    private ResponseModel<NotificationModel> buildResponse(ResponseStatus status, NotificationModel notificationModel) {
        ResponseModel<NotificationModel> response = new ResponseModel<>();

        response.setType(TYPE_NOTIFICATOIN_STATUS_GET);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(notificationModel);

        return response;
    }
}