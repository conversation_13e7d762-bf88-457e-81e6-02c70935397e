package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.DocumentHelper;
import com.btpns.fin.model.MsEGLS;
import com.btpns.fin.model.entity.MsOfficerNR;
import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResUploadModel;
import com.btpns.fin.model.response.ResponseListModel;
import com.btpns.fin.model.response.ResponseModel;
import com.btpns.fin.repository.IMsOfficerNRRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.SUCCESS;

@Service
public class EGLSUserIdService {
    @Autowired
    private IMsOfficerNRRepository iMsOfficerNRRepository;

    @Autowired
    private MinioService minioService;

    public ResponseModel<ResponseListModel<MsEGLS>> getEGLSUserIDs(Integer pageSize, Integer pageNumber, String searchFlag, String searchData, String status) {
        Page<MsOfficerNR> eglsPageable = findEGLSByUsernameOrFirstnameOrLastname(pageSize, pageNumber, searchFlag, searchData, status);

        List<MsEGLS> data = eglsPageable.getContent().stream().map(MsOfficerNR::toEGLS).collect(Collectors.toList());

        ResponseListModel<MsEGLS> details = ResponseListModel.<MsEGLS>builder()
                .data(data)
                .page(pageNumber)
                .limit(pageSize)
                .totalPages(eglsPageable.getTotalPages())
                .totalItems(eglsPageable.getTotalElements())
                .build();

        return ResponseModel.<ResponseListModel<MsEGLS>>builder()
                .type(TYPE_MS_EGLS_MANAGEMENT_GET_LIST)
                .status(SUCCESS.getCode())
                .statusDesc(SUCCESS.getValue())
                .details(details).build();
    }

    private Page<MsOfficerNR> findEGLSByUsernameOrFirstnameOrLastname(Integer pageSize, Integer pageNumber, String searchFlag, String searchData, String accountStatus) {
        Page<MsOfficerNR> eglsPageable = new PageImpl<>(new ArrayList<>());
        Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);

        if (searchFlag.isBlank() && searchData.isBlank()){
            eglsPageable = iMsOfficerNRRepository.findEGLSByStatusUser(accountStatus, pageable);
        }else {
            if (SEARCH_FLAG_USERNAME.equals(searchFlag)) {
                eglsPageable = iMsOfficerNRRepository.findEGLSByUsername(searchData, accountStatus, pageable);
            }
            if (SEARCH_FLAG_FIRST_NAME.equals(searchFlag)) {
                eglsPageable = iMsOfficerNRRepository.findEGLSByFirstName(searchData, accountStatus, pageable);
            }
            if (SEARCH_FLAG_LAST_NAME.equals(searchFlag)) {
                eglsPageable = iMsOfficerNRRepository.findEGLSByLastName(searchData, accountStatus, pageable);
            }
        }
        return eglsPageable;
    }

    public ResponseModel<ResUploadModel> getEGLSCsv(String accountStatus) throws Exception {
        List<MsOfficerNR> officerNRList = iMsOfficerNRRepository.findAllEGLSBySrcSystem(EGLS, accountStatus);
        List<MsEGLS> eglsList = officerNRList.stream().map(MsOfficerNR::toEGLS).collect(Collectors.toList());

        byte[] byteCSV = makeEglsCsv(eglsList);

        Map<String, String> result = minioService.uploadReportFile(byteCSV, DocumentHelper.generateReportFilePath(EGLS, CSV_EXTENSION), EGLS);

        return ResponseModel.<ResUploadModel>builder().type(TYPE_MS_EGLS_MANAGEMENT_DOWNLOAD).status(SUCCESS.getCode()).statusDesc(SUCCESS.getValue()).details(new ResUploadModel(result)).build();
    }

    public ResFileDownload directDownloadAlihDayaCSV(String accountStatus) throws Exception {
        List<MsOfficerNR> officerNRList = iMsOfficerNRRepository.findAllEGLSBySrcSystem(EGLS, accountStatus);
        List<MsEGLS> eglsList = officerNRList.stream().map(MsOfficerNR::toEGLS).collect(Collectors.toList());

        byte[] byteCSV = makeEglsCsv(eglsList);

        return new ResFileDownload(byteCSV, CommonHelper.concateTwoString(EGLS, CSV_EXTENSION));
    }

    private byte[] makeEglsCsv(List<MsEGLS> eglsList) throws Exception {
        StringBuffer csv = new StringBuffer();

        csv.append("\""+USERNAME_COL_NAME+"\",\""+FIRST_NAME_COL_NAME+"\",\""+LAST_NAME_COL_NAME+"\",\""+ROLE_ID_COL_NAME+"\",\""+ROLE_NAME_COL_NAME+"\",\""+EMAIL_COL_NAME+"\",\""+KODE_CABANG_COL_NAME+"\",\""+NAMA_CABANG_COL_NAME+"\",\""+ACCOUNT_STATUS_COL_NAME+"\",\""+LAST_LOGON_COL_NAME+"\""+"\r\n");
        eglsList.forEach(data -> csv.append("\""+"\'"+data.getUsername()+"\",\""+data.getFirstName()+"\",\""+data.getLastName()+"\",\""+data.getRoleID()+"\",\""+data.getRoleName()+"\",\""+data.getEmail()+"\",\""+data.getKodeCabang()+"\",\""+data.getNamaCabang()+"\",\""+data.getAccountStatus()+"\",\""+data.getLastLogon()+"\""+"\r\n"));

        return csv.toString().getBytes("windows-1252");
    }
}
