package com.btpns.fin.helper;

import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.TrxFuidRequestDTO;
import com.btpns.fin.model.request.TrxSetupParamRequestDTO;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.helper.Constants.TIKET;

@Component
public class RequestHelper {
    private static final int UNIQUE_ID_SIZE = 15;

    private static final String HEADER_EMAIL_API_VERSION = "X-Api-Version";
    private static final String HEADER_CHANNEL_ID = "X-Channel-Id";
    private static final String HEADER_NODE = "X-Node";
    private static final String HEADER_CORRELATION_ID = "X-Correlation-Id";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_STAN_ID = "X-Stan-Id";
    private static final String HEADER_TRANSMISSION_DATE_TIME = "X-Transmission-Date-Time";
    private static final String HEADER_TERMINAL_ID = "X-Terminal-Id";
    private static final String HEADER_API_KEY = "X-Api-Key";
    private static final String DATE_NO_SPACE_PATTERN = "yyyyMMdd";
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";
    private static final String EMAIL_SUFFIX = "@mail.btpnsyariah.com";
    private static final String HEADER_TERMINAL_NAME = "X-Terminal-Name";
    private static final String HEADER_ACQ_ID = "X-Acq-Id";
    private static final String HEADER_ORGUNIT_ID = "X-orgUnit-Id";

    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_NO_SPACE_PATTERN);

    @Value("${mail.api.version}")
    private String mailApiVersion;

    @Value("${channel.id}")
    protected String xChannelId;
    @Value("${node}")
    protected String xNode;
    @Value("${terminal.id}")
    protected String terminalId;
    @Value("${stage}")
    private String envStage;

    @Value("${mail.sender}")
    private String emailFrom;
    @Value("${mail.target}")
    private String emailTarget;

    @Value("${mail.cc.enable}")
    private boolean isMailCCEnable;
    @Value("${mail.cc}")
    private String emailCc;

    @Value("${url.fuid.detail}")
    private String urlFuidDetail;
    @Value("${url.param.detail}")
    private String urlParamDetail;

    @Value("${url.uar.detail}")
    private String urlUARDetail;

    @Value("${terminal.name}")
    protected String terminalName;
    @Value("${acq.id}")
    private String acqId;
    @Value("${orgunit.id}")
    private String orgunitId;
    @Value("${header.api_key}")
    private String apiKey;

    public Map<String, String> createEmailHeader() {
        Map<String, String> headerMap = createHeaderRequest(UUID.randomUUID().toString());
        headerMap.put(HEADER_EMAIL_API_VERSION, mailApiVersion);

        return headerMap;
    }

    public Map<String, String> createHeaderRequest(String correlationId) {
        Map<String, String> cosHeader = new HashMap<>();
        cosHeader.put(HEADER_CHANNEL_ID, xChannelId);
        cosHeader.put(HEADER_NODE, xNode);
        cosHeader.put(HEADER_CORRELATION_ID, correlationId);
        cosHeader.put(HEADER_CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        cosHeader.put(HEADER_STAN_ID, generateUniqId());
        cosHeader.put(HEADER_TRANSMISSION_DATE_TIME, getTransactionTime());
        cosHeader.put(HEADER_TERMINAL_ID, terminalId);
        cosHeader.put(HEADER_TERMINAL_NAME, terminalName);
        cosHeader.put(HEADER_ACQ_ID, acqId);
        cosHeader.put(HEADER_ORGUNIT_ID, orgunitId);
        cosHeader.put(HEADER_API_KEY, apiKey);
        return cosHeader;
    }

    private String getTransactionTime() {
        return new LocalDateTime().toString(DATE_TIME_PATTERN);
    }

    private String generateUniqId() {
        return UniqId.newId(UNIQUE_ID_SIZE);
    }

    public String getEmailUsingNik(String nik) {
        return nik.concat(EMAIL_SUFFIX);
    }

    private boolean isNotProductionEnv() {
        return !Constants.ENV_PROD.contains(envStage);
    }

    public String createContentApprovalFuid(TrxFuidRequest trxFuidRequest, MsEmployee waitingPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxFuidRequest.getDataKodeCabang() + " - " + trxFuidRequest.getDataNamaCabang();
        String alasan = trxFuidRequest.getAlasan();
        String alasanPengajuan = trxFuidRequest.getAlasanPengajuan() == null ? "" : trxFuidRequest.getAlasanPengajuan();
        String infoTambahan = trxFuidRequest.getInfoTambahan() == null ? "" : trxFuidRequest.getInfoTambahan();
        String urlRef = urlFuidDetail + trxFuidRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
            .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
            .append("Berikut ini informasi tiket membutuhkan persetujuan Bapak/Ibu :").append("<br />")
            .append("<table>")
            .append("<tr><td>Nomor Tiket</td><td>: ").append(trxFuidRequest.getTicketId()).append("</td></tr>")
            .append("<tr><td>Nik</td><td>: ").append(trxFuidRequest.getDataNik()).append("</td></tr>")
            .append("<tr><td>Nama</td><td>: ").append(trxFuidRequest.getDataNamaLengkap()).append("</td></tr>")
            .append("<tr><td>Jabatan</td><td>: ").append(trxFuidRequest.getDataJabatan()).append("</td></tr>")
            .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
            .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxFuidRequest.getTujuan()).append("</td></tr>")
            .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxFuidRequest.getAplikasi()).append("</td></tr>")
            .append("<tr><td>Deskripsi</td><td>: ").append(alasan).append("</td></tr>")
            .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
            .append("<tr><td></td><td>  ").append(infoTambahan).append("</td></tr>")
            .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxFuidRequest.getCreateDateTime())).append("</td></tr>")
            .append("<tr><td>Status Tiket</td><td>: ").append(trxFuidRequest.getTrxFuidApproval().getCurrentState()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
            .append("</table><br /><br />")
            .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
            .append("<a href=\"").append(urlRef).append("?role=approval").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
            .append("<br /><br />")
            .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
            .append("<br />")
            .append("Layanan [UPM] User & Parameter Management")
            .append("<br />")
            .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectApprovalFuid(String fuidTicket) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("PUK Approval Tiket " + fuidTicket);
        return sb.toString();
    }

    public String createContentApprovalSetupParam(TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee waitingPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxSetupParamRequest.getDataKodeCabang() + " - " + trxSetupParamRequest.getDataNamaCabang();
        String paramLama = trxSetupParamRequest.getParameterLama() == null ? "" : trxSetupParamRequest.getParameterLama();
        String paramBaru = trxSetupParamRequest.getParameterBaru() == null ? "" : trxSetupParamRequest.getParameterBaru();
        String alasanPengajuan = trxSetupParamRequest.getAlasanPengajuan() == null ? "" : trxSetupParamRequest.getAlasanPengajuan();
        String urlRef = urlParamDetail + trxSetupParamRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket membutuhkan persetujuan Bapak/Ibu :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxSetupParamRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxSetupParamRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxSetupParamRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxSetupParamRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxSetupParamRequest.getKategoriParamName()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxSetupParamRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(paramLama).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(paramBaru).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxSetupParamRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxSetupParamRequest.getTrxSetupParamApproval().getCurrentState()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=approval").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectApprovalSetupParam(String setupParamTicket) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("PUK Approval Tiket " + setupParamTicket);
        return sb.toString();
    }

    public BodyEmail createRequestFuidEmail(String userNik, TrxFuidRequest trxFuidRequest, MsEmployee waitingApprovalPUK) {
        BodyEmail bodyEmail = new BodyEmail();
        bodyEmail.setTo(getEmailUsingNik(userNik));
        bodyEmail.setMessage(createContentRequestFuid(trxFuidRequest, waitingApprovalPUK));
        bodyEmail.setType(Constants.TYPE_MESSAGING_EMAIL);
        bodyEmail.setFrom(emailFrom);
        List<Attachment> attachmentList = new ArrayList<Attachment>();
        bodyEmail.setAttachment(attachmentList);

        String fuidTicket = trxFuidRequest.getTicketId();
        bodyEmail.setSubject(createSubjectRequestFuid(fuidTicket));
        if (isMailCCEnable) {
            bodyEmail.setCc(emailCc);
        } else {
            bodyEmail.setCc("");
        }
        bodyEmail.setBcc("");
        return bodyEmail;
    }

    public String createContentRequestFuid(TrxFuidRequest trxFuidRequest, MsEmployee waitingApprovalPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxFuidRequest.getDataKodeCabang() + " - " + trxFuidRequest.getDataNamaCabang();
        String alasan = trxFuidRequest.getAlasan();
        String alasanPengajuan = trxFuidRequest.getAlasanPengajuan() == null ? "" : trxFuidRequest.getAlasanPengajuan();
        String infoTambahan = trxFuidRequest.getInfoTambahan() == null ? "" : trxFuidRequest.getInfoTambahan();
        String urlRef = urlFuidDetail + trxFuidRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket Bapak/Ibu:").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxFuidRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxFuidRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxFuidRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxFuidRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxFuidRequest.getTujuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxFuidRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(alasan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(infoTambahan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxFuidRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxFuidRequest.getTrxFuidApproval().getCurrentState()).append(getWaitingPUKInfo(waitingApprovalPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectRequestFuid(String fuidTicket) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Informasi Tiket Baru " + fuidTicket);
        return sb.toString();
    }

    public String createContentRequestFuidSpesimen(TrxFuidRequest trxFuidRequest) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxFuidRequest.getDataKodeCabang() + " - " + trxFuidRequest.getDataNamaCabang();
        String alasan = trxFuidRequest.getAlasan();
        String alasanPengajuan = trxFuidRequest.getAlasanPengajuan() == null ? "" : trxFuidRequest.getAlasanPengajuan();
        String infoTambahan = trxFuidRequest.getInfoTambahan() == null ? "" : trxFuidRequest.getInfoTambahan();
        String urlRef = urlFuidDetail + trxFuidRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket Bapak/Ibu:").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxFuidRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxFuidRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxFuidRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxFuidRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxFuidRequest.getTujuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxFuidRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(alasan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(infoTambahan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxFuidRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxFuidRequest.getTrxFuidApproval().getCurrentState()).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createContentRequestSetupParam(TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee waitingPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxSetupParamRequest.getDataKodeCabang() + " - " + trxSetupParamRequest.getDataNamaCabang();
        String paramLama = trxSetupParamRequest.getParameterLama() == null ? "" : trxSetupParamRequest.getParameterLama();
        String paramBaru = trxSetupParamRequest.getParameterBaru() == null ? "" : trxSetupParamRequest.getParameterBaru();
        String alasanPengajuan = trxSetupParamRequest.getAlasanPengajuan() == null ? "" : trxSetupParamRequest.getAlasanPengajuan();
        String urlRef = urlParamDetail + trxSetupParamRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket Bapak/Ibu:").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxSetupParamRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxSetupParamRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxSetupParamRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxSetupParamRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxSetupParamRequest.getKategoriParamName()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxSetupParamRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(paramLama).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(paramBaru).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxSetupParamRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxSetupParamRequest.getTrxSetupParamApproval().getCurrentState()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectRequestSetupParam(String setupParamTicket) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Informasi Tiket Baru " + setupParamTicket);
        return sb.toString();
    }

    public String createContentFuidApproved(TrxFuidRequestDTO trxFuidRequest, MsEmployee waitingPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxFuidRequest.getDataKodeCabang() + " - " + trxFuidRequest.getDataNamaCabang();
        String alasan = trxFuidRequest.getAlasan();
        String alasanPengajuan = trxFuidRequest.getAlasanPengajuan() == null ? "" : trxFuidRequest.getAlasanPengajuan();
        String infoTambahan = trxFuidRequest.getInfoTambahan() == null ? "" : trxFuidRequest.getInfoTambahan();
        String urlRef = urlFuidDetail + trxFuidRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket yang telah mendapatkan persetujuan oleh PUK :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxFuidRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxFuidRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxFuidRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxFuidRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxFuidRequest.getTujuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxFuidRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(alasan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(infoTambahan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxFuidRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxFuidRequest.getTrxFuidApproval().getCurrentState()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    private String getWaitingPUKInfo(MsEmployee waitingPUK) {
        if (waitingPUK != null) {
            return " (".concat(waitingPUK.getNik()).concat(" - ").concat("Bapak/Ibu ").concat(waitingPUK.getFullName()).concat(")");
        }
        return "";
    }

    public String createSubjectApproved(String fuidTicket) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Pengajuan Telah Mendapatkan Persetujuan Oleh PUK Tiket " + fuidTicket);
        return sb.toString();
    }

    public String createContentSetupParamApproved(TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee waitingPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxSetupParamRequest.getDataKodeCabang() + " - " + trxSetupParamRequest.getDataNamaCabang();
        String paramLama = trxSetupParamRequest.getParameterLama() == null ? "" : trxSetupParamRequest.getParameterLama();
        String paramBaru = trxSetupParamRequest.getParameterBaru() == null ? "" : trxSetupParamRequest.getParameterBaru();
        String alasanPengajuan = trxSetupParamRequest.getAlasanPengajuan() == null ? "" : trxSetupParamRequest.getAlasanPengajuan();
        String urlRef = urlParamDetail + trxSetupParamRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket yang telah mendapatkan persetujuan oleh PUK :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxSetupParamRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxSetupParamRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxSetupParamRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxSetupParamRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxSetupParamRequest.getKategoriParamName()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxSetupParamRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(paramLama).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(paramBaru).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxSetupParamRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxSetupParamRequest.getTrxSetupParamApproval().getCurrentState()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createContentFuidRejected(TrxFuidRequestDTO trxFuidRequest, MsEmployee rejectedPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxFuidRequest.getDataKodeCabang() + " - " + trxFuidRequest.getDataNamaCabang();
        String alasan = trxFuidRequest.getAlasan();
        String alasanPengajuan = trxFuidRequest.getAlasanPengajuan() == null ? "" : trxFuidRequest.getAlasanPengajuan();
        String infoTambahan = trxFuidRequest.getInfoTambahan() == null ? "" : trxFuidRequest.getInfoTambahan();
        String urlRef = urlFuidDetail + trxFuidRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket yang telah belum mendapatkan persetujuan oleh PUK :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxFuidRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxFuidRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxFuidRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxFuidRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxFuidRequest.getTujuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxFuidRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(alasan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(infoTambahan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxFuidRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxFuidRequest.getTrxFuidApproval().getCurrentState()).append(getWaitingPUKInfo(rejectedPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectRejected(String fuidTicket) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Pengajuan Belum Mendapatkan Persetujuan PUK Tiket " + fuidTicket);
        return sb.toString();
    }

    public String createContentSetupParamRejected(TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee rejectedPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxSetupParamRequest.getDataKodeCabang() + " - " + trxSetupParamRequest.getDataNamaCabang();
        String paramLama = trxSetupParamRequest.getParameterLama() == null ? "" : trxSetupParamRequest.getParameterLama();
        String paramBaru = trxSetupParamRequest.getParameterBaru() == null ? "" : trxSetupParamRequest.getParameterBaru();
        String alasanPengajuan = trxSetupParamRequest.getAlasanPengajuan() == null ? "" : trxSetupParamRequest.getAlasanPengajuan();
        String urlRef = urlParamDetail + trxSetupParamRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket yang telah belum mendapatkan persetujuan oleh PUK :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxSetupParamRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxSetupParamRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxSetupParamRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxSetupParamRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxSetupParamRequest.getKategoriParamName()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxSetupParamRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(paramLama).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(paramBaru).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxSetupParamRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxSetupParamRequest.getTrxSetupParamApproval().getCurrentState()).append(getWaitingPUKInfo(rejectedPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectUPMInprogress(String ticketId) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Tiket Sedang di Proses oleh UPM " + ticketId);
        return sb.toString();
    }

    public String createContentUPMInprogress(ContentEmail contentEmail, MsEmployee upmInfo) {
        String urlRef = urlFuidDetail + contentEmail.getNomorTiket();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket yang sedang di proses oleh UPM :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(contentEmail.getNomorTiket()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(contentEmail.getNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(contentEmail.getNama()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(contentEmail.getJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(contentEmail.getKodeDanNamaCabang()).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(contentEmail.getJenisPengajuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(contentEmail.getAplikasiPengajuan()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(contentEmail.getDeskripsi()).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(contentEmail.getTanggalTiket()).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(contentEmail.getStatusTiket()).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectUPMDone(String ticketId) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Tiket Selesai Oleh UPM " + ticketId);
        return sb.toString();
    }

    public String createContentUPMDone(EmailUpmModel emailUpmModel, ContentEmail contentEmail, MsEmployee upmInfo) {
        String urlRef = urlFuidDetail + emailUpmModel.getTicketId();
        String notes = getInformasiUPM(emailUpmModel);
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket yang sudah selesai oleh UPM :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(contentEmail.getNomorTiket()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(contentEmail.getNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(contentEmail.getNama()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(contentEmail.getJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(contentEmail.getKodeDanNamaCabang()).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(contentEmail.getJenisPengajuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(contentEmail.getAplikasiPengajuan()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(contentEmail.getDeskripsi()).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(contentEmail.getTanggalTiket()).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(contentEmail.getStatusTiket()).append("</td></tr>")
                .append("<tr style=\"vertical-align:top\"><td>Informasi UPM</td><td>: ").append(notes).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String getInformasiUPM(EmailUpmModel emailUpmModel){
        //mapping new line notes
        String notes = emailUpmModel.getNotes();
        String arrNote[] = notes.split("\\r?\\n");
        String newNotes = arrNote[0];
        if(arrNote.length > 0) {
            for (int i = 1; i < arrNote.length; i++) {
                newNotes = newNotes + "<br />" + arrNote[i];
            }
        }
        return newNotes;
    }

    public String createSubjectUPMReject(String ticketId) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Tiket di Tolak oleh UPM " + ticketId);
        return sb.toString();
    }

    public String createContentUPMReject(EmailUpmModel emailUpmModel, ContentEmail contentEmail, MsEmployee upmInfo) {
        String urlRef = urlFuidDetail + emailUpmModel.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket yang di tolak/Reject oleh UPM :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(contentEmail.getNomorTiket()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(contentEmail.getNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(contentEmail.getNama()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(contentEmail.getJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(contentEmail.getKodeDanNamaCabang()).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(contentEmail.getJenisPengajuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(contentEmail.getAplikasiPengajuan()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(contentEmail.getDeskripsi()).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(contentEmail.getTanggalTiket()).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(contentEmail.getStatusTiket()).append("</td></tr>")
                .append("<tr><td>Alasan Reject</td><td>: ").append(emailUpmModel.getNotes()).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Silahkan lakukan perbaikan untuk permohonan tersebut").append("<br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectEmailDelegatedPUK() {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Informasi Delegasi User PUK TEMA");
        return sb.toString();
    }

    public String createContentEmailDelegatedPUK(TrxDelegation trxDelegation) {
        StringBuilder sb = new StringBuilder();
        sb.append("<b>Berikut adalah informasi delegasi persetujuan PUK pada TEMA</b>").append("<br /><br />")
                .append("<b>Pemberi Tugas</b>").append("<br />")
                .append("<table>")
                .append("<tr><td>NIK</td><td>: ").append(trxDelegation.getNikRequester()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxDelegation.getNamaRequester()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxDelegation.getJabatanRequester()).append("</td></tr>")
                .append("<tr><td>Tgl Mulai</td><td>: ").append(DateTimeHelper.getDateDelegationAsString(trxDelegation.getStartDate())).append("</td></tr>")
                .append("<tr><td>Tgl Selesai</td><td>: ").append(DateTimeHelper.getDateDelegationAsString(trxDelegation.getEndDate())).append("</td></tr>")
                .append("<tr><td>Keterangan</td><td>: ").append(trxDelegation.getInfo()).append("</td></tr>")
                .append("</table><br /><br />")
                .append("<table>")
                .append("<b>Penerima Tugas</b>").append("<br />")
                .append("<tr><td>NIK</td><td>: ").append(trxDelegation.getNikDelegation()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxDelegation.getNamaDelegation()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxDelegation.getJabatanDelegation()).append("</td></tr>")
                .append("</table><br /><br /><br />")
                .append("<b>Selanjutnya persetujuan TEMA dalam periode tersebut akan memerlukan persetujuan Penerima Tugas</b>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createContentApproval2Fuid(TrxFuidRequestDTO trxFuidRequest, MsEmployee waitingPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxFuidRequest.getDataKodeCabang() + " - " + trxFuidRequest.getDataNamaCabang();
        String alasan = trxFuidRequest.getAlasan();
        String alasanPengajuan = trxFuidRequest.getAlasanPengajuan() == null ? "" : trxFuidRequest.getAlasanPengajuan();
        String infoTambahan = trxFuidRequest.getInfoTambahan() == null ? "" : trxFuidRequest.getInfoTambahan();
        String urlRef = urlFuidDetail + trxFuidRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket membutuhkan persetujuan Bapak/Ibu :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxFuidRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxFuidRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxFuidRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxFuidRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxFuidRequest.getTujuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxFuidRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(alasan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(infoTambahan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxFuidRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxFuidRequest.getTrxFuidApproval().getCurrentState()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=approval").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectApproval2(String ticketId) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("PUK2 Approval Tiket " + ticketId);
        return sb.toString();
    }

    public String createContentApproval2SetupParam(TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee waitingPUK) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = trxSetupParamRequest.getDataKodeCabang() + " - " + trxSetupParamRequest.getDataNamaCabang();
        String paramLama = trxSetupParamRequest.getParameterLama() == null ? "" : trxSetupParamRequest.getParameterLama();
        String paramBaru = trxSetupParamRequest.getParameterBaru() == null ? "" : trxSetupParamRequest.getParameterBaru();
        String alasanPengajuan = trxSetupParamRequest.getAlasanPengajuan() == null ? "" : trxSetupParamRequest.getAlasanPengajuan();
        String urlRef = urlParamDetail + trxSetupParamRequest.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket membutuhkan persetujuan Bapak/Ibu :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(trxSetupParamRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(trxSetupParamRequest.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(trxSetupParamRequest.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(trxSetupParamRequest.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(trxSetupParamRequest.getKategoriParamName()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(trxSetupParamRequest.getAplikasi()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(paramLama).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(paramBaru).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(trxSetupParamRequest.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(trxSetupParamRequest.getTrxSetupParamApproval().getCurrentState()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=approval").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectUPMComment(String ticketId) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
              .append(envStage.toUpperCase())
              .append("]")
              .append(" - ");
        }
        sb.append("Tiket ").append(ticketId).append("  di Komentari oleh UPM");
        return sb.toString();
    }

    public String createContentUPMComment(TrxComment trxComment) {
        StringBuilder sb = new StringBuilder();
        String urlRef = urlParamDetail + trxComment.getTicketId();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi ticket yang di komentari oleh UPM :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>:</td><td>").append(trxComment.getTicketId()).append("</td></tr>")
                .append("<tr style=\"vertical-align:top\"><td>Isi Komentar</td><td>:</td><td>").append(CommonHelper.getStringLine(trxComment.getContent())).append("</td></tr>")
                .append("</table><br /><br />")
                .append("----------------------------------------------------------------------------------").append("<br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut : <br />")
                .append("<a href=\"").append(urlRef).append("?role=user").append("\" target=\"_blank\">").append("LIHAT TIKET").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectExpiredTicket(String ticketId) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
                    .append(envStage.toUpperCase())
                    .append("]")
                    .append(" - ");
        }
        sb.append("Penonaktifan User ID Tiket ").append(ticketId);
        return sb.toString();
    }

    public String createContentExpiredTicket(TrxExpiredFuid trxExpiredFuid) {
        String urlRef = urlFuidDetail + trxExpiredFuid.getTicketId();

        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
          .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
          .append("Berdasarkan pengajuan nomor tiket ").append(trxExpiredFuid.getTicketId())
          .append(" dimana pengajuan sudah berakhir pada tanggal ").append(DateTimeHelper.getDateToDateStringDDMMYYYY(trxExpiredFuid.getTanggalMasaBerlaku()))
          .append(" maka user tersebut sudah di nonaktifkan.").append("<br /><br />")
          .append("Terima Kasih").append("<br /><br />")
          .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
          .append("<a href=\"").append(urlRef).append("?role=approval").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
          .append("<br /><br />")
          .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
          .append("<br />")
          .append("Layanan [UPM] User & Parameter Management")
          .append("<br />")
          .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectUARUserConfirmation(TrxUARRequest uarRequest, String appDesc) {
        StringBuilder sb = new StringBuilder();
        appendEnvDev(sb);
        sb.append("User Access Review Aplikasi ").append(appDesc).append(" ").append(uarRequest.getPeriodQuarter()).append(" Triwulan ").append(uarRequest.getPeriodYear());
        return sb.toString();
    }

    private void appendEnvDev(StringBuilder sb) {
        if (isNotProductionEnv()) {
            sb.append("[")
                    .append(envStage.toUpperCase())
                    .append("]")
                    .append(" - ");
        }
    }

    public String createContentUARUserConfirmation(TrxUARRequest uarRequest, String appDesc, String paramDetailDesc, Map<String, MsHolidayList> holidayMap) {
        String urlRef = urlUARDetail + uarRequest.getTicketId();

        StringBuilder sb = new StringBuilder();

        LocalDate createdDate = LocalDate.now();
        if (uarRequest.getReminder() != null && uarRequest.getReminder() > 0) {
            sb.append("Reminder ").append(uarRequest.getReminder()).append("<br /><br />");
            createdDate = uarRequest.getCreatedAt().toLocalDate();
        }

        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Dear Bapak/Ibu,").append("<br /><br />")
                .append("Sehubungan dilakukannya User Access Review Aplikasi ").append(appDesc)
                .append(" oleh team User & Parameter Management (UPM) kantor Pusat BTPN Syariah, dengan proses ini kami ingin mengetahui apakah user ").append(appDesc)
                .append(" masih dipergunakan atau tidak. Mohon bantuannya untuk konfirmasi melalui Tema-Web.").append("<br /><br />")
                .append(" Berikut ini informasi tiket UAR :").append("<br /><br />");

        appendUARData(sb, uarRequest, appDesc, paramDetailDesc, Constants.EMPTY, Constants.EMPTY);

        sb.append("Karena konfirmasi data ini sangat penting, mohon bantuannya untuk merespon paling lambat ")
                .append(DateTimeHelper.getDatePlus4WorkdaysAsString(createdDate, holidayMap)).append("<br /><br />")
                .append("Demikian informasi yang dapat kami sampaikan. Terimakasih.").append("<br /><br />");

        appendFooterEmail(sb, urlRef);

        return sb.toString();
    }

    private static void appendUARData(StringBuilder sb, TrxUARRequest uarRequest, String appDesc, String currentStatusDesc, String pukName, String notes) {
        sb.append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(uarRequest.getTicketId()).append("</td></tr>")
                .append("<tr><td>NIK</td><td>: ").append(uarRequest.getNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(uarRequest.getNamaUser()).append("</td></tr>")
                .append("<tr><td>Kewenangan</td><td>: ").append(uarRequest.getKewenangan()).append("</td></tr>");
        if (uarRequest.getJabatan() != null) {
            sb.append("<tr><td>Jabatan</td><td>: ").append(uarRequest.getJabatan()).append("</td></tr>");
        }

        sb.append("<tr><td>Unit Kerja</td><td>: ").append(uarRequest.getUnitKerja()).append("</td></tr>")
                .append("<tr><td>Aplikasi UAR</td><td>: ").append(appDesc).append("</td></tr>");

        if (uarRequest.getTrxUARApproval().getUserConfirmation() == null) {
            sb.append("<tr><td>Status Konfirmasi</td><td>: ").append("User ID Aktif/Hapus").append("</td></tr>");
        } else {
            sb.append("<tr><td>Status Konfirmasi</td><td>: ").append("User ID ").append(Mapper.getConfirmationStatusValue(uarRequest.getTrxUARApproval().getUserConfirmation())).append("</td></tr>");
        }

        sb.append("<tr><td>Keterangan</td><td>: ").append(uarRequest.getTrxUARApproval().getUserNikNotes() != null ? uarRequest.getTrxUARApproval().getUserNikNotes() : Constants.EMPTY).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(currentStatusDesc);
        if (!pukName.isEmpty()) {
            sb.append(" (").append(uarRequest.getTrxUARApproval().getPukNik()).append(" - Bapak/Ibu ").append(pukName).append(")");
        }
        if (!notes.isEmpty()) {
            sb.append("<tr><td>Alasan</td><td>: ").append(notes);
        }

        sb.append("</table><br /><br />");
    }

    private static void appendFooterEmail(StringBuilder sb, String urlRef) {
        sb.append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");
    }

    public String createSubjectUARPUKApproval(TrxUARRequest uarRequest, String appDesc) {
        StringBuilder sb = new StringBuilder();
        appendEnvDev(sb);
        sb.append("Persetujuan User Access Review Aplikasi ").append(appDesc).append(" ").append(uarRequest.getPeriodQuarter()).append(" Triwulan ").append(uarRequest.getPeriodYear());
        return sb.toString();
    }

    public String createContentUARPUKApproval(TrxUARRequest uarRequest, String appDesc, String currentStatusDesc) {
        String urlRef = urlUARDetail + uarRequest.getTicketId();

        StringBuilder sb = new StringBuilder();

        if (uarRequest.getReminder() != null && uarRequest.getReminder() > 0) {
            sb.append("Reminder ").append(uarRequest.getReminder()).append("<br /><br />");
        }

        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket UAR yang membutuhkan persetujuan Bapak/Ibu : ").append("<br /><br />");

        appendUARData(sb, uarRequest, appDesc, currentStatusDesc, uarRequest.getTrxUARApproval().getPukName(), Constants.EMPTY);

        appendFooterEmail(sb, urlRef);

        return sb.toString();
    }

    public String createSubjectUARPUKApproved(TrxUARRequest uarRequest, String appDesc) {
        StringBuilder sb = new StringBuilder();
        appendEnvDev(sb);
        sb.append("Disetujui PUK User Access Review Aplikasi ").append(appDesc).append(" ").append(uarRequest.getPeriodQuarter()).append(" Triwulan ").append(uarRequest.getPeriodYear());
        return sb.toString();
    }

    public String createContentUARPUKApproved(TrxUARRequest uarRequest, String appDesc, String currentStatusDesc) {
        String urlRef = urlUARDetail + uarRequest.getTicketId();

        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket UAR yang telah mendapatkan persetujuan oleh PUK : ").append("<br /><br />");

        appendUARData(sb, uarRequest, appDesc, currentStatusDesc, Constants.EMPTY, Constants.EMPTY);

        appendFooterEmail(sb, urlRef);

        return sb.toString();
    }

    public String createSubjectUARDone(TrxUARRequest uarRequest, String appDesc) {
        StringBuilder sb = new StringBuilder();
        appendEnvDev(sb);
        sb.append("[Status Done] User Access Review Aplikasi ").append(appDesc).append(" ").append(uarRequest.getPeriodQuarter()).append(" Triwulan ").append(uarRequest.getPeriodYear());
        return sb.toString();
    }

    public String createContentUARDone(TrxUARRequest uarRequest, String appDesc, String currentStatusDesc) {
        String urlRef = urlUARDetail + uarRequest.getTicketId();

        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket UAR yang sudah selesai :").append("<br /><br />");

        appendUARData(sb, uarRequest, appDesc, currentStatusDesc, Constants.EMPTY, Constants.EMPTY);

        appendFooterEmail(sb, urlRef);

        return sb.toString();
    }


    public String createSubjectUARRejected(TrxUARRequest uarRequest, String appDesc, String rejectedBy) {
        StringBuilder sb = new StringBuilder();
        appendEnvDev(sb);
        sb.append("Ditolak ").append(rejectedBy).append(" User Access Review Aplikasi ").append(appDesc).append(" ").append(uarRequest.getPeriodQuarter()).append(" Triwulan ").append(uarRequest.getPeriodYear());
        return sb.toString();
    }

    public String createContentUARRejected(TrxUARRequest uarRequest, String appDesc, String currentStatusDesc, String rejectedBy, String notes) {
        String urlRef = urlUARDetail + uarRequest.getTicketId();

        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket UAR yang tidak mendapatkan persetujuan oleh ").append(rejectedBy).append(": <br /><br />");

        appendUARData(sb, uarRequest, appDesc, currentStatusDesc, Constants.EMPTY, notes);

        appendFooterEmail(sb, urlRef);

        return sb.toString();
    }

    public String createContentDelegatedApprovalFuid(ContentEmail contentEmail, MsEmployee waitingPUK) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket membutuhkan persetujuan Bapak/Ibu :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(contentEmail.getNomorTiket()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(contentEmail.getNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(contentEmail.getNama()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(contentEmail.getJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(contentEmail.getKodeDanNamaCabang()).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(contentEmail.getJenisPengajuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(contentEmail.getAplikasiPengajuan()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(contentEmail.getDeskripsi()).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(contentEmail.getAlasanPengajuan()).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(contentEmail.getInfoTambahan()).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(contentEmail.getTanggalTiket()).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(contentEmail.getStatusTiket()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlFuidDetail + contentEmail.getNomorTiket()).append("?role=user").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }
    public String createContentDelegatedApprovalSetupParam(ContentEmail contentEmail, MsEmployee waitingPUK) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket membutuhkan persetujuan Bapak/Ibu :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(contentEmail.getNomorTiket()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(contentEmail.getNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(contentEmail.getNama()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(contentEmail.getJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(contentEmail.getKodeDanNamaCabang()).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(contentEmail.getJenisPengajuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(contentEmail.getAplikasiPengajuan()).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(contentEmail.getDeskripsi()).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(contentEmail.getTanggalTiket()).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(contentEmail.getStatusTiket()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlParamDetail + contentEmail.getNomorTiket()).append("?role=approval").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectUARReminder(TrxUARRequest uarRequest, String appDesc) {
        StringBuilder sb = new StringBuilder();
        appendEnvDev(sb);
        sb.append("Reminder ").append(uarRequest.getReminder()).append(": User Access Review Aplikasi ").append(appDesc).append(" ").append(uarRequest.getPeriodQuarter()).append(" Triwulan ").append(uarRequest.getPeriodYear());

        return sb.toString();
    }

    public String createSubjectAndContentDataCoreSystem() {
        LocalDate date = LocalDate.now();

        StringBuilder sb = new StringBuilder();
        appendEnvDev(sb);
        sb.append("Data T24 dan Prospera Periode ")
          .append(date.getMonth())
          .append(" ")
          .append(date.getYear());

        return sb.toString();
    }

    public String createSubjectReminderApproval(String ticketId, Integer puk1ApprovalReminder, Integer puk2ApprovalReminder, Boolean isWaitingPuk1) {
        StringBuilder sb = new StringBuilder();
        if (isNotProductionEnv()) {
            sb.append("[")
                    .append(envStage.toUpperCase())
                    .append("]")
                    .append(" - ");
        }
        sb.append("Reminder ");
        if (isWaitingPuk1){
            sb.append(puk1ApprovalReminder);
        }else {
            sb.append(puk2ApprovalReminder);
        }
        if (isWaitingPuk1){
            sb.append(" PUK1 Approval Tiket " + ticketId);
        }else {
            sb.append(" PUK2 Approval Tiket " + ticketId);
        }
        return sb.toString();
    }

    public String createContentReminderApprovalFuid(PUKApprovalReminder pukApprovalReminder, MsEmployee waitingPUK, String aplikasi) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = pukApprovalReminder.getDataKodeCabang() + " - " + pukApprovalReminder.getDataNamaCabang();
        String alasan = pukApprovalReminder.getDeskripsi();
        String alasanPengajuan = pukApprovalReminder.getAlasanPengajuan() == null ? "" : pukApprovalReminder.getAlasanPengajuan();
        String infoTambahan = pukApprovalReminder.getInfoTambahan() == null ? "" : pukApprovalReminder.getInfoTambahan();
        String urlRef = urlFuidDetail + pukApprovalReminder.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket membutuhkan persetujuan Bapak/Ibu :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(pukApprovalReminder.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(pukApprovalReminder.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(pukApprovalReminder.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(pukApprovalReminder.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(pukApprovalReminder.getJenisPengajuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(aplikasi).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(alasan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(infoTambahan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(pukApprovalReminder.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(pukApprovalReminder.getCurrentStateDesc()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=approval").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createContentReminderApprovalSetupParam(PUKApprovalReminder pukApprovalReminder, MsEmployee waitingPUK, String aplikasi) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String kodeNamaCabang = pukApprovalReminder.getDataKodeCabang() + " - " + pukApprovalReminder.getDataNamaCabang();
        String paramLama = pukApprovalReminder.getDeskripsi() == null ? "" : pukApprovalReminder.getDeskripsi();
        String paramBaru = pukApprovalReminder.getAlasanPengajuan() == null ? "" : pukApprovalReminder.getAlasanPengajuan();
        String alasanPengajuan = pukApprovalReminder.getInfoTambahan() == null ? "" : pukApprovalReminder.getInfoTambahan();
        String urlRef = urlParamDetail + pukApprovalReminder.getTicketId();
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Berikut ini informasi tiket membutuhkan persetujuan Bapak/Ibu :").append("<br />")
                .append("<table>")
                .append("<tr><td>Nomor Tiket</td><td>: ").append(pukApprovalReminder.getTicketId()).append("</td></tr>")
                .append("<tr><td>Nik</td><td>: ").append(pukApprovalReminder.getDataNik()).append("</td></tr>")
                .append("<tr><td>Nama</td><td>: ").append(pukApprovalReminder.getDataNamaLengkap()).append("</td></tr>")
                .append("<tr><td>Jabatan</td><td>: ").append(pukApprovalReminder.getDataJabatan()).append("</td></tr>")
                .append("<tr><td>Kode & Nama Cabang</td><td>: ").append(kodeNamaCabang).append("</td></tr>")
                .append("<tr><td>Jenis Pengajuan</td><td>: ").append(pukApprovalReminder.getJenisPengajuan()).append("</td></tr>")
                .append("<tr><td>Aplikasi Pengajuan</td><td>: ").append(aplikasi).append("</td></tr>")
                .append("<tr><td>Deskripsi</td><td>: ").append(paramLama).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(paramBaru).append("</td></tr>")
                .append("<tr><td></td><td>  ").append(alasanPengajuan).append("</td></tr>")
                .append("<tr><td>Tanggal Tiket</td><td>: ").append(dateFormater1.format(pukApprovalReminder.getCreateDateTime())).append("</td></tr>")
                .append("<tr><td>Status Tiket</td><td>: ").append(pukApprovalReminder.getCurrentStateDesc()).append(getWaitingPUKInfo(waitingPUK)).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Untuk Informasi lebih lanjut silahkan akses melalui link TEMA berikut :").append("<br />")
                .append("<a href=\"").append(urlRef).append("?role=approval").append("\" target=\"_blank\">").append("[TEMA-WEB]").append("</a>")
                .append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");

        return sb.toString();
    }

    public String createSubjectSendReportPermohonan(String reportType, String startDate, String endDate) {
        StringBuilder sb = new StringBuilder();
        appendEnvDev(sb);
        if (TIKET.equalsIgnoreCase(reportType)){
            sb.append("Data Report Permohonan Per Tiket Tanggal ");
        }else {
            sb.append("Data Report Permohonan Per Aplikasi Tanggal ");
        }
        sb.append(startDate.split(" ")[0])
          .append(" - ")
          .append(endDate.split(" ")[0]);
        return sb.toString();
    }

    public String createContentSendReportPermohonan(String reportType, String startDate, String endDate) {
        StringBuilder sb = new StringBuilder();
        if (TIKET.equalsIgnoreCase(reportType)){
            sb.append("Terlampir file report permohonan per tiket tanggal ");
        }else {
            sb.append("Terlampir file report permohonan per aplikasi tanggal ");
        }
        sb.append(startDate.split(" ")[0])
          .append(" - ")
          .append(endDate.split(" ")[0]);
        return sb.toString();
    }

    public String createSubjectPreviousDelegatedApproval(String ticketId) {
        StringBuilder sb = new StringBuilder();
        sb.append("Tiket")
                .append(" ")
                .append(ticketId)
                .append(" ")
                .append("Sudah Di Alihkan ke PUK Lain");
        return sb.toString();
    }

    public String createContentPreviousDelegatedApproval(String ticketId, MsEmployee waitingPUK) {
        StringBuilder sb = new StringBuilder();
        sb.append("Assalamualaikum Wr Wb").append("<br /><br />")
                .append("Terima kasih telah menggunakan TEMA BTPN Syariah").append("<br /><br />")
                .append("Untuk tiket").append(" ")
                .append(ticketId).append(" ")
                .append("sudah <b>dialihkan persetujuan</b> nya kepada")
                .append(getWaitingPUKInfo(waitingPUK)).append(" ")
                .append("atas permintaan user pemohon.").append("<br />")
                .append("Mohon untuk mengabaikan email sebelumnya.").append("<br /><br />")
                .append("Terima Kasih").append("<br /><br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply")
                .append("<br />")
                .append("Layanan [UPM] User & Parameter Management")
                .append("<br />")
                .append("Email : <EMAIL>");
        return sb.toString();
    }
}
