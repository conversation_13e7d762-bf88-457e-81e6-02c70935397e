package com.btpns.fin.helper;

import com.btpns.fin.model.MsEmployeeDirectorLocalMemory;
import com.btpns.fin.model.entity.MsEmployeeDirector;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Component
public class MsEmployeeDirectorLocalMemoryHelper {
    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    MsEmployeeDirectorLocalMemory msEmployeeDirectorLocalMemory;

    MsEmployeeDirectorLocalMemoryHelper(){
        msEmployeeDirectorLocalMemory = new MsEmployeeDirectorLocalMemory();
    }

    public String getNIKOptimaDirector(String nikLdap) {
        String result = "";

        LocalDateTime dateTimeNow = LocalDateTime.now();
        if (msEmployeeDirectorLocalMemory.getInsertDateTime() != null){
            if (checkAgeOfMemory(msEmployeeDirectorLocalMemory.getInsertDateTime(), dateTimeNow) > 15){
                List<MsEmployeeDirector> msEmployeeDirector = iMsEmployeeDirectorRepository.findAll();
                if (msEmployeeDirector != null){
                    saveData(msEmployeeDirector, dateTimeNow);
                    result = getNikOptimaDirector(msEmployeeDirector, nikLdap);
                }
            }else {
                result = getNikOptimaDirector(msEmployeeDirectorLocalMemory.getMsEmployeeDirector(), nikLdap);
            }
        }else {
            List<MsEmployeeDirector> msEmployeeDirector = iMsEmployeeDirectorRepository.findAll();
            if (msEmployeeDirector != null) {
                saveData(msEmployeeDirector, dateTimeNow);
                result = getNikOptimaDirector(msEmployeeDirector, nikLdap);
            }
        }

        return result;
    }

    private long checkAgeOfMemory(LocalDateTime insertTime, LocalDateTime dateTimeNow) {
        return ChronoUnit.MINUTES.between(insertTime, dateTimeNow);
    }

    private String getNikOptimaDirector(List<MsEmployeeDirector> msEmployeeDirector, String nikLdap) {
        String result = nikLdap;
        for (MsEmployeeDirector director : msEmployeeDirector) {
            if (nikLdap.equalsIgnoreCase(director.getNikLdap())) {
                result = director.getNikOptima();
                break;
            }
        }
        return result;
    }

    private void saveData(List<MsEmployeeDirector> msEmployeeDirector, LocalDateTime dateTimeNow) {
        msEmployeeDirectorLocalMemory.setInsertDateTime(dateTimeNow);
        msEmployeeDirectorLocalMemory.setMsEmployeeDirector(msEmployeeDirector);
    }
}
