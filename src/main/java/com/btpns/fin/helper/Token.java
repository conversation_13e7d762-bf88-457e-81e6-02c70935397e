package com.btpns.fin.helper;
import com.google.gson.Gson;
import java.util.Base64;

public class Token {
    private String header;
    private String payload;
    private String unknown;
    private Profile profile;
    private String dataProfile;
    private Object oHeader;
    private String dataHeader;
    private Object oUnkown;
    private String dataUnknown;
    private String countChunks;

    public Token(String bearer){
        String bearerNative = bearer.replaceAll("Bearer ","");
        String[] chunks = bearerNative.split("\\.");
        countChunks = String.valueOf(chunks.length);
        Base64.Decoder decoder = Base64.getDecoder();
        header = new String(decoder.decode(chunks[0]));
        payload = new String(decoder.decode(chunks[1]));
        //unknown = new String(decoder.decode(chunks[2]));
        Gson gson = new Gson();
        profile = gson.fromJson(payload, Profile.class);
        oHeader = gson.fromJson(header, Object.class);
        oUnkown = gson.fromJson(unknown, Object.class);
        dataProfile = gson.toJson(profile);
        dataHeader = gson.toJson(oHeader);
        //dataUnknown = gson.toJson(oUnkown);
    }

    public String getHeader() {
        return header;
    }

    public String getPayload() {
        return payload;
    }

    public Profile getProfile() {
        return profile;
    }

    public String getDataProfile() {
        return dataProfile;
    }

    public String getDataHeader() {
        return dataHeader;
    }

    public String getDataUnknown(){
        return dataUnknown;
    }

    public  String getCountChunks(){
        return countChunks;
    }
}
