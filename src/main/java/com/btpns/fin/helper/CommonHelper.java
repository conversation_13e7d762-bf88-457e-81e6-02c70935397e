package com.btpns.fin.helper;

import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.ReqUserIDModel;
import com.btpns.fin.repository.IMsProsperaRoleRepository;
import com.btpns.fin.service.MsEmployeeService;
import com.btpns.fin.service.MsSystemParamService;
import com.btpns.fin.service.MsTemaApplicationService;
import com.google.gson.Gson;
import liquibase.util.file.FilenameUtils;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.*;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.GZIPOutputStream;

import static com.btpns.fin.helper.Constants.*;

@Component
public class CommonHelper {
    private static Integer maximumUpmLimitForBwmpNominalInput;

    @Autowired
    public CommonHelper(@Value("${max.upm.limit.for.bwmp.nominal.input}") Integer maximumUpmLimitForBwmpNominalInput) {
        this.maximumUpmLimitForBwmpNominalInput = maximumUpmLimitForBwmpNominalInput;
    }

    @Autowired
    MsEmployeeService msEmployeeService;
    @Autowired
    IMsProsperaRoleRepository iMsProsperaRoleRepository;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsSystemParamService msSystemParamService;

    public static String getProfile(Gson gson, Token token) {
        if (token != null && token.getProfile() != null && token.getProfile().getProfile() != null) {
            JSONObject profile = new JSONObject((Map) token.getProfile().getProfile());
            profile.put("preferred_username", token.getProfile().getPreferred_username());
            return gson.toJson(profile);
        } else if (token != null && token.getProfile() != null && token.getProfile().getPreferred_username() != null) {
            return gson.toJson(token.getProfile().getPreferred_username());
        } else if (token != null && token.getProfile() != null) {
            return gson.toJson(token.getProfile());
        } else if (token != null) {
            return gson.toJson(token);
        }
        return "{Token not found}";
    }

    public static Map.Entry<String, String> getPeriod(String period) {
        LocalDate dateNow = LocalDate.now();
        LocalTime timeNow = LocalTime.now();
        LocalTime timeEndCutOff = LocalTime.of(18, 30);
        LocalDateTime dateTimeNow = LocalDateTime.now();
        LocalDateTime startDateTime = dateTimeNow;
        LocalDateTime endDateTime = dateTimeNow;

        if (period.equals(Constants.PARAM_PERIOD_DAILY)) {
            //daily
            if (timeNow.compareTo(timeEndCutOff) < 0) {
                LocalDate dateHMin1 = dateNow.minus(Period.ofDays(1));
                startDateTime = LocalDateTime.of(dateHMin1, timeEndCutOff);
            } else {
                startDateTime = LocalDateTime.of(dateNow, timeEndCutOff);
            }
        } else if (period.equals(Constants.PARAM_PERIOD_WEEKLY)) {
            //weekly
            int dayOfWeekInt = dateNow.getDayOfWeek().getValue();
            int tempDayDif = 0;
            if (dayOfWeekInt <= 4) {
                tempDayDif = dayOfWeekInt + 2;
                LocalDate startDate = dateNow.minus(Period.ofDays(tempDayDif));
                startDateTime = LocalDateTime.of(startDate, timeEndCutOff);
            } else {
                if (dayOfWeekInt == 5) {
                    if (timeNow.compareTo(timeEndCutOff) < 0) {
                        LocalDate startDate = dateNow.minus(Period.ofWeeks(1));
                        startDateTime = LocalDateTime.of(startDate, timeEndCutOff);
                    } else {
                        startDateTime = LocalDateTime.of(dateNow, timeEndCutOff);
                    }
                } else if (dayOfWeekInt == 6) {
                    tempDayDif = 1;
                    LocalDate startDate = dateNow.minus(Period.ofDays(tempDayDif));
                    startDateTime = LocalDateTime.of(startDate, timeEndCutOff);
                } else if (dayOfWeekInt == 7) {
                    tempDayDif = 2;
                    LocalDate startDate = dateNow.minus(Period.ofDays(tempDayDif));
                    startDateTime = LocalDateTime.of(startDate, timeEndCutOff);
                }
            }
        } else if (period.equals(Constants.PARAM_PERIOD_MONTHLY)) {
            YearMonth month = YearMonth.from(dateNow);
            LocalDate firstDayOfMonth = month.atDay(1);
            LocalDate endDayOfMonth = month.atEndOfMonth();
            LocalDateTime cutOffMonthly = LocalDateTime.of(endDayOfMonth, timeEndCutOff);
            if (dateTimeNow.compareTo(cutOffMonthly) < 0) {
                startDateTime = LocalDateTime.of(firstDayOfMonth.minus(Period.ofDays(1)), timeEndCutOff);
            } else {
                startDateTime = LocalDateTime.of(dateNow, timeEndCutOff);
            }
        }

        String strStartDateTime = DateTimeHelper.getDateCreateDate(startDateTime);
        String strEndDateTime = DateTimeHelper.getDateCreateDate(endDateTime);
        return Map.entry(strStartDateTime, strEndDateTime);
    }

    public static Map.Entry<String, String> getPeriodEffectiveDate(String period) throws Exception {
        Map.Entry<String, String> mapPeriod = getPeriod(period);
        if (period.equalsIgnoreCase(Constants.PARAM_PERIOD_DAILY)) {
            return Map.entry(mapPeriod.getValue().split(" ")[0], mapPeriod.getValue().split(" ")[0]);
        }
        String strStartDateTime = LocalDate.parse(mapPeriod.getKey().split(" ")[0]).plusDays(1).toString();
        String strEndDateTime = mapPeriod.getValue().split(" ")[0];

        return Map.entry(strStartDateTime, strEndDateTime);
    }

    public static boolean validateGetDetail(String nikRequester, String savedNikRequester, String nikPuk1, String nikPuk2, TrxUpmRole trxUpmRole) {
        boolean passValidation = false;
        if (nikRequester.toLowerCase().equals(savedNikRequester.toLowerCase()) || trxUpmRole != null) {
            passValidation = true;
        }
        if (nikPuk1 != null) {
            if (nikRequester.toLowerCase().equals(nikPuk1.toLowerCase())) {
                passValidation = true;
            }
        }
        if (nikPuk2 != null) {
            if (nikRequester.toLowerCase().equals(nikPuk2.toLowerCase())) {
                passValidation = true;
            }
        }
        return passValidation;
    }

    public static boolean validateUploadExtension(MultipartFile docs) {
        boolean passValidation = false;
        String extension = FilenameUtils.getExtension(docs.getOriginalFilename().toLowerCase());
        String[] arrApprovedExtension = {"pdf", "xls", "xlsx", "ods", "doc", "docs", "docx", "csv", "odt", "zip", "7z", "png", "jpg", "jpeg", "txt", "ppt", "pptx", "odp"};
        List<String> lApprovedExtension = Arrays.asList(arrApprovedExtension);
        if (lApprovedExtension.contains(extension)) {
            passValidation = true;
        }
        return passValidation;
    }

    public static boolean validateUPMTicket(String nikRequester, TrxUpmRole trxUpmRole) {
        boolean passValidation = false;
        if (trxUpmRole != null) {
            passValidation = true;
        }
        return passValidation;
    }

    public static void encodeRequestFuid(TrxFuidRequestModel requestModel) {
        requestModel.getFuid().setAlasanPengajuan(requestModel.getFuid().getAlasanPengajuan());
        requestModel.getFuid().setInfoTambahan(requestModel.getFuid().getInfoTambahan());

        requestModel.getFuid().getData().setJabatan(requestModel.getFuid().getData().getJabatan());
        requestModel.getFuid().getData().setNIK(encodeIfnotNull(requestModel.getFuid().getData().getNIK()));
        requestModel.getFuid().getData().setUserId(encodeIfnotNull(requestModel.getFuid().getData().getUserId()));
        requestModel.getFuid().getData().setTelepon(encodeIfnotNull(requestModel.getFuid().getData().getTelepon()));
        if (requestModel.getFuid().getData().getEmail() != null && requestModel.getFuid().getData().getEmail().split("@").length == 2) {
            String[] emailPart = requestModel.getFuid().getData().getEmail().split("@");
            requestModel.getFuid().getData().setEmail(encodeIfnotNull(emailPart[0]).concat("@").concat(emailPart[1]));
        } else if (requestModel.getFuid().getData().getEmail() != null) {
            requestModel.getFuid().getData().setEmail(encodeIfnotNull(requestModel.getFuid().getData().getEmail()));
        }
    }

    public static void encodeRequestSP(TrxSetupParamRequestModel request) {
        request.getSetupParameter().setParameterLama(request.getSetupParameter().getParameterLama());
        request.getSetupParameter().setParameterBaru(request.getSetupParameter().getParameterBaru());
        request.getSetupParameter().setAlasanPengajuan(request.getSetupParameter().getAlasanPengajuan());

        request.getSetupParameter().getData().setJabatan(request.getSetupParameter().getData().getJabatan());
        request.getSetupParameter().getData().setNIK(encodeIfnotNull(request.getSetupParameter().getData().getNIK()));
        request.getSetupParameter().getData().setTelepon(encodeIfnotNull(request.getSetupParameter().getData().getTelepon()));
        if (request.getSetupParameter().getData().getEmail() != null && request.getSetupParameter().getData().getEmail().split("@").length == 2) {
            String[] emailPart = request.getSetupParameter().getData().getEmail().split("@");
            request.getSetupParameter().getData().setEmail(encodeIfnotNull(emailPart[0]).concat("@").concat(emailPart[1]));
        } else if (request.getSetupParameter().getData().getEmail() != null) {
            request.getSetupParameter().getData().setEmail(encodeIfnotNull(request.getSetupParameter().getData().getEmail()));
        }
    }

    public static void encodeRequestDel(DelegationModel request) {
        request.setInfo(encodeIfnotNull(request.getInfo()));
    }

    private static String encodeIfnotNull(String data) {
        if (data != null) {
            return StringEscapeUtils.escapeHtml4(data);
        }
        return null;
    }

    public static boolean validateSubmitFuid(TrxFuidRequestModel trxFuidRequestModel) {
        boolean passValidation = true;
        FuidModel fm = trxFuidRequestModel.getFuid();
        DataFuidModel dfm = fm.getData();

        //tujuan
        if (fm.getTujuan() != null) {
            if (!fm.getTujuan().equals("")) {
                passValidation = findSpecialChar(fm.getTujuan(), Constants.TYPE1_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //alasan
        if (fm.getAlasan() != null) {
            if (!fm.getAlasan().equals("")) {
                passValidation = findSpecialChar(fm.getAlasan(), Constants.TYPE1_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //tingkatan
        if (fm.getTingkatan() != null) {
            if (!fm.getTingkatan().equals("")) {
                passValidation = findSpecialChar(fm.getTingkatan(), Constants.TYPE2_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //statusMasaBerlaku
        if (fm.getStatusMasaBerlaku() != null) {
            if (!fm.getStatusMasaBerlaku().equals("")) {
                passValidation = findSpecialChar(fm.getStatusMasaBerlaku(), Constants.TYPE2_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //masaBerlaku
        if (fm.getMasaBerlaku() != null) {
            if (fm.getMasaBerlaku().equals("")) {
                return false;
            }
        }
        //alasanPengajuan
        if (fm.getAlasanPengajuan() != null) {
            if (!fm.getAlasanPengajuan().equals("")) {
                passValidation = findSpecialChar(fm.getAlasanPengajuan(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //infoTambahan
        if (fm.getInfoTambahan() != null) {
            if (!fm.getInfoTambahan().equals("")) {
                passValidation = findSpecialChar(fm.getInfoTambahan(), Constants.TYPE3_REGEX);
            }
        }
        if (passValidation == false) {
            return false;
        }
        //namaLengkap
        if (dfm.getNamaLengkap() != null) {
            if (!dfm.getNamaLengkap().equals("")) {
                passValidation = findSpecialChar(dfm.getNamaLengkap(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //nik
        if (dfm.getNIK() != null) {
            if (!dfm.getNIK().equals("")) {
                passValidation = findSpecialChar(dfm.getNIK(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //jabatan
        if (dfm.getJabatan() != null) {
            if (!dfm.getJabatan().equals("")) {
                passValidation = findSpecialChar(dfm.getJabatan(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //kodeCabang
        if (dfm.getKodeCabang() != null) {
            if (!dfm.getKodeCabang().equals("")) {
                passValidation = findSpecialChar(dfm.getKodeCabang(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //email
        if (dfm.getEmail() != null) {
            if (!dfm.getEmail().equals("")) {
                passValidation = findSpecialChar(dfm.getEmail(), Constants.TYPE3_REGEX);
            }
        }
        if (passValidation == false) {
            return false;
        }
        //telepon
        if (dfm.getTelepon() != null) {
            if (!dfm.getTelepon().equals("")) {
                passValidation = findSpecialChar(dfm.getTelepon(), Constants.TYPE4_REGEX);
            }
        }

        return passValidation;
    }

    public static boolean validateSubmitSetupParam(TrxSetupParamRequestModel trxSetupParamRequestModel) {
        boolean passValidation = true;
        SetupParamModel spm = trxSetupParamRequestModel.getSetupParameter();
        DataSetupParamModel dspm = spm.getData();

        //paramLama
        if (spm.getParameterLama() != null) {
            if (!spm.getParameterLama().equals("")) {
                passValidation = findSpecialChar(spm.getParameterLama(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //paramBaru
        if (spm.getParameterBaru() != null) {
            if (!spm.getParameterBaru().equals("")) {
                passValidation = findSpecialChar(spm.getParameterBaru(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //alasanPengajuan
        if (spm.getAlasanPengajuan() != null) {
            if (!spm.getAlasanPengajuan().equals("")) {
                passValidation = findSpecialChar(spm.getAlasanPengajuan(), Constants.TYPE3_REGEX);
            }
        }
        if (passValidation == false) {
            return false;
        }
        //kategoriParam
        if (spm.getKategoriParam() != null) {
            if (!spm.getKategoriParam().equals("")) {
                passValidation = findSpecialChar(spm.getKategoriParam(), Constants.TYPE5_REGEX);
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //namaLengkap
        if (dspm.getNamaLengkap() != null) {
            if (!dspm.getNamaLengkap().equals("")) {
                passValidation = findSpecialChar(dspm.getNamaLengkap(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //nik
        if (dspm.getNIK() != null) {
            if (!dspm.getNIK().equals("")) {
                passValidation = findSpecialChar(dspm.getNIK(), Constants.TYPE3_REGEX);
            }
        }
        if (passValidation == false) {
            return false;
        }
        //email
        if (dspm.getEmail() != null) {
            if (!dspm.getEmail().equals("")) {
                passValidation = findSpecialChar(dspm.getEmail(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //jabatan
        if (dspm.getJabatan() != null) {
            if (!dspm.getJabatan().equals("")) {
                passValidation = findSpecialChar(dspm.getJabatan(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //kodeCabang
        if (dspm.getKodeCabang() != null) {
            if (!dspm.getKodeCabang().equals("")) {
                passValidation = findSpecialChar(dspm.getKodeCabang(), Constants.TYPE3_REGEX);
            } else {
                passValidation = false;
            }
        } else {
            passValidation = false;
        }
        if (passValidation == false) {
            return false;
        }
        //telepon
        if (dspm.getTelepon() != null) {
            if (!dspm.getTelepon().equals("")) {
                passValidation = findSpecialChar(dspm.getTelepon(), Constants.TYPE4_REGEX);
            }
        }

        return passValidation;
    }

    public static boolean findSpecialChar(String input, String type) {
        boolean passValidation = true;
        String charRegex = "";
        if (type.equals(Constants.TYPE1_REGEX)) {
            charRegex = "[^a-zA-Z_/]+";
        }
        if (type.equals(Constants.TYPE2_REGEX)) {
            charRegex = "[^a-zA-Z]+";
        }
        if (type.equals(Constants.TYPE3_REGEX)) {
            charRegex = "[<>\";#]+";
        }
        if (type.equals(Constants.TYPE4_REGEX)) {
            charRegex = "[^0-9]+";
        }
        if (type.equals(Constants.TYPE5_REGEX)) {
            charRegex = "[^a-zA-Z0-9]+";
        }
        Pattern pattern = Pattern.compile(charRegex, Pattern.CASE_INSENSITIVE);
        Matcher my_match = pattern.matcher(input);
        boolean check = my_match.find();
        if (check) {
            passValidation = false;
        }
        return passValidation;
    }

    public static String getHttpStatusDetail(HttpStatus httpStatus) {
        return httpStatus.value() + " - " + httpStatus.getReasonPhrase();
    }

    public static String getStringLine(String string) {
        String arrNote[] = string.split("\\r?\\n");
        if (arrNote.length > 1) {
            return Arrays.stream(arrNote).map(data -> data + "<br />").collect(Collectors.joining());
        }
        return string;
    }

    public static List<String> distinctList(List<String> list) {
        if (list != null && list.size() > 0) {
            return new ArrayList<>(new HashSet<>(list));
        }
        return list;
    }

    public static LocalDateTime getDateArriveUPM(LocalDateTime puk2Dt, LocalDateTime puk1Dt, LocalDateTime crateDate, Date effectiveDate) {
        LocalDateTime effectiveDateTime = effectiveDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime approveOrCreateDateTime = getApproveOrCreateTicket(puk2Dt, puk1Dt, crateDate);
        if (effectiveDateTime.isAfter(approveOrCreateDateTime)) {
            return effectiveDateTime;
        }
        return approveOrCreateDateTime;
    }

    public static LocalDateTime getApproveOrCreateTicket(LocalDateTime puk2Dt, LocalDateTime puk1Dt, LocalDateTime crateDate) {
        if (puk2Dt != null) {
            return puk2Dt;
        } else if (puk1Dt != null) {
            return puk1Dt;
        }
        return crateDate;
    }

    public static SlaModel calculateSLA(LocalDateTime ticketArrived, LocalDateTime ticketFinished, Map<String, MsHolidayList> holidayMap) {
        if (ticketArrived.getHour() < 18) {
            if (ticketArrived.toLocalDate().equals(ticketFinished.toLocalDate())) {
                return new SlaModel(1, "Start : " + ticketArrived + " Done :" + ticketFinished);
            }
            return new SlaModel(0, "Start : " + ticketArrived + " Done :" + ticketFinished);
        } else {
            LocalDate deadline = getNextWorkingDay(ticketArrived.toLocalDate(), holidayMap);
            Map<String, LocalDate> dateList = ticketArrived.toLocalDate().datesUntil(deadline).collect(Collectors.toMap(LocalDate::toString, Function.identity()));
            if (dateList.get(ticketFinished.toLocalDate().toString()) != null) {
                return new SlaModel(1, "Start : " + ticketArrived + " Done :" + ticketFinished);
            }
            return new SlaModel(0, "Start : " + ticketArrived + " Done :" + ticketFinished);
        }
    }

    private static LocalDate getNextWorkingDay(LocalDate date, Map<String, MsHolidayList> holidayMap) {
        date = date.plusDays(1);
        String dateString = date.toString();
        if (date.getDayOfWeek().getValue() == 6
                || date.getDayOfWeek().getValue() == 7 || holidayMap.get(dateString) != null) {
            return getNextWorkingDay(date, holidayMap);
        }
        return date;
    }

    public static Boolean isUserMMS(String occupation) {
        if (OCCUPATION_USER_MMS.contains(occupation)) {
            return TRUE_FLAG_BOOL;
        }
        return FALSE_FLAG_BOOL;
    }

    public static Boolean isUserWhitelistPropsera(Set<String> userWhitelistMenuProsperaByNik, Set<String> userWhitelistMenuProsperaByOfficeCode, String nik, List<MsOfficerNR> msOfficerNRList) {
        return (userWhitelistMenuProsperaByNik.stream().anyMatch(whitelist -> (whitelist.equalsIgnoreCase(nik) || ALL_WHITELIST.equalsIgnoreCase(whitelist)))
                || isWhiteListByOfficeCode(msOfficerNRList, userWhitelistMenuProsperaByOfficeCode));
    }

    private static boolean isWhiteListByOfficeCode(List<MsOfficerNR> msOfficerNRList, Set<String> userWhitelistMenuProsperaByOfficeCode) {
        List<String> officeCodeList = msOfficerNRList.stream().map(MsOfficerNR::getMmsCode).collect(Collectors.toList());
        return (officeCodeList.stream().anyMatch(userWhitelistMenuProsperaByOfficeCode::contains)
                || userWhitelistMenuProsperaByOfficeCode.stream().anyMatch(whitelist -> ALL_WHITELIST.equalsIgnoreCase(whitelist)));
    }

    public static String getEndDateTimeOnHold() {
        LocalDateTime dateTimeNow = LocalDateTime.now();
        YearMonth month = YearMonth.from(LocalDate.now());
        LocalDate endDayOfMonth = month.atEndOfMonth();
        LocalTime timeEndCutOff = LocalTime.of(18, 30);
        LocalDateTime cutOffMonthly = LocalDateTime.of(endDayOfMonth, timeEndCutOff);

        if (dateTimeNow.compareTo(cutOffMonthly) < 0) {
            return DateTimeHelper.getDateCreateDate(LocalDateTime.of(endDayOfMonth.plusMonths(2), timeEndCutOff));
        }
        return DateTimeHelper.getDateCreateDate(LocalDateTime.of(LocalDate.now().plusMonths(3), timeEndCutOff));
    }

    public static String getStartDateTimeOnHold() {
        LocalDateTime dateTimeNow = LocalDateTime.now();
        YearMonth month = YearMonth.from(LocalDate.now());
        LocalDate endDayOfMonth = month.atEndOfMonth();
        LocalTime timeEndCutOff = LocalTime.of(18, 30);
        LocalDateTime cutOffMonthly = LocalDateTime.of(endDayOfMonth, timeEndCutOff);

        if (dateTimeNow.compareTo(cutOffMonthly) < 0) {
            return DateTimeHelper.getDateCreateDate(LocalDateTime.of(LocalDate.now(), timeEndCutOff).minusDays(1));
        }
        return DateTimeHelper.getDateCreateDate(LocalDateTime.of(LocalDate.now(), timeEndCutOff));
    }

    public static boolean isTicketFuidUPMManual(String ticketId) {
        return ticketId.startsWith(PREFFIX_TICKET_FUID_UPM_MANUAL);
    }

    public static boolean isTicketFuidResignOptima(String ticketId) {
        return ticketId.startsWith(PREFFIX_TICKET_FUID_RESIGN_OPTIMA);
    }

    public static boolean isTicketFuidSimplifikasi(String ticketId) {
        return ticketId.startsWith(PREFFIX_TICKET_FUID_SIMPLIFIKASI);
    }

    public static boolean isTicketFuidProspera(String ticketId) {
        return ticketId.startsWith(PREFFIX_TICKET_FUID_PROSPERA);
    }

    public static boolean isTicketFuid(String ticketId) {
        return ticketId.startsWith(PREFFIX_TICKET_FUID);
    }

    public static boolean isTicketSP(String ticketId) {
        return ticketId.startsWith(PREFFIX_TICKET_SP);
    }

    public static boolean isTicketUAR(String ticketId) {
        return ticketId.startsWith(PREFFIX_TICKET_UAR);
    }

    public static char getNextChar(char c) {
        return (char) ((c + 1 - 'A') % ('Z' - 'A' + 1) + 'A');
    }

    public static String concateTwoString(String firstString, String secondString) {
        return firstString.concat(secondString);
    }

    public static String checkValueString(String strValue) {
        return (strValue != null && !strValue.isEmpty()) ? strValue : EMPTY;
    }

    public static String buildTemplateUPMNotesRegisterProspera(TrxFuidRequest tfr, String role, String centerCodeOfficer, String password, String statusProgress) {
        String result = "";

        if (isTicketFuidSimplifikasi(tfr.getTicketId())) {
            if (TUJUAN_PENDAFTARAN_BARU.equalsIgnoreCase(tfr.getTujuan()) && ALASAN_MUTASI_ROTASI_PROMOSI.equalsIgnoreCase(tfr.getAlasan())) {
                result = templateUPMNotesRegisterProsperaFUSMutasi(tfr, role, centerCodeOfficer, password);
            } else {
                result = templateUPMNotesRegisterProsperaFUS(tfr, role, centerCodeOfficer, password);
            }
        } else {
            result = templateUPMNotesRegisterProsperaFUP(tfr, role, centerCodeOfficer, statusProgress);
        }

        return result;
    }

    private static String templateUPMNotesRegisterProsperaFUSMutasi(TrxFuidRequest tfr, String role, String centerCodeOfficer, String password) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n")
                .append("Pengajuan User Id Karyawan Mutasi sudah selesai di proses\n")
                .append("Untuk user id dan password email tidak ada perubahan, Untuk Login TERRA dan Agendaku menggunakan NIK dan password E-Mail\n\n")
                .append("Berikut informasi user Prospera (Terra/Agendaku) :\n")
                .append("NIK\t\t\t\t\t\t: ").append(tfr.getDataNik()).append("\n")
                .append("Nama\t\t\t\t\t: ").append(tfr.getDataNamaLengkap()).append("\n")
                .append("Role\t\t\t\t\t: ").append(role).append("\n")
                .append("Kode dan Nama Cabang\t: ").append(tfr.getDataKodeCabang()).append(" - ").append(tfr.getDataNamaCabang()).append("\n\n")
                .append("Step selanjutnya silahkan untuk melakukan setting email di tablet dengan cara open tiket di service now dengan klik link berikut").append("\n")
                .append(IT_SUPPORT_URL).append(" atau bisa dengan menghubungi IT Service Desk di email").append("\n")
                .append(IT_HELPDESK_EMAIL).append("\n\n")
                .append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    private static String templateUPMNotesRegisterProsperaFUS(TrxFuidRequest tfr, String role, String centerCodeOfficer, String password) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n")
                .append("Pengajuan user id sudah selesai di proses\n\n")
                .append("Berikut informasi user id Prospera :\n")
                .append("NIK\t\t\t\t\t\t: ").append(tfr.getDataNik()).append("\n")
                .append("Nama\t\t\t\t\t: ").append(tfr.getDataNamaLengkap()).append("\n")
                .append("Role\t\t\t\t\t: ").append(role).append("\n");
        if (centerCodeOfficer != null) {
            sb.append("Kode Officer\t\t\t\t: ").append(centerCodeOfficer).append("\n");
        }
        sb.append("Kode dan Nama Cabang\t: ").append(tfr.getDataKodeCabang()).append(" - ").append(tfr.getDataNamaCabang()).append("\n\n")
                .append("Berikut informasi user id Email :\n")
                .append("User ID\t\t\t\t\t: ").append(tfr.getDataNik().concat("@mail.btpnsyariah.com")).append("\n")
                .append("Password\t\t\t\t: ").append(password != null ? password : PSW_TEMPALTE_UPM).append("\n\n")
                .append("Sebelum login pastikan melakukan pergantian password terlebih dahulu").append("\n\n")
                .append("Cara melakukan pergantian di WEB corpmail.btpnsyariah.com(OWA) silahkan klik link dibawah").append("\n")
                .append("https://corpmail.btpnsyariah.com/reset").append("\n\n")
                .append("Password harus mengikuti ketentuan sbb :").append("\n")
                .append("1. Tidak menggunakan 13 password terakhir.").append("\n")
                .append("2. Minimal 8 karakter, termasuk huruf besar, kecil, angka dan/atau karakter khusus.").append("\n")
                .append("3. Password tidak mengandung firstname atau lastname.").append("\n\n")
                .append("Step selanjutnya silahkan untuk melakukan setting email di tablet dengan cara open tiket di service now dengan klik link berikut").append("\n")
                .append(IT_SUPPORT_URL).append(" atau bisa dengan menghubungi IT Service Desk di email").append("\n")
                .append(IT_HELPDESK_EMAIL).append("\n")
                .append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    private static String templateUPMNotesRegisterProsperaFUP(TrxFuidRequest tfr, String role, String centerCodeOfficer, String statusProgress) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n");
        if (STATUS_SEDANG_DIPROSES.equalsIgnoreCase(statusProgress)) {
            sb.append("Pengajuan Pendaftaran user id Prospera (Terra/Agendaku) sedang di proses\n");
        } else {
            sb.append("Pengajuan Pendaftaran user id Prospera (Terra/Agendaku) sudah selesai di proses\n");
        }
        sb.append("Untuk Login TERRA/AGENDAKU Menggunakan NIK dan password email\n")
                .append("Berikut informasi user id Prospera (Terra/Agendaku) :\n")
                .append("NIK\t\t\t\t\t\t: ").append(tfr.getDataNik()).append("\n")
                .append("Nama\t\t\t\t\t: ").append(tfr.getDataNamaLengkap()).append("\n")
                .append("Peran (Role)\t\t\t\t: ").append(role).append("\n");
        if (centerCodeOfficer.isEmpty()) {
            sb.append("Kode Officer\t\t\t\t: ").append("Kode Officer akan di generate setelah tiket selesai di proses").append("\n");
        } else {
            sb.append("Kode Officer\t\t\t\t: ").append(centerCodeOfficer).append("\n");
        }
        sb.append("Kode dan Nama Cabang\t: ").append(tfr.getDataKodeCabang()).append(" - ").append(tfr.getDataNamaCabang()).append("\n\n");
        sb.append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    public static String buildTemplateUPMNotesDeleteProspera(TrxFuidRequest tfr, String statusProgress) {
        String result = "";

        if (isTicketFuidSimplifikasi(tfr.getTicketId())) {
            if (TUJUAN_PENGHAPUSAN.equalsIgnoreCase(tfr.getTujuan()) && ALASAN_MUTASI_ROTASI_PROMOSI.equalsIgnoreCase(tfr.getAlasan())) {
                result = templateUPMNotesDeleteProsperaFUP(tfr, STATUS_DONE);
            } else {
                result = templateUPMNotesDeleteProsperaFUS(tfr);
            }
        } else {
            result = templateUPMNotesDeleteProsperaFUP(tfr, statusProgress);
        }

        return result;
    }

    private static String templateUPMNotesDeleteProsperaFUS(TrxFuidRequest tfr) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n")
                .append("Berikut informasi user id Batal Joint yang sudah di nonaktifkan di system\n")
                .append("NIK\t\t\t\t\t\t: ").append(tfr.getDataNik()).append("\n")
                .append("Nama\t\t\t\t\t: ").append(tfr.getDataNamaLengkap()).append("\n")
                .append("Kode dan Nama Cabang\t: ").append(tfr.getDataKodeCabang()).append(" - ").append(tfr.getDataNamaCabang()).append("\n\n")
                .append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    private static String templateUPMNotesDeleteProsperaFUP(TrxFuidRequest tfr, String statusProgress) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n");
        if (STATUS_SEDANG_DIPROSES.equalsIgnoreCase(statusProgress)) {
            sb.append("Pengajuan Pengahapusan user id Prospera (Terra/Agendaku) sedang di proses\n\n");
        } else {
            sb.append("Pengajuan Pengahapusan user id Prospera (Terra/Agendaku) sudah selesai di proses\n\n");
        }
        sb.append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    public static String buildTemplateUPMNotesResetPswProspera(String loginName, String password, String statusProgress) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n");
        if (STATUS_SEDANG_DIPROSES.equalsIgnoreCase(statusProgress)) {
            sb.append("Pengajuan Reset Password Prospera sedang diproses\n");
        } else {
            sb.append("Pengajuan Reset Password Prospera sudah dilakukan (Done).\n");
        }
        sb.append("Berikut informasi username dan passwordnya :\n");
        if (loginName.isEmpty() || password.isEmpty()) {
            sb.append("Login Name\t\t\t\t: ").append("Login Name akan di generate setelah tiket selesai di proses").append("\n");
            sb.append("Password\t\t\t\t: ").append("Password akan di generate setelah tiket selesai di proses").append("\n\n");
        } else {
            sb.append("Login Name\t\t\t\t: ").append(loginName).append("\n");
            sb.append("Password\t\t\t\t: ").append(password).append("\n\n");
        }
        sb.append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    public static String buildTemplateUPMNotesMutasiProspera(TrxFuidRequest tfr, String role, String centerCodeOfficer, String loginName, String password, String statusProgress) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n");
        if (STATUS_SEDANG_DIPROSES.equalsIgnoreCase(statusProgress)) {
            sb.append("Pengajuan Pendaftaran Mutasi/Promosi/Rotasi Prospera (Terra/Agendaku) sedang di proses\n");
        } else {
            sb.append("Pengajuan Pendaftaran Mutasi/Promosi/Rotasi Prospera (Terra/Agendaku) sudah selesai di proses\n");
        }
        sb.append("Untuk Login TERRA/AGENDAKU Menggunakan NIK dan password email\n")
                .append("Berikut informasi user id Prospera (Terra/Agendaku) :\n")
                .append("NIK\t\t\t\t\t\t: ").append(tfr.getDataNik()).append("\n")
                .append("Nama\t\t\t\t\t: ").append(tfr.getDataNamaLengkap()).append("\n")
                .append("Peran (Role)\t\t\t\t: ").append(role).append("\n");
        if (centerCodeOfficer.isEmpty()) {
            sb.append("Kode Officer\t\t\t\t: ").append("Kode Officer akan di generate setelah tiket selesai di proses").append("\n");
        } else {
            sb.append("Kode Officer\t\t\t\t: ").append(centerCodeOfficer).append("\n");
        }
        sb.append("Kode dan Nama Cabang\t: ").append(tfr.getDataKodeCabang()).append(" - ").append(tfr.getDataNamaCabang()).append("\n\n")
                .append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    public static String buildTemplateUPMNotesAlternateProspera(TrxFuidRequest tfr, String role, String centerCodeOfficer, String loginName, String password, String statusProgress, String upmAction) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n");
        if (STATUS_SEDANG_DIPROSES.equalsIgnoreCase(statusProgress)) {
            sb.append("Pengajuan Alternate/Delegasi sedang diproses\n");
        } else {
            sb.append("Pengajuan Alternate/Delegasi - Done\n");
        }

        if (UPM_TICKET_TYPE_PROCESS.equalsIgnoreCase(upmAction)) {
            if (role.isEmpty() && centerCodeOfficer.isEmpty() && loginName.isEmpty() && password.isEmpty()) {
                sb.append("Untuk Login menggunakan NIK dan password E-Mail").append("\n\n");
            } else {
                sb.append("Berikut informasi user id Prospera (Terra/Agendaku) :\n")
                        .append("NIK\t\t\t\t\t\t: ").append(tfr.getDataNik()).append("\n")
                        .append("Nama\t\t\t\t\t: ").append(tfr.getDataNamaLengkap()).append("\n")
                        .append("Peran (Role)\t\t\t\t: ").append(role).append("\n")
                        .append("Kode Officer\t\t\t\t: ").append(centerCodeOfficer).append("\n")
                        .append("Kode dan Nama Cabang\t: ").append(tfr.getDataKodeCabang()).append(" - ").append(tfr.getDataNamaCabang()).append("\n\n");
            }
        }

        sb.append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    public static String buildTemplateUPMNotesUpdateLimitProspera(String statusProgress) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n");
        if (STATUS_SEDANG_DIPROSES.equalsIgnoreCase(statusProgress)) {
            sb.append("Pengajuan Update Limit BWMP sedang diproses.\n");
        } else {
            sb.append("Pengajuan Update Limit BWMP sudah kami proses (Done).\n");
        }
        sb.append("Silahkan di cek kembali 1x24 Jam\n\n")
                .append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    public static String buildTemplateUPMNotesPerubahanProspera(TrxFuidRequest tfr, String role, String centerCodeOfficer, String loginName, String password, String statusProgress, String upmAction) {
        StringBuilder sb = new StringBuilder();

        sb.append("Assalamualaikum Wr Wb\n\n");
        if (STATUS_SEDANG_DIPROSES.equalsIgnoreCase(statusProgress)) {
            sb.append("Pengajuan Perubahan User ID sedang diproses\n\n");
        } else {
            sb.append("Pengajuan Perubahan User ID - Done\n\n");
        }

        if (UPM_TICKET_TYPE_PROCESS.equalsIgnoreCase(upmAction)) {
            if (role.isEmpty() && centerCodeOfficer.isEmpty() && loginName.isEmpty() && password.isEmpty()) {
                sb.append("User id dan password email tidak ada perubahan").append("\n")
                        .append("Untuk Login TERRA dan Agendaku menggunakan NIK dan password E-Mail").append("\n\n");
            } else {
                sb.append("Berikut informasi user id Prospera (Terra/Agendaku) :\n")
                        .append("NIK\t\t\t\t\t\t: ").append(tfr.getDataNik()).append("\n")
                        .append("Nama\t\t\t\t\t: ").append(tfr.getDataNamaLengkap()).append("\n")
                        .append("Peran (Role)\t\t\t\t: ").append(role).append("\n")
                        .append("Kode Officer\t\t\t\t: ").append(centerCodeOfficer).append("\n")
                        .append("Kode dan Nama Cabang\t: ").append(tfr.getDataKodeCabang()).append(" - ").append(tfr.getDataNamaCabang()).append("\n\n");
            }
        }

        sb.append("Terima Kasih\n")
                .append("UPM");

        return sb.toString();
    }

    public static String generateLoginName(List<UserProspera> userProspera, String nik) {
        String generatedLoginName = nik;

        List<UserProspera> userProsperaList = new ArrayList<>();
        if (!userProspera.isEmpty()) {
            userProspera.forEach(data -> {
                if (nik.equalsIgnoreCase(data.getNik())) {
                    userProsperaList.add(data);
                }
            });

            if (userProsperaList.size() == 1) {
                generatedLoginName = concateTwoString(nik, String.valueOf(getNextChar('Z')));
            } else {
                List<String> sortedUserProspera = userProspera
                        .stream()
                        .filter(data -> !data.getWismaCode().startsWith(PREFIX_MMS) && Character.isAlphabetic(data.getLoginName().charAt(data.getLoginName().length() - 1)))
                        .map(str -> str.getLoginName().toUpperCase())
                        .sorted(Comparator.reverseOrder())
                        .collect(Collectors.toList());

                if (!sortedUserProspera.isEmpty()) {
                    String lastLoginName = sortedUserProspera.get(0);
                    generatedLoginName = concateTwoString(nik, String.valueOf(getNextChar(lastLoginName.charAt(lastLoginName.length() - 1))));
                }
            }
        }
        return generatedLoginName;
    }

    public static String generateUserPassword(String nik, String dateNow) {
        StringBuilder sb = new StringBuilder();

        sb.append(BTPNS)
                .append(nik)
                .append(dateNow.split("-")[2]);

        return sb.toString();
    }

    public static Map<String, String> buildHeader(String authorization) {
        Map<String, String> headerMap = new HashMap<>();

        headerMap.put("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        headerMap.put("Authorization", buildToken(authorization));

        return headerMap;
    }

    private static String buildToken(String authorization) {
        StringBuilder sb = new StringBuilder();

        sb.append("Bearer")
                .append(" ")
                .append(authorization);

        return sb.toString();
    }

    public static boolean isNotDigitOnly(String input) {
        boolean isNotDigitOnly = false;
        String charRegex = "[^0-9]+";
        Pattern pattern = Pattern.compile(charRegex, Pattern.CASE_INSENSITIVE);
        Matcher my_match = pattern.matcher(input);
        boolean check = my_match.find();
        if (check) {
            isNotDigitOnly = true;
        }
        return isNotDigitOnly;
    }

    public static boolean isValidOfficerStatusCode(String filterStatus) {
        return filterStatus.isEmpty() || filterStatus.equals(OFFICER_STATUS_ACTIVE_CODE.toString()) || filterStatus.equals(OFFICER_STATUS_INACTIVE_CODE.toString());
    }

    public static boolean isExistInInterval(Boolean validInterval) {
        return validInterval;
    }

    public static boolean isDuplicateData(List<String> listNik) {
        return !listNik.stream().allMatch(new HashSet<>()::add);
    }

    public static Integer getOfficeLevel(String officeName) {
        Integer officeLevel = null;

        if (officeName.startsWith(BTPN_HO)) {
            officeLevel = 1;
        } else if (officeName.startsWith(KC)) {
            officeLevel = 3;
        } else if (officeName.startsWith(KFO)) {
            officeLevel = 4;
        } else if (officeName.startsWith(MMS)) {
            officeLevel = 5;
        }

        return officeLevel;
    }

    public static boolean isValidDateDuration(String startDateString, String endDateString) {
        LocalDate startDate = DateTimeHelper.convertToLocalDate(startDateString);
        LocalDate endDate = DateTimeHelper.convertToLocalDate(endDateString);

        return endDate.isAfter(startDate) || endDate.isEqual(startDate);
    }

    public static boolean isValidUserIDRequest(ReqUserIDModel request) {

        return request.getNik() != null
                && request.getNamaUser() != null
                && request.getKewenangan() != null
                && request.getJabatan() != null
                && request.getUnitKerja() != null;
    }

    public static boolean isNonViewerUPMRole(TrxUpmRole upmRole) {
        return upmRole != null && !upmRole.getRole().equals(UPM_ROLE_VIEWER);
    }

    public static boolean isBot(TrxUpmRole upmRole) {
        return RPAUPMIN.equals(upmRole.getNik()) || RPAUPMOTT.equals(upmRole.getNik());
    }

    public static boolean isInquiryUPMRole(TrxUpmRole upmRole) {
        return upmRole != null && upmRole.getRole().equals(UPM_ROLE_INQUIRY);
    }

    public static String resolveParamDetailDescS4(String paramDetailDesc) {
        if (APP_DESC_S4.equalsIgnoreCase(paramDetailDesc)) {
            paramDetailDesc = APP_DESC_SSSS;
        }
        return paramDetailDesc;
    }

    public static String trimDashOnwards(String string) {
        String[] parts = string.split("-");
        return parts[0];
    }

    public static JasperReport compileJasperReportTemplate(String resourceLocation) throws FileNotFoundException, JRException {
        File mainReportFile = ResourceUtils.getFile(resourceLocation);
        return JasperCompileManager.compileReport(mainReportFile.getAbsolutePath());
    }

    public static ByteArrayOutputStream exportJasperReports(List<JasperPrint> jasperPrints) throws JRException {
        ByteArrayOutputStream pdfReportStream = new ByteArrayOutputStream();
        JRPdfExporter exporter = new JRPdfExporter();
        exporter.setExporterInput(SimpleExporterInput.getInstance(jasperPrints));
        exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(pdfReportStream));
        exporter.exportReport();

        return pdfReportStream;
    }

    public static String generateBtpnsEmailFormat(String nik) {
        return nik.concat(PREFFIX_EMAIL_BTPNS);
    }

    public static Map.Entry<String, String> getFormatedPeriod(String startDate, String endDate) {
        if (startDate.length() == 10 && endDate.length() == 10) {
            startDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getFullDateAsDate(startDate + " 00:00:00"));
            endDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getFullDateAsDate(endDate + " 23:59:59"));
        } else if (startDate.length() == 7 && endDate.length() == 7) {
            startDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getBeginningofMonth(startDate));
            endDate = DateTimeHelper.getDateCreateDate(DateTimeHelper.getEndofMonth(endDate));
        }
        return Map.entry(startDate, endDate);
    }

    public static boolean isMoreThanOneMonthPeriod(String startDate, String endDate) throws Exception {
        Map.Entry<String, String> period = CommonHelper.getFormatedPeriod(startDate, endDate);
        LocalDate startDateDt = DateTimeHelper.getLocalDateYYYYMMDD(period.getKey().split(" ")[0]);
        LocalDate endDateDt = DateTimeHelper.getLocalDateYYYYMMDD(period.getValue().split(" ")[0]);

        return endDateDt.isAfter(startDateDt.plusMonths(1));
    }

    public String getApplicationDesc(String aplikasi) {
        String result = "";

        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationMap(Arrays.asList(KODE_APLIKASI_FUID, KODE_APLIKASI_SETUP_PARAM));
        if (!StringUtils.isEmpty(aplikasi)) {
            String[] split = aplikasi.split(",");
            List<String> collect = Arrays.stream(split).map(data -> {
                return msTemaApplicationMap.get(data).getParamDetailDesc();
            }).collect(Collectors.toList());
            result = String.join(",", collect);
        }
        return result;
    }

    public String getAppCodeByAppDesc(List<String> aplikasi) {
        Map<String, MsTemaApplication> msTemaApplicationMap = msTemaApplicationService.getMsTemaApplicationWithParamDetailIdDescKeyMap(Arrays.asList(KODE_APLIKASI_FUID));

        String result = "";
        if (aplikasi.size() > 0) {
            String[] split = aplikasi.stream().map(s -> s.replaceFirst("^\\s*", "")).collect(Collectors.toList()).toArray(String[]::new);
            List<String> collect = Arrays.stream(split).map(data -> {
                return msTemaApplicationMap.get(data).getParamDetailId();
            }).collect(Collectors.toList());
            result = String.join(",", collect);
        }
        return result;
    }

    public String getTujuanCodeByTujuanDesc(String tujuan) {
        Map<String, MsSystemParamDetail> msSystemParamDetailMap = msSystemParamService.getMsSystemParamDetailWithParamDetailIdDescMap(Arrays.asList(KODE_TUJUAN));

        String result = "";
        if (!StringUtils.isEmpty(tujuan)) {
            result = msSystemParamDetailMap.get(tujuan).getParamDetailId();
        }
        return result;
    }

    public DataAplikasiModel getProsperaRoleCodeAndDesc(String role) {
        MsProsperaRole msProsperaRole = iMsProsperaRoleRepository.findAllByTemaRoleCode(role);
        DataAplikasiModel roleModel = new DataAplikasiModel();
        roleModel.setCode(msProsperaRole.getTemaRoleCode());
        roleModel.setDesc(msProsperaRole.getRoleDesc());
        return roleModel;
    }

    public static boolean isValidNominalTransaksiUPM(Double nominalTransaksiUPM) {
        return nominalTransaksiUPM != null && nominalTransaksiUPM <= maximumUpmLimitForBwmpNominalInput;
    }

    public static String getRpCurrencyFormat() {
        Locale localId = new Locale("in", "ID");
        NumberFormat formatter = NumberFormat.getCurrencyInstance(localId);
        String strFormat = formatter.format(maximumUpmLimitForBwmpNominalInput);
        return strFormat;
    }

    public static Integer generateCounterReminder(Integer reminder) {
        if (reminder == null) {
            reminder = 1;
        } else {
            reminder++;
        }

        return reminder;
    }

    public static String generateKodeNamaCabang(String dataKodeCabang, String dataNamaCabang) {
        StringBuilder sb = new StringBuilder();
        sb.append(dataKodeCabang)
                .append(" - ")
                .append(dataNamaCabang);
        return sb.toString();
    }

    public String validateCCNikDirectPuk(String nikRequester, String puk1NIK) {
        String nikDirectPuk = "";
        MsEmployee directPUK = msEmployeeService.getDirectPUK(nikRequester);
        if (directPUK != null) {
            boolean isCCDirectPuk = false;
            if (!puk1NIK.equals(directPUK.getNik())) {
                isCCDirectPuk = true;
            }
            if (isCCDirectPuk) {
                nikDirectPuk = directPUK.getNik();
            }
        }
        return nikDirectPuk;
    }

    public static String generateOfficeApprovalLevel(boolean isHO, boolean isKcKFO) {
        String officeLevel = "";
        if (isHO & isKcKFO) {
            officeLevel = HO_KCKFO;
        } else {
            if (isHO) {
                officeLevel = HO;
            }
            if (isKcKFO) {
                officeLevel = KCKFO;
            }
        }
        return officeLevel;
    }

    public static String genereteNewParamDetailId(String lastParamDetailId) {
        return lastParamDetailId.substring(0, 3) + String.format("%07d", (Integer.parseInt(lastParamDetailId.substring(8, 10)) + 1));
    }

    public HashMap<String, String> getDateByDateFlag(String startDate, String endDate, String dateFlag) {
        HashMap<String, String> dateMap = new HashMap<String, String>();
        dateMap.put(EFFECTIVE_DT_START, "");
        dateMap.put(EFFECTIVE_DT_END, "");
        dateMap.put(CREATE_DT_START, "");
        dateMap.put(CREATE_DT_END, "");
        dateMap.put(CURRENT_STATE_DT_START, "");
        dateMap.put(CURRENT_STATE_DT_END, "");

        if (SEARCH_FLAG_EFFECTIVE_DT.equalsIgnoreCase(dateFlag)) {
            dateMap.put(EFFECTIVE_DT_START, startDate.split(" ")[0]);
            dateMap.put(EFFECTIVE_DT_END, endDate.split(" ")[0]);
        } else {
            if (SEARCH_FLAG_CREATED_DT.equalsIgnoreCase(dateFlag)) {
                dateMap.put(CREATE_DT_START, startDate);
                dateMap.put(CREATE_DT_END, endDate);
            } else if (SEARCH_FLAG_CURRENT_STATE_DT.equalsIgnoreCase(dateFlag)) {
                dateMap.put(CURRENT_STATE_DT_START, startDate);
                dateMap.put(CURRENT_STATE_DT_END, endDate);
            }
        }

        return dateMap;
    }

    public static void gzipFile(String sourcePath, String targetPath) throws Exception {
        byte[] buffer = new byte[2048];
        FileInputStream inputStream = new FileInputStream(sourcePath);
        FileOutputStream outputStream = new FileOutputStream(targetPath);
        GZIPOutputStream gzipOutputStream = new GZIPOutputStream(outputStream);
        int length;
        while ((length = inputStream.read(buffer)) > 0) {
            gzipOutputStream.write(buffer, 0, length);
        }
        inputStream.close();
        gzipOutputStream.close();
    }
}
