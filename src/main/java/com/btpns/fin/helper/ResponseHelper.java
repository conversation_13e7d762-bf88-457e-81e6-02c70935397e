package com.btpns.fin.helper;

import com.btpns.fin.model.response.ResFileDownload;
import com.btpns.fin.model.response.ResponseModel;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayInputStream;


import static com.btpns.fin.helper.CommonHelper.*;

public class ResponseHelper {
    private static final Logger logger = LoggerFactory.getLogger(ResponseHelper.class);
    public static ResponseEntity responseFailed(String type, ResponseStatus status, String methodName, Gson gson) {
        ResponseModel response = new ResponseModel();
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setType(type);

        logger.info("Response >>> {} : {}", methodName, gson.toJson(response));
        return ResponseEntity.ok(response);
    }

    public static ResponseEntity responseForbidden(String methodName) {
        logger.info("Response >>> {} : {}", methodName, getHttpStatusDetail(HttpStatus.FORBIDDEN));
        return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }

    public static ResponseEntity responseToManyRequest(String methodName) {
        logger.info("Response >>> {} : {}", methodName, getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public static ResponseEntity responseBadRequest(String methodName) {
        logger.info("Response >>> {} : {}", methodName, getHttpStatusDetail(HttpStatus.BAD_REQUEST));
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
    }

    public static ResponseEntity responseInternalServerError(String methodName, Exception e) {
        logger.error("Fail {}", "", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }

    public static ResponseEntity responseHttpSuccessWithStatus(String type, ResponseStatus status, String methodName, Gson gson){
        ResponseModel response = new ResponseModel();
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setType(type);

        logger.info("Response >>> {} : {}", methodName, gson.toJson(response));
        return ResponseEntity.ok(response);
    }

    public static ResponseEntity responseDownloadFile(String methodName, ResFileDownload file) {
        logger.info("Response >>> {} : {}", methodName, file.getFileName());
        HttpHeaders headers = new HttpHeaders();
        headers.add("fileName", file.getFileName());

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.getFileContent().length)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new InputStreamResource(new ByteArrayInputStream(file.getFileContent())));
    }
}
