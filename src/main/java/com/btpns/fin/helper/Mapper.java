package com.btpns.fin.helper;

import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.*;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.*;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.io.IOException;
import java.lang.reflect.Type;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.helper.Constants.*;

@Component
public class Mapper {
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsUserIDApplicationService msUserIDApplicationService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    TrxSetupParamRequestService trxSetupParamRequestService;

    @Autowired
    MsCabangMmsService msCabangMmsService;

    @Autowired
    CommonHelper commonHelper;

    @Value("${path.logo.btpns}")
    private String pathLogoBtpns;

    public static TrxFuidRequest toTrxFuidRequestEntity(TrxFuidRequestModel trxFuidRequestModel) throws ParseException {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        DateFormat dateFormater2 = new SimpleDateFormat("dd-MM-yyyy");
        TrxFuidRequest tfr = new TrxFuidRequest();
        tfr.setTicketId(trxFuidRequestModel.getTicketId());
        tfr.setRequestId(trxFuidRequestModel.getRequestId());

        FuidModel fm = trxFuidRequestModel.getFuid();
        tfr.setTujuan(fm.getTujuan());
        tfr.setAlasan(fm.getAlasan());
        tfr.setTanggalEfektif(dateFormater2.parse(fm.getTanggalEfektif()));
        tfr.setAplikasi(String.join(",", fm.getAplikasi()));
        String tingkatan = "";
        if(fm.getTingkatan() != null && !fm.getTingkatan().equals("")) {
            tingkatan = fm.getTingkatan();
        }
        tfr.setTingkatan(tingkatan);
        String statusMasaBerlaku = "";
        if(fm.getStatusMasaBerlaku() != null && !fm.getStatusMasaBerlaku().equals("")){
            statusMasaBerlaku = fm.getStatusMasaBerlaku();
        }
        tfr.setStatusMasaBerlaku(statusMasaBerlaku);
        Date masaBerlaku = null;
        if(fm.getMasaBerlaku() != null && !fm.getMasaBerlaku().equals("")) {
            masaBerlaku = dateFormater2.parse(fm.getMasaBerlaku());
        }
        tfr.setMasaBerlakuSampai(masaBerlaku);
        tfr.setAlasanPengajuan(fm.getAlasanPengajuan());
        tfr.setInfoTambahan(fm.getInfoTambahan());
        List<AttachmentModel> lAttachment = new ArrayList<AttachmentModel>();
        if(fm.getAttachment().size() > 0){
            lAttachment = fm.getAttachment();
        }
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        tfr.setAttachment(new Gson().toJson(lAttachment, attachmentListType));
        tfr.setFileName(fm.getFileName());
        String tipeLimitTransaksi = "";
        if(fm.getTipeLimitTransaksi() != null && !fm.getTipeLimitTransaksi().equals("")) {
            tipeLimitTransaksi = fm.getTipeLimitTransaksi();
        }
        tfr.setTipeLimitTransaksi(tipeLimitTransaksi);
        Double nominalTransaksi = null;
        if(fm.getNominalTransaksi() != null) {
            nominalTransaksi = fm.getNominalTransaksi();
        }
        tfr.setNominalTransaksi(nominalTransaksi);
        String unitKerjaLama = "";
        if(fm.getUnitKerjaLama() != null) {
            unitKerjaLama = fm.getUnitKerjaLama();
        }
        tfr.setUnitKerjaLama(unitKerjaLama);
        String unitKerjaBaru = "";
        if(fm.getUnitKerjaBaru() != null) {
            unitKerjaBaru = fm.getUnitKerjaBaru();
        }
        tfr.setUnitKerjaBaru(unitKerjaBaru);
        String tipeKewenanganLimit = "";
        if(fm.getTipeKewenanganLimit() != null) {
            tipeKewenanganLimit = fm.getTipeKewenanganLimit();
        }
        tfr.setTipeKewenanganLimit(tipeKewenanganLimit);
        //TipeKaryawanBaru for non-fte/vendor need
        String tipeKaryawanBaru = "";
        if(fm.getTipeKaryawanBaru() != null){
            tipeKaryawanBaru = fm.getTipeKaryawanBaru();
        }
        tfr.setTipeKaryawanBaru(tipeKaryawanBaru);
        String occupationDesc = "", organization = "", location = "";
        if(fm.getOccupationDesc() != null){
            occupationDesc = fm.getOccupationDesc();
        }
        if(fm.getOrganization() != null){
            organization = fm.getOrganization();
        }
        if(fm.getLocation() != null){
            location = fm.getLocation();
        }
        tfr.setOccupationDesc(occupationDesc);
        tfr.setOrganization(organization);
        tfr.setLocation(location);

        if (fm.getAplikasi().contains(KODE_APLIKASI_EMAIL_GROUP)) {
            tfr.setPicEmailGroup(fm.getPicEmailGroup());
            tfr.setPicEmailGroupName(fm.getPicEmailGroupName());
            tfr.setPicEmailGroupOccupation(fm.getPicEmailGroupOccupation());
            tfr.setAltPicEmailGroup(fm.getAltPicEmailGroup());
            tfr.setAltPicEmailGroupName(fm.getAltPicEmailGroupName());
            tfr.setAltPicEmailGroupOccupation(fm.getAltPicEmailGroupOccupation());
        }

        if (fm.getAlasan().contains(ALASAN_MUTASI_ROTASI_PROMOSI) && fm.getRole() != null) {
            tfr.setRole(fm.getRole());
        }

        DataFuidModel data = fm.getData();
        //validation
        String namaCabang = ""; String telepon = ""; String namaVendor = ""; String userId = ""; String email = ""; String jabatan = "";
        if(data.getNamaCabang() != null && data.getNamaCabang() != ""){
            namaCabang = data.getNamaCabang();
        }
        if(data.getTelepon() != null && data.getTelepon() != ""){
            telepon = data.getTelepon();
        }
        if(data.getNamaVendor() != null && data.getNamaVendor() != ""){
            namaVendor = data.getNamaVendor();
        }
        if(data.getUserId() != null && data.getUserId() != ""){
            userId = data.getUserId();
        }
        if(data.getEmail() != null && data.getEmail() != ""){
            email = data.getEmail();
        }
        if(data.getJabatan() != null && data.getJabatan() != ""){
            jabatan = data.getJabatan();
        }
        tfr.setDataNik(data.getNIK());
        tfr.setDataNamaLengkap(data.getNamaLengkap());
        tfr.setDataJabatan(jabatan);
        tfr.setDataKodeCabang(data.getKodeCabang());
        tfr.setDataNamaCabang(namaCabang);
        tfr.setDataTelepon(telepon);
        tfr.setDataNamaVendor(namaVendor);
        tfr.setDataUserId(userId);
        tfr.setDataEmail(email);

        if (TIPE_KARYAWAN_BARU_NON_FTE.equals(fm.getTipeKaryawanBaru())) {
            tfr.setPukVendorNIK(data.getNikPUKVendor());
            tfr.setPukVendorName(data.getNamaPUKVendor());
            tfr.setPukVendorOccupation(data.getJabatanPUKVendor());
        }

        LocalDateTime createDt = LocalDateTime.now();
        String sCreateDt = dateFormater1.format(createDt);
        tfr.setCreateDateTime(LocalDateTime.parse(sCreateDt, dateFormater1));

        return tfr;
    }

    public static TrxFuidRequest toTrxFuidRequestEntity(FuidModel fm,
                                                        String ticketId,
                                                        String nikRequester,
                                                        String batchId,
                                                        String tujuan,
                                                        String alasan,
                                                        String alasanPengajuan,
                                                        List<String> aplikasi) throws ParseException {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        DateFormat dateFormater2 = new SimpleDateFormat("dd-MM-yyyy");
        TrxFuidRequest tfr = new TrxFuidRequest();
        tfr.setTicketId(ticketId);
        tfr.setRequestId(EMPTY);
        tfr.setNikRequester(nikRequester);
        tfr.setTujuan(tujuan);
        tfr.setAlasan(alasan);
        tfr.setTanggalEfektif(dateFormater2.parse(fm.getTanggalEfektif()));
        tfr.setAplikasi(String.join(",", aplikasi));
        String tingkatan = "";
        if(fm.getTingkatan() != null && !fm.getTingkatan().equals("")) {
            tingkatan = fm.getTingkatan();
        }
        tfr.setTingkatan(tingkatan);
        tfr.setStatusMasaBerlaku(STATUS_MASA_BERLAKU_TETAP);
        tfr.setMasaBerlakuSampai(null);
        if (fm.getAlasanPengajuan() != null) {
            tfr.setAlasanPengajuan(fm.getAlasanPengajuan());
        } else {
            tfr.setAlasanPengajuan(alasanPengajuan + DateTimeHelper.getDateToDateStringDDMMYYYY(tfr.getTanggalEfektif()));
        }

        tfr.setInfoTambahan(fm.getInfoTambahan());
        List<AttachmentModel> lAttachment = new ArrayList<AttachmentModel>();
        if(fm.getAttachment().size() > 0){
            lAttachment = fm.getAttachment();
        }
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        tfr.setAttachment(new Gson().toJson(lAttachment, attachmentListType));
        tfr.setFileName(fm.getFileName());
        String tipeLimitTransaksi = "";
        if(fm.getTipeLimitTransaksi() != null && !fm.getTipeLimitTransaksi().equals("")) {
            tipeLimitTransaksi = fm.getTipeLimitTransaksi();
        }
        tfr.setTipeLimitTransaksi(tipeLimitTransaksi);
        Double nominalTransaksi = null;
        if(fm.getNominalTransaksi() != null) {
            nominalTransaksi = fm.getNominalTransaksi();
        }
        tfr.setNominalTransaksi(nominalTransaksi);
        String unitKerjaLama = "";
        if(fm.getUnitKerjaLama() != null) {
            unitKerjaLama = fm.getUnitKerjaLama();
        }
        tfr.setUnitKerjaLama(unitKerjaLama);
        String unitKerjaBaru = "";
        if(fm.getUnitKerjaBaru() != null) {
            unitKerjaBaru = fm.getUnitKerjaBaru();
        }
        tfr.setUnitKerjaBaru(unitKerjaBaru);
        String tipeKewenanganLimit = "";
        if(fm.getTipeKewenanganLimit() != null) {
            tipeKewenanganLimit = fm.getTipeKewenanganLimit();
        }
        tfr.setTipeKewenanganLimit(tipeKewenanganLimit);
        //TipeKaryawanBaru for non-fte/vendor need
        tfr.setTipeKaryawanBaru(TIPE_KARYAWAN_BARU_FTE);
        String occupationDesc = "", organization = "", location = "";
        if(fm.getOccupationDesc() != null){
            occupationDesc = fm.getOccupationDesc();
        }
        if(fm.getOrganization() != null){
            organization = fm.getOrganization();
        }
        if(fm.getLocation() != null){
            location = fm.getLocation();
        }
        tfr.setOccupationDesc(occupationDesc);
        tfr.setOrganization(organization);
        tfr.setLocation(location);

        DataFuidModel data = fm.getData();
        //validation
        String namaCabang = ""; String telepon = ""; String namaVendor = ""; String userId = ""; String email = ""; String jabatan = "";
        if(data.getNamaCabang() != null && data.getNamaCabang() != ""){
            namaCabang = data.getNamaCabang();
        }
        if(data.getTelepon() != null && data.getTelepon() != ""){
            telepon = data.getTelepon();
        }
        if(data.getNamaVendor() != null && data.getNamaVendor() != ""){
            namaVendor = data.getNamaVendor();
        }
        if(data.getUserId() != null && data.getUserId() != ""){
            userId = data.getUserId();
        }
        if(data.getEmail() != null && data.getEmail() != ""){
            email = data.getEmail();
        }
        if(data.getJabatan() != null && data.getJabatan() != ""){
            jabatan = data.getJabatan();
        }
        tfr.setDataNik(data.getNIK());
        tfr.setDataNamaLengkap(data.getNamaLengkap());
        tfr.setDataJabatan(jabatan);
        tfr.setDataKodeCabang(data.getKodeCabang());
        tfr.setDataNamaCabang(namaCabang);
        tfr.setDataTelepon(telepon);
        tfr.setDataNamaVendor(namaVendor);
        tfr.setDataUserId(userId);
        tfr.setDataEmail(email);

        LocalDateTime createDt = LocalDateTime.now();
        String sCreateDt = dateFormater1.format(createDt);
        tfr.setCreateDateTime(LocalDateTime.parse(sCreateDt, dateFormater1));
        tfr.setInputType(INPUT_TYPE_BATCH);
        tfr.setBatchId(batchId);

        return tfr;
    }

    public static TrxFuidRequest toTrxFuidRequestEntity(FuidModel fm, TrxFuidBatchModel tfbm, String ticketId) throws Exception {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        DateFormat dateFormater2 = new SimpleDateFormat("dd-MM-yyyy");
        TrxFuidRequest tfr = new TrxFuidRequest();
        tfr.setTicketId(ticketId);
        tfr.setRequestId(EMPTY);
        tfr.setNikRequester(fm.getData().getNIK());
        tfr.setTujuan(fm.getTujuan());
        tfr.setAlasan(fm.getAlasan());
        tfr.setTanggalEfektif(dateFormater2.parse(fm.getTanggalEfektif()));
        tfr.setAplikasi(String.join(",", fm.getAplikasi()));
        String tingkatan = "";
        if(fm.getTingkatan() != null && !fm.getTingkatan().equals("")) {
            tingkatan = fm.getTingkatan();
        }
        tfr.setTingkatan(tingkatan);
        tfr.setStatusMasaBerlaku(STATUS_MASA_BERLAKU_TETAP);
        tfr.setMasaBerlakuSampai(null);
        if (fm.getAlasanPengajuan() != null) {
            tfr.setAlasanPengajuan(fm.getAlasanPengajuan());
        }

        tfr.setInfoTambahan(fm.getInfoTambahan());
        List<AttachmentModel> lAttachment = new ArrayList<AttachmentModel>();
        if(fm.getAttachment().size() > 0){
            lAttachment = fm.getAttachment();
        }
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        tfr.setAttachment(new Gson().toJson(lAttachment, attachmentListType));
        tfr.setFileName(fm.getFileName());
        String tipeLimitTransaksi = "";
        if(fm.getTipeLimitTransaksi() != null && !fm.getTipeLimitTransaksi().equals("")) {
            tipeLimitTransaksi = fm.getTipeLimitTransaksi();
        }
        tfr.setTipeLimitTransaksi(tipeLimitTransaksi);
        Double nominalTransaksi = null;
        if(fm.getNominalTransaksi() != null) {
            nominalTransaksi = fm.getNominalTransaksi();
        }
        tfr.setNominalTransaksi(nominalTransaksi);
        String unitKerjaLama = "";
        if(fm.getUnitKerjaLama() != null) {
            unitKerjaLama = fm.getUnitKerjaLama();
        }
        tfr.setUnitKerjaLama(unitKerjaLama);
        String unitKerjaBaru = "";
        if(fm.getUnitKerjaBaru() != null) {
            unitKerjaBaru = fm.getUnitKerjaBaru();
        }
        tfr.setUnitKerjaBaru(unitKerjaBaru);
        String tipeKewenanganLimit = "";
        if(fm.getTipeKewenanganLimit() != null) {
            tipeKewenanganLimit = fm.getTipeKewenanganLimit();
        }
        tfr.setTipeKewenanganLimit(tipeKewenanganLimit);
        //TipeKaryawanBaru for non-fte/vendor need
        tfr.setTipeKaryawanBaru(fm.getTipeKaryawanBaru());
        String occupationDesc = "", organization = "", location = "";
        if(fm.getOccupationDesc() != null){
            occupationDesc = fm.getOccupationDesc();
        }
        if(fm.getOrganization() != null){
            organization = fm.getOrganization();
        }
        if(fm.getLocation() != null){
            location = fm.getLocation();
        }
        tfr.setOccupationDesc(occupationDesc);
        tfr.setOrganization(organization);
        tfr.setLocation(location);

        DataFuidModel data = fm.getData();
        //validation
        String namaCabang = ""; String telepon = ""; String namaVendor = ""; String userId = ""; String email = ""; String jabatan = "";
        if(data.getNamaCabang() != null && data.getNamaCabang() != ""){
            namaCabang = data.getNamaCabang();
        }
        if(data.getTelepon() != null && data.getTelepon() != ""){
            telepon = data.getTelepon();
        }
        if(data.getNamaVendor() != null && data.getNamaVendor() != ""){
            namaVendor = data.getNamaVendor();
        }
        if(data.getUserId() != null && data.getUserId() != ""){
            userId = data.getUserId();
        }
        if(data.getEmail() != null && data.getEmail() != ""){
            email = data.getEmail();
        }
        if(data.getJabatan() != null && data.getJabatan() != ""){
            jabatan = data.getJabatan();
        }
        tfr.setDataNik(data.getNIK());
        tfr.setDataNamaLengkap(data.getNamaLengkap());
        tfr.setDataJabatan(jabatan);
        tfr.setDataKodeCabang(data.getKodeCabang());
        tfr.setDataNamaCabang(namaCabang);
        tfr.setDataTelepon(telepon);
        tfr.setDataNamaVendor(namaVendor);
        tfr.setDataUserId(userId);
        tfr.setDataEmail(email);

        LocalDateTime createDt = LocalDateTime.now();
        String sCreateDt = dateFormater1.format(createDt);
        tfr.setCreateDateTime(LocalDateTime.parse(sCreateDt, dateFormater1));
        tfr.setInputType(INPUT_TYPE_RESIGN);
        tfr.setBatchId(tfbm.getBatchId());

        return tfr;
    }

    public static TrxUploadUserIdAudittrail toTrxUploadUserIdAudittrail(String paramDetailId, String dataUser) {
        TrxUploadUserIdAudittrail trxUploadUserIdAudittrail = new TrxUploadUserIdAudittrail();
        trxUploadUserIdAudittrail.setParamDetailId(paramDetailId);
        trxUploadUserIdAudittrail.setUploadAt(LocalDateTime.now());
        trxUploadUserIdAudittrail.setDataUser(dataUser);
        return trxUploadUserIdAudittrail;
    }

    public TrxFuidRequest toTrxFuidRequest(FuidModel fm, TrxFuidBatchModel trxFuidBatchModel, String ticketId) throws Exception {
        DateFormat dateFormater = new SimpleDateFormat("dd-MM-yyyy");

        TrxFuidRequest tfr = new TrxFuidRequest();
        tfr.setTicketId(ticketId);
        tfr.setNikRequester(fm.getData().getNIK());
        tfr.setTujuan(commonHelper.getTujuanCodeByTujuanDesc(fm.getTujuan()));
        tfr.setAlasan(ALASAN_LAINNYA);
        tfr.setTanggalEfektif(dateFormater.parse(fm.getTanggalEfektif()));
        tfr.setAplikasi(commonHelper.getAppCodeByAppDesc(fm.getAplikasi()));
        tfr.setTingkatan(fm.getTingkatan() != null ? fm.getTingkatan() : EMPTY);
        if(STATUS_MASA_BERLAKU_TETAP.equalsIgnoreCase(fm.getStatusMasaBerlaku())){
            tfr.setStatusMasaBerlaku(STATUS_MASA_BERLAKU_TETAP);
            tfr.setMasaBerlakuSampai(null);
        }else {
            tfr.setStatusMasaBerlaku(STATUS_MASA_BERLAKU_TANGGAL);
            tfr.setMasaBerlakuSampai(dateFormater.parse(fm.getStatusMasaBerlaku()));
        }
        tfr.setAlasanPengajuan(fm.getInfoTambahan());
        tfr.setAttachment(new Gson().toJson(new ArrayList<AttachmentModel>(), new TypeToken<ArrayList<AttachmentModel>>(){}.getType()));
        tfr.setDataNik(fm.getData().getNIK());
        tfr.setDataNamaLengkap(fm.getData().getNamaLengkap());
        tfr.setDataJabatan(fm.getData().getJabatan());
        tfr.setDataKodeCabang(fm.getData().getKodeCabang());
        tfr.setDataNamaCabang(fm.getData().getNamaCabang());
        tfr.setDataEmail(fm.getData().getEmail() != null ? fm.getData().getEmail() : EMPTY);
        tfr.setDataTelepon(fm.getData().getTelepon() != null ? fm.getData().getTelepon() : EMPTY);
        tfr.setCreateDateTime(LocalDateTime.now());
        tfr.setInputType(INPUT_TYPE_BULK);
        tfr.setBatchId(trxFuidBatchModel.getBatchId());

        return tfr;
    }

    public static TrxFuidRequestAplikasi toTrxFuidRequestAplikasiEntity(String ticketId, MsTemaApplication msta) throws ParseException {
        Date date = new Date();
        TrxFuidRequestAplikasi tfrap = new TrxFuidRequestAplikasi();
        tfrap.setTicketId(ticketId);
        tfrap.setAplikasi(msta.getParamDetailId());
        tfrap.setAplikasiName(msta.getParamDetailDesc());
        tfrap.setPeriodDate(DateTimeHelper.getDatePeriodDate(date));
        tfrap.setPeriodMonth(DateTimeHelper.getDatePeriodMonth(date));

        LocalDateTime createDt = LocalDateTime.now();
        String sCreateDt = DateTimeHelper.getDateCreateDate(createDt);
        tfrap.setCreateDateTime(DateTimeHelper.getDateCreateDateAsDate(sCreateDt));
        return tfrap;
    }

    public TrxFuidRequestDetailModel toTrxFuidRequestDetailModel(TrxFuidRequest trxFuidRequest) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        DateFormat dateFormater2 = new SimpleDateFormat("dd-MM-yyyy");

        DataFuidModel data = new DataFuidModel();
        data.setNIK(trxFuidRequest.getDataNik());
        data.setNamaLengkap(trxFuidRequest.getDataNamaLengkap());
        data.setJabatan(trxFuidRequest.getDataJabatan());
        data.setKodeCabang(trxFuidRequest.getDataKodeCabang());
        data.setNamaCabang(trxFuidRequest.getDataNamaCabang());
        data.setTelepon(trxFuidRequest.getDataTelepon());
        data.setNamaVendor(trxFuidRequest.getDataNamaVendor());
        data.setUserId(trxFuidRequest.getDataUserId());
        data.setEmail(trxFuidRequest.getDataEmail());

        //ProgressModel pm = new ProgressModel();
        LinkedHashMap<String,Object> pm = new LinkedHashMap<>();
        TrxFuidApproval tfa = trxFuidRequest.getTrxFuidApproval();
        //puk1
        if(tfa.getPuk1NIK() != null){
            PUK1Model puk1 = new PUK1Model();
            puk1.setNik(tfa.getPuk1NIK());
            if (tfa.getPuk1Name() == null && tfa.getPuk1Occupation() == null){
                MsEmployee msEmployee = msEmployeeService.getEmployeeByNik(tfa.getPuk1NIK());
                puk1.setNama(msEmployee.getFullName());
                puk1.setJabatan(msEmployee.getOccupationDesc());
            }else {
                puk1.setNama(tfa.getPuk1Name());
                puk1.setJabatan(tfa.getPuk1Occupation());
            }
            puk1.setDateTime(tfa.getPuk1Dt() != null ? dateFormater1.format(tfa.getPuk1Dt()) : EMPTY);
            puk1.setNotes(tfa.getPuk1Notes());
            StatusModel smPUK1 = new StatusModel();
            smPUK1.setKeyStatus(tfa.getPuk1Status());
            if(CURR_STATUS_APPROVED.equalsIgnoreCase(tfa.getPuk1Status())){
                smPUK1.setValueStatus(CURR_STATUS_PUK1_DESC);
            }
            if(STATUS_WAITING.equalsIgnoreCase(tfa.getPuk1Status())){
                smPUK1.setValueStatus(CURR_STATUS_PUK1_DESC);
            }
            if(CURR_STATUS_REJECTED.equalsIgnoreCase(tfa.getPuk1Status())){
                smPUK1.setValueStatus(CURR_STATUS_REJECTED_PUK1_DESC);
            }
            puk1.setStatus(smPUK1);
            pm.put("puk1",puk1);
        }
        //puk2
        if(tfa.getPuk2NIK() != null){
            PUK2Model puk2 = new PUK2Model();
            puk2.setNik(tfa.getPuk2NIK());
            if (tfa.getPuk1Name() == null && tfa.getPuk1Occupation() == null){
                MsEmployee msEmployee = msEmployeeService.getEmployeeByNik(tfa.getPuk2NIK());
                puk2.setNama(msEmployee.getFullName());
                puk2.setJabatan(msEmployee.getOccupationDesc());
            }else {
                puk2.setNama(tfa.getPuk2Name());
                puk2.setJabatan(tfa.getPuk2Occupation());
            }
            puk2.setDateTime(tfa.getPuk2Dt() != null ? dateFormater1.format(tfa.getPuk2Dt()) : EMPTY);
            puk2.setNotes(tfa.getPuk2Notes());
            StatusModel smPUK2 = new StatusModel();
            smPUK2.setKeyStatus(tfa.getPuk2Status());
            if(CURR_STATUS_APPROVED.equalsIgnoreCase(tfa.getPuk2Status())){
                smPUK2.setValueStatus(CURR_STATUS_PUK2_DESC);
            }
            if(STATUS_WAITING.equalsIgnoreCase(tfa.getPuk2Status())){
                smPUK2.setValueStatus(CURR_STATUS_PUK2_DESC);
            }
            if(CURR_STATUS_REJECTED.equalsIgnoreCase(tfa.getPuk2Status())){
                smPUK2.setValueStatus(CURR_STATUS_REJECTED_PUK2_DESC);
            }
            puk2.setStatus(smPUK2);
            pm.put("puk2",puk2);
        }
        //upm process
        if(tfa.getUpmInputNIK() != null){
            UPMProcessModel upmProgress = new UPMProcessModel();
            upmProgress.setNik(tfa.getUpmInputNIK());
            upmProgress.setDateTime(tfa.getUpmInputDt() != null ? dateFormater1.format(tfa.getUpmInputDt()) : EMPTY);
            TrxUpmRole trxUpmRole = trxFuidRequestService.getUPMProcessRole(upmProgress);
            upmProgress.setNama(trxUpmRole.getNama());
            upmProgress.setJabatan(trxUpmRole.getRole());
            upmProgress.setNotes(tfa.getUpmInputNotes());

            if (UPM_STATUS_INPROGRESS.equalsIgnoreCase(tfa.getUpmInputStatus())) {
                upmProgress.setStatus(buildStatusModel(tfa.getUpmInputStatus(), CURR_STATUS_INPROGRESS_DESC));
                pm.put("upmProgress", upmProgress);
                pm.put("upmProcess", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_VERIFICATION_DESC));
            }

            if (UPM_STATUS_VERIFICATION.equalsIgnoreCase(tfa.getUpmInputStatus())){
                upmProgress.setStatus(buildStatusModel(UPM_STATUS_INPROGRESS, CURR_STATUS_INPROGRESS_DESC));
                pm.put("upmProgress", upmProgress);

                UPMProcessModel upmProcess = new UPMProcessModel();
                upmProcess.setNik(tfa.getUpmInputNIK());
                upmProcess.setDateTime(tfa.getUpmInputDt() != null ? dateFormater1.format(tfa.getUpmInputDt()) : EMPTY);
                upmProcess.setNama(trxUpmRole.getNama());
                upmProcess.setJabatan(trxUpmRole.getRole());
                upmProcess.setNotes(tfa.getUpmInputNotes());
                upmProcess.setStatus(buildStatusModel(tfa.getUpmInputStatus(), CURR_STATUS_VERIFICATION_DESC));
                pm.put("upmProcess", upmProcess);
            }

            if (CURR_STATUS_REJECTED.equalsIgnoreCase(tfa.getUpmInputStatus())){
                upmProgress.setStatus(buildStatusModel(CURR_STATUS_REJECTED, CURR_STATUS_REJECTED_MAKER_DESC));
                pm.put("upmProgress", upmProgress);
                pm.put("upmProcess", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_VERIFICATION_DESC));
            }
        }else {
            pm.put("upmProgress", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_INPROGRESS_DESC));
            pm.put("upmProcess", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_VERIFICATION_DESC));
        }

        //upm checker
        if(tfa.getUpmCheckerNIK() != null){
            UPMCheckerModel upmChecker = new UPMCheckerModel();
            upmChecker.setNik(tfa.getUpmCheckerNIK());
            TrxUpmRole trxUpmRole = trxFuidRequestService.getUPMCheckerRole(upmChecker);
            upmChecker.setNama(trxUpmRole.getNama());
            upmChecker.setJabatan(trxUpmRole.getRole());
            upmChecker.setDateTime(tfa.getUpmCheckerDt() != null ? dateFormater1.format(tfa.getUpmCheckerDt()) : EMPTY);
            upmChecker.setNotes(tfa.getUpmCheckerNotes());

            StatusModel smUpmChecker = new StatusModel();
            smUpmChecker.setKeyStatus(tfa.getUpmCheckerStatus());
            smUpmChecker.setValueStatus(CURR_STATUS_REJECTED.equalsIgnoreCase(tfa.getUpmCheckerStatus()) ? CURR_STATUS_REJECTED_CHECKER_DESC : CURR_STATUS_DONE_DESC);
            upmChecker.setStatus(smUpmChecker);
            pm.put("upmChecker",upmChecker);
        }else {
            pm.put("upmChecker", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_DONE_DESC));
        }

        TrxFuidRequestDetailModel tfrdm = new TrxFuidRequestDetailModel();
        tfrdm.setTicketId(trxFuidRequest.getTicketId());
        tfrdm.setTujuan(trxFuidRequest.getTujuan());
        tfrdm.setAlasan(trxFuidRequest.getAlasan());
        tfrdm.setNikRequester(trxFuidRequest.getNikRequester());
        String tipeKaryawanBaru = "";
        if(trxFuidRequest.getTipeKaryawanBaru() != null){
            tipeKaryawanBaru = trxFuidRequest.getTipeKaryawanBaru();
        }
        tfrdm.setTipeKaryawanBaru(tipeKaryawanBaru);
        tfrdm.setTanggalEfektif(dateFormater2.format(trxFuidRequest.getTanggalEfektif()));
        tfrdm.setTingkatan(trxFuidRequest.getTingkatan());
        tfrdm.setRole(trxFuidRequest.getRole() != null ? commonHelper.getProsperaRoleCodeAndDesc(trxFuidRequest.getRole()) : new DataAplikasiModel());
        tfrdm.setStatusMasaBerlaku(trxFuidRequest.getStatusMasaBerlaku());
        String strMasaBerlaku = "";
        if(trxFuidRequest.getMasaBerlakuSampai() != null){
            strMasaBerlaku = dateFormater2.format(trxFuidRequest.getMasaBerlakuSampai());
        }
        tfrdm.setMasaBerlaku(strMasaBerlaku);
        tfrdm.setAlasanPengajuan(trxFuidRequest.getAlasanPengajuan());
        tfrdm.setInfoTambahan(trxFuidRequest.getInfoTambahan());
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        tfrdm.setAttachment(new Gson().fromJson(trxFuidRequest.getAttachment(), attachmentListType));
        tfrdm.setAttachmentUPMInput(new Gson().fromJson(trxFuidRequest.getTrxFuidApproval().getUpmInputAttachment(), attachmentListType));
        tfrdm.setFileName(trxFuidRequest.getFileName());
        StatusModel smCurr = new StatusModel();
        smCurr.setKeyStatus(tfa.getCurrentState());
        tfrdm.setStatus(smCurr);
        tfrdm.setCreateDate(dateFormater1.format(trxFuidRequest.getCreateDateTime()));
        tfrdm.setData(data);
        tfrdm.setTipeLimitTransaksi(trxFuidRequest.getTipeLimitTransaksi());
        Double nominalTransaksi = null;
        if(trxFuidRequest.getNominalTransaksi() != null){
            nominalTransaksi = trxFuidRequest.getNominalTransaksi();
            nominalTransaksi = Math.round(nominalTransaksi * 100.0) /100.0;
        }
        tfrdm.setNominalTransaksi(nominalTransaksi);
        tfrdm.setNominalTransaksiUPM(trxFuidRequest.getNominalTransaksiUPM() != null ? Math.round(trxFuidRequest.getNominalTransaksiUPM() * 100.0) /100.0 : null);
        tfrdm.setUnitKerjaLama(trxFuidRequest.getUnitKerjaLama() != null ? msCabangMmsService.generateKodeNamaCabang(trxFuidRequest.getUnitKerjaLama()) : EMPTY);
        tfrdm.setUnitKerjaBaru(trxFuidRequest.getUnitKerjaBaru());
        tfrdm.setTipeKewenanganLimit(trxFuidRequest.getTipeKewenanganLimit());
        tfrdm.setOccupationDesc(trxFuidRequest.getOccupationDesc());
        tfrdm.setOrganization(trxFuidRequest.getOrganization());
        tfrdm.setLocation(trxFuidRequest.getLocation());
        tfrdm.setProgress(pm);
        tfrdm.setIsInActivePersonnelProspera(trxFuidRequest.getIsInActivePersonnelProspera() != null ? trxFuidRequest.getIsInActivePersonnelProspera() : 0);

        return tfrdm;
    }

    private UPMProcessModel buildTemplateProgressModel(String statusKey, String statusValue) {
        UPMProcessModel upmProcessModel = new UPMProcessModel();
        upmProcessModel.setStatus(buildStatusModel(statusKey, statusValue));
        return upmProcessModel;
    }

    private StatusModel buildStatusModel(String statusKey, String statusValue) {
        StatusModel statusModel = new StatusModel();
        statusModel.setKeyStatus(statusKey);
        statusModel.setValueStatus(statusValue);
        return statusModel;
    }

    public static TrxFuidApproval toTrxFuidApprovalEntity(String ticketId) throws ParseException {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        TrxFuidApproval tfra = new TrxFuidApproval();
        tfra.setTicketId(ticketId);
        LocalDateTime currStateDt = LocalDateTime.now();
        String sCurrStateDt = dateFormater1.format(currStateDt);
        tfra.setCurrentStateDT(LocalDateTime.parse(sCurrStateDt, dateFormater1));
        //set empty list attachment upm input
        List<AttachmentModel> lAttachment = new ArrayList<AttachmentModel>();
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        tfra.setUpmInputAttachment(new Gson().toJson(lAttachment, attachmentListType));

        return tfra;
    }

    public static TrxAudittrail toTrxAudittrailEntity(String ticketId) throws ParseException {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        TrxAudittrail ta = new TrxAudittrail();
        LocalDateTime currStateDt = LocalDateTime.now();
        String sCreateDt = dateFormater1.format(currStateDt);
        ta.setCreateDateTime(LocalDateTime.parse(sCreateDt, dateFormater1));
        ta.setTicketId(ticketId);

        return ta;
    }

    public static TrxAudittrail toTrxAudittrailEntity(TrxSetupParamRequestModel trxSetupParamRequestModel) throws ParseException {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        TrxAudittrail ta = new TrxAudittrail();
        LocalDateTime currStateDt = LocalDateTime.now();
        String sCreateDt = dateFormater1.format(currStateDt);
        ta.setCreateDateTime(LocalDateTime.parse(sCreateDt, dateFormater1));
        ta.setTicketId(trxSetupParamRequestModel.getTicketId());

        return ta;
    }

    public static TrxSetupParamRequest toTrxSetupParamRequestEntity(TrxSetupParamRequestModel trxSetupParamRequestModel) throws ParseException{
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        TrxSetupParamRequest tspr = new TrxSetupParamRequest();
        tspr.setTicketId(trxSetupParamRequestModel.getTicketId());
        tspr.setRequestId(trxSetupParamRequestModel.getRequestId());

        SetupParamModel spm = trxSetupParamRequestModel.getSetupParameter();
        tspr.setTanggalEfektif(new SimpleDateFormat("dd-MM-yyyy").parse(spm.getTanggalEfektif()));
        tspr.setAplikasi(String.join(",", spm.getAplikasi()));
        tspr.setParameterLama(spm.getParameterLama());
        tspr.setParameterBaru(spm.getParameterBaru());
        List<AttachmentModel> lAttachment = new ArrayList<AttachmentModel>();
        if(spm.getAttachment().size() > 0){
            lAttachment = spm.getAttachment();
        }
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        tspr.setAttachment(new Gson().toJson(lAttachment, attachmentListType));
        tspr.setFileName(spm.getFileName());
        tspr.setKategoriParamId(spm.getKategoriParam());
        String alasanPengajuan = "";
        if(spm.getAlasanPengajuan() != null && spm.getAlasanPengajuan() != ""){
            alasanPengajuan = spm.getAlasanPengajuan();
        }
        tspr.setAlasanPengajuan(alasanPengajuan);

        DataSetupParamModel dspm = spm.getData();
        //validation
        String namaCabang = ""; String telepon = "";
        if(dspm.getNamaCabang() != null && dspm.getNamaCabang() != ""){
            namaCabang = dspm.getNamaCabang();
        }
        if(dspm.getTelepon() != null && dspm.getTelepon() != ""){
            telepon = dspm.getTelepon();
        }
        tspr.setDataNik(dspm.getNIK());
        tspr.setDataNamaLengkap(dspm.getNamaLengkap());
        tspr.setDataJabatan(dspm.getJabatan());
        tspr.setDataKodeCabang(dspm.getKodeCabang());
        tspr.setDataNamaCabang(namaCabang);
        tspr.setDataTelepon(telepon);
        tspr.setDataEmail(dspm.getEmail());

        LocalDateTime createDt = LocalDateTime.now();
        String sCreateDt = dateFormater1.format(createDt);
        tspr.setCreateDateTime(LocalDateTime.parse(sCreateDt, dateFormater1));

        return tspr;
    }

    public static TrxSetupParamRequestAplikasi toTrxSetupParamRequestAplikasiEntity(String ticketId, MsTemaApplication msta) throws ParseException {
        Date date = new Date();
        TrxSetupParamRequestAplikasi tsprap = new TrxSetupParamRequestAplikasi();
        tsprap.setTicketId(ticketId);
        tsprap.setAplikasi(msta.getParamDetailId());
        tsprap.setAplikasiName(msta.getParamDetailDesc());
        tsprap.setPeriodDate(DateTimeHelper.getDatePeriodDate(date));
        tsprap.setPeriodMonth(DateTimeHelper.getDatePeriodMonth(date));

        LocalDateTime createDt = LocalDateTime.now();
        String sCreateDt = DateTimeHelper.getDateCreateDate(createDt);
        tsprap.setCreateDateTime(DateTimeHelper.getDateCreateDateAsDate(sCreateDt));
        return tsprap;
    }

    public static TrxSetupParamApproval toTrxSetupParamApprovalEntity(TrxSetupParamRequestModel trxSetupParamRequestModel) throws ParseException {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        TrxSetupParamApproval tspa = new TrxSetupParamApproval();
        tspa.setTicketId(trxSetupParamRequestModel.getTicketId());
        LocalDateTime currStateDt = LocalDateTime.now();
        String sCurrStateDt = dateFormater1.format(currStateDt);
        tspa.setCurrentStateDT(LocalDateTime.parse(sCurrStateDt, dateFormater1));
        //set empty list attachment upm input
        List<AttachmentModel> lAttachment = new ArrayList<AttachmentModel>();
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        tspa.setUpmInputAttachment(new Gson().toJson(lAttachment, attachmentListType));

        return tspa;
    }

    public TrxSetupParamRequestDetailModel toTrxSetupParamRequestDetailModel(TrxSetupParamRequest trxSetupParamRequest) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        DateFormat dateFormater2 = new SimpleDateFormat("dd-MM-yyyy");

        DataSetupParamModel data = new DataSetupParamModel();
        data.setNIK(trxSetupParamRequest.getDataNik());
        data.setNamaLengkap(trxSetupParamRequest.getDataNamaLengkap());
        data.setJabatan(trxSetupParamRequest.getDataJabatan());
        data.setKodeCabang(trxSetupParamRequest.getDataKodeCabang());
        data.setNamaCabang(trxSetupParamRequest.getDataNamaCabang());
        data.setTelepon(trxSetupParamRequest.getDataTelepon());
        data.setEmail(trxSetupParamRequest.getDataEmail());

        //ProgressModel pm = new ProgressModel();
        LinkedHashMap<String,Object> pm = new LinkedHashMap<>();
        TrxSetupParamApproval tspa = trxSetupParamRequest.getTrxSetupParamApproval();
        //puk1
        if(tspa.getPuk1NIK() != null){
            PUK1Model puk1 = new PUK1Model();
            puk1.setNik(tspa.getPuk1NIK());
            if (tspa.getPuk1Name() == null && tspa.getPuk1Occupation() == null){
                MsEmployee msEmployee = msEmployeeService.getEmployeeByNik(tspa.getPuk1NIK());
                puk1.setNama(msEmployee.getFullName());
                puk1.setJabatan(msEmployee.getOccupationDesc());
            }else {
                puk1.setNama(tspa.getPuk1Name());
                puk1.setJabatan(tspa.getPuk1Occupation());
            }
            puk1.setDateTime(tspa.getPuk1Dt() != null ? dateFormater1.format(tspa.getPuk1Dt()) : EMPTY);
            puk1.setNotes(tspa.getPuk1Notes());
            StatusModel smPUK1 = new StatusModel();
            smPUK1.setKeyStatus(tspa.getPuk1Status());
            if(CURR_STATUS_APPROVED.equalsIgnoreCase(tspa.getPuk1Status())){
                smPUK1.setValueStatus(CURR_STATUS_PUK1_DESC);
            }
            if(STATUS_WAITING.equalsIgnoreCase(tspa.getPuk1Status())){
                smPUK1.setValueStatus(CURR_STATUS_PUK1_DESC);
            }
            if(CURR_STATUS_REJECTED.equalsIgnoreCase(tspa.getPuk1Status())){
                smPUK1.setValueStatus(CURR_STATUS_REJECTED_PUK1_DESC);
            }
            puk1.setStatus(smPUK1 );
            pm.put("puk1",puk1);
        }
        //puk2
        if(tspa.getPuk2NIK() != null){
            PUK2Model puk2 = new PUK2Model();
            puk2.setNik(tspa.getPuk2NIK());
            if (tspa.getPuk1Name() == null && tspa.getPuk1Occupation() == null){
                MsEmployee msEmployee = msEmployeeService.getEmployeeByNik(tspa.getPuk2NIK());
                puk2.setNama(msEmployee.getFullName());
                puk2.setJabatan(msEmployee.getOccupationDesc());
            }else {
                puk2.setNama(tspa.getPuk2Name());
                puk2.setJabatan(tspa.getPuk2Occupation());
            }
            puk2.setDateTime(tspa.getPuk2Dt() != null ? dateFormater1.format(tspa.getPuk2Dt()) : EMPTY);
            puk2.setNotes(tspa.getPuk2Notes());
            StatusModel smPUK2 = new StatusModel();
            smPUK2.setKeyStatus(tspa.getPuk2Status());
            if(CURR_STATUS_APPROVED.equalsIgnoreCase(tspa.getPuk2Status())){
                smPUK2.setValueStatus(CURR_STATUS_PUK2_DESC);
            }
            if(STATUS_WAITING.equalsIgnoreCase(tspa.getPuk2Status())){
                smPUK2.setValueStatus(CURR_STATUS_PUK2_DESC);
            }
            if(CURR_STATUS_REJECTED.equalsIgnoreCase(tspa.getPuk2Status())){
                smPUK2.setValueStatus(CURR_STATUS_REJECTED_PUK2_DESC);
            }
            puk2.setStatus(smPUK2);
            pm.put("puk2",puk2);
        }
        //upm process
        if(tspa.getUpmInputNIK() != null){
            UPMProcessModel upmProgress = new UPMProcessModel();
            upmProgress.setNik(tspa.getUpmInputNIK());
            upmProgress.setDateTime(tspa.getUpmInputDt() != null ? dateFormater1.format(tspa.getUpmInputDt()) : EMPTY);
            TrxUpmRole trxUpmRole = trxSetupParamRequestService.getUPMProcessRole(upmProgress);
            upmProgress.setNama(trxUpmRole.getNama());
            upmProgress.setJabatan(trxUpmRole.getRole());
            upmProgress.setNotes(tspa.getUpmInputNotes());

            if (UPM_STATUS_INPROGRESS.equalsIgnoreCase(tspa.getUpmInputStatus())) {
                upmProgress.setStatus(buildStatusModel(tspa.getUpmInputStatus(), CURR_STATUS_INPROGRESS_DESC));
                pm.put("upmProgress", upmProgress);
                pm.put("upmProcess", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_VERIFICATION_DESC));
            }

            if (UPM_STATUS_VERIFICATION.equalsIgnoreCase(tspa.getUpmInputStatus())){
                upmProgress.setStatus(buildStatusModel(UPM_STATUS_INPROGRESS, CURR_STATUS_INPROGRESS_DESC));
                pm.put("upmProgress", upmProgress);

                UPMProcessModel upmProcess = new UPMProcessModel();
                upmProcess.setNik(tspa.getUpmInputNIK());
                upmProcess.setDateTime(tspa.getUpmInputDt() != null ? dateFormater1.format(tspa.getUpmInputDt()) : EMPTY);
                upmProcess.setNama(trxUpmRole.getNama());
                upmProcess.setJabatan(trxUpmRole.getRole());
                upmProcess.setNotes(tspa.getUpmInputNotes());
                upmProcess.setStatus(buildStatusModel(tspa.getUpmInputStatus(), CURR_STATUS_VERIFICATION_DESC));
                pm.put("upmProcess", upmProcess);
            }

            if (CURR_STATUS_REJECTED.equalsIgnoreCase(tspa.getUpmInputStatus())){
                upmProgress.setStatus(buildStatusModel(CURR_STATUS_REJECTED, CURR_STATUS_REJECTED_MAKER_DESC));
                pm.put("upmProgress", upmProgress);
                pm.put("upmProcess", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_VERIFICATION_DESC));
            }
        }else {
            pm.put("upmProgress", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_INPROGRESS_DESC));
            pm.put("upmProcess", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_VERIFICATION_DESC));
        }
        //upm checker
        if(tspa.getUpmCheckerNIK() != null){
            UPMCheckerModel upmChecker = new UPMCheckerModel();
            upmChecker.setNik(tspa.getUpmCheckerNIK());
            upmChecker.setDateTime(tspa.getUpmCheckerDt() != null ? dateFormater1.format(tspa.getUpmCheckerDt()) : EMPTY);

            StatusModel smUpmChecker = new StatusModel();
            smUpmChecker.setKeyStatus(tspa.getUpmCheckerStatus());
            smUpmChecker.setValueStatus(CURR_STATUS_REJECTED.equalsIgnoreCase(tspa.getUpmCheckerStatus()) ? CURR_STATUS_REJECTED_CHECKER_DESC : CURR_STATUS_DONE_DESC);
            upmChecker.setStatus(smUpmChecker);
            upmChecker.setNotes(tspa.getUpmCheckerNotes());
            pm.put("upmChecker",upmChecker);
        }else {
            pm.put("upmChecker", buildTemplateProgressModel(STATUS_WAITING, CURR_STATUS_DONE_DESC));
        }


        TrxSetupParamRequestDetailModel tsprdm = new TrxSetupParamRequestDetailModel();
        tsprdm.setTicketId(trxSetupParamRequest.getTicketId());
        tsprdm.setNikRequester(trxSetupParamRequest.getNikRequester());
        tsprdm.setTanggalEfektif(dateFormater2.format(trxSetupParamRequest.getTanggalEfektif()));
        tsprdm.setData(data);
        String paramLama = ""; String paramBaru = ""; String alasanPengajuan = "";
        if(trxSetupParamRequest.getParameterLama() != null){
            paramLama = trxSetupParamRequest.getParameterLama();
        }
        if(trxSetupParamRequest.getParameterBaru() != null){
            paramBaru = trxSetupParamRequest.getParameterBaru();
        }
        if(trxSetupParamRequest.getAlasanPengajuan() != null){
            alasanPengajuan = trxSetupParamRequest.getAlasanPengajuan();
        }
        tsprdm.setParameterLama(paramLama);
        tsprdm.setParameterBaru(paramBaru);
        tsprdm.setAlasanPengajuan(alasanPengajuan);
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        tsprdm.setAttachment(new Gson().fromJson(trxSetupParamRequest.getAttachment(), attachmentListType));
        tsprdm.setAttachmentUPMInput(new Gson().fromJson(trxSetupParamRequest.getTrxSetupParamApproval().getUpmInputAttachment(), attachmentListType));
        tsprdm.setFileName(trxSetupParamRequest.getFileName());
        StatusModel currSm = new StatusModel();
        currSm.setKeyStatus(tspa.getCurrentState());
        tsprdm.setStatus(currSm);
        tsprdm.setCreateDate(dateFormater1.format(trxSetupParamRequest.getCreateDateTime()));
        tsprdm.setProgress(pm);

        return tsprdm;
    }

    public static OwnTicketFuidModel toOwnTicketFuidModel(TrxFuidRequest trxFuidRequest){
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        TrxFuidApproval tfa = trxFuidRequest.getTrxFuidApproval();

        OwnTicketFuidModel otfm = new OwnTicketFuidModel();
        otfm.setTicketId(trxFuidRequest.getTicketId());
        otfm.setTujuan(trxFuidRequest.getTujuan());
        otfm.setAlasan(trxFuidRequest.getAlasan());
        StatusModel sm = new StatusModel();
        sm.setKeyStatus(tfa.getCurrentState());
        otfm.setStatus(sm);
        otfm.setCreateDate(dateFormater1.format(trxFuidRequest.getCreateDateTime()));

        return otfm;
    }

    public static WaitingTicketFuidModel toWaitTicketFuidModel(TrxFuidRequest trxFuidRequest){
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        TrxFuidApproval tfa = trxFuidRequest.getTrxFuidApproval();

        WaitingTicketFuidModel wtfm = new WaitingTicketFuidModel();
        wtfm.setTicketId(trxFuidRequest.getTicketId());
        wtfm.setTujuan(trxFuidRequest.getTujuan());
        wtfm.setAlasan(trxFuidRequest.getAlasan());
        StatusModel sm = new StatusModel();
        sm.setKeyStatus(tfa.getCurrentState());
        wtfm.setStatus(sm);
        wtfm.setCreateDate(dateFormater1.format(trxFuidRequest.getCreateDateTime()));
        wtfm.setNikRequester(trxFuidRequest.getDataNik());
        wtfm.setNameRequester(trxFuidRequest.getDataNamaLengkap());

        return wtfm;
    }

    private static String getName(String nik, Map<String, MsEmployee> msEmployeeMap) {
        if (msEmployeeMap.get(nik.toLowerCase()) != null && msEmployeeMap.get(nik.toLowerCase()).getFullName() != null) {
            return msEmployeeMap.get(nik.toLowerCase()).getFullName();
        }
        return EMPTY;
    }

    public static OwnTicketSetupParamModel toOwnTicketSetupParamModel(TrxSetupParamRequest trxSetupParamRequest){
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");

        OwnTicketSetupParamModel otspm = new OwnTicketSetupParamModel();
        otspm.setTicketId(trxSetupParamRequest.getTicketId());
        TrxSetupParamApproval tspa = trxSetupParamRequest.getTrxSetupParamApproval();
        StatusModel sm = new StatusModel();
        sm.setKeyStatus(tspa.getCurrentState());
        otspm.setStatus(sm);
        otspm.setCreateDate(dateFormater1.format(trxSetupParamRequest.getCreateDateTime()));
        otspm.setAplikasi(trxSetupParamRequest.getAplikasi());
        otspm.setKategoriParam(trxSetupParamRequest.getKategoriParamName());

        return otspm;
    }

    public static WaitingTicketSetupParamModel toWaitTicketSetupParamModel(TrxSetupParamRequest trxSetupParamRequest){
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");

        WaitingTicketSetupParamModel wtspm = new WaitingTicketSetupParamModel();
        wtspm.setTicketId(trxSetupParamRequest.getTicketId());
        TrxSetupParamApproval tspa = trxSetupParamRequest.getTrxSetupParamApproval();
        StatusModel sm = new StatusModel();
        sm.setKeyStatus(tspa.getCurrentState());
        wtspm.setStatus(sm);
        wtspm.setCreateDate(dateFormater1.format(trxSetupParamRequest.getCreateDateTime()));
        wtspm.setAplikasi(trxSetupParamRequest.getAplikasi());
        wtspm.setKategoriParam(trxSetupParamRequest.getKategoriParamName());
        wtspm.setNikRequester(trxSetupParamRequest.getDataNik());
        wtspm.setNameRequester(trxSetupParamRequest.getDataNamaLengkap());

        return wtspm;
    }

    public static List<DataCabangMmsModel> toDataCabangMmsModelsFC(List<MsCabang> msCabangs){
        List<DataCabangMmsModel> dataCabangMmsModels = new ArrayList<DataCabangMmsModel>();
        Iterator<MsCabang> iterator = msCabangs.iterator();
        while (iterator.hasNext()){
            MsCabang msCabang = iterator.next();

            DataCabangMmsModel dcmm = new DataCabangMmsModel();
            dcmm.setId(msCabang.getCabangId());
            dcmm.setName(msCabang.getCabangId() + " - " + msCabang.getCabangDesc());

            dataCabangMmsModels.add(dcmm);
        }
        return dataCabangMmsModels;
    }
    public static List<DataCabangMmsModel> toDataCabangMmsModelsFM(List<MMS_UPM> msMMSs){
        List<DataCabangMmsModel> dataCabangMmsModels = new ArrayList<DataCabangMmsModel>();
        Iterator<MMS_UPM> iterator = msMMSs.iterator();
        while (iterator.hasNext()){
            MMS_UPM msMMS = iterator.next();

            DataCabangMmsModel dcmm = new DataCabangMmsModel();
            dcmm.setId(msMMS.getMmsCode());
            dcmm.setName(msMMS.getMmsCode() + " - " + msMMS.getMmsName());

            dataCabangMmsModels.add(dcmm);
        }
        return dataCabangMmsModels;
    }

    public static List<DataCabangMmsModel> toDataCabangMmsModelsProspera(List<ResListOfficeProspera> resOfficeDetailProspera) {
        List<DataCabangMmsModel> dataCabangMmsModelList = new ArrayList<>();
        resOfficeDetailProspera.forEach(data -> {
            DataCabangMmsModel dataCabangMmsModel = new DataCabangMmsModel();
            dataCabangMmsModel.setId(data.getOfficeCode());
            dataCabangMmsModel.setName(data.getOfficeName());
            dataCabangMmsModelList.add(dataCabangMmsModel);
        });
        return dataCabangMmsModelList;
    }

    public static UpmTicketModel toUpmTicketModel(UpmTicket upmTicket) {
        DateFormat dateFormater2 = new SimpleDateFormat("dd-MM-yyyy");

        UpmTicketModel utm = new UpmTicketModel();
        utm.setTicketId(upmTicket.getTicketId());
        utm.setTanggalEfektif(dateFormater2.format(upmTicket.getTanggalEfektif()));
        utm.setDataNIK(upmTicket.getDataNIK());
        utm.setDataNama(upmTicket.getDataNamaLengkap());
        utm.setDataUserId(upmTicket.getDataUserId());
        utm.setDataKodeCabang(upmTicket.getDataKodeCabang());
        utm.setDataNamaCabang(upmTicket.getDataNamaCabang());
        utm.setAplikasi(upmTicket.getAplikasi());
        utm.setJenisPengajuan(upmTicket.getJenisPengajuan());
        utm.setAlasanPengajuan(upmTicket.getDeskripsi());
        utm.setKeterangan(upmTicket.getKeterangan());
        utm.setStatus(upmTicket.getCurrentState());
        utm.setPicProcess(upmTicket.getUpmInputNIK());
        utm.setPicApprove(upmTicket.getUpmCheckerNIK());

        return utm;
    }

    public static PukModel toPUKModel(MsEmployee msEmployee, int i){
        PukModel pm = new PukModel();
        pm.setRole("puk" + i);
        pm.setNik(msEmployee.getNik());
        pm.setNama(msEmployee.getFullName());
        pm.setJabatan(msEmployee.getOccupationDesc());
        return pm;
    }

    public static DelegationListModel toDelegationListModel(TrxDelegation td){
        DelegationListModel delegationListModel = new DelegationListModel();
        delegationListModel.setId(td.getDelegationId());
        delegationListModel.setStartDate(DateTimeHelper.getDateDelegationAsString(td.getStartDate()));
        delegationListModel.setEndDate(DateTimeHelper.getDateDelegationAsString(td.getEndDate()));
        delegationListModel.setInfo(td.getInfo());
        delegationListModel.setStatus(td.getStatus());
        DelegatedUserModel dum = new DelegatedUserModel();
        dum.setNik(td.getNikDelegation());
        dum.setNama(td.getNamaDelegation());
        dum.setJabatan(td.getJabatanDelegation());
        delegationListModel.setDelegatedUser(dum);
        return delegationListModel;
    }

    public static DelegationDetailModel toDelegationDetailModel(TrxDelegation trxDelegation){
        DelegationDetailModel delegationDetailModel = new DelegationDetailModel();
        delegationDetailModel.setId(trxDelegation.getDelegationId());

        DelegatedUserModel user = new DelegatedUserModel();
        user.setNik(trxDelegation.getNikRequester());
        user.setNama(trxDelegation.getNamaRequester());
        user.setJabatan(trxDelegation.getJabatanRequester());
        delegationDetailModel.setUser(user);

        delegationDetailModel.setStartDate(DateTimeHelper.getDateDelegationAsString(trxDelegation.getStartDate()));
        delegationDetailModel.setEndDate(DateTimeHelper.getDateDelegationAsString(trxDelegation.getEndDate()));
        delegationDetailModel.setInfo(trxDelegation.getInfo());

        DelegatedUserModel delegatedUser = new DelegatedUserModel();
        delegatedUser.setNik(trxDelegation.getNikDelegation());
        delegatedUser.setNama(trxDelegation.getNamaDelegation());
        delegatedUser.setJabatan(trxDelegation.getJabatanDelegation());
        delegationDetailModel.setDelegatedUser(delegatedUser);

        delegationDetailModel.setStatus(trxDelegation.getStatus());
        return delegationDetailModel;
    }

    public static MsEmployee toMsEmployee(TrxPUKVendor trxPUKVendor){
        MsEmployee msEmployee = new MsEmployee();
        msEmployee.setNik(trxPUKVendor.getNikVendor());
        msEmployee.setFullName(trxPUKVendor.getNameVendor());
        msEmployee.setOccupation(trxPUKVendor.getOccupationVendor());
        msEmployee.setOccupationDesc(trxPUKVendor.getOccupationDescVendor());
        msEmployee.setDirectSupervisorNIK(trxPUKVendor.getNikPUK());
        msEmployee.setDirectSupervisorName(trxPUKVendor.getNamePUK());
        return msEmployee;
    }

    public static ReportAplikasiPerTiketModel toReportAplikasiPerTiketModel(ReportAplikasiPerTiket reportAplikasiPerTiket) {
        ReportAplikasiPerTiketModel raptm = new ReportAplikasiPerTiketModel();
        raptm.setTicketId(reportAplikasiPerTiket.getTicketId());
        raptm.setCreateDate(DateTimeHelper.getDateCreateDate(reportAplikasiPerTiket.getCreateDatetime()));
        raptm.setAplikasi(reportAplikasiPerTiket.getAplikasi());
        raptm.setTanggalEfektif(DateTimeHelper.getDateEfektif(reportAplikasiPerTiket.getTanggalEfektif()));
        String tingkatanUser = reportAplikasiPerTiket.getTingkatanUser().equals("") ? "" : upperCaseFirstChar(reportAplikasiPerTiket.getTingkatanUser());
        raptm.setTingkatanUser(tingkatanUser);
        String attachment = "Tidak";
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        List<AttachmentModel> listAttachment = new Gson().fromJson(reportAplikasiPerTiket.getAttachment(), attachmentListType);
        if(listAttachment.size() > 0){
            attachment = "Ya";
        }
        raptm.setAttachment(attachment);
        raptm.setDataNIK(reportAplikasiPerTiket.getDataNIK());
        raptm.setDataNama(reportAplikasiPerTiket.getDataNamaLengkap());
        String jabatan = "";
        if(reportAplikasiPerTiket.getDataJabatan() != null){
            jabatan = reportAplikasiPerTiket.getDataJabatan();
        }
        raptm.setDataJabatan(jabatan);
        raptm.setDataUserId(reportAplikasiPerTiket.getDataUserId());
        String dataKodeCabang = reportAplikasiPerTiket.getDataKodeCabang();
        raptm.setDataKodeCabang(dataKodeCabang);
        raptm.setDataNamaCabang(reportAplikasiPerTiket.getDataNamaCabang());
        String location = "";
        if (dataKodeCabang.startsWith(PREFIX_MMS)) {
            location = MMS;
        } else if (dataKodeCabang.equals(KODE_KANTOR_PUSAT)) {
            location = HEAD_OFFICE;
        } else if (reportAplikasiPerTiket.getDataNamaCabang().startsWith(PREFIX_CABANG)) {
            location = KC_KFO;
        }
        raptm.setLocation(location);
        String email = "", telepon = "";
        if(reportAplikasiPerTiket.getDataEmail() != null){
            email = reportAplikasiPerTiket.getDataEmail();
        }
        if(reportAplikasiPerTiket.getDataTelepon() != null){
            telepon = reportAplikasiPerTiket.getDataTelepon();
        }
        raptm.setDataEmail(email);
        raptm.setDataTelepon(telepon);
        raptm.setJenisPengajuan(reportAplikasiPerTiket.getJenisPengajuan());
        raptm.setAlasanPengajuan(reportAplikasiPerTiket.getAlasanPengajuan() != null ? reportAplikasiPerTiket.getAlasanPengajuan() : "");
        raptm.setDeskripsi(reportAplikasiPerTiket.getDeskripsi());
        raptm.setKategori(reportAplikasiPerTiket.getKategori());
        raptm.setStatus(reportAplikasiPerTiket.getCurrentState());
        raptm.setStatusDesc(reportAplikasiPerTiket.getCurrentStateDesc());
        String doneDate = "";
        if(reportAplikasiPerTiket.getDoneDt() != null) {
            doneDate = DateTimeHelper.getDateCreateDate(reportAplikasiPerTiket.getDoneDt());
        }
        raptm.setDoneDate(doneDate);
        raptm.setCurrentStateDate(DateTimeHelper.getDateCreateDate(reportAplikasiPerTiket.getCurrentStateDT()));
        raptm.setPicProcess(reportAplikasiPerTiket.getUpmInputNIK() != null ? reportAplikasiPerTiket.getUpmInputNIK() : "");
        raptm.setPicApprove(reportAplikasiPerTiket.getUpmCheckerNIK() != null ? reportAplikasiPerTiket.getUpmCheckerNIK() : "");
        raptm.setInfoTambahan(reportAplikasiPerTiket.getInfoTambahan() != null ? reportAplikasiPerTiket.getInfoTambahan() : "");
        String masaBerlaku = "Tetap";
        if(reportAplikasiPerTiket.getMasaBerlakuSampai() != null){
            masaBerlaku = DateTimeHelper.getDateEfektif(reportAplikasiPerTiket.getMasaBerlakuSampai());
        }
        raptm.setMasaBerlaku(masaBerlaku);
        String puk1NIK = reportAplikasiPerTiket.getPuk1NIK() != null ? reportAplikasiPerTiket.getPuk1NIK() : "";
        String puk1Nama = reportAplikasiPerTiket.getPuk1Nama() != null ? reportAplikasiPerTiket.getPuk1Nama() : "";
        String puk1Jabatan = reportAplikasiPerTiket.getPuk1Jabatan() != null ? reportAplikasiPerTiket.getPuk1Jabatan() : "";
        String puk1ApproveDate = reportAplikasiPerTiket.getPuk1Dt() != null ? DateTimeHelper.getDateCreateDate(reportAplikasiPerTiket.getPuk1Dt()) : "";
        String puk2NIK = reportAplikasiPerTiket.getPuk2NIK() != null ? reportAplikasiPerTiket.getPuk2NIK() : "";
        String puk2Nama = reportAplikasiPerTiket.getPuk2Nama() != null ? reportAplikasiPerTiket.getPuk2Nama() : "";
        String puk2Jabatan = reportAplikasiPerTiket.getPuk2Jabatan() != null ? reportAplikasiPerTiket.getPuk2Jabatan() : "";
        String puk2ApproveDate = reportAplikasiPerTiket.getPuk2Dt() != null ? DateTimeHelper.getDateCreateDate(reportAplikasiPerTiket.getPuk2Dt()) : "";
        raptm.setPuk1NIK(puk1NIK);
        raptm.setPuk1Nama(puk1Nama);
        raptm.setPuk1Jabatan(puk1Jabatan);
        raptm.setPuk1ApproveDate(puk1ApproveDate);
        raptm.setPuk2NIK(puk2NIK);
        raptm.setPuk2Nama(puk2Nama);
        raptm.setPuk2Jabatan(puk2Jabatan);
        raptm.setPuk2ApproveDate(puk2ApproveDate);

        return raptm;
    }

    public static String upperCaseFirstChar(String word){
        String first = word.substring(0,1);
        String afterFirst = word.substring(1);
        return first.toUpperCase() + afterFirst;
    }

    public static ReportProductivityModel toReportProductivityModel(ReportProductivity reportProductivity) {
        ReportProductivityModel rpm = new ReportProductivityModel();
        rpm.setNikProcessor(reportProductivity.getNik());
        rpm.setNameProcessor(reportProductivity.getNama());
        rpm.setCountPerTiket(reportProductivity.getCountPerTiket());
        rpm.setCountPerAplikasi(reportProductivity.getCountPerAplikasi());
        return rpm;
    }

    public static AnalyticCountModel toAnalyticCountModel(AnalyticCount ac){
        AnalyticCountModel acm = new AnalyticCountModel();
        acm.setNik(ac.getNik());
        acm.setNama(ac.getNama());
        acm.setCountTicketInprogress(ac.getCountTicketInprogress());
        acm.setCountTicketVerification(ac.getCountTicketVerification());
        acm.setCountTicketDone(ac.getCountTicketDone());
        return acm;
    }

    public static CommentModel toCommentModel(TrxComment tcm){
        CommentModel cm = new CommentModel();
        cm.setTicketId(tcm.getTicketId());
        cm.setCommentId(tcm.getCommentId());
        cm.setNikComment(tcm.getNikComment());
        cm.setNamaComment(tcm.getNamaComment());
        cm.setCreateDateTime(DateTimeHelper.getFullDate(tcm.getCreateDateTime()));
        cm.setStatus(tcm.getStatus());
        cm.setReadDateTime(DateTimeHelper.getFullDate(tcm.getReadDateTime()));
        cm.setContent(tcm.getContent());
        Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
        cm.setAttachment(new Gson().fromJson(tcm.getAttachment(), attachmentListType));
        cm.setPicUpmMakerNik(tcm.getPicUpmMakerNik());
        cm.setPicUpmMakerNama(tcm.getPicUpmMakerNama());
        return cm;
    }

    public static MsEmployeeHierarchyModel toMsEmployeeHierarchyModel(MsEmployeeHierarchy mh){
        MsEmployeeHierarchyModel mhm = new MsEmployeeHierarchyModel();
        mhm.setNik(mh.getNik());
        mhm.setFullName(mh.getFullName());
        mhm.setOccupationDesc(mh.getOccupationDesc());
        mhm.setOrganization(mh.getOrganization());
        mhm.setLocation(mh.getLocation());
        return mhm;
    }

    public static TrxPUKVendor toTrxPUKVendor(TrxFuidRequest savedTFR, TrxFuidRequestModel trxFuidRequestModel) {
        TrxPUKVendor trxPUKVendor = new TrxPUKVendor();

        trxPUKVendor.setMasaBerlakuSampai(savedTFR.getMasaBerlakuSampai());
        trxPUKVendor.setNikVendor(trxFuidRequestModel.getFuid().getData().getNIK());
        trxPUKVendor.setNameVendor(trxFuidRequestModel.getFuid().getData().getNamaLengkap());
        trxPUKVendor.setOccupationVendor(trxFuidRequestModel.getFuid().getData().getJabatan());
        trxPUKVendor.setOccupationDescVendor(trxFuidRequestModel.getFuid().getData().getJabatan());
        trxPUKVendor.setNikPUK(trxFuidRequestModel.getFuid().getData().getNikPUKVendor());
        LocalDateTime currDt = LocalDateTime.now();
        trxPUKVendor.setCreateDatetime(currDt);
        trxPUKVendor.setUpdateDateTime(currDt);

        return trxPUKVendor;
    }

    public static EmailNotification toEmailNotification(String emailSubject, String emailDestination, String emailCc, String emailBcc, String emailMessage, List emailAttachments) {
        EmailNotification emailNotification = new EmailNotification();

        emailNotification.setEmailSubject(emailSubject);
        emailNotification.setEmailDestination(emailDestination);
        emailNotification.setEmailCc(emailCc);
        emailNotification.setEmailBcc(emailBcc);
        emailNotification.setEmailMessage(emailMessage);
        emailNotification.setEmailAttachments(emailAttachments);

        return emailNotification;
    }

    public Map<String, Object> toParameterMap(List<TimelineModel> listTM) throws Exception {
        Map<String, Object> parameters = new HashMap<>();
        String timeline = this.getTimelineTicket(listTM);

        parameters.put("timelines", timeline);
        Resource logoBtpnsResource = loadResource(pathLogoBtpns);
        parameters.put("logoBTPNS", ImageIO.read(logoBtpnsResource.getInputStream()));

        return parameters;
    }
    private String getTimelineTicket(List<TimelineModel> listTM) {
        StringBuilder sb = new StringBuilder();
        String lastAssignedInputer = "";
        for (int index = 0; index < listTM.size(); index++) {
            String currentStatus = listTM.get(index).getStatus();
            String notes = "";
            String pic = listTM.get(index).getPic();

            if (currentStatus.equalsIgnoreCase(TIMELINE_STATUS_INPROGRESS_UPM)) {
                lastAssignedInputer = listTM.get(index).getNik();
            }

            if (currentStatus.equalsIgnoreCase(TIMELINE_STATUS_REASSIGN_UPM)) {
                String substringPic = StringUtils.substringAfterLast(pic, ":");
                String nik = StringUtils.substringBeforeLast(substringPic, "-").strip();
                currentStatus = TIMELINE_STATUS_REASSIGN.concat(lastAssignedInputer).concat(" to ").concat(nik);
                pic = TIMELINE_PIC_UPM.concat(listTM.get(index).getNik()).concat(" - ").concat(listTM.get(index).getName());
                lastAssignedInputer = nik;
            }

            if (listTM.get(index).getNote() != null && (listTM.get(index).getStatus().equalsIgnoreCase(TIMELINE_STATUS_REJECT) || listTM.get(index).getStatus().equalsIgnoreCase(TIMELINE_STATUS_REJECT_TICKET))) {
                if (index == listTM.size() - 1) {
                    notes = "   catatan : " + listTM.get((index)).getNote() + "\n";
                } else {
                    notes = "|  catatan : " + listTM.get((index)).getNote() + "\n";
                }
            }

            if (index == listTM.size() - 1) {
                sb.append("+  ").append(currentStatus).append("\n")
                        .append("   ").append(pic).append("\n")
                        .append(notes)
                        .append("   ").append(listTM.get(index).getTimestamp()).append("\n");

            } else {
                sb.append("+  ").append(currentStatus).append("\n")
                        .append("|  ").append(pic).append("\n")
                        .append(notes)
                        .append("|  ").append(listTM.get(index).getTimestamp()).append("\n")
                        .append("|  ").append("\n");
            }
        }
        return sb.toString();
    }

    public Resource loadResource(String path) {
        return resourceLoader.getResource(path);
    }

    public static TrxFuidApproval toTrxFuidApproval(UpmManageTicketModel umtModel, TrxFuidApproval trxFuidApproval, String currState) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        String sCurrStateDt = dateFormater1.format(LocalDateTime.now());

        TrxFuidApproval tfa = trxFuidApproval;
        if(umtModel.getAttachmentUPMInput() != null){
            List<AttachmentModel> lAttachUPMInput = umtModel.getAttachmentUPMInput();
            Type attachmentListType = new TypeToken<ArrayList<AttachmentModel>>(){}.getType();
            tfa.setUpmInputAttachment(new Gson().toJson(lAttachUPMInput, attachmentListType));
        }
        tfa.setCurrentState(currState);
        tfa.setCurrentStateDT(LocalDateTime.parse(sCurrStateDt, dateFormater1));
        tfa.setUpmInputStatus(currState);

        return tfa;
    }

    public static TrxAudittrail toTrxAudittrail(UpmManageTicketModel umtModel, String currState, String timeLineStatusUpm, String timeLinePicUpm, String nik, String nikName) {
        DateTimeFormatter dateFormater1 = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        DateTimeFormatter dateFormater2 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
        String sCurrStateDt = dateFormater1.format(LocalDateTime.now());

        TrxAudittrail ta = new TrxAudittrail();

        ta.setNik(nik);
        ta.setTicketId(umtModel.getTicketId());
        ta.setAction(currState);
        ta.setCreateDateTime(LocalDateTime.parse(sCurrStateDt, dateFormater1));
        //json additional info
        TimelineStatusModel tsm = new TimelineStatusModel();
        tsm.setStatus(timeLineStatusUpm);
        tsm.setPic(timeLinePicUpm + nik + " - " + nikName);
        tsm.setTimestamp(dateFormater2.format(ta.getCreateDateTime()));
        ta.setAdditionalInfo(new Gson().toJson(tsm));

        return ta;
    }

    public static TrxProsperaRequest toTrxProsperaRequest(UpmManageTicketModel umtModel, TrxFuidRequest tfr, ReqPersonnelProspera reqPersonnelProspera) {
        TrxProsperaRequest trxProsperaRequest = new TrxProsperaRequest();

        trxProsperaRequest.setTicketId(umtModel.getTicketId());
        trxProsperaRequest.setCreateDateTime(LocalDateTime.now());
        trxProsperaRequest.setTujuan(tfr.getTujuan());
        trxProsperaRequest.setAlasan(tfr.getAlasan());
        trxProsperaRequest.setPayloadRequest(new Gson().toJson(reqPersonnelProspera));

        return trxProsperaRequest;
    }

    public static MsHolidayList toMsHolidayListEntity(ReqMsHolidayModel request){
        MsHolidayList mhl = new MsHolidayList();

        mhl.setId(request.getId());
        String holidayDateDDMMYYYY = request.getHolidayDate();
        String[] split = holidayDateDDMMYYYY.split("-");
        mhl.setHolidayDate(split[2] + "-" + split[1] + "-" + split[0]);
        mhl.setHolidayYear(split[2]);
        mhl.setHolidayDesc(request.getHolidayDesc());

        return mhl;
    }

    public static MsHolidayModel toMsHolidayModel(MsHolidayList mhl){
        MsHolidayModel mhm = new MsHolidayModel();

        mhm.setId(mhl.getId());
        String holidayDateYYYYMMDD = mhl.getHolidayDate();
        String[] split = holidayDateYYYYMMDD.split("-");
        mhm.setHolidayDate(split[2] + "-" + split[1] + "-" + split[0]);
        mhm.setHolidayYear(mhl.getHolidayYear());
        mhm.setHolidayDesc(mhl.getHolidayDesc());
        mhm.setCreateDatetime(DateTimeHelper.getFullDate(mhl.getCreateDatetime()));
        mhm.setUpdateDatetime(DateTimeHelper.getFullDate(mhl.getUpdateDatetime()));

        return mhm;
    }

    public static MsEmployee toMsEmployee(MsEmployeeDirector msEmployeeDirector){
        MsEmployee msEmployee = new MsEmployee();
        msEmployee.setNik(msEmployeeDirector.getNikOptima());
        msEmployee.setFullName(msEmployeeDirector.getNamaLengkap());
        msEmployee.setOccupation(msEmployeeDirector.getKeterangan());
        return msEmployee;
    }

    public static MsEmployeeDirector toMsEmployeeDirectorEntity(ReqMsEmployeeDirectorModel request){
        MsEmployeeDirector med = new MsEmployeeDirector();

        med.setNikOptima(request.getNikOptima());
        med.setNikLdap(request.getNikLdap());
        med.setNamaLengkap(request.getNamaLengkap());
        med.setEmail(request.getEmail());
        med.setKeterangan(request.getKeterangan() != null ? request.getKeterangan() : "");

        return med;
    }

    public static ResBatchUserId toResBatchUserIdModel(String batchId, int totalData) {
        ResBatchUserId resBatchUserId = new ResBatchUserId();

        resBatchUserId.setBatchId(batchId);
        resBatchUserId.setTotalData(totalData);

        return resBatchUserId;
    }

    public static ResponseModel<ResBatchUserId> toResBatchUserIdResponseModel(String type, ResponseStatus status, ResBatchUserId resBatchUserId) {
        ResponseModel<ResBatchUserId> response = new ResponseModel<>();

        response.setType(type);
        response.setDetails(resBatchUserId);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());

        return response;
    }

    public static TrxUserIdBatch toTrxUserIdBatch(String batchId, String fileName, int totalData, String type, String nikRequester, String nameRequester) {
        TrxUserIdBatch trxUserIdBatch = new TrxUserIdBatch();

        trxUserIdBatch.setBatchId(batchId);
        trxUserIdBatch.setCreateDateTime(LocalDateTime.now());
        trxUserIdBatch.setType(type);
        trxUserIdBatch.setBatchFileName(fileName);
        trxUserIdBatch.setTotalData(totalData);
        trxUserIdBatch.setUploaderNIK(nikRequester);
        trxUserIdBatch.setUploaderName(nameRequester);

        return trxUserIdBatch;
    }

    public static ResponseModel<ResUploadModel> buildResponse(String type, ResponseStatus status, Map<String, String> result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(new ResUploadModel(result));

        return response;
    }

    public static ResponseModel<ResponseListModel> buildResponseList(String type, ResponseStatus status, ResponseListModel data) {
        ResponseModel<ResponseListModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(data);

        return response;
    }

    public static ResponseModel<ResCUDUserIdModel> tobuildResponseCUDUserId(String type, ResponseStatus status, ResCUDUserIdModel resCUDUserIdModel) {
        ResponseModel<ResCUDUserIdModel> response = new ResponseModel<>();

        response.setType(type);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(resCUDUserIdModel);

        return response;
    }

    public static ResCUDUserIdModel toResCUDUserIdModel(String type, String nik) {
        ResCUDUserIdModel resCUDUserIdModel = new ResCUDUserIdModel();

        resCUDUserIdModel.setType(type);
        resCUDUserIdModel.setNik(nik);

        return resCUDUserIdModel;
    }

    public static TrxUARRequest buildTrxUARRequest(UARRequestModel uarRequestModel, String ticketId, String nik, String namaUser, String kewenangan, String jabatan, String unitKerja) {
        TrxUARRequest uarRequest = new TrxUARRequest();

        uarRequest.setTicketId(ticketId);
        uarRequest.setAplikasi(uarRequestModel.getAplikasi());
        uarRequest.setPeriodYear(uarRequestModel.getPeriodYear());
        uarRequest.setPeriodQuarter(uarRequestModel.getPeriodQuarter());
        uarRequest.setNik(nik);
        uarRequest.setNamaUser(namaUser);
        uarRequest.setKewenangan(kewenangan);
        uarRequest.setJabatan(jabatan);
        uarRequest.setUnitKerja(unitKerja);
        uarRequest.setReminder(0);

        return uarRequest;
    }

    public static TrxUARAudittrail buildUARAudittrail(Profile profile, TrxUARRequest uarRequest, String action, String status, String timelinePIC, String notes) {
        TrxUARAudittrail uarAudittrail = new TrxUARAudittrail();

        uarAudittrail.setNik(profile.getPreferred_username());
        uarAudittrail.setTicketId(uarRequest.getTicketId());
        uarAudittrail.setAction(action);
        uarAudittrail.setAdditionalInfo(new Gson().toJson(buildTimelineStatus(profile, status, timelinePIC, notes)));
        return uarAudittrail;
    }

    public static TimelineStatusModel buildTimelineStatus(Profile profile, String status, String timelinePIC, String notes) {
        TimelineStatusModel timelineStatus = new TimelineStatusModel();

        timelineStatus.setStatus(status);
        timelineStatus.setPic(timelinePIC + profile.getPreferred_username() + " - " + profile.getName());
        timelineStatus.setTimestamp(DateTimeHelper.getDateFormater3(LocalDateTime.now()));
        timelineStatus.setNote(notes);
        return timelineStatus;
    }

    public Map<String, Object> generateReportParameters(String triwulan, String tahun, String appDesc) throws IOException {
        Resource logoBtpnsResource = loadResource(pathLogoBtpns);
        return new HashMap<>() {{
            put("period", triwulan.concat("/").concat(tahun));
            put("aplikasi", appDesc);
            put("logoBTPNS", ImageIO.read(logoBtpnsResource.getInputStream()));
        }};
    }

    public Map<String, Object> generatePdfUploadUserIdParameters(String appDesc, LocalDateTime uploadDate) throws IOException {
        Resource logoBtpnsResource = loadResource(pathLogoBtpns);
        return new HashMap<>() {{
            put("tanggalUpload", DateTimeHelper.getDate(uploadDate));
            put("aplikasi", appDesc);
            put("logoBTPNS", ImageIO.read(logoBtpnsResource.getInputStream()));
        }};
    }

    public static ResBatchProcess buildResBatchProcess(Integer totalData, String status) {
        ResBatchProcess resBatchProcess = new ResBatchProcess();

        resBatchProcess.setTotalData(totalData);
        resBatchProcess.setStatus(status);

        return resBatchProcess;
    }

    public static ResponseListModel<UserIDModel> buildResUserIdListModel(Integer pageSize, Integer pageNumber, List<UserIDModel> userIDList, int totalPages, long totalItems) {
        ResponseListModel<UserIDModel> responseListModel = new ResponseListModel<>();

        responseListModel.setData(userIDList);
        responseListModel.setLimit(pageSize);
        responseListModel.setPage(pageNumber);
        responseListModel.setTotalPages(totalPages);
        responseListModel.setTotalItems(totalItems);

        return responseListModel;
    }

    public Map<String, Object> generateUARSummaryParameters(String refNumber, String triwulan, String tahun, String appDesc, int totalUser, int totalActiveUser, int totalInactiveUser, String noSOP) throws IOException {
        Resource logoBtpnsResource = loadResource(pathLogoBtpns);
        String createdAt = DateTimeHelper.convertToLongDateFormatAsString(LocalDate.now());

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("refNumber", refNumber);
        parameters.put("logoBTPNS", ImageIO.read(logoBtpnsResource.getInputStream()));
        parameters.put("aplikasi", appDesc);
        parameters.put("period", Triwulan.convertToRoman(triwulan));
        parameters.put("tahun", tahun);
        parameters.put("rangePeriod", getRangePeriod(triwulan, tahun));
        parameters.put("totalUser", String.valueOf(totalUser));
        parameters.put("totalActiveUser", String.valueOf(totalActiveUser));
        parameters.put("totalInactiveUser", String.valueOf(totalInactiveUser));
        parameters.put("createdAt", createdAt);
        parameters.put("noSOP", noSOP);

        return parameters;
    }

    public static String getRangePeriod(String triwulan, String tahun) {
        return Triwulan.convertToMonthRange(triwulan) + " " + tahun + " (Triwulan " + Triwulan.convertToRoman(triwulan) + ")";
    }

    public static String getConfirmationStatusValue(Integer confirmationStatus) {
        return confirmationStatus == 1 ? Constants.UAR_CONFIRMATION_ACTIVE : Constants.UAR_CONFIRMATION_DELETE;
    }

    public List<UARModel> mapToUARModelList(Page<TrxUARRequest> uars) {
        Map<String, TrxUpmRole> upmRoleMap = trxUpmRoleService.getMapUpmRoles();
        Map<String, MsUserIDApplication> userIDAppMap = msUserIDApplicationService.getUserIDAppMap();

        List<UARModel> uarModels = new ArrayList<>();
        for (TrxUARRequest uarRequest : uars.getContent()) {
            UARModel uarModel = new UARModel();

            uarModel.setTicketId(uarRequest.getTicketId());
            uarModel.setAplikasi(userIDAppMap.get(uarRequest.getAplikasi()).getParamDetailDesc());
            uarModel.setPeriodeTahun(uarRequest.getPeriodYear());
            uarModel.setPeriodeTriwulan(uarRequest.getPeriodQuarter());
            uarModel.setNik(uarRequest.getNik());
            uarModel.setNamaUser(uarRequest.getNamaUser());
            uarModel.setKewenangan(uarRequest.getKewenangan());
            uarModel.setJabatan(uarRequest.getJabatan() != null ? uarRequest.getJabatan() : EMPTY);
            uarModel.setUnitKerja(uarRequest.getUnitKerja());
            uarModel.setKonfirmasiAkses(uarRequest.getTrxUARApproval().getUserConfirmation() != null ? Mapper.getConfirmationStatusValue(uarRequest.getTrxUARApproval().getUserConfirmation()) : EMPTY);
            uarModel.setKeterangan(uarRequest.getTrxUARApproval().getUserNikNotes() != null ? uarRequest.getTrxUARApproval().getUserNikNotes() : EMPTY);
            uarModel.setReminder(Optional.ofNullable(uarRequest.getReminder()).orElse(0) != 0 ? CommonHelper.concateTwoString(REMINDER, uarRequest.getReminder().toString()) : EMPTY);
            uarModel.setPicProcess(uarRequest.getTrxUARApproval().getUpmMakerNik() != null ? upmRoleMap.get(uarRequest.getTrxUARApproval().getUpmMakerNik().toUpperCase()).getNama() : EMPTY);
            uarModel.setPicApprove(uarRequest.getTrxUARApproval().getUpmCheckerNik() != null ? upmRoleMap.get(uarRequest.getTrxUARApproval().getUpmCheckerNik().toUpperCase()).getNama() : EMPTY);

            uarModels.add(uarModel);
        }
        return uarModels;
    }

    public TrxFuidRequest buildFormatedContentEmailTicketFU(TrxFuidRequest trxFuidRequest, TrxFuidApproval savedTFRA) {
        TrxFuidRequest email = gson.fromJson(gson.toJson(trxFuidRequest), TrxFuidRequest.class);

        email.setTrxFuidApproval(savedTFRA);

        String[] splitAplikasi = email.getAplikasi().split(",");
        List<String> listAplikasi = Arrays.asList(splitAplikasi);
        List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
        email.setAplikasi(msta.toString().replaceAll("\\[|\\]", ""));

        String valueTujuan = msSystemParamService.getMsSystemParamDetail(email.getTujuan()).getParamDetailDesc();
        email.setTujuan(valueTujuan);

        String valueAlasan = msSystemParamService.getMsSystemParamDetail(email.getAlasan()).getParamDetailDesc();
        email.setAlasan(valueAlasan);

        String ecurrState = msSystemParamService.getMsSystemParamDetail(savedTFRA.getCurrentState()).getParamDetailDesc();
        email.getTrxFuidApproval().setCurrentState(ecurrState);

        return email;
    }

    public TrxSetupParamRequestDTO buildFormatedContentEmailTicketSP(TrxSetupParamRequest trxSetupParamRequest, TrxSetupParamApproval savedTsa) {
        TrxSetupParamRequestDTO email = gson.fromJson(gson.toJson(trxSetupParamRequest), TrxSetupParamRequestDTO.class);

        email.setTrxSetupParamApproval(trxSetupParamRequestService.mappingTrxSetupParamApprovalDTO(savedTsa));

        String[] splitAplikasi = email.getAplikasi().split(",");
        List<String> listAplikasi = Arrays.asList(splitAplikasi);
        List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
        email.setAplikasi(msta.toString().replaceAll("\\[|\\]", ""));

        String ecurrState = msSystemParamService.getMsSystemParamDetail(savedTsa.getCurrentState()).getParamDetailDesc();
        email.getTrxSetupParamApproval().setCurrentState(ecurrState);

        return email;
    }

    public Map<String, Object> generateReportTicketUserParameters(String nik, String fullName, String startDate, String endDate, Integer isUser) throws Exception {
        Map<String, Object> parameters = new HashMap<>();

        Resource logoBtpnsResource = loadResource(pathLogoBtpns);
        parameters.put("pemohon", nik.concat(" - ").concat(fullName));
        parameters.put("logoBTPNS", ImageIO.read(logoBtpnsResource.getInputStream()));
        buildPeriodParameter(parameters, startDate, endDate, isUser);
        buildJenisPemohonAndJudulParameter(parameters, isUser);

        return parameters;
    }

    private void buildPeriodParameter(Map<String, Object> parameters, String startDate, String endDate, Integer isUser) throws Exception {
        if (startDate == null || endDate == null){
            if (TRUE_FLAG_INT.equals(isUser)){
                parameters.put("period", "Semua Riwayat Permohonan");
            }else {
                parameters.put("period", "Semua Persetujuan");
            }
        }else {
            String strStartPeriod = DateTimeHelper.convertLocalDateTimeStrToDateStr(startDate.substring(0, 10));
            String strEndPeriod = DateTimeHelper.convertLocalDateTimeStrToDateStr(endDate.substring(0, 10));

            parameters.put("period", strStartPeriod.concat(" s/d ").concat(strEndPeriod));
        }
    }

    private void buildJenisPemohonAndJudulParameter(Map<String, Object> parameters, Integer isUser) {
        if (TRUE_FLAG_INT.equals(isUser)){
            parameters.put("jenisPemohon", "User Pemohon :");
            parameters.put("judul", "Laporan Permohonan TEMA");
        }else {
            parameters.put("jenisPemohon", "User PUK :");
            parameters.put("judul", "Laporan Persetujuan TEMA");
        }
    }

    public TrxAudittrail buildTrxAudittrail(String ticketId, String upmNIK, String currentState, String timelineStatusUPM, String timelinePicUPM) throws ParseException {
        TrxAudittrail ta = Mapper.toTrxAudittrailEntity(ticketId);
        ta.setNik(upmNIK);
        ta.setAction(currentState);
        TimelineStatusModel tsmInprogressUPM = mapToTsmModel(upmNIK, timelineStatusUPM, timelinePicUPM, ta.getCreateDateTime());
        ta.setAdditionalInfo(gson.toJson(tsmInprogressUPM));
        return ta;
    }

    public TimelineStatusModel mapToTsmModel(String upmNIK, String timelineStatusUPM, String timelinePicUPM, LocalDateTime createDateTime) {
        DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
        TimelineStatusModel tsm = new TimelineStatusModel();

        tsm.setStatus(timelineStatusUPM);
        tsm.setPic(timelinePicUPM + upmNIK + " - " + trxUpmRoleService.getTrxUpmRole(upmNIK).getNama());
        tsm.setTimestamp(dateFormater3.format(createDateTime));

        return tsm;
    }
}
