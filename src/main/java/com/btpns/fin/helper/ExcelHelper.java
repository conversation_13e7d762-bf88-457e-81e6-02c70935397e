package com.btpns.fin.helper;

import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.ResAnalyticVolumeTrxUser;
import com.btpns.fin.model.response.ResAnalyticVolumeTrxUserDetail;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.openxmlformats.schemas.drawingml.x2006.chart.*;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;

import static com.btpns.fin.helper.Constants.*;

@Component
public class ExcelHelper {
    private SXSSFWorkbook workbook;

    public ExcelHelper() {
        workbook = new SXSSFWorkbook(1001);
    }

    private void autoFitCellExcel(SXSSFSheet sheet, int rowNumber) {
        for (int i = 0; i < sheet.getRow(rowNumber).getLastCellNum(); i++) {
            sheet.autoSizeColumn(i);
        }
    }

    private Row initRowHeader(SXSSFSheet sheet) {
        Row row = sheet.createRow(0);
        row.setHeightInPoints(15);
        return row;
    }

    private CellStyle initCellStyle(int fontSize) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) fontSize);
        style.setFont(font);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Double) {
            cell.setCellValue((double) value);
        } else if (value instanceof Long) {
            cell.setCellValue((long) value);
        } else {
            cell.setCellValue((String) value);
        }
        cell.setCellStyle(style);
    }

    public byte[] exportExcelUserID(List<UserIDModel> userIDList, String fileName) throws Exception {
        removeFirstSheetIfExist();

        SXSSFSheet sheet = workbook.createSheet(fileName);
        sheet.trackAllColumnsForAutoSizing();

        createUserIDExcelHeader(sheet);
        writeUserIDExcel(sheet, userIDList);

        autoFitCellExcel(sheet, sheet.getFirstRowNum());

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        workbook.write(stream);
        byte[] result = stream.toByteArray();
        workbook.removeSheetAt(workbook.getSheetIndex(fileName));
        stream.close();

        return result;
    }

    private void createUserIDExcelHeader(SXSSFSheet sheet) {
        Row row = initRowHeader(sheet);
        CellStyle style = initCellStyle(12);

        int column = 0;

        createCell(row, column++, NIK_COL_NAME, style);
        createCell(row, column++, NAMA_USER_COL_NAME, style);
        createCell(row, column++, KEWENANGAN_COL_NAME, style);
        createCell(row, column++, JABATAN_COL_NAME, style);
        createCell(row, column++, UNIT_KERJA_COL_NAME, style);
    }

    private void writeUserIDExcel(SXSSFSheet sheet, List<UserIDModel> userIDList) {
        AtomicInteger rowCount = new AtomicInteger(1);

        CellStyle style = initCellStyle(11);

        for (UserIDModel userID : userIDList) {
            Row row = sheet.createRow(rowCount.getAndIncrement());

            int column = 0;

            createCell(row, column++, userID.getNik(), style);
            createCell(row, column++, userID.getNamaUser(), style);
            createCell(row, column++, userID.getKewenangan(), style);
            createCell(row, column++, userID.getJabatan(), style);
            createCell(row, column++, userID.getUnitKerja(), style);
        }
    }

    public byte[] exportExcelBiCAC(List<MsBiCAC> msBiCACList, String fileName) throws Exception {
        removeFirstSheetIfExist();

        SXSSFSheet sheet = workbook.createSheet(fileName);
        sheet.trackAllColumnsForAutoSizing();

        createBiCACExcelHeader(sheet);
        writeBiCACExcel(sheet, msBiCACList);

        autoFitCellExcel(sheet, sheet.getFirstRowNum());

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        workbook.write(stream);
        byte[] result = stream.toByteArray();
        workbook.removeSheetAt(workbook.getSheetIndex(fileName));
        stream.close();

        return result;
    }

    private void removeFirstSheetIfExist() {
        if (workbook.getNumberOfSheets() > 0) {
            workbook.removeSheetAt(0);
        }
    }

    private void createBiCACExcelHeader(SXSSFSheet sheet) {
        Row row = initRowHeader(sheet);
        CellStyle style = initCellStyle(12);

        int column = 0;

        createCell(row, column++, NIK_COL_NAME, style);
        createCell(row, column++, ID_USER_COL_NAME, style);
        createCell(row, column++, NAMA_USER_COL_NAME, style);
        createCell(row, column++, EMAIL_COL_NAME, style);
        createCell(row, column++, INSTITUSI_COL_NAME, style);
        createCell(row, column++, UNIT_KERJA_COL_NAME, style);
        createCell(row, column++, GRUP_USER_COL_NAME, style);
        createCell(row, column++, TANGGAL_AKTIF_COL_NAME, style);
        createCell(row, column++, TANGGAL_NONAKTIF_COL_NAME, style);
        createCell(row, column++, STATUS_USER_COL_NAME, style);
        createCell(row, column++, STATUS_LOGIN_COL_NAME, style);
    }

    private void writeBiCACExcel(SXSSFSheet sheet, List<MsBiCAC> msBiCACList) {
        AtomicInteger rowCount = new AtomicInteger(1);

        CellStyle style = initCellStyle(11);

        for (MsBiCAC msBiCAC : msBiCACList) {
            Row row = sheet.createRow(rowCount.getAndIncrement());

            int column = 0;

            createCell(row, column++, msBiCAC.getNik(), style);
            createCell(row, column++, msBiCAC.getIdUser(), style);
            createCell(row, column++, msBiCAC.getNamaUser(), style);
            createCell(row, column++, msBiCAC.getEmail(), style);
            createCell(row, column++, msBiCAC.getInstitusi(), style);
            createCell(row, column++, msBiCAC.getUnitKerja(), style);
            createCell(row, column++, msBiCAC.getGrupUser(), style);
            createCell(row, column++, (msBiCAC.getTanggalAktif() != null) ? DateTimeHelper.getDateToDateStringDDMMYYYY(msBiCAC.getTanggalAktif()) : "", style);
            createCell(row, column++, (msBiCAC.getTanggalNonAktif() != null) ? DateTimeHelper.getDateToDateStringDDMMYYYY(msBiCAC.getTanggalNonAktif()) : "", style);
            createCell(row, column++, msBiCAC.getStatusUser(), style);
            createCell(row, column++, msBiCAC.getStatusLogin(), style);
        }
    }

    public byte[] exportExcelDigitus(List<MsDigitus> msDigitusList, String fileName) throws Exception {
        removeFirstSheetIfExist();

        SXSSFSheet sheet = workbook.createSheet(fileName);
        sheet.trackAllColumnsForAutoSizing();

        createDigitusExcelHeader(sheet);
        writeDigitusExcel(sheet, msDigitusList);

        autoFitCellExcel(sheet, sheet.getFirstRowNum());

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        workbook.write(stream);
        byte[] result = stream.toByteArray();
        workbook.removeSheetAt(workbook.getSheetIndex(fileName));
        stream.close();

        return result;
    }

    private void createDigitusExcelHeader(SXSSFSheet sheet) {
        Row row = initRowHeader(sheet);
        CellStyle style = initCellStyle(12);

        int column = 0;

        createCell(row, column++, NIK_COL_NAME, style);
        createCell(row, column++, NAMA_USER_COL_NAME, style);
        createCell(row, column++, KEWENANGAN_COL_NAME, style);
        createCell(row, column++, JABATAN_COL_NAME, style);
    }

    private void writeDigitusExcel(SXSSFSheet sheet, List<MsDigitus> msDigitusList) {
        AtomicInteger rowCount = new AtomicInteger(1);

        CellStyle style = initCellStyle(11);
        for (MsDigitus msDigitus : msDigitusList) {
            Row row = sheet.createRow(rowCount.getAndIncrement());

            int column = 0;

            createCell(row, column++, msDigitus.getNik(), style);
            createCell(row, column++, msDigitus.getNamaUser(), style);
            createCell(row, column++, msDigitus.getKewenangan(), style);
            createCell(row, column++, msDigitus.getJabatan(), style);
        }
    }

    public byte[] exportExcelSummaryReportUPM(SummaryReportUPM summaryReportUPM, String fileName, int month, int year) throws IOException {
        removeFirstSheetIfExist();

        SXSSFSheet sheet = workbook.createSheet(fileName);
        sheet.setDisplayGridlines(false);
        sheet.trackAllColumnsForAutoSizing();

        createReportSummaryHeader(sheet, summaryReportUPM.getVolumeTrxUPMLast3Months(), month, year);
        writeReportSummary(sheet, summaryReportUPM);

        autoFitCellExcel(sheet, 2);
        sheet.setColumnWidth(1, sheet.getColumnWidth(3));
        sheet.setColumnWidth(2, sheet.getColumnWidth(3));

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        workbook.write(stream);
        byte[] result = stream.toByteArray();
        workbook.removeSheetAt(workbook.getSheetIndex(fileName));
        stream.close();

        return result;
    }

    private void createReportSummaryHeader(SXSSFSheet sheet, ResAnalyticVolumeTrxUser volumeTrxUPMLast3Months, int month, int year) {
        CellStyle headerStyle = initCustomCellStyle(true, IndexedColors.WHITE, IndexedColors.TEAL, IndexedColors.DARK_BLUE);

        writeTableName(sheet, month, year);
        createHeaderTotalVolumeIn3Months(sheet, volumeTrxUPMLast3Months.getData());
        createHeaderTotalActivity(sheet, headerStyle);
        createHeaderTotalVolumeByBranchType(sheet);
    }

    private void writeTableName(SXSSFSheet sheet, int month, int year) {
        Row row = sheet.createRow(1);
        String period = Month.of(month).getDisplayName(TextStyle.SHORT, Locale.ENGLISH) + "-" + year;
        CellStyle style = initCustomCellStyleNoBorder(true, IndexedColors.WHITE);

        createCell(row, 6, period, style);
        createCell(row, 9, period, style);
    }

    private CellStyle initCustomCellStyle(boolean isBold, IndexedColors fontColor, IndexedColors backgroundColor, IndexedColors borderColor) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        font.setColor(fontColor.getIndex());
        font.setBold(isBold);
        style.setFont(font);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBottomBorderColor(borderColor.getIndex());
        style.setTopBorderColor(borderColor.getIndex());
        style.setLeftBorderColor(borderColor.getIndex());
        style.setRightBorderColor(borderColor.getIndex());
        style.setFillForegroundColor(backgroundColor.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    private CellStyle initCustomCellStyleNoBorder(boolean isBold, IndexedColors backgroundColor) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        font.setBold(isBold);
        style.setFont(font);
        style.setFillForegroundColor(backgroundColor.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    private void createHeaderTotalVolumeIn3Months(SXSSFSheet sheet, List<ResAnalyticVolumeTrxUserDetail> volumeTrxUPMLast3Months) {
        Row row = sheet.createRow(2);
        row.setHeightInPoints(15);

        CellStyle style = initCustomCellStyleNoBorder(true, IndexedColors.WHITE);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.ORANGE.getIndex());
        style.setBottomBorderColor(IndexedColors.ORANGE.getIndex());
        style.setAlignment(HorizontalAlignment.CENTER);

        createCell(row, 0, KATEGORI_COL_NAME, style);
        createCell(row, 1, getShortMonth(volumeTrxUPMLast3Months.get(0).getPeriodMonth1()), style);
        createCell(row, 2, getShortMonth(volumeTrxUPMLast3Months.get(0).getPeriodMonth2()), style);
        createCell(row, 3, getShortMonth(volumeTrxUPMLast3Months.get(0).getPeriodMonth3()), style);
    }

    private String getShortMonth(String periodMonth) {
        YearMonth yearMonth = YearMonth.parse(periodMonth, DateTimeFormatter.ofPattern("yyyyMM"));
        return yearMonth.getMonth().toString().substring(0, 3);
    }

    private void createHeaderTotalActivity(SXSSFSheet sheet, CellStyle style) {
        Row row = sheet.getRow(2);

        createCell(row, 5, NO_COL_NAME, style);
        createCell(row, 6, ACTIVITY_COL_NAME, style);
        createCell(row, 7, JUMLAH_COL_NAME, style);
    }

    private void createHeaderTotalVolumeByBranchType(SXSSFSheet sheet) {
        Row row = sheet.getRow(2);

        CellStyle headerStyle = initCustomCellStyle(true, IndexedColors.AUTOMATIC, IndexedColors.LIGHT_CORNFLOWER_BLUE, IndexedColors.LIGHT_BLUE);

        createCell(row, 9, KATEGORI_COL_NAME, headerStyle);
        createCell(row, 10, JENIS_PENGAJUAN_COL_NAME, headerStyle);
        createCell(row, 11, HEAD_OFFICE, headerStyle);
        createCell(row, 12, KC_KFO, headerStyle);
        createCell(row, 13, MMS, headerStyle);
        createCell(row, 14, GRAND_TOTAL_COL_NAME, headerStyle);
    }

    private void writeReportSummary(SXSSFSheet sheet, SummaryReportUPM summaryReportUPM) {
        CellStyle darkBlueBorderStyle = initCustomCellStyle(false, IndexedColors.AUTOMATIC, IndexedColors.WHITE, IndexedColors.DARK_BLUE);
        CellStyle blueBorderStyle = initCustomCellStyle(false, IndexedColors.AUTOMATIC, IndexedColors.WHITE, IndexedColors.LIGHT_BLUE);

        writeDataTotalActivity(sheet, summaryReportUPM.getTotalActivity(), darkBlueBorderStyle);
        writeDataTotalVolumeByBranchType(sheet, summaryReportUPM.getTotalJenisPengajuanPerKategori(), blueBorderStyle);
        writeDataVolumeTrxLast3Month(sheet, summaryReportUPM.getVolumeTrxUPMLast3Months());

        generateChartVolumeTrxLast3Month(sheet);

        createHeaderTop5Activity(sheet);
        writeDataTop5Activity(sheet, summaryReportUPM.getTop5Activity().getData(), darkBlueBorderStyle);

        generateChartTop5Activity(sheet);
    }

    private void writeDataVolumeTrxLast3Month(SXSSFSheet sheet, ResAnalyticVolumeTrxUser volumeTrxUPMLast3Months) {
        AtomicInteger rowCount = new AtomicInteger(3);

        List<ResAnalyticVolumeTrxUserDetail> volumeTrxDetails = volumeTrxUPMLast3Months.getData();
        for (int index = 0; index <= volumeTrxDetails.size(); index++) {
            CellStyle style = initCustomCellStyleNoBorder(false, IndexedColors.WHITE);
            int rowNumber = rowCount.getAndIncrement();
            Row row = sheet.getRow(rowNumber);
            if (row == null) {
                row = sheet.createRow(rowNumber);
            }

            int column = 0;

            if (index % 2 == 0) {
                style = initCustomCellStyleNoBorder(false, IndexedColors.TAN);
            }

            if (index == volumeTrxDetails.size()) {
                style = initCustomCellStyleNoBorder(false, IndexedColors.TAN);
                Font font = workbook.createFont();
                font.setBold(true);
                style.setFont(font);
                style.setBorderBottom(BorderStyle.THIN);
                style.setBottomBorderColor(IndexedColors.ORANGE.getIndex());

                createCell(row, column++, SLA, style);

                style.setDataFormat(workbook.createDataFormat().getFormat("0%"));
                createCell(row, column++, 1, style);
                createCell(row, column++, 1, style);
                createCell(row, column++, 1, style);
                break;
            }

            createCell(row, column++, volumeTrxDetails.get(index).getType(), style);
            createCell(row, column++, volumeTrxDetails.get(index).getTotal1(), style);
            createCell(row, column++, volumeTrxDetails.get(index).getTotal2(), style);
            createCell(row, column++, volumeTrxDetails.get(index).getTotal3(), style);
        }
        CellStyle style = initCustomCellStyleNoBorder(true, IndexedColors.TEAL);

        Row row = sheet.getRow(1);

        createCell(row, 1, volumeTrxUPMLast3Months.getTotalPeriodMonth1(), style);
        createCell(row, 2, volumeTrxUPMLast3Months.getTotalPeriodMonth2(), style);
        createCell(row, 3, volumeTrxUPMLast3Months.getTotalPeriodMonth3(), style);
    }

    private void writeDataTotalActivity(SXSSFSheet sheet, List<TotalActivity> totalActivities, CellStyle style) {
        AtomicInteger rowCount = new AtomicInteger(3);
        int number = 1;
        for (TotalActivity totalActivity : totalActivities) {
            Row row = sheet.createRow(rowCount.getAndIncrement());

            int column = 5;

            createCell(row, column++, number++, style);
            createCell(row, column++, totalActivity.getActivity(), style);
            createCell(row, column++, totalActivity.getTotal(), style);
        }
    }

    private void writeDataTotalVolumeByBranchType(SXSSFSheet sheet, TotalTujuanPerKategori totalTujuanPerKategori, CellStyle style) {
        AtomicInteger currentRow = writeDataVolumeUserIDMaintenance(sheet, totalTujuanPerKategori.getUserIDMaintenance(), style);
        writeDataVolumeParameterMaintenance(sheet, totalTujuanPerKategori.getParameterMaintenance(), style, currentRow);

        writeDataVolumeGrandTotal(sheet, totalTujuanPerKategori.getGrandTotal());
    }

    private AtomicInteger writeDataVolumeUserIDMaintenance(SXSSFSheet sheet, List<TotalTujuanPerBranchType> totalTujuanPerBranchTypes, CellStyle style) {
        AtomicInteger userIDRowNumber = new AtomicInteger(3);
        int currentIndex = 0;
        for (TotalTujuanPerBranchType totalTujuanPerBranchType : totalTujuanPerBranchTypes) {
            Row row = sheet.getRow(userIDRowNumber.getAndIncrement());
            if (row == null) {
                row = sheet.createRow(userIDRowNumber.getAndIncrement());
            }

            int column = 9;

            if (currentIndex == 0) {
                CellStyle categoryStyle = initCustomCellStyle(true, IndexedColors.AUTOMATIC, IndexedColors.WHITE, IndexedColors.LIGHT_BLUE);
                createCell(row, column++, USER_ID_MAINTENANCE, categoryStyle);
            } else {
                createCell(row, column++, EMPTY, style);
            }

            createCell(row, column++, totalTujuanPerBranchType.getJenisPengajuan(), style);
            createCell(row, column++, totalTujuanPerBranchType.getHo(), style);
            createCell(row, column++, totalTujuanPerBranchType.getKcKfo(), style);
            createCell(row, column++, totalTujuanPerBranchType.getMms(), style);
            createCell(row, column++, totalTujuanPerBranchType.getGrandTotal(), style);

            currentIndex++;
        }

        return userIDRowNumber;
    }

    private void writeDataVolumeParameterMaintenance(SXSSFSheet sheet, List<TotalTujuanPerBranchType> totalTujuanPerBranchTypes, CellStyle style, AtomicInteger currentRow) {
        int currentIndex = 0;
        for (TotalTujuanPerBranchType totalTujuanPerBranchType : totalTujuanPerBranchTypes) {
            Row row = sheet.getRow(currentRow.getAndIncrement());
            if (row == null) row = sheet.createRow(currentRow.getAndIncrement());

            int column = 9;

            if (currentIndex == 0) {
                CellStyle categoryStyle = initCustomCellStyle(true, IndexedColors.AUTOMATIC, IndexedColors.WHITE, IndexedColors.LIGHT_BLUE);
                createCell(row, column++, PARAMETER_MAINTENANCE, categoryStyle);
            } else {
                createCell(row, column++, EMPTY, style);
            }

            createCell(row, column++, totalTujuanPerBranchType.getJenisPengajuan(), style);
            createCell(row, column++, totalTujuanPerBranchType.getHo(), style);
            createCell(row, column++, totalTujuanPerBranchType.getKcKfo(), style);
            createCell(row, column++, totalTujuanPerBranchType.getMms(), style);
            createCell(row, column++, totalTujuanPerBranchType.getGrandTotal(), style);

            currentIndex++;
        }
    }

    private void writeDataVolumeGrandTotal(SXSSFSheet sheet, GrandTotalPerBranchType grandTotal) {
        Row row = sheet.createRow(sheet.getLastRowNum() + 1);

        CellStyle headerStyle = initCustomCellStyle(true, IndexedColors.AUTOMATIC, IndexedColors.LIGHT_CORNFLOWER_BLUE, IndexedColors.LIGHT_BLUE);

        createCell(row, 9, GRAND_TOTAL_COL_NAME, headerStyle);
        createCell(row, 10, EMPTY, headerStyle);
        createCell(row, 11, grandTotal.getHo(), headerStyle);
        createCell(row, 12, grandTotal.getKcKfo(), headerStyle);
        createCell(row, 13, grandTotal.getMms(), headerStyle);
        createCell(row, 14, grandTotal.getGrandTotal(), headerStyle);
    }

    private void createHeaderTop5Activity(SXSSFSheet sheet) {
        Row row = sheet.createRow(28);

        CellStyle headerStyle = initCustomCellStyle(true, IndexedColors.WHITE, IndexedColors.TEAL, IndexedColors.DARK_BLUE);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        createCell(row, 0, TOP_5_ACTIVITY_COL_NAME, headerStyle);
        createCell(row, 1, NON_MMS_COL_NAME, headerStyle);
        createCell(row, 2, MMS, headerStyle);
        createCell(row, 3, GRAND_TOTAL_COL_NAME, headerStyle);
    }

    private void writeDataTop5Activity(SXSSFSheet sheet, List<TopActivity> top5Activity, CellStyle style) {
        int totalNonMMS = 0;
        int totalMMS = 0;

        for (TopActivity topActivity : top5Activity) {
            totalNonMMS = totalNonMMS + topActivity.getNonMMS();
            totalMMS = totalMMS + topActivity.getMms();

            Row row = sheet.createRow(sheet.getLastRowNum() + 1);
            int column = 0;

            createCell(row, column++, topActivity.getActivity(), style);
            createCell(row, column++, topActivity.getNonMMS(), style);
            createCell(row, column++, topActivity.getMms(), style);
            createCell(row, column++, topActivity.getGrandTotal(), style);
        }

        if (top5Activity.size() < 5){
            for (int i = 0; i < 5-top5Activity.size(); i++) {
                Row row = sheet.createRow(sheet.getLastRowNum() + 1);
                int column = 0;

                createCell(row, column++, EMPTY, style);
                createCell(row, column++, EMPTY, style);
                createCell(row, column++, EMPTY, style);
                createCell(row, column++, EMPTY, style);
            }
        }

        Row row = sheet.createRow(sheet.getLastRowNum() + 1);
        CellStyle headerStyle = initCustomCellStyle(true, IndexedColors.WHITE, IndexedColors.TEAL, IndexedColors.DARK_BLUE);

        createCell(row, 0, GRAND_TOTAL_COL_NAME, headerStyle);
        createCell(row, 1, totalNonMMS, headerStyle);
        createCell(row, 2, totalMMS, headerStyle);
        createCell(row, 3, totalNonMMS + totalMMS, headerStyle);
    }

    private void generateChartVolumeTrxLast3Month(SXSSFSheet sheet) {
        CTChart ctChart = initiateChart(sheet, VOLUME_TRX_UPM_3_BULAN_TERAKHIR_TITLE_CHART, 1, 0, 1, 0, 0, 6, 4, 27);
        CTPlotArea ctPlotArea = ctChart.getPlotArea();

        String monthsRefer = sheet.getSheetName()+"!$B$3:$D$3";

        CTBarChart ctBarChart = ctPlotArea.addNewBarChart();
        ctBarChart.addNewVaryColors().setVal(false);
        ctBarChart.addNewBarDir().setVal(STBarDir.COL);
        ctBarChart.addNewGrouping().setVal(STBarGrouping.STACKED);
        ctPlotArea.getBarChartArray(0).addNewOverlap().setVal((byte) 100);

        String headerDataFuid = CommonHelper.concateTwoString(sheet.getSheetName(),"!$A$4");
        String valueDataFuid = CommonHelper.concateTwoString(sheet.getSheetName(),"!$B$4:$D$4");
        createNewBarSeries(ctBarChart, headerDataFuid, monthsRefer, valueDataFuid, 0);

        String headerDataSP = CommonHelper.concateTwoString(sheet.getSheetName(),"!$A$5");
        String valueDataSP = CommonHelper.concateTwoString(sheet.getSheetName(),"!$B$5:$D$5");
        createNewBarSeries(ctBarChart, headerDataSP, monthsRefer, valueDataSP, 0);

        ctBarChart.addNewAxId().setVal(1122);
        ctBarChart.addNewAxId().setVal(2233);

        CTCatAx ctCatAx = ctPlotArea.addNewCatAx();
        ctCatAx.addNewAxId().setVal(1122);
        CTScaling ctScaling = ctCatAx.addNewScaling();
        ctScaling.addNewOrientation().setVal(STOrientation.MIN_MAX);
        ctCatAx.addNewDelete().setVal(false);
        ctCatAx.addNewAxPos().setVal(STAxPos.B);
        ctCatAx.addNewCrossAx().setVal(2233);
        ctCatAx.addNewMinorTickMark().setVal(STTickMark.NONE);
        ctCatAx.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);

        CTValAx ctValAx = ctPlotArea.addNewValAx();
        ctValAx.addNewAxId().setVal(2233);
        ctScaling = ctValAx.addNewScaling();
        ctScaling.addNewOrientation().setVal(STOrientation.MIN_MAX);
        ctValAx.addNewDelete().setVal(false);
        ctValAx.addNewAxPos().setVal(STAxPos.L);
        ctValAx.addNewCrossAx().setVal(1122);
        ctValAx.addNewMinorTickMark().setVal(STTickMark.NONE);
        ctValAx.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);

        CTLineChart ctLineChart = ctPlotArea.addNewLineChart();
        ctLineChart.addNewVaryColors().setVal(false);

        String headerDataSLA = CommonHelper.concateTwoString(sheet.getSheetName(),"!$A$6");
        String valueDataSLA = CommonHelper.concateTwoString(sheet.getSheetName(),"!$B$6:$D$6");
        createNewLineSeries(ctLineChart, headerDataSLA, monthsRefer, valueDataSLA, 4);

        ctLineChart.addNewAxId().setVal(3344);
        ctLineChart.addNewAxId().setVal(4455);

        CTCatAx ctCatAx1 = ctPlotArea.addNewCatAx();
        ctCatAx1.addNewAxId().setVal(3344);
        CTScaling ctScaling1 = ctCatAx1.addNewScaling();
        ctScaling1.addNewOrientation().setVal(STOrientation.MIN_MAX);
        ctCatAx1.addNewDelete().setVal(true);
        ctCatAx1.addNewAxPos().setVal(STAxPos.B);
        ctCatAx1.addNewCrossAx().setVal(4455);

        CTValAx ctValAx2 = ctPlotArea.addNewValAx();
        ctValAx2.addNewAxId().setVal(4455);
        ctScaling1 = ctValAx2.addNewScaling();
        ctScaling1.addNewOrientation().setVal(STOrientation.MIN_MAX);
        ctScaling1.addNewMax().setVal(Math.ceil(1*100)/100);
        ctValAx2.addNewDelete().setVal(false);
        ctValAx2.addNewAxPos().setVal(STAxPos.R);
        ctValAx2.addNewCrossAx().setVal(3344);
        ctValAx2.addNewMinorTickMark().setVal(STTickMark.NONE);
        ctValAx2.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);
        ctValAx2.addNewCrosses().setVal(STCrosses.MAX);
    }

    private CTChart initiateChart(SXSSFSheet sheet, String title, int dx1, int dy1, int dx2, int dy2, int col1, int row1, int col2, int row2) {
        sheet.createDrawingPatriarch();
        XSSFDrawing drawing = sheet.getDrawingPatriarch();
        XSSFClientAnchor anchor = drawing.createAnchor(dx1, dy1, dx2, dy2, col1, row1, col2, row2);
        XSSFChart chart = drawing.createChart(anchor);
        chart.setTitleText(title);
        chart.setTitleOverlay(false);

        return chart.getCTChart();
    }

    private void createNewBarSeries(CTBarChart ctBarChart, String headerData, String dataRefer, String valueData, int color) {
        CTBarSer ctBarSer = ctBarChart.addNewSer();
        CTSerTx ctSerTx = ctBarSer.addNewTx();
        CTStrRef ctStrRef = ctSerTx.addNewStrRef();
        ctStrRef.setF(headerData);
        ctBarSer.addNewIdx().setVal(color);

        CTAxDataSource ctAxDataSource = ctBarSer.addNewCat();
        ctStrRef = ctAxDataSource.addNewStrRef();
        ctStrRef.setF(dataRefer);

        CTNumDataSource ctNumDataSource = ctBarSer.addNewVal();
        CTNumRef ctNumRef = ctNumDataSource.addNewNumRef();
        ctNumRef.setF(valueData);
    }

    private void createNewLineSeries(CTLineChart ctLineChart, String headerData, String dataRefer, String valueData, int color) {
        CTLineSer ctLineSer = ctLineChart.addNewSer();
        CTSerTx ctSerTx1 = ctLineSer.addNewTx();
        CTStrRef ctStrRef1 = ctSerTx1.addNewStrRef();
        ctStrRef1.setF(headerData);
        ctLineSer.addNewIdx().setVal(color);

        CTAxDataSource ctAxDataSource1 = ctLineSer.addNewCat();
        ctStrRef1 = ctAxDataSource1.addNewStrRef();
        ctStrRef1.setF(dataRefer);

        CTNumDataSource ctNumDataSource1 = ctLineSer.addNewVal();
        CTNumRef ctNumRef1 = ctNumDataSource1.addNewNumRef();
        ctNumRef1.setF(valueData);
    }

    private void generateChartTop5Activity(SXSSFSheet sheet) {
        CTChart ctChart = initiateChart(sheet, TOP_5_PROSES_UPM_BY_LOKASI_KERJA_TITLE_CHART, 1, 0, 1, 0, 0, 35, 9, 55);
        CTPlotArea ctPlotArea = ctChart.getPlotArea();

        String activitiesRefer = sheet.getSheetName()+"!$A$30:$A$34";

        CTBarChart ctBarChart = ctPlotArea.addNewBarChart();
        ctBarChart.addNewVaryColors().setVal(false);
        ctBarChart.addNewBarDir().setVal(STBarDir.COL);
        ctBarChart.addNewGrouping().setVal(STBarGrouping.CLUSTERED);

        String headerDataNonMMS = CommonHelper.concateTwoString(sheet.getSheetName(),"!$B$29");
        String valueDataNonMMS = CommonHelper.concateTwoString(sheet.getSheetName(),"!$B$30:$B$34");
        createNewBarSeries(ctBarChart, headerDataNonMMS, activitiesRefer, valueDataNonMMS, 5);

        String headerDataMMS = CommonHelper.concateTwoString(sheet.getSheetName(),"!$C$29");
        String valueDataMMS = CommonHelper.concateTwoString(sheet.getSheetName(),"!$C$30:$C$34");
        createNewBarSeries(ctBarChart, headerDataMMS, activitiesRefer, valueDataMMS, 0);

        ctBarChart.addNewAxId().setVal(5566);
        ctBarChart.addNewAxId().setVal(6677);

        CTCatAx ctCatAx = ctPlotArea.addNewCatAx();
        ctCatAx.addNewAxId().setVal(5566);
        CTScaling ctScaling = ctCatAx.addNewScaling();
        ctScaling.addNewOrientation().setVal(STOrientation.MIN_MAX);
        ctCatAx.addNewDelete().setVal(false);
        ctCatAx.addNewAxPos().setVal(STAxPos.B);
        ctCatAx.addNewCrossAx().setVal(6677);
        ctCatAx.addNewMinorTickMark().setVal(STTickMark.NONE);
        ctCatAx.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);

        CTValAx ctValAx = ctPlotArea.addNewValAx();
        ctValAx.addNewAxId().setVal(6677);
        ctScaling = ctValAx.addNewScaling();
        ctScaling.addNewOrientation().setVal(STOrientation.MIN_MAX);
        ctValAx.addNewDelete().setVal(false);
        ctValAx.addNewAxPos().setVal(STAxPos.L);
        ctValAx.addNewCrossAx().setVal(5566);
        ctValAx.addNewMinorTickMark().setVal(STTickMark.NONE);
        ctValAx.addNewTickLblPos().setVal(STTickLblPos.NEXT_TO);

        CTLineChart ctLineChart = ctPlotArea.addNewLineChart();
        ctLineChart.addNewVaryColors().setVal(false);
        ctLineChart.addNewGrouping().setVal(STGrouping.STACKED);

        String headerDataGrandTotal = CommonHelper.concateTwoString(sheet.getSheetName(),"!$D$29");
        String valueDataGrandTotal = CommonHelper.concateTwoString(sheet.getSheetName(),"!$D$30:$D$34");
        createNewLineSeries(ctLineChart, headerDataGrandTotal, activitiesRefer, valueDataGrandTotal, 2);

        ctLineChart.addNewAxId().setVal(7788);
        ctLineChart.addNewAxId().setVal(8899);

        CTCatAx ctCatAx1 = ctPlotArea.addNewCatAx();
        ctCatAx1.addNewAxId().setVal(7788);
        CTScaling ctScaling1 = ctCatAx1.addNewScaling();
        ctScaling1.addNewOrientation().setVal(STOrientation.MIN_MAX);
        ctCatAx1.addNewDelete().setVal(true);
        ctCatAx1.addNewAxPos().setVal(STAxPos.B);
        ctCatAx1.addNewCrossAx().setVal(8899);

        CTValAx ctValAx1 = ctPlotArea.addNewValAx();
        ctValAx1.addNewAxId().setVal(8899);
        ctScaling1 = ctValAx1.addNewScaling();
        ctScaling1.addNewOrientation().setVal(STOrientation.MIN_MAX);
        ctValAx1.addNewDelete().setVal(true);
        ctValAx1.addNewAxPos().setVal(STAxPos.R);
        ctValAx1.addNewCrossAx().setVal(7788);
        ctValAx1.addNewMinorTickMark().setVal(STTickMark.NONE);
        ctValAx1.addNewTickLblPos().setVal(STTickLblPos.NONE);
        ctValAx1.addNewCrosses().setVal(STCrosses.MAX);

        CTLegend ctLegend = ctChart.addNewLegend();
        ctLegend.addNewLegendPos().setVal(STLegendPos.B);
        ctLegend.addNewOverlay().setVal(false);
    }

    public byte[] exportExcelTemplateFuidBulkUserId(String fileName) throws Exception {
        removeFirstSheetIfExist();

        SXSSFSheet sheet = workbook.createSheet(fileName);
        sheet.trackAllColumnsForAutoSizing();

        createTemplateBulkUserIDExcelHeader(sheet);
        writeTemplateBulkUserIDExcel(sheet);

        autoFitCellExcel(sheet, sheet.getFirstRowNum());

        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        workbook.write(stream);
        byte[] result = stream.toByteArray();
        workbook.removeSheetAt(workbook.getSheetIndex(fileName));
        stream.close();

        return result;
    }

    private void createTemplateBulkUserIDExcelHeader(SXSSFSheet sheet) {
        Row row = initRowHeader(sheet);
        CellStyle style = initCellStyle(12);

        int column = 0;

        createCell(row, column++, NIK_COL_NAME, style);
        createCell(row, column++, NAMA_COL_NAME, style);
        createCell(row, column++, JABATAN_COL_NAME, style);
        createCell(row, column++, KODE_CABANG_COL_NAME, style);
        createCell(row, column++, NAMA_CABANG_COL_NAME, style);
        createCell(row, column++, JENIS_APLIKASI_COL_NAME, style);
        createCell(row, column++, MASA_BERLAKU_COL_NAME, style);
        createCell(row, column++, JENIS_PENGAJUAN_COL_NAME, style);
        createCell(row, column++, PIC_PROCESS_COL_NAME, style);
        createCell(row, column++, PIC_APPROVE_COL_NAME, style);
        createCell(row, column++, INFO_TIKET_NAME, style);
    }

    private void writeTemplateBulkUserIDExcel(SXSSFSheet sheet) {
        Row row = sheet.createRow(sheet.getLastRowNum() + 1);
        CellStyle style = initCellStyle(12);

        int column = 0;

        createCell(row, column++, DATA_NIK_TEMPLATE, style);
        createCell(row, column++, DATA_NAMA_TEMPLATE, style);
        createCell(row, column++, DATA_JABATAN_TEMPLATE, style);
        createCell(row, column++, DATA_KODE_CABANG_TEMPLATE, style);
        createCell(row, column++, DATA_NAMA_CABANG_TEMPLATE, style);
        createCell(row, column++, DATA_JENIS_APLIKASI_TEMPLATE, style);
        createCell(row, column++, DATA_MASA_BERLAKU_TEMPLATE, style);
        createCell(row, column++, DATA_JENIS_PENGAJUAN_TEMPLATE, style);
        createCell(row, column++, DATA_PIC_PROCESS_TEMPLATE, style);
        createCell(row, column++, DATA_PIC_APPROVE_TEMPLATE, style);
        createCell(row, column++, DATA_INFO_TIKET_TEMPLATE, style);
    }
}
