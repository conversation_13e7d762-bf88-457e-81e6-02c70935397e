package com.btpns.fin.helper;

import com.btpns.fin.model.entity.MsHolidayList;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

import static com.btpns.fin.helper.Constants.EMPTY;

public class DateTimeHelper {
    private static final DateTimeFormatter dateFormaterFull = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
    private static final DateTimeFormatter dateFormaterDateOnly = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    private static final DateTimeFormatter dateFormaterDatePadding = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter dateFormaterDatePeriodMonth = DateTimeFormatter.ofPattern("MM-yyyy");
    private static final DateTimeFormatter dateFormaterCreateDate = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter datetimeFormaterYYYYMMDD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateFormat dateFormaterEfektif = new SimpleDateFormat("yyyy-MM-dd");
    private static final DateFormat dateFormaterPeriodDate = new SimpleDateFormat("yyyyMMdd");
    private static final DateFormat dateFormaterPeriodMonth = new SimpleDateFormat("yyyyMM");
    private static final DateFormat dateFormaterDelegation = new SimpleDateFormat("dd-MM-yyyy");
    private static final DateFormat dateFormaterDDMMYYYY = new SimpleDateFormat("dd-MM-yyyy");
    private static final DateFormat dateFormaterYYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");
    private static final DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
    private static final DateTimeFormatter dateFormatterEEEEddMMMMyyyy = DateTimeFormatter.ofPattern("EEEE, dd MMMM yyyy", new Locale("id", "ID"));
    private static final DateTimeFormatter dateFormatterLongDate = DateTimeFormatter.ofPattern("dd MMMM yyyy", new Locale("id", "ID"));

    public static String getDate(LocalDateTime createDt) {
        return dateFormaterDateOnly.format(createDt);
    }

    public static String getFullDate(LocalDateTime createDt) {
        if (createDt == null) {
            return EMPTY;
        }
        return dateFormaterFull.format(createDt);
    }

    public static LocalDateTime getFullDateAsDate(String sCreateDt){
        return LocalDateTime.parse(sCreateDt, dateFormaterFull);
    }

    public static String getDatePadding(LocalDateTime createDt) {
        return dateFormaterDatePadding.format(createDt);
    }

    public static String getDateCreateDate(LocalDateTime createDt) {
        return dateFormaterCreateDate.format(createDt);
    }

    public static LocalDateTime getDateCreateDateAsDate(String sCreateDt) {
        return LocalDateTime.parse(sCreateDt, dateFormaterCreateDate);
    }

    public static String getDatePeriodDate(Date createDt) {
        return dateFormaterPeriodDate.format(createDt);
    }

    public static String getDatePeriodMonth(Date createDt) {
        return dateFormaterPeriodMonth.format(createDt);
    }

    public static String getDatePeriodMonth(LocalDate date) {
        return dateFormaterDatePeriodMonth.format(date);
    }

    public static String getDateEfektif(Date createDt) { return dateFormaterEfektif.format(createDt); }

    public static Date getDateDelegation(String sCreateDt) throws ParseException {
        return dateFormaterDelegation.parse(sCreateDt);
    }

    public static Date getStringToDateYYYYMMDD(String date) throws ParseException { return dateFormaterYYYYMMDD.parse(date); }

    public static String getDateToDateStringDDMMYYYY(Date date) { return dateFormaterDDMMYYYY.format(date); }

    public static String getDateToDateStringYYYYMMDD(Date date) { return dateFormaterYYYYMMDD.format(date); }

    public static String getDateDelegationAsString(Date createDt) {
        return dateFormaterDelegation.format(createDt);
    }

    public static Date getDateYYYYMMDD(String sCreateDt) throws ParseException {
        return dateFormaterYYYYMMDD.parse(sCreateDt);
    }

    public static LocalDate getLocalDateYYYYMMDD(String date) throws Exception {
        return LocalDate.parse(date, datetimeFormaterYYYYMMDD);
    }

    public static String getDateDDMMYYYYAsString(Date createDt) {
        return dateFormaterDDMMYYYY.format(createDt);
    }

    public static String convertLocalDateTimeStrToDateStr(String localDateTimeStr) throws Exception {
        Date dtDate = getDateYYYYMMDD(localDateTimeStr);
        return getDateDDMMYYYYAsString(dtDate);
    }

    public static String getDateFormater3(LocalDateTime createDt) {
        return dateFormater3.format(createDt);
    }

    public static LocalDateTime getBeginningofMonth(String month) {
        return LocalDateTime.parse("01-" + month + " 00:00:00", dateFormaterFull);
    }
    public static LocalDateTime getEndofMonth(String month){
        return LocalDateTime.parse("01-" + month + " 23:59:59", dateFormaterFull).plusMonths(1).minusDays(1);
    }

    public static String getDatePlus4WorkdaysAsString(LocalDate localDate, Map<String, MsHolidayList> holidayMap) {
        LocalDate futureDate = localDate;

        int workdays = 0;
        int targetWorkdays = 4;
        while (workdays < targetWorkdays) {
            futureDate = futureDate.plusDays(1);
            if (futureDate.getDayOfWeek() != DayOfWeek.SATURDAY && futureDate.getDayOfWeek() != DayOfWeek.SUNDAY && !holidayMap.containsKey(futureDate.toString())) {
                workdays++;
            }
        }

        return dateFormatterEEEEddMMMMyyyy.format(futureDate);
    }

    public static LocalDate convertToLocalDate(String dateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        return LocalDate.parse(dateString, formatter);
    }

    public static String convertToLongDateFormatAsString(LocalDate localDate) {
        return dateFormatterLongDate.format(localDate);
    }
}
