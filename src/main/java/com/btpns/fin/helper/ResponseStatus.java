package com.btpns.fin.helper;

public enum ResponseStatus {

    SUCCESS("Success","200"),
    FAILED("failed","201"),
    SUCCESS_FAIL_NOTIF("Success but fail send notification","202"),
    FAILED_MIN_INPUT_REQUIRED("Input minimal 3 Karakter", "204"),
    FAILED_SET_STATUS_PERSONNEL_PROSPERA("Status tidak dapat diubah sebab pengguna telah memiliki satu atau lebih nasabah yang didaftarkan","421"),
    FAILED_SET_PROSPERA_ROLE_ATTRIBUTES("Kode/Nama Role tidak sesuai", "422"),
    FAILED_PROCESSED_BY_PROSPERA("Failed processed by Prospera","406"),
    FAILED_ENRICH_DATA_FROM_PROSPERA("Failed Enrich Data From Prospera","412"),
    FAILED_REGISTER_BY_PROSPERA("Failed to register by <PERSON>spera", "413"),
    FAILED_GET_DATA_FROM_PROSPERA("Failed to get data from Prospera", "414"),
    FAILED_MAX_DIGIT_REQUIRED("The maximum transaction nominal input is " +CommonHelper.getRpCurrencyFormat(), "415"),
    FORBIDDEN("Forbidden","403"),
    NOT_FOUND("Not Found","404"),
    ALREADY_EXIST("Already exist / Conflict","409");

    private String value;

    private String code;

    ResponseStatus(String value, String code){
        this.value = value;
        this.code = code;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

}