package com.btpns.fin.helper;

public enum Triwulan {
    Q1("Q1", "I", "01 Januari - 31 Maret"),
    Q2("Q2", "II", "01 April - 30 Juni"),
    Q3("Q3", "III", "01 Juli - 30 September"),
    Q4("Q4", "IV", "01 Oktober - 31 Desember");

    private final String triwulan;
    private final String romanNumeral;
    private final String monthRange;

    Triwulan(String triwulan, String romanNumeral, String monthRange) {
        this.triwulan = triwulan;
        this.romanNumeral = romanNumeral;
        this.monthRange = monthRange;
    }

    public String getTriwulan() {
        return triwulan;
    }

    public String getRomanNumeral() {
        return romanNumeral;
    }

    public String getMonthRange() {
        return monthRange;
    }

    public static String convertToRoman(String triwulan) {
        for (Triwulan value : values()) {
            if (value.triwulan.equals(triwulan)) {
                return value.romanNumeral;
            }
        }
        throw new IllegalArgumentException(triwulan);
    }

    public static String convertToMonthRange(String triwulan) {
        for (Triwulan value : values()) {
            if (value.triwulan.equals(triwulan)) {
                return value.monthRange;
            }
        }
        throw new IllegalArgumentException(triwulan);
    }

    public static Triwulan getTriwulan(int month) {
        if (month == 4) {
            return Triwulan.Q1;
        }
        if (month == 7) {
            return Triwulan.Q2;
        }
        if (month == 10) {
            return Triwulan.Q3;
        }
        return Triwulan.Q4;
    }
}
