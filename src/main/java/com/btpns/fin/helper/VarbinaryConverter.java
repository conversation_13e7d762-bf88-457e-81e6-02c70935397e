package com.btpns.fin.helper;

import javax.persistence.AttributeConverter;
import java.nio.charset.StandardCharsets;

public class VarbinaryConverter implements AttributeConverter<String, byte[]> {
    @Override
    public byte[] convertToDatabaseColumn(String attribute) {
        if (attribute != null) {
            if(!attribute.equals("")) {
                return attribute.getBytes(StandardCharsets.UTF_8);
            }
        }
        return null;
    }

    @Override
    public String convertToEntityAttribute(byte[] dbData) {
        if(dbData != null){
            return new String(dbData, StandardCharsets.UTF_8);
        }
        return null;
    }
}
