package com.btpns.fin.helper;

import com.btpns.fin.model.ReportAplikasiPerTiketModel;
import liquibase.util.file.FilenameUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.Constants.KATEGORI_COL_NAME;

public class DocumentHelper {

    private static final String UNDERSCORE_SEPARATOR = "_";
    private static final String SLASH_SEPARATOR = "/";

    private static final String SEQUENCE_LABEL ="SEQ";
    private static final String OF_SEPARATOR = "of";
    private static final String UPM_TEMA_FOLDER = "UPM/TEMA";

    // pattern UPM/TEMA/DATE/MILLIS_FILENAME.extention
    // UPM/TEMA/20220524/1653393506232_lampiran.pdf

    public static String generateMMIFilePath(MultipartFile file, LocalDateTime localDateTime){
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        String filename = FilenameUtils.getBaseName(file.getOriginalFilename());
        String convertedFileName = removeSpecialCharInFileName(filename);
        StringBuilder sb = new StringBuilder();
        sb.append(UPM_TEMA_FOLDER)
            .append(SLASH_SEPARATOR)
            .append(DateTimeHelper.getDatePadding(localDateTime))
            .append(SLASH_SEPARATOR)
            .append(System.currentTimeMillis())
            .append(UNDERSCORE_SEPARATOR)
            .append(convertedFileName)
            .append(".")
            .append(extension);
        return sb.toString();

    }

    public static String removeSpecialCharInFileName(String fileName){
        String specialChar = "[^a-zA-Z0-9]+";
        return fileName.replaceAll(specialChar, "");
    }

    public static String generateMMISpesimenFilePath(MultipartFile file){
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        String filename = FilenameUtils.getBaseName(file.getOriginalFilename());
        StringBuilder sb = new StringBuilder();
        sb.append(UPM_TEMA_FOLDER)
                .append(SLASH_SEPARATOR)
                .append(filename)
                .append(".")
                .append(extension);
        return sb.toString();

    }

    public static String generateReportFilePath(String fileName, String extension){
        StringBuilder sb = new StringBuilder();
        sb.append(UPM_TEMA_FOLDER)
                .append(SLASH_SEPARATOR)
                .append(System.currentTimeMillis())
                .append(UNDERSCORE_SEPARATOR)
                .append(fileName)
                .append(extension);
        return sb.toString();

    }

    public static String generateReportFileName(Integer totalFile, Integer file){
        StringBuilder sb = new StringBuilder();
        if (totalFile > 1){
            sb.append(REPORT_FILE_NAME)
              .append(" ")
              .append("Pt.")
              .append(" ")
              .append(file);
        }else {
            sb.append(REPORT_FILE_NAME);
        }
        return sb.toString();

    }

    public static String generateReportDetailFilePath(String ticketId, String fileName, String extension) {
        StringBuilder sb = new StringBuilder();
        sb.append(UPM_TEMA_FOLDER)
                .append(SLASH_SEPARATOR)
                .append(System.currentTimeMillis())
                .append(UNDERSCORE_SEPARATOR)
                .append(fileName)
                .append(UNDERSCORE_SEPARATOR)
                .append(ticketId)
                .append(extension);
        return sb.toString();
    }

    public static byte[] getFileBase64(File csvFile) throws IOException {
        return FileUtils.readFileToByteArray(csvFile);
    }

    public static String generateReportDetailUPMFileName(String reportType, String startDate, String endDate) {
        StringBuilder sb = new StringBuilder();

        if (TIKET.equalsIgnoreCase(reportType)){
            sb.append("Data Report Permohonan Per Tiket Tanggal ");
        }else {
            sb.append("Data Report Permohonan Per Aplikasi Tanggal ");
        }
        sb.append(startDate.split(" ")[0])
                .append(" - ")
                .append(endDate.split(" ")[0]);
        return sb.toString();
    }

    public static String[] createHeaderCsvreportDetailUPM() {
        return new String[]{
                TICKET_ID_COL_NAME,
                CREATE_DATE_COL_NAME,
                TANGGAL_EFEKTIF_COL_NAME,
                NIK_COL_NAME,NAMA_COL_NAME,
                JABATAN_COL_NAME,
                USER_ID_COL_NAME,
                KODE_CABANG_COL_NAME,
                NAMA_CABANG_COL_NAME,
                LOCATION_COL_NAME,
                EMAIL_COL_NAME,
                TELEPON_COL_NAME,
                JENIS_APLIKASI_COL_NAME,
                JENIS_TINGKATAN_USER_COL_NAME,
                MASA_BERLAKU_COL_NAME,
                JENIS_PENGAJUAN_COL_NAME,
                DESKRIPSI_ALASAN_COL_NAME,
                KETERANGAN_COL_NAME,
                INFO_TAMBAHAN_COL_NAME,
                STATUS_LAMPIRAN_COL_NAME,
                PUK1_NIK_COL_NAME,
                PUK1_NAMA_COL_NAME,
                PUK1_JABATAN_COL_NAME,
                PUK1_TGL_APPROVE_COL_NAME,
                PUK2_NIK_COL_NAME,
                PUK2_NAMA_COL_NAME,
                PUK2_JABATAN_COL_NAME,
                PUK2_TGL_APPROVE_COL_NAME,
                STATUS_COL_NAME,
                PIC_PROCESS_COL_NAME,
                TGL_PROCESS_COL_NAME,
                PIC_APPROVE_COL_NAME,
                TGL_APPROVE_COL_NAME,
                KATEGORI_COL_NAME
        };
    }

    public static String[] createBodyCsvreportDetailUPM(ReportAplikasiPerTiketModel raptm) {
        return new String[]{
                raptm.getTicketId(),
                raptm.getCreateDate(),
                raptm.getTanggalEfektif(),
                CommonHelper.concateTwoString("'", CommonHelper.checkValueString(raptm.getDataNIK())),
                raptm.getDataNama(),
                raptm.getDataJabatan(),
                CommonHelper.concateTwoString("'", CommonHelper.checkValueString(raptm.getDataUserId())),
                CommonHelper.concateTwoString("'", CommonHelper.checkValueString(raptm.getDataKodeCabang())),
                raptm.getDataNamaCabang(),
                raptm.getLocation(),
                raptm.getDataEmail(),
                CommonHelper.concateTwoString("'", CommonHelper.checkValueString(raptm.getDataTelepon())),
                raptm.getAplikasi(),
                raptm.getTingkatanUser(),
                raptm.getMasaBerlaku(),
                raptm.getJenisPengajuan(),
                raptm.getDeskripsi(),
                raptm.getAlasanPengajuan().replaceAll("\n"," "),
                raptm.getInfoTambahan(),
                raptm.getAttachment(),
                CommonHelper.concateTwoString("'", CommonHelper.checkValueString(raptm.getPuk1NIK())),
                raptm.getPuk1Nama(),
                raptm.getPuk1Jabatan(),
                raptm.getPuk1ApproveDate(),
                CommonHelper.concateTwoString("'", CommonHelper.checkValueString(raptm.getPuk2NIK())),
                raptm.getPuk2Nama(),
                raptm.getPuk2Jabatan(),
                raptm.getPuk2ApproveDate(),
                raptm.getStatus(),
                raptm.getPicProcess(),
                raptm.getCurrentStateDate(),
                raptm.getPicApprove(),
                raptm.getDoneDate(),
                raptm.getKategori()

        };
    }
}
