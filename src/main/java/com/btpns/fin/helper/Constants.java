package com.btpns.fin.helper;

import java.util.*;

public class Constants {
    public final static String ASIA_JAKARTA_TIME_ZONE = "Asia/Jakarta";
    public final static String CURR_STATUS_WAITING_PUK1 = "waiting_puk1";
    public final static String CURR_STATUS_WAITING_PUK2 = "waiting_puk2";
    public final static String CURR_STATUS_APPROVED = "approved";
    public final static String CURR_STATUS_REJECTED = "rejected";
    public final static String CURR_STATUS_PENDING = "pending";
    public final static String CURR_STATUS_PENDING_DES = "Pending";
    public final static String PUK1_STATUS_WAITING = "waiting";
    public final static String PUK2_STATUS_WAITING = "waiting";
    public final static String STATUS_PENDING_USER = "pending_user";
    public final static String STATUS_PENDING_PUK = "pending_puk";
    public final static String STATUS_WAITING = "waiting";
    public final static String STATUS_CONFIRMED = "confirmed";
    public final static String UPM = "upm";
    public final static String USER = "user";
    public final static String PUK1 = "puk1";
    public final static String PUK2 = "puk2";
    public final static String UPM_PROCESS = "upmProcess";
    public final static String UPM_CHECKER = "upmChecker";
    public final static String BTPNS = "Btpns";
    public final static String TYPE_SYS_PARAM_APLIKASI_SETUP_PARAM = "aplikasi-setupparameter";
    public final static String TYPE_SYS_PARAM_KATEGORI_PARAM = "kategori-parameter";
    public final static String TYPE_SYS_PARAM_ROLE = "role";
    public final static String KANTOR_PUSAT = "KANTOR PUSAT";
    public final static String BTPN_HO = "BTPN HO";
    public final static String HO = "HO";
    public final static String KCKFO = "KCKFO";
    public final static String HO_KCKFO = "HO & KCKFO";
    public final static String KC = "KC";
    public final static String KFO = "KFO";
    public final static String MMS = "MMS";
    public final static String TIKET = "tiket";
    public final static String APLIKASI = "aplikasi";
    public final static String KODE_APLIKASI_FUID = "A000000001";
    public final static String KODE_APLIKASI_SETUP_PARAM = "A000000002";
    public static final String KODE_APLIKASI_USER_ID = "A000000003";
    public final static String KODE_KATEGORI_PARAM = "CP00000000";
    public final static String KODE_ALASAN = "ALS0000000";
    public final static String KODE_TUJUAN = "T000000000";
    public final static String KODE_STATUS = "STS0000000";
    public final static String KODE_ROLE_MMS = "R000000001";
    public final static String KODE_ROLE_CABANG = "R000000002";
    public final static String KODE_ROLE_HO = "R000000003";
    public static final Set<String> TEMA_ROLE_CODE = new HashSet<>() {{
        add(KODE_ROLE_HO);
        add(KODE_ROLE_CABANG);
        add(KODE_ROLE_MMS);
    }};
    public final static String FILTER_CABANG_MMS = "all";
    public final static String FILTER_CABANG = "cabang";
    public final static String UPM_STATUS_WAITING_PUK1 = "waiting_puk1";
    public final static String UPM_STATUS_WAITING_PUK2 = "waiting_puk2";
    public final static String UPM_STATUS_REJECTED = "rejected";
    public final static String UPM_STATUS_NEW = "new_upm";
    public final static String UPM_STATUS_PENDING = "pending_upm";
    public final static String UPM_STATUS_INPROGRESS = "inprogress_upm";
    public final static String UPM_STATUS_VERIFICATION = "verification_upm";
    public final static String UPM_STATUS_DONE = "done_upm";
    public static final String UPM_STATUS_SEND_REMINDER = "send_reminder";
    public static final String REMINDER = "Reminder ";
    public final static String UPM_STATUS_ALL = "all";
    public final static String STATUS_DONE = "Done";
    public final static String STATUS_SEDANG_DIPROSES = "Sedang diproses";
    public final static String UPM_FILTER_TYPE_USERID = "userId";
    public final static String UPM_FILTER_TYPE_ALL = "all";
    public final static String UPM_FILTER_TYPE_SETUPPARAM = "setupParam";
    public final static String TYPE_MESSAGING_SMS = "0001";
    public final static String TYPE_MESSAGING_EMAIL = "0002";
    public final static String TYPE_MESSAGING_MBANK = "0003";
    public final static String RPAUPMIN = "RPAUPMIN";
    public final static String RPAUPMOTT = "RPAUPMOTT";
    public final static String UPM_ROLE_MAKER = "Maker";
    public final static String UPM_ROLE_CHECKER = "Checker";
    public final static String UPM_ROLE_ADMIN = "Admin";
    public final static String UPM_ROLE_VIEWER = "Viewer";
    public final static String UPM_ROLE_INQUIRY = "Inquiry";
    public final static String UPM_ROLE_MAKER_LOWERCASE = "maker";
    public final static String UPM_ROLE_CHECKER_LOWERCASE = "checker";
    public final static String UPM_ROLE_ADMIN_LOWERCASE = "admin";
    public final static String UPM_TICKET_TYPE_PICK = "pick";
    public final static String UPM_TICKET_TYPE_REJECT = "reject";
    public final static String UPM_TICKET_TYPE_ASSIGN = "assign";
    public final static String UPM_TICKET_TYPE_REASSIGN = "reassign";
    public final static String UPM_TICKET_TYPE_PROCESS = "process";
    public final static String UPM_TICKET_TYPE_PROCESS_PROSPERA = "process-prospera";
    public final static String UPM_TICKET_TYPE_DELETE_PROSPERA = "delete-prospera";
    public final static String UPM_TICKET_TYPE_RESUBMIT_DELETE_PROSPERA = "resubmit-delete-prospera";
    public final static String UPM_TICKET_TYPE_RESET_PASSWORD_PROSPERA = "reset-password-prospera";
    public final static String UPM_TICKET_TYPE_MUTASI_PROSPERA = "mutasi-prospera";
    public final static String UPM_TICKET_TYPE_ALTERNATE_DELEGASI_PROSPERA = "alternate-prospera";
    public final static String UPM_TICKET_TYPE_LIMIT_BWMP_PROSPERA = "limit-bwmp-prospera";
    public final static String UPM_TICKET_TYPE_PERUBAHAN_PROSPERA = "perubahan-prospera";
    public final static String UPM_TICKET_TYPE_PROCESS_EXPIRED_PROSPERA = "process-expired-prospera";
    public final static String ACTION_CHANGE_ROLE_PROSPERA = "change-role";
    public final static String ACTION_CHANGE_ORGANIZATION_PROSPERA = "change-organization";
    public final static String ACTION_INACTICVE_USER_PROSPERA = "inactive-user";
    public final static String ACTION_REGISTER_USER_PROSPERA = "register-user";
    public final static String ACTION_REACTIVATE_USER_PROSPERA = "reactivate-user";
    public final static String UPM_TICKET_TYPE_CHECKER_VERIFY = "checker-verify";
    public final static String TYPE_CONFIRM = "confirm";
    public final static String TYPE_APPROVE = "approve";
    public final static String TYPE_RESUBMIT = "resubmit";
    public static final String TYPE_SEND_REMINDER = "send-reminder";
    public final static String TIMELINE_STATUS_CREATE_TICKET = "Create Ticket";
    public final static String TIMELINE_STATUS_CONFIRM_TICKET = "Confirm Ticket";
    public final static String TIMELINE_STATUS_APPROVE_TICKET = "Approve Ticket";
    public final static String TIMELINE_STATUS_REJECT_TICKET = "Reject Ticket";
    public final static String TIMELINE_STATUS_RESUBMIT_TICKET = "Resubmit Ticket";
    public final static String TIMELINE_STATUS_INPROGRESS_UPM = "In Progress UPM";
    public final static String TIMELINE_STATUS_REASSIGN_UPM = "Reassign UPM";
    public final static String TIMELINE_STATUS_MANUAL_CONFIRM_UPM = "Manual Confirmation by UPM";
    public final static String TIMELINE_STATUS_DONE_BY_UPM_MAKER = "Done by UPM Maker";
    public final static String TIMELINE_STATUS_REJECT = "Reject";
    public final static String TIMELINE_STATUS_DONE = "Done";
    public final static String TIMELINE_STATUS_DELEGATION_ACTIVE = "Active";
    public final static String TIMELINE_STATUS_DELEGATION_INACTIVE = "Inactive";
    public final static String TIMELINE_STATUS_REASSIGN = "Reassign ";
    public static final String TIMELINE_STATUS_SEND_REMINDER = "Send Reminder ";
    public final static String TIMELINE_PIC_USER = "User: ";
    public final static String TIMELINE_PIC_PUK = "PUK: ";
    public final static String TIMELINE_PIC_PUK_DELEGATION = "PUK Delegasi: ";
    public final static String TIMELINE_PIC_UPM = "UPM: ";
    public final static String TIMELINE_PIC_UPM_APPROVE = "UPM Approve: ";
    public final static String DELETED_CONTENT_URL = "deletedContentUrl";
    public final static String CONTENT_URL = "contentUrl";
    public final static String CONTENT_FILEPATH = "contentFilePath";
    public final static String CONTENT_FILENAME = "contentFileName";
    public final static String CONTENT_TYPE = "contentType";
    public final static String USER_ID_MAINTENANCE = "User ID Maintenance";
    public final static String PARAMETER_MAINTENANCE = "Parameter Maintenance";
    public final static String KEY_UPLOAD_SPESIMEN = "AF00000024";
    public final static String TUJUAN_PENDAFTARAN_BARU = "pendaftaran_baru";
    public final static String TUJUAN_PENDAFTARAN_BARU_PROSPERA = "pendaftaran_baru_prospera";
    public final static String TUJUAN_ALTERNATE_DELEGASI = "alternate/delegasi";
    public final static String TUJUAN_ALTERNATE_DELEGASI_PROSPERA = "alternate/delegasi_prospera";
    public final static String TUJUAN_PENGHAPUSAN = "penghapusan";
    public final static String TUJUAN_PENGHAPUSAN_PROSPERA = "penghapusan_prospera";
    public final static String TUJUAN_RESET_PASSWORD_PROSPERA = "reset_password_prospera";
    public final static String TUJUAN_PERUBAHAN_PROSPERA = "perubahan_prospera";
    public final static String TUJUAN_UPLOAD_SPECIMEN = "specimen_digitus";
    public final static String ALASAN_KARYAWAN_BARU = "karyawan_baru";
    public final static String ALASAN_RANGKAP_JABATAN = "rangkap_jabatan";
    public final static String ALASAN_LAINNYA = "lainnya";
    public final static String ALASAN_RESIGN = "resign";
    public final static String ALASAN_PENGAJUAN_RESIGN_OPTIMA = "Simplifikasi Batal Join Optima Tanggal ";
    public final static String ALASAN_MUTASI_ROTASI_PROMOSI = "mutasi/rotasi/promosi";
    public final static String ALASAN_KEWENANGAN_LIMIT = "kewenangan_limit";
    public final static String TIPE_KARYAWAN_BARU_NON_FTE = "non-fte";
    public final static String TIPE_KARYAWAN_BARU_FTE = "fte";
    public final static String TIPE_KEWENANGAN_LIMIT_TRANSAKSI = "transaksi";
    public final static String TIPE_KEWENANGAN_LIMIT_BWMP = "bwmp";
    public final static String TIPE_LIMIT_TRANSAKSI_TUNAI = "Tunai";
    public final static String TIPE_LIMIT_TRANSAKSI_NONTUNAI = "Non-Tunai";
    public final static String STATUS_MASA_BERLAKU_TETAP = "Tetap";
    public final static String STATUS_MASA_BERLAKU_TANGGAL = "Tanggal";
    public final static String KODE_APLIKASI_EMAIL_GROUP = "AF00000022";
    public final static String KODE_APLIKASI_EMAIL = "AF00000001";
    public final static String KODE_APLIKASI_PROSPERA = "AF00000002";
    public final static String KODE_APLIKASI_EMAIL_KELUAR = "AF00000023";
    public final static String KODE_APLIKASI_USER_ID_DBO_RTGS = "AU00000001";
    public final static String KODE_APLIKASI_USER_ID_S4 = "AU00000002";
    public final static String KODE_APLIKASI_USER_ID_SPK = "AU00000003";
    public final static String KODE_APLIKASI_USER_ID_SLIK = "AU00000004";
    public final static String KODE_APLIKASI_USER_ID_CMS = "AU00000005";
    public final static String KODE_APLIKASI_USER_ID_TEPAT_MBANKING_INDIVIDU = "AU00000006";
    public final static String KODE_APLIKASI_USER_ID_TEPAT_MBANKING_CORP = "AU00000007";
    public final static String KODE_APLIKASI_USER_ID_TEPAT_BICAC = "AU00000009";
    public final static String KODE_APLIKASI_USER_ID_DIGITUS = "AU00000010";
    public final static String APP_DESC_S4 = "S4";
    public final static String APP_DESC_SSSS = "SSSS(S4)";
    public final static String OPERATION_DISTRIBUTION_HEAD = "OPERATION DISTRIBUTION HEAD";
    public final static String NETWORK_OPERATION_MANAGER = "NETWORK OPERATION MANAGER";
    public final static String OPERATION_SUPERVISOR = "OPERATION SUPERVISOR";
    public final static String BACK_OFFICE_KFO = "BACK OFFICE - KFO";
    public final static String TELLER_KFO = "TELLER - KFO";
    public static final Set<String> KFO_OCCUPATION = new HashSet<>() {{
        add(NETWORK_OPERATION_MANAGER);
        add(OPERATION_SUPERVISOR);
        add(BACK_OFFICE_KFO);
        add(TELLER_KFO);
    }};
    public final static String BRANCH_OPERATION_MANAGER = "BRANCH OPERATION MANAGER";
    public final static String BRANCH_OPERATION_SUPERVISOR = "BRANCH OPERATION SUPERVISOR";
    public final static String BACK_OFFICE = "BACK OFFICE";
    public static final Set<String> KC_OCCUPATION = new HashSet<>() {{
        add(BACK_OFFICE);
        add(BRANCH_OPERATION_SUPERVISOR);
        add(BRANCH_OPERATION_MANAGER);
    }};
    public static final String RETAIL_WHOLESALE_FUNDING_HEAD = "RETAIL & WHOLESALE FUNDING HEAD";
    public static final String TROPS_TRANSFER_SERVICE_MANAGER = "TROPS & TRANSFER SERVICES MANAGER";
    public static final String TIME_DEPOSIT_SERVICES_MANAGER = "TIME DEPOSIT SERVICES MANAGER";
    public static final String PAYMENT_SERVICES_MANAGER = "PAYMENT SERVICES MANAGER";
    public final static String HEAD = "HEAD";
    public final static String DIRECTOR = "DIRECTOR";
    public final static String PRESIDENT_DIRECTOR = "PRESIDENT DIRECTOR";
    public final static String DELEGATION_STATUS_ACTIVE = "Active";
    public final static String DELEGATION_STATUS_INACTIVE = "Inactive";
    public final static String DELEGATION_ACTION_DELEGATED_ACTIVE = "active";
    public final static String DELEGATION_ACTION_DELEGATED_INACTIVE = "inactive";
    public final static String REASSIGN_PUK_ACTION = "reassign_puk";
    public final static String REASSIGN_UPM_ACTION = "reassign_upm";
    public final static String REPORT_DELEGASI_FILE_NAME = "delegasi";
    public final static String REPORT_FILE_NAME = "report";
    public final static String REPORT_USER_ALIH_DAYA_FILE_NAME = "alihdaya";
    public final static String REPORT_MS_CABANG_FILE_NAME = "msCabang";
    public final static String REPORT_MS_MMS_FILE_NAME = "msMms";
    public final static String REPORT_PROSPERA_REVAMP_FILE_NAME = "prosperaRevamp";
    public final static String DATA_CORE_SYSTEM = "Data Core System";
    public final static String DATA_REPORT_DETAIL_UPM = "Data Report Detail UPM";
    public final static String DBORTGS_USER_ID_FILE_NAME = "dboRTGS";
    public final static String S4_USER_ID_FILE_NAME = "ssss(s4)";
    public final static String SLIK_USER_ID_FILE_NAME = "slik";
    public final static String SPK_USER_ID_FILE_NAME = "spk";
    public final static String DIGITUS_USER_ID_FILE_NAME = "digitus";
    public final static String TEPAT_MBANKING_CORPORATE_USER_ID_FILE_NAME = "tepatMbankingCorporate";
    public final static String TEPAT_MBANKING_INDIVIDU_USER_ID_FILE_NAME = "tepatMbankingIndividu";
    public final static String CMS_USER_ID_FILE_NAME = "cms";
    public final static String BI_CAC_USER_ID_FILE_NAME = "biCAC";
    public static final String UAR_FILE_NAME = "userAccessReview";
    public static final String REPORT_SUMMARY_UPM_FILE_NAME = "reportSummaryUPM";
    public static final String REPORT_TICKET_USER_INQUIRY_FILE_NAME = "reportTicketUserInquiry";
    public static final String TEMPLATE_BULK_USERID_FILE_NAME = "templateBulkUserId";
    public static final String REPORT_UPLOAD_USERID_FILE_NAME = "reportUploadUserId";
    public static final String REPORT_TICKET_USER_FILE_NAME = "reportTicketUser";
    public static final String EGLS = "EGLS";
    public static final String T24 = "T24";
    public static final String TMP_FOLDER = "/tmp/";
    public final static String CSV_EXTENSION = ".csv";
    public final static String CONTENT_TYPE_CSV = "text/csv";
    public final static String PDF_EXTENSION = ".pdf";
    public final static String XLSX_EXTENSION = ".xlsx";
    public final static String GZIP_EXTENSION = ".gz";
    public final static String CONTENT_TYPE_PDF = "application/pdf";
    public final static String CONTENT_TYPE_EXCEL = "application/vnd.ms-excel";
    public final static String CONTENT_TYPE_GZIP = "application/x-gzip";
    public final static String DETAIL_FUID_FILE_NAME = "detail_fuid";
    public final static String DETAIL_SETUP_PARAMETER_FILE_NAME = "setup_parameter";
    public final static String DETAIL_UAR_FILE_NAME = "detail_uar";
    public final static String TICKET_ID_COL_NAME = "Ticket ID";
    public final static String NIK_USER_COL_NAME = "NIK User";
    public final static String NAMA_USER_COL_NAME = "Nama User";
    public final static String KEWENANGAN_COL_NAME = "Kewenangan";
    public final static String JABATAN_USER_COL_NAME = "Jabatan User";
    public final static String UNIT_KERJA_COL_NAME = "Unit Kerja";
    public final static String TANGGAL_MULAI_COL_NAME = "Tanggal Mulai";
    public final static String TANGGAL_SELESAI_COL_NAME = "Tanggal Selesai";
    public final static String NIK_PENERIMA_TUGAS_COL_NAME = "NIK Penerima Tugas";
    public final static String NAMA_PENERIMA_TUGAS_COL_NAME = "Nama Penerima Tugas";
    public final static String JABATAN_PENERIMA_TUGAS_COL_NAME = "Jabatan Penerima Tugas";
    public final static String CREATE_DATE_COL_NAME = "Create Date";
    public final static String TANGGAL_EFEKTIF_COL_NAME = "Tanggal Efektif";
    public final static String NIK_COL_NAME = "NIK";
    public final static String NAMA_COL_NAME = "Nama";
    public final static String JABATAN_COL_NAME = "Jabatan";
    public final static String USER_ID_COL_NAME = "User ID";
    public final static String KODE_CABANG_COL_NAME = "Kode Cabang";
    public final static String NAMA_CABANG_COL_NAME = "Nama Cabang";
    public final static String LOCATION_COL_NAME = "Location";
    public final static String EMAIL_COL_NAME = "Email";
    public final static String TELEPON_COL_NAME = "Telepon";
    public final static String JENIS_APLIKASI_COL_NAME = "Jenis Aplikasi";
    public final static String JENIS_TINGKATAN_USER_COL_NAME = "Jenis Tingkatan User";
    public final static String MASA_BERLAKU_COL_NAME = "Masa Berlaku";
    public final static String JENIS_PENGAJUAN_COL_NAME = "Jenis Pengajuan";
    public final static String DESKRIPSI_ALASAN_COL_NAME = "Deskripsi Alasan";
    public final static String KETERANGAN_COL_NAME = "Keterangan";
    public final static String INFO_TAMBAHAN_COL_NAME = "Info Tambahan";
    public final static String STATUS_LAMPIRAN_COL_NAME = "Status Lampiran";
    public final static String PUK1_NIK_COL_NAME = "Atasan 1 NIK";
    public final static String PUK1_NAMA_COL_NAME = "Atasan 1 Nama";
    public final static String PUK1_JABATAN_COL_NAME = "Atasan 1 Jabatan";
    public final static String PUK1_TGL_APPROVE_COL_NAME = "Atasan 1 Tanggal Approve";
    public final static String PUK2_NIK_COL_NAME = "Atasan 2 NIK";
    public final static String PUK2_NAMA_COL_NAME = "Atasan 2 Nama";
    public final static String PUK2_JABATAN_COL_NAME = "Atasan 2 Jabatan";
    public final static String PUK2_TGL_APPROVE_COL_NAME = "Atasan 2 Tanggal Approve";
    public final static String STATUS_COL_NAME = "Status";
    public final static String PIC_PROCESS_COL_NAME = "PIC Process";
    public final static String TGL_PROCESS_COL_NAME = "Tanggal Process";
    public final static String PIC_APPROVE_COL_NAME = "PIC Approve";
    public final static String TGL_APPROVE_COL_NAME = "Tanggal Approve";
    public final static String KATEGORI_COL_NAME = "Kategori";
    public final static String NIK_VENDOR_COL_NAME = "NIK Vendor";
    public final static String NAMA_VENDOR_COL_NAME = "Nama Vendor";
    public final static String JABATAN_VENDOR_COL_NAME = "Jabatan Vendor";
    public final static String NIK_PUK_VENDOR_COL_NAME = "NIK PUK";
    public final static String NAMA_PUK_VENDOR_COL_NAME = "Nama PUK";
    public final static String ID_CABANG_COL_NAME = "ID Cabang";
    public final static String KODE_MMS_COL_NAME = "Kode MMS";
    public final static String NAMA_MMS_COL_NAME = "Nama MMS";
    public final static String ALAMAT_COL_NAME = "Alamat";
    public final static String KODE_KFO_COL_NAME = "Kode KFO";
    public final static String NAMA_KFO_COL_NAME = "Nama KFO";
    public final static String KODE_KCS_COL_NAME = "Kode KCS";
    public final static String NAMA_KCS_COL_NAME = "Nama KCS";
    public final static String NIK_PROSPERA_COL_NAME = "Nik Prospera";
    public final static String ROLE_NAME_COL_NAME = "Role Name";
    public final static String LOGIN_NAME_COL_NAME = "Login Name";
    public final static String KODE_KANTOR_COL_NAME = "Kode Kantor";
    public final static String NAMA_KANTOR_COL_NAME = "Nama Kantor";
    public final static String LIMIT_BWMP_COL_NAME = "Limit BWMP";
    public final static String KODE_PEMBINA_SENTRA_COL_NAME = "Kode Pembina Sentra";
    public final static String ID_USER_COL_NAME = "ID User";
    public final static String INSTITUSI_COL_NAME = "Institusi";
    public final static String GRUP_USER_COL_NAME = "Grup User";
    public final static String TANGGAL_AKTIF_COL_NAME = "Tanggal Aktif";
    public final static String TANGGAL_NONAKTIF_COL_NAME = "Tanggal Non Aktif";
    public final static String STATUS_USER_COL_NAME = "Status User";
    public final static String STATUS_LOGIN_COL_NAME = "Status Login";
    public static final String USERNAME_COL_NAME = "Username";
    public static final String FIRST_NAME_COL_NAME = "First Name";
    public static final String LAST_NAME_COL_NAME = "Last Name";
    public static final String ROLE_ID_COL_NAME = "Role ID";
    public static final String ACCOUNT_STATUS_COL_NAME = "Account Status";
    public static final String LAST_LOGON_COL_NAME = "Last Logon";
    public static final String SIGN_ON_ID_COL_NAME = "Sign On ID";
    public static final String DEPT_COL_NAME = "Dept.";
    public static final String NAMA_DEPARTEMEN_COL_NAME = "Nama Departemen";
    public static final String MENU_COL_NAME = "Menu";
    public static final String TGL_MULAI_PROFIL_COL_NAME = "Tgl Mulai Profil";
    public static final String TGL_BERAKHIR_PROFIL_COL_NAME = "Tgl Berakhir Profil";
    public static final String VALIDITAS_PWD_COL_NAME = "Validitas Pwd";
    public static final String TGL_LOGIN_TERAKHIR = "Tgl Login Terakhir";
    public static final String KONFIRMASI_AKSES_COL_NAME = "Konfirmasi Akses";
    public static final String TAHUN_COL_NAME = "Tahun";
    public static final String TRIWULAN_COL_NAME = "Triwulan";
    public static final String NO_COL_NAME = "No";
    public static final String INFO_TIKET_NAME = "Info Tiket";
    public static final String ACTIVITY_COL_NAME = "Activity";
    public static final String JUMLAH_COL_NAME = "Jumlah";
    public static final String GRAND_TOTAL_COL_NAME = "Grand Total";
    public static final String TOP_5_ACTIVITY_COL_NAME = "Top 5 Activity";
    public static final String NON_MMS_COL_NAME = "Non MMS";
    public final static String SEARCH_FLAG_TICKET_ID = "ticketId";
    public final static String SEARCH_FLAG_NIK = "nik";
    public static final String SEARCH_FLAG_USERNAME = "username";
    public final static String SEARCH_FLAG_NAME = "name";
    public static final String SEARCH_FLAG_NAMA_USER = "namaUser";
    public static final String SEARCH_FLAG_FIRST_NAME = "firstName";
    public static final String SEARCH_FLAG_LAST_NAME = "lastName";
    public final static String SEARCH_FLAG_BRANCH_ID = "branchId";
    public final static String SEARCH_FLAG_BRANCH_NAME = "branchName";
    public final static String SEARCH_FLAG_MMSCODE = "mmsCode";
    public final static String SEARCH_FLAG_MMSNAME = "mmsName";
    public final static String SEARCH_FLAG_LOGIN_NAME = "loginName";
    public final static String SEARCH_FLAG_PARAM_DETAIL_ID = "paramDetailId";
    public final static String SEARCH_FLAG_PARAM_DETAIL_DESC = "paramDetailDesc";
    public final static String SEARCH_FLAG_APLIKASI_FUID = "fuid";
    public final static String SEARCH_FLAG_APLIKASI_SETUP_PARAMETER = "setup-parameter";
    public final static String SEARCH_FLAG_EFFECTIVE_DT = "effectiveDT";
    public final static String SEARCH_FLAG_CREATED_DT = "createDT";
    public final static String SEARCH_FLAG_CURRENT_STATE_DT = "currentStateDT";
    public static final String SEARCH_FLAG_SIGN_ON_ID = "signOnID";
    public static final String SEARCH_FLAG_USER_ID = "userID";
    public final static String EFFECTIVE_DT_START = "effectiveDTStart";
    public final static String EFFECTIVE_DT_END = "effectiveDTEnd";
    public final static String CREATE_DT_START = "createDTStart";
    public final static String CREATE_DT_END = "createDTEnd";
    public final static String CURRENT_STATE_DT_START = "currentStateDTStart";
    public final static String CURRENT_STATE_DT_END = "currentStateDTEnd";
    public final static String STATUS_ACTIVE = "Active";
    public final static String STATUS_APPLICATION_ACTIVE = "Active";
    public final static String STATUS_APPLICATION_INACTIVE = "Inactive";
    public final static String STATUS_APPLICATION_ALL = "all";
    public final static String PARAM_PERIOD_DAILY = "daily";
    public final static String PARAM_PERIOD_WEEKLY = "weekly";
    public final static String PARAM_PERIOD_MONTHLY = "monthly";
    public final static String TYPE1_REGEX = "type1";
    public final static String TYPE2_REGEX = "type2";
    public final static String TYPE3_REGEX = "type3";
    public final static String TYPE4_REGEX = "type4";
    public final static String TYPE5_REGEX = "type5";
    public final static String COMMENT_STATUS_DELIVER = "deliver";
    public final static String COMMENT_STATUS_READ = "read";
    public final static String EMPTY = "";
    public final static String BATCH_NEW_HIRE = "batch-new-hire";
    public final static String BATCH_MUTATION = "batch-mutation";
    public final static String BATCH_RESIGN = "batch-resign";
    public final static String BATCH_RESIGN_OPTIMA = "batch-resign-optima";
    public final static String BATCH_NEW_USERID = "batch-new-userId";
    public final static String BATCH_DBO_RTGS = "batch-dbo-rtgs";
    public final static String INPUT_TYPE_BATCH = "batch";
    public final static String INPUT_TYPE_MANUAL = "manual";
    public final static String INPUT_TYPE_RESIGN = "resign";
    public final static String INPUT_TYPE_BULK = "bulk";
    public final static String INPUT_TYPE_PROSPERA = "prospera";
    public final static String DELETE = "delete";
    public final static String ADD = "add";
    public final static String EDIT = "edit";
    public final static Boolean TRUE_FLAG_BOOL = true;
    public final static Boolean FALSE_FLAG_BOOL = false;
    public final static Integer TRUE_FLAG_INT = 1;
    public final static Integer FALSE_FLAG_INT = 0;
    public final static String TRUE_FLAG_STR = "true";
    public final static String FALSE_FLAG_STR = "false";
    public final static String SUCCESS_STATE = "success";
    public final static String FAILED_STATE = "failed";
    public final static double NOL_TYPE_DOUBLE = 0;
    public final static String NOL = "0";
    public final static String STATUS_PERSONNEL_PROSPERA_ACTIVE = "PersonnelStatus-Active";
    public final static Integer STATUS_PERSONNEL_PROSPERA_ACTIVE_CODE = 152;
    public final static Integer STATUS_PERSONNEL_PROSPERA_INACTIVE_CODE = 153;
    public final static String PROSPERA_SUCCESS_RESPONSE_CODE = "00";
    public final static Integer LEVEL_ID_PROSPERA = 61;
    public static final String PREFIX_FU = "FU";
    public static final String PREFIX_SP = "SP";
    public final static String PREFIX_HO = "HO";
    public final static String PREFIX_CABANG = "K";
    public final static String PREFIX_MMS = "W";
    public final static String FAILED_SET_STATUS_PROSPERA_RESPONSE_DESC = "Status tidak dapat diubah sebab pengguna telah memiliki satu atau lebih nasabah yang didaftarkan";

    public final static String TYPE_DETAIL_FUID_GET = "get_ticket_detail_fuid";
    public final static String TYPE_DETAIL_SETUP_PARAMETER_GET = "get_ticket_detail_setup_parameter";
    public final static String TYPE_DETAIL_UAR_GET = "get_ticket_detail_uar";
    public final static String TYPE_DETAIL_FUID_DOWNLOAD = "download_ticket_detail_fuid";
    public final static String TYPE_DETAIL_SETUP_PARAMETER_DOWNLOAD = "download_ticket_detail_setup_parameter";
    public final static String TYPE_ANALYTIC_APPLICATION_MONTHLY = "analytic_application_monthly";
    public final static String TYPE_ANALYTIC_COUNT_UPM = "analytic_count_upm";
    public final static String TYPE_ANALYTIC_TRX_VOLUME_USER = "analytic_trx_volume_user";
    public final static String TYPE_UPM_MANAGEMENT_USER_ADD_EDIT = "upm_management_user_add_edit";
    public final static String TYPE_UPM_MANAGEMENT_USER_GET = "upm_management_user_get";
    public final static String TYPE_UPM_MANAGEMENT_USER_GET_LIST = "upm_management_user_get_list";
    public final static String TYPE_UPM_MANAGEMENT_USER_DELETE = "upm_management_user_delete";
    public final static String TYPE_ALIH_DAYA_MANAGEMENT_USER_ADD_EDIT = "alih_daya_management_user_add_edit";
    public final static String TYPE_ALIH_DAYA_MANAGEMENT_USER_GET = "alih_daya_management_user_get";
    public final static String TYPE_ALIH_DAYA_MANAGEMENT_USER_GET_LIST = "alih_daya_management_user_get_list";
    public final static String TYPE_ALIH_DAYA_MANAGEMENT_USER_DELETE = "alih_daya_management_user_delete";
    public static final String TYPE_ALIH_DAYA_MANAGEMENT_BULK_ADD_EDIT = "alih_daya_management_bulk_add_edit";
    public final static String TYPE_UPM_TICKET_GET_LIST = "upm_ticket_get_list";
    public final static String TYPE_UPM_TICKET_SET = "upm_ticket_set";
    public final static String TYPE_UPM_TICKET_REASSIGN_PUK = "upm_ticket_reassign_puk";
    public final static String TYPE_UPM_FUID_REQUEST = "fuid_request_upm";
    public final static String TYPE_UPM_SETUP_PARAMETER_REQUEST = "setup_parameter_request_upm";
    public final static String TYPE_SIMPLIFIKASI_BULK_USER_RESIGN_OPTIMA = "simplifikasi_bulk_user_resign_optima";
    public final static String TYPE_SIMPLIFIKASI_BULK_USERID = "simplifikasi_bulk_userid";
    public final static String TYPE_DELEGASI_DOWNLOAD = "get_download_delegasi";
    public final static String TYPE_USER_ALIH_DAYA_DOWNLOAD = "get_download_alihdaya";
    public final static String TYPE_MS_CABANG_DOWNLOAD = "get_download_ms_cabang";
    public final static String TYPE_MS_MMS_DOWNLOAD = "get_download_ms_mms";
    public final static String TYPE_MS_OFFICER_DOWNLOAD = "get_download_ms_officer";
    public final static String TYPE_BRANCH_MANAGEMENT_GET_LIST = "branch_management_get_list";
    public final static String TYPE_BRANCH_MANAGEMENT_GET = "branch_management_get";
    public final static String TYPE_BRANCH_MANAGEMENT_ADD_EDIT = "branch_management_add_edit";
    public final static String TYPE_BRANCH_MANAGEMENT_DELETE = "branch_management_delete";
    public final static String TYPE_MMS_MANAGEMENT_GET_LIST = "mms_management_get_list";
    public final static String TYPE_MMS_MANAGEMENT_GET = "mms_management_get";
    public final static String TYPE_MMS_MANAGEMENT_ADD_EDIT = "mms_management_add_edit";
    public final static String TYPE_MMS_MANAGEMENT_DELETE = "mms_management_delete";
    public final static String TYPE_SP_CATEGORY_MANAGEMENT_GET_LIST = "sp_category_management_get_list";
    public final static String TYPE_SP_CATEGORY_MANAGEMENT_GET = "sp_category_management_get";
    public final static String TYPE_SP_CATEGORY_MANAGEMENT_ADD_EDIT = "sp_category_management_add_edit";
    public final static String TYPE_SP_CATEGORY_MANAGEMENT_DELETE = "sp_category_management_delete";
    public final static String TYPE_TEMA_APPLICATON_MANAGEMENT_GET_LIST = "tema_application_management_get_list";
    public final static String TYPE_TEMA_APPLICATON_MANAGEMENT_GET = "tema_application_management_get";
    public final static String TYPE_TEMA_APPLICATON_MANAGEMENT_ADD_EDIT = "tema_application_management_add_edit";
    public final static String TYPE_FTE_USER_MANAGEMENT_GET_LIST = "fte_user_management_get_list";
    public final static String TYPE_FTE_USER_MANAGEMENT_GET = "fte_user_management_get";
    public final static String TYPE_GENERETE_ID = "generate_id";
    public final static String TYPE_NOTIFICATOIN_STATUS_GET = "get_notification_status";
    public final static String TYPE_NOTIFICATION_REMINDER_UPDATE = "update_notification_reminder";
    public final static String TYPE_FUID_REQUEST_PROSPERA = "fuid_request_prospera";
    public final static String TYPE_EXPIRED_FUID_GET_LIST = "expired_fuid_get_list";
    public final static String TYPE_EXPIRED_FUID_PROCESS_BATCH = "expired_fuid_process_batch";
    public final static String TYPE_MS_DBO_RTGS_MANAGEMENT_BATCH = "ms_dbo_rtgs_management_batch";
    public final static String TYPE_MS_DBO_RTGS_MANAGEMENT_GET_LIST = "ms_dbo_rtgs_management_get_list";
    public final static String TYPE_MS_DBO_RTGS_MANAGEMENT_GET = "ms_dbo_rtgs_management_get";
    public final static String TYPE_MS_DBO_RTGS_MANAGEMENT_ADD_EDIT = "ms_dbo_rtgs_management_add_edit";
    public final static String TYPE_MS_DBO_RTGS_MANAGEMENT_DELETE = "ms_dbo_rtgs_management_delete";
    public final static String TYPE_MS_DBO_RTGS_MANAGEMENT_DOWNLOAD = "ms_dbo_rtgs_management_download";
    public final static String TYPE_MS_SLIK_MANAGEMENT_BATCH = "ms_slik_management_batch";
    public final static String TYPE_MS_SLIK_MANAGEMENT_GET_LIST = "ms_slik_management_get_list";
    public final static String TYPE_MS_SLIK_MANAGEMENT_GET = "ms_slik_management_get";
    public final static String TYPE_MS_SLIK_MANAGEMENT_ADD_EDIT = "ms_slik_management_add_edit";
    public final static String TYPE_MS_SLIK_MANAGEMENT_DELETE = "ms_slik_management_delete";
    public final static String TYPE_MS_SLIK_MANAGEMENT_DOWNLOAD = "ms_slik_management_download";
    public final static String TYPE_MS_CMS_MANAGEMENT_BATCH = "ms_cms_management_batch";
    public final static String TYPE_MS_CMS_MANAGEMENT_GET_LIST = "ms_cms_management_get_list";
    public final static String TYPE_MS_CMS_MANAGEMENT_GET = "ms_cms_management_get";
    public final static String TYPE_MS_CMS_MANAGEMENT_ADD_EDIT = "ms_cms_management_add_edit";
    public final static String TYPE_MS_CMS_MANAGEMENT_DELETE = "ms_cms_management_delete";
    public static final String TYPE_MS_CMS_MANAGEMENT_DOWNLOAD = "ms_cms_management_download";
    public final static String TYPE_MS_S4_MANAGEMENT_BATCH = "ms_s4_management_batch";
    public static final String TYPE_MS_S4_MANAGEMENT_GET_LIST = "ms_s4_management_get_list";
    public static final String TYPE_MS_S4_MANAGEMENT_ADD_EDIT = "ms_s4_management_add_edit";
    public static final String TYPE_MS_S4_MANAGEMENT_GET = "ms_s4_management_get";
    public static final String TYPE_MS_S4_MANAGEMENT_DELETE = "ms_s4_management_delete";
    public static final String TYPE_MS_S4_MANAGEMENT_DOWNLOAD = "ms_s4_management_download";
    public final static String TYPE_MS_SPK_MANAGEMENT_BATCH = "ms_spk_management_batch";
    public static final String TYPE_MS_SPK_MANAGEMENT_GET_LIST = "ms_spk_management_get_list";
    public static final String TYPE_MS_SPK_MANAGEMENT_ADD_EDIT = "ms_spk_management_add_edit";
    public static final String TYPE_MS_SPK_MANAGEMENT_GET = "ms_spk_management_get";
    public static final String TYPE_MS_SPK_MANAGEMENT_DELETE = "ms_spk_management_delete";
    public static final String TYPE_MS_SPK_MANAGEMENT_DOWNLOAD = "ms_spk_management_download";
    public final static String TYPE_MS_TEPAT_MBANKING_INDIVIDU_BATCH = "ms_tepat_mbanking_individu_management_batch";
    public static final String TYPE_MS_TEPAT_MBANKING_INDIVIDU_GET_LIST = "ms_tepat_mbanking_individu_management_get_list";
    public static final String TYPE_MS_TEPAT_MBANKING_INDIVIDU_ADD_EDIT = "ms_tepat_mbanking_individu_management_add_edit";
    public static final String TYPE_MS_TEPAT_MBANKING_INDIVIDU_GET = "ms_tepat_mbanking_individu_management_get";
    public static final String TYPE_MS_TEPAT_MBANKING_INDIVIDU_DELETE = "ms_tepat_mbanking_individu_management_delete";
    public final static String TYPE_MS_TEPAT_MBANKING_INDIVIDU_MANAGEMENT_DOWNLOAD = "ms_tepat_mbanking_individu_management_download";
    public final static String TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_BATCH = "ms_tepat_mbanking_corporate_management_batch";
    public final static String TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_GET_LIST = "ms_tepat_mbanking_corporate_management_get_list";
    public final static String TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_GET = "ms_tepat_mbanking_corporate_management_get";
    public final static String TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_ADD_EDIT = "ms_tepat_mbanking_corporate_management_add_edit";
    public final static String TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_DELETE = "ms_tepat_mbanking_corporate_management_delete";
    public final static String TYPE_MS_TEPAT_MBANKING_CORPORATE_MANAGEMENT_DOWNLOAD = "ms_tepat_mbanking_corporate_management_download";
    public final static String TYPE_MS_DIGITUS_MANAGEMENT_BATCH = "ms_digitus_management_batch";
    public static final String TYPE_MS_DIGITUS_MANAGEMENT_GET_LIST = "ms_digitus_management_get_list";
    public static final String TYPE_MS_DIGITUS_MANAGEMENT_GET = "ms_digitus_management_get";
    public static final String TYPE_MS_DIGITUS_MANAGEMENT_ADD_EDIT = "ms_digitus_management_add_edit";
    public static final String TYPE_MS_DIGITUS_MANAGEMENT_DELETE = "ms_digitus_management_delete";
    public static final String TYPE_MS_DIGITUS_MANAGEMENT_DOWNLOAD = "ms_digitus_management_download";
    public static final String TYPE_MS_EGLS_MANAGEMENT_GET_LIST = "ms_egls_management_get_list";
    public static final String TYPE_MS_EGLS_MANAGEMENT_DOWNLOAD = "ms_egls_management_download";
    public static final String TYPE_MS_T24_MANAGEMENT_GET_LIST = "ms_t24_management_get_list";
    public static final String TYPE_MS_T24_MANAGEMENT_DOWNLOAD = "ms_t24_management_download";
    public final static String TYPE_MS_BI_CAC_MANAGEMENT_BATCH = "ms_bi_cac_management_batch";
    public static final String TYPE_MS_BI_CAC_MANAGEMENT_GET_LIST = "ms_bi_cac_management_get_list";
    public static final String TYPE_MS_BI_CAC_MANAGEMENT_ADD_EDIT = "ms_bi_cac_management_add_edit";
    public static final String TYPE_MS_BI_CAC_MANAGEMENT_GET = "ms_bi_cac_management_get";
    public static final String TYPE_MS_BI_CAC_MANAGEMENT_DELETE = "ms_bi_cac_management_delete";
    public static final String TYPE_MS_BI_CAC_MANAGEMENT_DOWNLOAD = "ms_bi_cac_management_download";
    public static final String TYPE_MS_CUSTOM_USERID_MANAGEMENT_GET_LIST = "ms_custom_userid_management_get_list";
    public static final String TYPE_MS_CUSTOM_USERID_MANAGEMENT_GET = "ms_custom_userid_management_get";
    public static final String TYPE_MS_CUSTOM_USERID_MANAGEMENT_ADD_EDIT = "ms_custom_userid_management_add_edit";
    public static final String TYPE_MS_CUSTOM_USERID_MANAGEMENT_DELETE = "ms_custom_userid_management_delete";
    public static final String TYPE_MS_CUSTOM_USERID_MANAGEMENT_BATCH = "ms_custom_userid_management_batch";
    public static final String TYPE_MS_CUSTOM_USERID_MANAGEMENT_DOWNLOAD = "ms_custom_userid_management_download";
    public static final String TYPE_MS_USERID_OWNERSHIP_MANAGEMENT_GET_LIST = "ms_userid_ownership_management_get_list";
    public static final String TYPE_MS_USERID_OWNERSHIP_GET_DETAIL = "ms_userid_ownership_get_detail";
    public static final String TYPE_USER_ID_APPLICATION_UPM_GET_LIST = "user_id_application_upm_get_list";
    public static final String TYPE_USER_ID_APPLICATION_UPM_GET = "user_id_application_upm_get";
    public static final String TYPE_USER_ID_APPLICATION_UPM_ADD_EDIT = "user_id_application_upm_add_edit";
    public static final String TYPE_USER_ID_APPLICATION_GET_LIST = "user_id_application_get_list";
    public static final String TYPE_UAR_GET_LIST = "uar_management_get_list";
    public static final String TYPE_UAR_DOWNLOAD = "uar_management_download";
    public static final String TYPE_UAR_USER_CONFIRM_GET_LIST = "uar_user_confirmation_get_list";
    public static final String TYPE_UAR_USER_CONFIRM = "uar_user_confirm";
    public static final String TYPE_UAR_PUK_CONFIRM_GET_LIST = "uar_puk_confirmation_get_list";
    public static final String TYPE_UAR_UPM_PROCESS = "uar_upm_process";
    public static final String TYPE_UAR_UPM_SEND_REMINDER = "uar_upm_send_reminder";
    public static final String TYPE_UAR_UPM_SUMMARY_DOWNLOAD = "type_uar_upm_summary_download";
    public static final String TYPE_DETAIL_UAR_DOWNLOAD = "type_detail_uar_download";
    public static final String TYPE_UAR_UPM_SUMMARY_GET_LIST = "type_uar_upm_summary_get_list";
    public static final String TYPE_POSSIBLE_USERS_FOR_DELEGATION_GET = "possible_users_for_delegation_get";
    public static final String TYPE_TICKET_TIMELINE_GET = "ticket_timeline_get";
    public static final String TYPE_REPORT_SUMMARY_UPM_GET = "report_summary_upm_get";
    public static final String TYPE_REPORT_SUMMARY_UPM_DOWNLOAD = "report_summary_upm_download";
    public static final String TYPE_APPROVAL_KEWENANGAN_LIMIT_GET_LIST = "approval_kewenangan_limit_get_list";
    public static final String TYPE_APPROVAL_KEWENANGAN_LIMIT_GET = "approval_kewenangan_limit_get";
    public static final String TYPE_APPROVAL_KEWENANGAN_LIMIT_ADD_EDIT = "approval_kewenangan_limit_add_edit";
    public static final String TYPE_APPROVAL_KEWENANGAN_LIMIT_DELETE = "approval_kewenangan_limit_delete";
    public static final String TYPE_PROSPERA_ROLE_MANAGEMENT_GET_LIST = "prospera_role_management_get_list";
    public static final String TYPE_PROSPERA_ROLE_MANAGEMENT_GET = "prospera_role_management_get";
    public static final String TYPE_PROSPERA_ROLE_MANAGEMENT_ADD_EDIT = "prospera_role_management_add_edit";
    public static final String TYPE_ACCESS_ROLE_MANAGEMENT_GET_LIST = "access_role_management_get_list";
    public static final String TYPE_TICKET_USER_INQUIRY_GET_LIST = "ticket_user_inquiry_get_list";
    public static final String TYPE_TICKET_USER_INQUIRY_DOWNLOAD = "ticket_user_inquiry_download";
    public static final String TYPE_TEMPLATE_BULK_USERID_DOWNLOAD = "template_bulk_userid_download";
    public static final String TYPE_MS_FAQ_MANAGEMENT_GET_LIST = "ms_faq_management_get_list";
    public static final String TYPE_MS_FAQ_MANAGEMENT_GET = "ms_faq_management_get";
    public static final String TYPE_MS_FAQ_MANAGEMENT_ADD_EDIT = "ms_faq_management_add_edit";
    public static final String TYPE_MS_FAQ_MANAGEMENT_DELETE = "ms_faq_management_delete";
    public static final String TYPE_USER_TICKET_GET_LIST = "user_ticket_get_list";
    public static final String TYPE_USER_TICKET_DOWNLOAD = "user_ticket_download";
    public static final String TYPE_UPLOAD_USERID_GET_LIST = "upload_userid_get_list";
    public static final String TYPE_FILTER_STATUS_GET_LIST = "filter_status_get_list";
    public final static Integer PROSPERA_ROLE_CODE_CO = 24;
    public final static String COMMUNITY_OFFICER = "COMMUNITY OFFICER";
    public final static String SENIOR_COMMUNITY_OFFICER = "SENIOR COMMUNITY OFFICER";
    public final static String BUSINESS_MANAGER = "BUSINESS MANAGER";
    public final static String SENIOR_BUSINESS_MANAGER = "SENIOR BUSINESS MANAGER";
    public final static String BUSINESS_COACH = "BUSINESS COACH";
    public final static String SENIOR_BUSINESS_COACH = "SENIOR BUSINESS COACH";
    public final static List<String> OCCUPATION_USER_MMS = new ArrayList<>() {{
        add(COMMUNITY_OFFICER);
        add(SENIOR_COMMUNITY_OFFICER);
        add(BUSINESS_MANAGER);
        add(SENIOR_BUSINESS_MANAGER);
        add(BUSINESS_COACH);
        add(SENIOR_BUSINESS_COACH);
    }};

    public final static String TYPE_REPORT_PRODUCTIVITY_GET_LIST = "report_productivity_get_list";

    public final static String EAST = "east";
    public final static String SOUTH = "south";
    public final static String NORTH = "north";
    public final static String WEST = "west";

    public static final Set<String> ENV_PROD = new HashSet<>() {{
        add(EAST);
        add(SOUTH);
        add(NORTH);
        add(WEST);
    }};

    public static final Set<String> TYPE_PROCESS_PROSPERA = new HashSet<>() {{
        add(UPM_TICKET_TYPE_PROCESS_PROSPERA);
        add(UPM_TICKET_TYPE_DELETE_PROSPERA);
        add(UPM_TICKET_TYPE_RESUBMIT_DELETE_PROSPERA);
        add(UPM_TICKET_TYPE_RESET_PASSWORD_PROSPERA);
        add(UPM_TICKET_TYPE_MUTASI_PROSPERA);
        add(UPM_TICKET_TYPE_ALTERNATE_DELEGASI_PROSPERA);
        add(UPM_TICKET_TYPE_LIMIT_BWMP_PROSPERA);
        add(UPM_TICKET_TYPE_PERUBAHAN_PROSPERA);
        add(UPM_TICKET_TYPE_PROCESS_EXPIRED_PROSPERA);
    }};

    public final static String TYPE_SPESIMEN_DOWNLOAD = "get_download_spesimen";
    public final static String TYPE_REPORT_PERMOHONAN_PDF_DOWNLOAD = "get_download_report_permohonan_pdf";
    public final static String TYPE_REPORT_PER_APLIKASI_DOWNLOAD = "get_download_report_per_aplikasi";
    public final static String TYPE_REPORT_PER_TIKET_DOWNLOAD = "get_download_report_per_tiket";
    public final static String TYPE_REPORT_PER_APLIKASI = "type_report_per_aplikasi";
    public final static String TYPE_REPORT_PER_TIKET = "type_report_per_tiket";
    public final static String TYPE_REPORT_UAR_UPM = "type_report_uar_upm";
    public final static String TYPE_REPORT_UAR_USER = "type_report_uar_user";
    public final static String TYPE_REPORT_UAR_UPM_DOWNLOAD = "type_report_uar_upm_download";
    public final static String TYPE_MS_HOLIDAY_GET_LIST = "holiday_management_get_list";
    public final static String TYPE_MS_HOLIDAY_GET = "holiday_management_get";
    public final static String TYPE_MS_HOLIDAY_ADD_EDIT = "holiday_management_add_edit";
    public final static String TYPE_MS_HOLIDAY_DELETE = "holiday_management_delete";
    public static final String TYPE_USER_DETAILS_PERMISSION_INQUIRY = "user_details_and_permission_inquiry";
    public final static String TYPE_MS_SOP_GET = "ms_sop_get";
    public final static String TYPE_MS_SOP_EDIT = "ms_sop_edit";
    public final static String TYPE_DIRECTOR_GET_LIST = "director_management_get_list";
    public final static String TYPE_DIRECTOR_GET = "director_management_get";
    public final static String TYPE_DIRECTOR_ADD_EDIT = "director_management_add_edit";
    public final static String TYPE_DIRECTOR_DELETE = "director_management_delete";
    public static final String TYPE_UAR_REMINDER = "uar-reminder";
    public static final String TYPE_MONTHLY_REMINDER = "monthly-reminder";
    public final static String KEY_IS_PUK_DIRECTOR = "isPukDirector";
    public final static String KEY_EMAIL_PUK_DIRECTOR = "emailPukDirector";
    public static final String RUPIAH_SYMBOL = "Rp ";
    public static final String FILTER_ALL = "all";
    public final static String PREFFIX_TICKET_FUID_UPM_MANUAL = "FUM";
    public final static String PREFFIX_TICKET_FUID_RESIGN_OPTIMA = "FUR";
    public final static String PREFFIX_TICKET_FUID_SIMPLIFIKASI = "FUS";
    public final static String PREFFIX_TICKET_FUID_PROSPERA = "FUP";
    public final static String PREFFIX_TICKET_FUID = "FU";
    public final static String PREFFIX_TICKET_SP = "SP";
    public final static String PREFFIX_TICKET_UAR = "UAR";
    public final static String PREFFIX_EMAIL_BTPNS = "@mail.btpnsyariah.com";
    public final static String TYPE_MS_OFFICER_MANAGEMENT_GET_LIST = "ms_officer_management_get_list";
    public final static String OFFICE_NAME_KANTOR_PUSAT = "Kantor Pusat";
    public final static String PROVINCE_NAME_DKI_JAKARTA = "DKI Jakarta";
    public final static String KODE_KANTOR_PUSAT = "0001";
    public final static String HEAD_OFFICE = "Head Office";
    public final static String KC_KFO = "KC & KFO";

    public static final String OFFICE_NAME_KEY_MAP = "officeName";
    public static final String PROVINCE_KEY_MAP = "province";

    public static final Integer OFFICER_STATUS_ACTIVE_CODE = 1;
    public static final Integer OFFICER_STATUS_INACTIVE_CODE = 2;
    public final static String CO = "CO";
    public final static String BM = "BM";
    public final static String ALTERNATE_BM = "_BM_Alternate";
    public final static String B_M_Coord_ = "B_M_Coord_";
    public final static String BC = "BC";
    public final static String SDH = "SDH";

    public static final Set<String> WHITELIST_ROLE_KEWENANGAN_LIMIT_PROSPERA = new HashSet<>() {{
        add(BM);
        add(ALTERNATE_BM);
        add(B_M_Coord_);
        add(BC);
        add(SDH);
    }};

    public final static String USER_HO_VIEW = "User HO View";
    public final static String CS_BO_FD = "CS KC/ KFO (BO - FD)";
    public final static String CS_TELLER = "CS KC/ KFO (Teller)";
    public final static String SCS_BOM = "SCS/ BOM - FD";

    public static final Set<String> WHITELIST_ROLE_EXPIRED_TICKET_PROSPERA_HOKCKFO = new HashSet<>() {{
        add(USER_HO_VIEW);
        add(CS_BO_FD);
        add(CS_TELLER);
        add(SCS_BOM);
    }};

    public static final String INFORMATION_TECHNOLOGY = "INFORMATION TECHNOLOGY";
    public static final String HUMAN_CAPITAL = "HUMAN CAPITAL";
    public static final String BUSINESS_DEVELOPMENT = "BUSINESS DEVELOPMENT";
    public static final String FUNDING = "FUNDING";
    public static final String WHOLESALE = "WHOLESALE";
    public static final String FILTER_CODE_ALL = "-1";
    public static final String NOTIFICATION_MESSAGE_TYPE = "emailNotification";
    public static final String FORM_FUID = "fu";
    public static final String FORM_SETUP_PARAMETER = "sp";
    public static final String PREFIX_BNKBNK = "BNKBNK";
    public static final String PREFIX_UAR = "UAR";
    public static final String STATUS_PROCESSED = "processed";
    public static final String STATUS_REMINDED = "reminded";
    public static final String STATUS_DELETED = "deleted";
    public static final String UAR_CONFIRMATION_ACTIVE = "Aktif";
    public static final String UAR_CONFIRMATION_DELETE = "Hapus";
    public static final Set<String> PERIOD_QUARTERS = new HashSet<>() {{
        add("Q1");
        add("Q2");
        add("Q3");
        add("Q4");
    }};
    public static final String KEY_USER = "user";
    public static final String KEY_PUK = "puk";
    public static final String AKTIF = "Aktif";
    public static final String TIDAK_AKTIF = "Tidak Aktif";
    public static final String IYA = "Iya";
    public static final String TIDAK = "Tidak";
    public static final String HAPUS = "Hapus";
    public static final String TIDAK_HAPUS = "Tidak Hapus";
    public static final String SCHEDULER_STATUS_START = "start";
    public static final String SCHEDULER_STATUS_END = "end";
    public static final String SCHEDULER_RETRY_EMAIL = "schedulerRetryEmail";
    public static final String SCHEDULER_UAR_REMINDER = "schedulerUARReminder";
    public static final String SCHEDULER_MONTHLY_MONITORING_REMINDER = "schedulerMonthlyMonitoringReminder";
    public static final String SCHEDULER_MONTHLY_SEND_DATA_CORE_TO_UPM = "schedulerMonthlySendDataCoreToUPM";
    public static final String SCHEDULER_SEND_REMINDER_EMAIL_TICKET_WAITING_APPROVAL_PUK = "schedulerSendReminderEmailTicketWaitingApprovalPUK";
    public static final String EMAIL_BININPUT01 = "<EMAIL>";
    public static final String EMAIL_BINUSER01 = "<EMAIL>";
    public static final String EMAIL_BINPUK01 = "<EMAIL>";
    public static final String EMAIL_BINPUK02 = "<EMAIL>";
    public static final String EMAIL_BINPUK03 = "<EMAIL>";
    public static final String EMAIL_BINPUK04 = "<EMAIL>";
    public static final String EMAIL_BINPUK05 = "<EMAIL>";
    public static final String EMAIL_BINPUK06 = "<EMAIL>";
    public static final String EMAIL_BINUPMINPUT01 = "<EMAIL>";
    public static final String EMAIL_BINUPMINPUT02 = "<EMAIL>";
    public static final String EMAIL_BINUPMCHECKER01 = "<EMAIL>";
    public static final String EMAIL_BINUPMCHECKER02 = "<EMAIL>";
    public final static List<String> EMAIL_DUMMY_USER_TEMA = new ArrayList<>() {{
        add(EMAIL_BININPUT01);
        add(EMAIL_BINUSER01);
        add(EMAIL_BINPUK01);
        add(EMAIL_BINPUK02);
        add(EMAIL_BINPUK03);
        add(EMAIL_BINPUK04);
        add(EMAIL_BINPUK05);
        add(EMAIL_BINPUK06);
        add(EMAIL_BINUPMINPUT01);
        add(EMAIL_BINUPMINPUT02);
        add(EMAIL_BINUPMCHECKER01);
        add(EMAIL_BINUPMCHECKER02);
    }};

    public final static Map<String, String> STATUS_PERMOHONAN_MAP = new HashMap<String, String>() {{
        put(CURR_STATUS_WAITING_PUK1, CURR_STATUS_WAITING_PUK1_DESC);
        put(CURR_STATUS_WAITING_PUK2, CURR_STATUS_WAITING_PUK2_DESC);
        put(CURR_STATUS_APPROVED, CURR_STATUS_APPROVED_DESC);
        put(CURR_STATUS_PENDING, CURR_STATUS_PENDING_DESC);
        put(UPM_STATUS_INPROGRESS, CURR_STATUS_INPROGRESS_DESC);
        put(UPM_STATUS_VERIFICATION, CURR_STATUS_VERIFICATION_DESC);
        put(UPM_STATUS_DONE, CURR_STATUS_DONE_DESC);
        put(CURR_STATUS_REJECTED, CURR_STATUS_REJECTED_DESC);
    }};
    public static final String CURR_STATUS_WAITING_PUK1_DESC = "Menunggu Persetujuan PUK 1";
    public static final String CURR_STATUS_WAITING_PUK2_DESC = "Menunggu Persetujuan PUK 2";
    public static final String CURR_STATUS_APPROVED_DESC = "Disetujui PUK";
    public static final String CURR_STATUS_PENDING_DESC = "Pending";
    public static final String CURR_STATUS_INPROGRESS_DESC = "Proses UPM";
    public static final String CURR_STATUS_VERIFICATION_DESC = "Verifikasi UPM";
    public static final String CURR_STATUS_DONE_DESC = "Selesai";
    public static final String CURR_STATUS_REJECTED_DESC = "Ditolak";
    public static final String CURR_STATUS_PUK1_DESC = "Persetujuan PUK 1";
    public static final String CURR_STATUS_PUK2_DESC = "Persetujuan PUK 2";
    public static final String CURR_STATUS_REJECTED_PUK1_DESC = "Ditolak PUK 1";
    public static final String CURR_STATUS_REJECTED_PUK2_DESC = "Ditolak PUK 2";
    public static final String CURR_STATUS_REJECTED_MAKER_DESC = "Ditolak UPM Maker";
    public static final String CURR_STATUS_REJECTED_CHECKER_DESC = "Ditolak UPM Checker";
    public static final String PSW_TEMPALTE_UPM = "Btpnsyariah.1";
    public static final String IT_SUPPORT_URL = "http://itsupport.btpnsyariah.com";
    public static final String IT_HELPDESK_EMAIL = "<EMAIL>";
    public static final String ALL_WHITELIST = "all";
    public static final String MONTHLY_REMINDER_MESSAGE = "Saatnya melakukan Monitoring Bulanan";
    public static final String FUR_UPM_NOTES = "Penghapusan sudah dilakukan";
    public static final String FUB_UPM_NOTES = "Pengajuan sudah diproses";
    public static final String SLA = "SLA";
    public static final String VOLUME_TRX_UPM_3_BULAN_TERAKHIR_TITLE_CHART = "Volume Trx UPM 3 Bulan Terakhir";
    public static final String TOP_5_PROSES_UPM_BY_LOKASI_KERJA_TITLE_CHART = "Top 5 Proses Upm By Lokasi Kerja";
    public static final String DATA_NIK_TEMPLATE = "BINUSER01";
    public static final String DATA_NAMA_TEMPLATE = "BINUSER01";
    public static final String DATA_JABATAN_TEMPLATE = "IT";
    public static final String DATA_KODE_CABANG_TEMPLATE = "0001";
    public static final String DATA_NAMA_CABANG_TEMPLATE = "KANTOR PUSAT";
    public static final String DATA_JENIS_APLIKASI_TEMPLATE = "Prospera / Terra / Agendaku (sesuai inputan data excel)";
    public static final String DATA_MASA_BERLAKU_TEMPLATE = "Tetap atau DD-MM-YYYY";
    public static final String DATA_JENIS_PENGAJUAN_TEMPLATE = "Pendaftaran Baru (sesuai inputan data excel)";
    public static final String DATA_PIC_PROCESS_TEMPLATE = "BINUPMINPUT01";
    public static final String DATA_PIC_APPROVE_TEMPLATE = "BINUPMCHECKER01";
    public static final String DATA_INFO_TIKET_TEMPLATE = "Pengajuan Bulk Upload terkait tiket FUXXXXXXX";
}