package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsCenterCodeOfficerProspera;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IMsCenterCodeOfficerProsperaRepository extends JpaRepository<MsCenterCodeOfficerProspera,String> {
    @Query(value = "SELECT cco.generatedCenterCodeOfficer FROM MsCenterCodeOfficerProspera cco with(nolock) where cco.cabangId = :cabangId ORDER BY cco.CreateDatetime DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastCenterCodeOfficer(@Param("cabangId") String cabangId);
}
