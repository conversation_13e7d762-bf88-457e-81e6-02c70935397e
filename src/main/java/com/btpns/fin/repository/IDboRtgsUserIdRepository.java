package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsDboRTGS;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

@Repository
public interface IDboRtgsUserIdRepository extends JpaRepository<MsDboRTGS, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM MsDboRTGS", nativeQuery = true)
    int deleteAllMsDboRTGS();

    @Modifying
    @Query(value = "DELETE FROM MsDboRTGS WHERE NIK = :nik", nativeQuery = true)
    int deleteMsDboRTGSByNik(String nik);

    @Query(value = "SELECT * FROM MsDboRTGS mdr with(nolock) ORDER BY mdr.Kewenangan ASC, mdr.NamaUser ASC ", nativeQuery = true)
    Page<MsDboRTGS> getListMsDboRTGS(Pageable pageable);

    @Query(value = "SELECT * FROM MsDboRTGS mdr with(nolock) Where mdr.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsDboRTGS> findAllByNikUser(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsDboRTGS mdr with(nolock) Where mdr.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsDboRTGS> findAllByNameUser(String searchData, Pageable pageable);

    @Query(value = "SELECT mdr.* FROM MsDboRTGS mdr with(nolock) WHERE mdr.NIK = :nik", nativeQuery = true)
    MsDboRTGS findByNikUser(String nik);
}
