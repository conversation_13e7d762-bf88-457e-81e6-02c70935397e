package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsSystemParam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface IMsSystemParamRepository extends JpaRepository<MsSystemParam,String> {
    @Query(value = "SELECT * FROM MsSystemParam msp with(nolock) where msp.paramId = :paramId", nativeQuery = true)
    public MsSystemParam getMsSystemParam(@Param("paramId") String paramId);
}
