package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsTepatMBankingCorporate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

@Repository
public interface ITepatMBankingCorporateUserIdRepository extends JpaRepository<MsTepatMBankingCorporate, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM MsTepatMBankingCorporate", nativeQuery = true)
    int deleteAllMssaveBatchMsMBankingCorporate();

    @Modifying
    @Query(value = "DELETE FROM MsTepatMBankingCorporate WHERE NIK = :nik", nativeQuery = true)
    int deleteMsTepatMBankingCorporateByNik(String nik);

    @Query(value = "SELECT * FROM MsTepatMBankingCorporate mtmc with(nolock) ORDER BY mtmc.Kewenangan ASC, mtmc.NamaUser ASC ", nativeQuery = true)
    Page<MsTepatMBankingCorporate> getListMsTepatMBankingCorporate(Pageable pageable);

    @Query(value = "SELECT * FROM MsTepatMBankingCorporate mtmc with(nolock) Where mtmc.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsTepatMBankingCorporate> findAllByNikUser(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsTepatMBankingCorporate mtmc with(nolock) Where mtmc.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsTepatMBankingCorporate> findAllByNameUser(String searchData, Pageable pageable);

    @Query(value = "SELECT mtmc.* FROM MsTepatMBankingCorporate mtmc with(nolock) WHERE mtmc.NIK = :nik", nativeQuery = true)
    MsTepatMBankingCorporate findByNikUser(String nik);
}
