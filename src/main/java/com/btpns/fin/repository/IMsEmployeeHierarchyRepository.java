package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsEmployeeHierarchy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface IMsEmployeeHierarchyRepository extends JpaRepository<MsEmployeeHierarchy, String> {
    @Query(value = "SELECT * FROM MsEmployeeHierarchy mh with(nolock) where mh.nik = :nik", nativeQuery = true)
    public MsEmployeeHierarchy getMsEmployeeHierarchyByNik(@Param("nik") String nik);

    @Query(value = "SELECT * FROM MsEmployeeHierarchy mh with(nolock) ORDER BY mh.OccupationDesc ASC, mh.FullName ASC ", nativeQuery = true)
    Page<MsEmployeeHierarchy> getListEmployee(Pageable pageable);

    @Query(value = "SELECT * FROM MsEmployeeHierarchy mh with(nolock) Where mh.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsEmployeeHierarchy> findAllByNikEmployee(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsEmployeeHierarchy mh with(nolock) Where mh.FullName LIKE %:searchData%", nativeQuery = true)
    Page<MsEmployeeHierarchy> findAllByNameEmployee(String searchData, Pageable pageable);
}