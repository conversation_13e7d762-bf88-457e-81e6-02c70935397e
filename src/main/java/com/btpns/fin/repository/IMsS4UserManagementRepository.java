package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsS4;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.Optional;

public interface IMsS4UserManagementRepository extends JpaRepository<MsS4, BigInteger> {
    Optional<MsS4> findByNik(String nik);

    @Modifying
    @Query(value = "DELETE FROM MsS4 WHERE NIK = :nik", nativeQuery = true)
    public int deleteMsS4User(@Param("nik") String nik);

    @Query(value = "SELECT * FROM MsS4 ms with(nolock) Where ms.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsS4> findAllByNik(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsS4 ms with(nolock) Where ms.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsS4> findAllByNamaUser(String searchData, Pageable pageable);
}
