package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxFuidApproval;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.List;
import java.util.Optional;

public interface ITrxFuidApprovalRepository extends JpaRepository<TrxFuidApproval, BigInteger> {
    @Query(value = "SELECT * FROM TrxFuidApproval tfa with(nolock) WHERE tfa.ticketId = :ticketId", nativeQuery = true)
    public Optional<TrxFuidApproval> getTrxFuidApprovalByTicketId(@Param("ticketId") String ticketId);

    @Modifying
    @Query(value = "DELETE FROM TrxFuidApproval WHERE ticketId = :ticketId", nativeQuery = true)
    int deleteTrxFuidRequestByTicketId(@Param("ticketId") String ticketId);

    @Modifying
    @Query(value = "UPDATE TrxFuidApproval set puk1NIK = :puk1NIK, puk1DelegationId = :puk1DelegationId WHERE ticketId = :ticketId", nativeQuery = true)
    public int updateTrxFuidApprovalPUK1ByTicketId(@Param("ticketId") String ticketId, @Param("puk1NIK") String puk1NIK, @Param("puk1DelegationId") String puk1DelegationId);

    @Modifying
    @Query(value = "UPDATE TrxFuidApproval set puk2NIK = :puk2NIK, puk2DelegationId = :puk2DelegationId WHERE ticketId = :ticketId", nativeQuery = true)
    public int updateTrxFuidApprovalPUK2ByTicketId(@Param("ticketId") String ticketId, @Param("puk2NIK") String puk2NIK, @Param("puk2DelegationId") String puk2DelegationId);

    @Query(value = "SELECT * FROM TrxFuidApproval tfa with(nolock) WHERE tfa.ticketId = :ticketId", nativeQuery = true)
    TrxFuidApproval findByTicketId(@Param("ticketId") String ticketId);

    @Query(value = "SELECT * FROM TrxFuidApproval tfa with(nolock) WHERE CurrentState = 'waiting_puk2' and PUK1NIK = :nikRequester", nativeQuery = true)
    List<TrxFuidApproval> getPendingPUK2ByNik(String nikRequester);
}