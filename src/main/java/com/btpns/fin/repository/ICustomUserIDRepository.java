package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsCustomUserID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Optional;

@Repository
public interface ICustomUserIDRepository extends JpaRepository<MsCustomUserID, BigInteger> {
    Page<MsCustomUserID> findAllByParamDetailId(String paramDetailId, Pageable pageable);

    @Query(value = "SELECT * FROM MsCustomUserID mcu with(nolock) WHERE mcu.ParamDetailId = :paramDetailId AND mcu.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsCustomUserID> findAllByParamDetailIdByNik(String paramDetailId, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsCustomUserID mcu with(nolock) WHERE mcu.ParamDetailId = :paramDetailId AND mcu.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsCustomUserID> findAllByParamDetailIdByNamaUser(String paramDetailId, String searchData, Pageable pageable);
    Optional<MsCustomUserID> findByNikAndParamDetailId(String nik, String paramDetailId);

    @Modifying
    @Query(value = "DELETE FROM MsCustomUserID WHERE ParamDetailId = :paramDetailId", nativeQuery = true)
    void deleteAllByParamDetailId(String paramDetailId);
}
