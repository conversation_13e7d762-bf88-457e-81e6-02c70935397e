package com.btpns.fin.repository;

import com.btpns.fin.model.entity.UserTicket;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IUserTicketRepository extends JpaRepository<UserTicket, String> {
    @Query(value = "SELECT * FROM ( " +
            "SELECT tfr.TicketId, tfr.CreateDatetime, tfa.CurrentState, mspd.ParamDetailDesc as CurrentStateDesc, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.<PERSON><PERSON><PERSON><PERSON><PERSON>, tfa.PUK1<PERSON>K, tfa.PUK2NIK, " +
            "tfr.<PERSON>p<PERSON> AS Aplikasi, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS DeskripsiAlasan, 'FU' AS Kategori " +
            "FROM TrxFuidRequest tfr with(nolock) " +
            "INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan with(nolock) on tujuan.ParamDetailId = " +
            "CASE  " +
                "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                "ELSE tfr.Tujuan " +
            "END " +
            "INNER JOIN MsSystemParamDetail alasan with(nolock) on tfr.Alasan = alasan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail mspd with(nolock) on mspd.ParamDetailId = tfa.CurrentState " +
            "UNION " +
            "SELECT tspr.TicketId, tspr.CreateDatetime, tspa.CurrentState, mspd.ParamDetailDesc as CurrentStateDesc, tspr.TanggalEfektif,tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.NikRequester, tspa.PUK1NIK, tspa.PUK2NIK, " +
            "tspr.Aplikasi AS Aplikasi, tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterLama AS DeskripsiAlasan, 'SP' AS Kategori " +
            "FROM TrxSetupParamRequest tspr with(nolock) " +
            "INNER JOIN TrxSetupParamApproval tspa with(nolock) on tspr.ticketId = tspa.ticketId " +
            "INNER JOIN MsSystemParamDetail mspd with(nolock) on mspd.ParamDetailId = tspa.CurrentState " +
            ") x " +
            "WHERE x.CurrentState IN (:status) " +
            "AND x.Kategori IN (:type) " +
            "AND ((:isUser = 1 AND x.NikRequester = :nik) OR ((:isUser = 0 AND :isWaitingApproval = 1 AND ((x.PUK1NIK = :nik AND x.CurrentState = 'waiting_puk1') OR (x.PUK2NIK = :nik AND x.CurrentState = 'waiting_puk2'))) OR (:isUser = 0 AND :isWaitingApproval = 0 AND (x.PUK1NIK = :nik OR x.PUK2NIK = :nik)))) " +
            "AND ((:startDate is null OR :endDate is null) OR (x.CreateDatetime BETWEEN :startDate AND :endDate)) " +
            "ORDER BY x.CreateDatetime DESC ", nativeQuery = true,
            countQuery = "SELECT COUNT(*) FROM ( " +
                    "SELECT tfr.TicketId, tfr.CreateDatetime, tfa.CurrentState, mspd.ParamDetailDesc as CurrentStateDesc, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.NikRequester, tfa.PUK1NIK, tfa.PUK2NIK, " +
                    "tfr.Aplikasi AS Aplikasi, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS DeskripsiAlasan, 'FU' AS Kategori " +
                    "FROM TrxFuidRequest tfr with(nolock) " +
                    "INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId " +
                    "INNER JOIN MsSystemParamDetail tujuan with(nolock) on tujuan.ParamDetailId = " +
                    "CASE " +
                        "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                        "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                        "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                        "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                        "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                        "ELSE tfr.Tujuan " +
                    "END " +
                    "INNER JOIN MsSystemParamDetail alasan with(nolock) on tfr.Alasan = alasan.ParamDetailId " +
                    "INNER JOIN MsSystemParamDetail mspd with(nolock) on mspd.ParamDetailId = tfa.CurrentState " +
                    "UNION " +
                    "SELECT tspr.TicketId, tspr.CreateDatetime, tspa.CurrentState, mspd.ParamDetailDesc as CurrentStateDesc, tspr.TanggalEfektif,tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.NikRequester, tspa.PUK1NIK, tspa.PUK2NIK, " +
                    "tspr.Aplikasi AS Aplikasi, tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterLama AS DeskripsiAlasan, 'SP' AS Kategori " +
                    "FROM TrxSetupParamRequest tspr with(nolock) " +
                    "INNER JOIN TrxSetupParamApproval tspa with(nolock) on tspr.ticketId = tspa.ticketId " +
                    "INNER JOIN MsSystemParamDetail mspd with(nolock) on mspd.ParamDetailId = tspa.CurrentState " +
                    ") x  " +
                    "WHERE x.CurrentState IN (:status) " +
                    "AND x.Kategori IN (:type) " +
                    "AND ((:isUser = 1 AND x.NikRequester = :nik) OR ((:isUser = 0 AND :isWaitingApproval = 1 AND ((x.PUK1NIK = :nik AND x.CurrentState = 'waiting_puk1') OR (x.PUK2NIK = :nik AND x.CurrentState = 'waiting_puk2'))) OR (:isUser = 0 AND :isWaitingApproval = 0 AND (x.PUK1NIK = :nik OR x.PUK2NIK = :nik)))) " +
                    "AND ((:startDate is null OR :endDate is null) OR (x.CreateDatetime BETWEEN :startDate AND :endDate)) ")
    Page<UserTicket> getListUserTicket(@Param("status") List<String> status,
                                       @Param("type") List<String> type,
                                       @Param("nik") String nik,
                                       @Param("isUser") Integer isUser,
                                       @Param("isWaitingApproval") Integer isWaitingApproval,
                                       @Param("startDate") String startDate,
                                       @Param("endDate") String endDate,
                                       @Param("pageable") Pageable pageable);
}
