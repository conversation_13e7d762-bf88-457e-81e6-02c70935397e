package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsTepatMBankingIndividu;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.Optional;

public interface IMsTepatMBankingIndividuRepository extends JpaRepository<MsTepatMBankingIndividu, BigInteger> {
    @Query(value = "SELECT * FROM MsTepatMBankingIndividu mtmi with(nolock) Where mtmi.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsTepatMBankingIndividu> findAllByNik(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsTepatMBankingIndividu mtmi with(nolock) Where mtmi.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsTepatMBankingIndividu> findAllByNamaUser(String searchData, Pageable pageable);

    Optional<MsTepatMBankingIndividu> findByNik(String nik);

    @Modifying
    @Query(value = "DELETE FROM MsTepatMBankingIndividu WHERE NIK = :nik", nativeQuery = true)
    public int deleteMsTepatMBankingIndividuUser(@Param("nik") String nik);
}
