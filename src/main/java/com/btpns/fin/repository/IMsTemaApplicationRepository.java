package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsTemaApplication;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IMsTemaApplicationRepository extends JpaRepository<MsTemaApplication,String> {
    @Query(value = "SELECT * FROM MsTemaApplication mta with(nolock) where mta.paramId IN (:paramId)", nativeQuery = true)
    Page<MsTemaApplication> findAllApplicationTypePageable(List<String> paramId, Pageable pageable);

    @Query(value = "SELECT * FROM MsTemaApplication mta with(nolock) where mta.paramId = :paramId and mta.paramDetailId LIKE %:searchData%", nativeQuery = true)
    Page<MsTemaApplication> findAllByParamDetailIdPageable(String paramId, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsTemaApplication mta with(nolock) where mta.paramId = :paramId and mta.paramDetailDesc LIKE %:searchData%", nativeQuery = true)
    Page<MsTemaApplication> findAllByParamDetailDescPageable(String paramId, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsTemaApplication mta with(nolock) where mta.paramId IN (:paramId) and mta.paramDetailId = :paramDetailId", nativeQuery = true)
    MsTemaApplication findByParamDetailId(List<String> paramId, String paramDetailId);

    @Query(value = "SELECT mta.ParamDetailId FROM MsTemaApplication mta with(nolock) where mta.ParamId = :paramId ORDER BY mta.ParamDetailId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastParamDetailIdByParamId(String paramId);

    @Query(value = "SELECT * FROM MsTemaApplication mta with(nolock) where mta.paramDetailId = :paramDetailId", nativeQuery = true)
    MsTemaApplication getMsTemaApplication(String paramDetailId);

    @Query(value = "SELECT mta.paramDetailDesc FROM MsTemaApplication mta with(nolock) where mta.paramDetailId IN (:paramDetailIds)", nativeQuery = true)
    List<String> getMsTemaApplicationList(List<String> paramDetailIds);

    @Query(value = "SELECT * FROM MsTemaApplication mta with(nolock) where mta.paramId IN :paramIds", nativeQuery = true)
    List<MsTemaApplication> getMsTemaApplicationByParamId(List<String> paramIds);
}
