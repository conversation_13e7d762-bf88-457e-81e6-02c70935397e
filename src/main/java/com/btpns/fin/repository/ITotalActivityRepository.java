package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TotalActivity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ITotalActivityRepository extends JpaRepository<TotalActivity, String> {

    @Query(value = "SELECT mspd.ParamDetailDesc as activity, COUNT(*) as total " +
            "FROM TrxFuidRequestAplikasi tfra with(nolock) " +
            "INNER JOIN TrxFuidRequest tfr with(nolock) on tfra.TicketId = tfr.TicketId " +
            "INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail mspd with(nolock) on mspd.ParamDetailId = " +
            "CASE " +
                "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                "ELSE tfr.Tujuan " +
            "END " +
            "WHERE tfa.CurrentState = 'done_upm' AND MONTH(tfr.TanggalEfektif) = :month AND YEAR(tfr.TanggalEfektif) = :year " +
            "GROUP BY mspd.ParamDetailDesc " +
            "UNION " +
            "SELECT tspr.KategoriParamName as activity, COUNT(*) as total " +
            "FROM TrxSetupParamRequestAplikasi tspra with(nolock) " +
            "INNER JOIN TrxSetupParamRequest tspr with(nolock) on tspra.TicketId = tspr.TicketId " +
            "INNER JOIN TrxSetupParamApproval tspa with(nolock) on tspr.ticketId = tspa.ticketId " +
            "WHERE tspa.CurrentState = 'done_upm' AND MONTH(tspr.TanggalEfektif) = :month AND YEAR(tspr.TanggalEfektif) = :year " +
            "GROUP BY tspr.KategoriParamName " +
            "ORDER BY total DESC", nativeQuery = true)
    public List<TotalActivity> getTotalActivityByMonth(int month, int year);
}
