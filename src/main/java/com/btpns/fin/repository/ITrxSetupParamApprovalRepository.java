package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxSetupParamApproval;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.Optional;

public interface ITrxSetupParamApprovalRepository extends JpaRepository<TrxSetupParamApproval, BigInteger> {
    @Query(value = "SELECT * FROM TrxSetupParamApproval tspa with(nolock) WHERE tspa.ticketId = :ticketId", nativeQuery = true)
    public Optional<TrxSetupParamApproval> getTrxSetupParamApprovalByTicketId(@Param("ticketId") String ticketId);

    @Modifying
    @Query(value = "DELETE FROM TrxSetupParamApproval WHERE ticketId = :ticketId", nativeQuery = true)
    int deleteTrxSetupParamRequestByTicketId(@Param("ticketId") String ticketId);

    @Modifying
    @Query(value = "UPDATE TrxSetupParamApproval set puk1NIK = :puk1NIK, puk1DelegationId = :puk1DelegationId WHERE ticketId = :ticketId", nativeQuery = true)
    public int updateTrxSetupParamApprovalPUK1ByTicketId(@Param("ticketId") String ticketId, @Param("puk1NIK") String puk1NIK, @Param("puk1DelegationId") String puk1DelegationId);

    @Modifying
    @Query(value = "UPDATE TrxSetupParamApproval set puk2NIK = :puk2NIK, puk2DelegationId = :puk2DelegationId WHERE ticketId = :ticketId", nativeQuery = true)
    public int updateTrxSetupParamApprovalPUK2ByTicketId(@Param("ticketId") String ticketId, @Param("puk2NIK") String puk2NIK, @Param("puk2DelegationId") String puk2DelegationId);

    @Query(value = "SELECT * FROM TrxSetupParamApproval tspa with(nolock) WHERE tspa.ticketId = :ticketId", nativeQuery = true)
    TrxSetupParamApproval findByTicketId(@Param("ticketId") String ticketId);
}
