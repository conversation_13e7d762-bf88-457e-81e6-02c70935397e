package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsSPK;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.Optional;

public interface IMsSPKUserManagementRepository extends JpaRepository<MsSPK, BigInteger> {
    Optional<MsSPK> findByNik(String nik);

    @Modifying
    @Query(value = "DELETE FROM MsSPK WHERE NIK = :nik", nativeQuery = true)
    public int deleteMsSPKUser(@Param("nik") String nik);

    @Query(value = "SELECT * FROM MsSPK ms with(nolock) Where ms.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsSPK> findAllByNik(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsSPK ms with(nolock) Where ms.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsSPK> findAllByNamaUser(String searchData, Pageable pageable);
}
