package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsDigitus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.math.BigInteger;

public interface IDigitusUserIdRepository extends JpaRepository<MsDigitus, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM MsDigitus", nativeQuery = true)
    int deleteAllMsDigitus();

    @Modifying
    @Query(value = "DELETE FROM MsDigitus WHERE NIK = :nik", nativeQuery = true)
    int deleteMsDigitusByNik(String nik);

    @Query(value = "SELECT * FROM MsDigitus md with(nolock) ORDER BY md.Kewenangan ASC, md.NamaUser ASC ", nativeQuery = true)
    Page<MsDigitus> getListMsDigitus(Pageable pageable);

    @Query(value = "SELECT * FROM MsDigitus md with(nolock) Where md.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsDigitus> findAllByNikUser(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsDigitus md with(nolock) Where md.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsDigitus> findAllByNameUser(String searchData, Pageable pageable);

    @Query(value = "SELECT md.* FROM MsDigitus md with(nolock) WHERE md.NIK = :nik", nativeQuery = true)
    MsDigitus findByNikUser(String nik);
}
