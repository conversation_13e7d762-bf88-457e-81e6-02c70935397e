package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxComment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ITrxCommentRepository extends JpaRepository<TrxComment, String> {
    @Query(value = "SELECT * FROM TrxComment tc with(nolock) WHERE tc.ticketId = :ticketId ORDER BY tc.commentId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    public TrxComment getLastCommentByTicketId(@Param("ticketId") String ticketId);

    @Query(value = "SELECT * FROM TrxComment tc with(nolock) WHERE tc.ticketId = :ticketId ORDER BY tc.commentId DESC", nativeQuery = true)
    public List<TrxComment> getListCommentByTicketId(@Param("ticketId") String ticketId);

    @Query(value = "SELECT * FROM TrxComment tc with(nolock) WHERE tc.ticketId = :ticketId AND tc.commentId = :commentId ", nativeQuery = true)
    public TrxComment getCommentByTicketIdAndCommentId(@Param("ticketId") String ticketId, @Param("commentId") String commentId);

    @Query(value = "SELECT * FROM TrxComment tc with(nolock) WHERE tc.status = :status " +
            "AND nikComment NOT IN (SELECT NIK FROM TrxUpmRole)", nativeQuery = true)
    public Page<TrxComment> getListCommentByStatus(@Param("status") String status, @Param("pageable") Pageable pageable);

    @Query(value = "SELECT * FROM TrxComment tc with(nolock) WHERE tc.status = :status AND nikComment NOT IN (SELECT NIK FROM TrxUpmRole)", nativeQuery = true)
    public List<TrxComment> findAllCommentByStatus(@Param("status") String status);
}
