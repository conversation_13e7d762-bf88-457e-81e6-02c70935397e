package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsSystemParamDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IMsSystemParamDetailRepository extends JpaRepository<MsSystemParamDetail,String> {
    @Query(value = "SELECT * FROM MsSystemParamDetail mspd with(nolock) where mspd.paramDetailId = :paramDetailId", nativeQuery = true)
    public MsSystemParamDetail getMsSystemParamDetail(@Param("paramDetailId") String paramDetailId);

    @Query(value = "SELECT * FROM MsSystemParamDetail mspd with(nolock) where mspd.paramId IN (:paramIds)", nativeQuery = true)
    public List<MsSystemParamDetail> getMsSystemParamDetailByParamId(@Param("paramIds") List<String> paramIds);

    @Query(value = "SELECT * FROM MsSystemParamDetail mspd with(nolock) where mspd.paramId = :paramId ORDER BY mspd.paramDetailId ASC", nativeQuery = true)
    Page<MsSystemParamDetail> findAllSPCategoryDataPageable(@Param("paramId") String paramId, @Param("pageable") Pageable pageable);

    @Query(value = "SELECT * FROM MsSystemParamDetail mspd with(nolock) where mspd.paramId = :paramId and mspd.paramDetailId LIKE %:searchData%", nativeQuery = true)
    Page<MsSystemParamDetail> findAllByParamDetailIdPageable(String paramId, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsSystemParamDetail mspd with(nolock) where mspd.paramId = :paramId and mspd.paramDetailDesc LIKE %:searchData%", nativeQuery = true)
    Page<MsSystemParamDetail> findAllByParamDetailDescPageable(String paramId, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsSystemParamDetail mspd with(nolock) where mspd.paramId IN (:paramId) and mspd.paramDetailId = :paramDetailId", nativeQuery = true)
    MsSystemParamDetail findByParamDetailId(List<String> paramId, String paramDetailId);

    @Query(value = "SELECT * FROM MsSystemParamDetail mspd with(nolock) where mspd.paramId IN (:paramId)", nativeQuery = true)
    Page<MsSystemParamDetail> findAllApplicationTypePageable(List<String> paramId, Pageable pageable);

    @Query(value = "SELECT mspd.ParamDetailId FROM MsSystemParamDetail mspd with(nolock) where mspd.ParamId = :paramId ORDER BY mspd.ParamDetailId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastParamDetailIdByParamId(String paramId);
}
