package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsBiCAC;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.Optional;

public interface IMsBiCACUserManagementRepository extends JpaRepository<MsBiCAC, BigInteger> {
    Optional<MsBiCAC> findByNik(String nik);
    Optional<MsBiCAC> findByIdUser(String idUser);

    @Modifying
    @Query(value = "DELETE FROM MsBiCAC WHERE NIK = :nik", nativeQuery = true)
    public int deleteMsBiCACUser(@Param("nik") String nik);

    @Query(value = "SELECT * FROM MsBiCAC mbc with(nolock) Where mbc.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsBiCAC> findAllByNik(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsBiCAC mbc with(nolock) Where mbc.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsBiCAC> findAllByNamaUser(String searchData, Pageable pageable);
}
