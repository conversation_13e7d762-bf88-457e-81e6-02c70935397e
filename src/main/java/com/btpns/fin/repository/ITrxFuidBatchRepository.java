package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxFuidBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;

public interface ITrxFuidBatchRepository extends JpaRepository<TrxFuidBatch, String> {

    @Query(value = "SELECT * from TrxFuidBatch tfb with(nolock) WHERE tfb.BatchId = :batchId", nativeQuery = true)
    public TrxFuidBatch getFuidBatchByBatchId(@Param("batchId") String batchId);

    @Query(value = "SELECT count(*) FROM TrxFuidBatch tfr with(nolock) WHERE createDateTime between :startDate and :endDate and uploaderNIK = :uploaderNIK and type = :type", nativeQuery = true)
    public Integer checkRequestInterval(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("uploaderNIK") String uploaderNIK, @Param("type") String type);
}
