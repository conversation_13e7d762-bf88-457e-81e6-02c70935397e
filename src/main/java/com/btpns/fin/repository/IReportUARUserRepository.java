package com.btpns.fin.repository;

import com.btpns.fin.model.response.ResReportUARUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IReportUARUserRepository extends JpaRepository<ResReportUARUser, String> {
    @Query(value = "Select tur.TicketId, tur.NIK, tur.namaUser as nama, tur.kewenangan, tur.jabatan, tur.unitKerja, tua.UserConfirmation as status, tua.UserNIKNotes as keterangan, tua.CurrentState as statusTiket " +
                    "FROM TrxUARRequest tur WITH (NOLOCK) INNER JOIN TrxUARApproval tua WITH (NOLOCK) on tur.ticketId = tua.ticketId " +
                    "WHERE tur.NIK = :nik and tur.PeriodYear = :tahun and tur.PeriodQuarter = :triwulan and tur.Aplikasi = :aplikasi and " +
                    "NOT EXISTS (" +
                        "SELECT 1 " +
                        "FROM TrxUARApproval tua_inner WITH (NOLOCK) " +
                        "WHERE tur.ticketId = tua_inner.ticketId " +
                        "AND tua_inner.CurrentState IN (:currentStates)" +
                    ")" +
                    "order by tua.CurrentStateDT DESC",
            countQuery = "SELECT COUNT(*) " +
                        "FROM TrxUARRequest tur WITH (NOLOCK) INNER JOIN TrxUARApproval tua WITH (NOLOCK) ON tur.ticketId = tua.ticketId " +
                        "WHERE tur.NIK = :nik AND tur.PeriodYear = :tahun AND tur.PeriodQuarter = :triwulan AND tur.Aplikasi = :aplikasi AND " +
                        "NOT EXISTS (" +
                        "SELECT 1 " +
                        "FROM TrxUARApproval tua_inner WITH (NOLOCK) " +
                        "WHERE tur.ticketId = tua_inner.ticketId " +
                        "AND tua_inner.CurrentState IN (:currentStates)" +
                        ")", nativeQuery = true)
    Page<ResReportUARUser> getReportUARUser(String nik, String triwulan, String tahun, String aplikasi, List<String> currentStates, Pageable pageable);
}
