package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxUARAudittrail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.math.BigInteger;
import java.util.List;

public interface ITrxUARAudittrailRepository extends JpaRepository<TrxUARAudittrail, BigInteger> {
    @Query(value = "SELECT * FROM TrxUARAudittrail with(nolock) WHERE ticketId = :ticketId order by id asc", nativeQuery = true)
    List<TrxUARAudittrail> findAllByTicketId(String ticketId);

    @Modifying
    @Query(value = "DELETE FROM TrxUARAudittrail WHERE ticketId = :ticketId", nativeQuery = true)
    int deleteTrxUARAudditrailByTicketId(String ticketId);
}
