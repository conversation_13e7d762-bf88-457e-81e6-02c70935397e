package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxUARRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ITrxUARRequestRepository extends JpaRepository<TrxUARRequest, String>, JpaSpecificationExecutor<TrxUARRequest> {
    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) ORDER BY tur.ticketId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    Optional<TrxUARRequest> findLatestTrxUARRequest();

    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tur.TicketId = tua.ticketId " +
            "WHERE tua.CurrentState IN (:statuses) " +
            "AND ((:aplikasi = 'all') OR (tur.Aplikasi = :aplikasi)) " +
            "AND ((:periodYear = -1) OR (tur.PeriodYear = :periodYear)) " +
            "AND ((:periodQuarter = 'all') OR (tur.PeriodQuarter = :periodQuarter)) " +
            "ORDER BY tua.CurrentStateDT DESC", nativeQuery = true)
    Page<TrxUARRequest> findByAplikasiByYearByQuarterInStatuses(List<String> statuses, String aplikasi, Integer periodYear, String periodQuarter, Pageable pageable);

    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tur.TicketId = tua.ticketId " +
            "WHERE (('all' IN (:statuses)) OR (tua.CurrentState IN (:statuses))) " +
            "AND tur.TicketId = :searchData " +
            "AND ((:aplikasi = 'all') OR (tur.Aplikasi = :aplikasi)) " +
            "AND ((:periodYear = -1) OR (tur.PeriodYear = :periodYear)) " +
            "AND ((:periodQuarter = 'all') OR (tur.PeriodQuarter = :periodQuarter)) " +
            "ORDER BY tua.CurrentStateDT DESC", nativeQuery = true)
    Page<TrxUARRequest> findByTicketIdByAplikasiByYearByQuarterInStatuses(List<String> statuses, String aplikasi, Integer periodYear, String periodQuarter, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tur.TicketId = tua.ticketId " +
            "WHERE tua.CurrentState IN (:statuses) " +
            "AND tur.NIK = :searchData " +
            "AND ((:aplikasi = 'all') OR (tur.Aplikasi = :aplikasi)) " +
            "AND ((:periodYear = -1) OR (tur.PeriodYear = :periodYear)) " +
            "AND ((:periodQuarter = 'all') OR (tur.PeriodQuarter = :periodQuarter)) " +
            "ORDER BY tua.CurrentStateDT DESC", nativeQuery = true)
    Page<TrxUARRequest> findByNIKByAplikasiByYearByQuarterInStatuses(List<String> statuses, String aplikasi, Integer periodYear, String periodQuarter, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tur.TicketId = tua.ticketId " +
            "WHERE tua.CurrentState IN (:statuses) " +
            "AND tur.NamaUser LIKE %:searchData% " +
            "AND ((:aplikasi = 'all') OR (tur.Aplikasi = :aplikasi)) " +
            "AND ((:periodYear = -1) OR (tur.PeriodYear = :periodYear)) " +
            "AND ((:periodQuarter = 'all') OR (tur.PeriodQuarter = :periodQuarter)) " +
            "ORDER BY tua.CurrentStateDT DESC", nativeQuery = true)
    Page<TrxUARRequest> findByNameByAplikasiByYearByQuarterInStatuses(List<String> statuses, String aplikasi, Integer periodYear, String periodQuarter, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tur.TicketId = tua.ticketId WHERE tua.CurrentState IN (:statuses) AND tua.UserNIK = :userNik ORDER BY tur.Aplikasi ASC", nativeQuery = true)
    List<TrxUARRequest> findAllUARByNikInStatuses(List<String> statuses, String userNik);

    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tur.TicketId = tua.ticketId WHERE tua.CurrentState = :status AND tua.PUKNIK = :nik ORDER BY tua.CurrentStateDT DESC", nativeQuery = true)
    Page<TrxUARRequest> findAllPageableUARByStatusByPukNik(String status, String nik, Pageable pageable);

    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tur.TicketId = tua.ticketId WHERE " +
                   "(tua.UserNIK = :nik AND tua.CurrentState = 'pending_user') OR " +
                   "(tua.PUKNIK = :nik AND tua.CurrentState = 'pending_puk')", nativeQuery = true)
    List<TrxUARRequest> findAllUARByStatusByUserNikOrPukNik(String nik);

    @Query(value = "SELECT * FROM TrxUARRequest tur with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tur.TicketId = tua.ticketId WHERE tur.TicketId = :ticketId", nativeQuery = true)
    TrxUARRequest findByTicketId(String ticketId);

    @Query(value = "SELECT DISTINCT tu.Aplikasi from TrxUARRequest tu with(nolock) INNER JOIN TrxUARApproval tua with(nolock) on tu.TicketId = tua.ticketId WHERE tu.NIK = :nik AND tua.CurrentState != :status", nativeQuery = true)
    List<String> findDistinctApplicationIdByNik(String nik, String status);

    @Modifying
    @Query(value = "DELETE FROM TrxUARRequest WHERE ticketId = :ticketId", nativeQuery = true)
    int deleteTrxUARRequestByTicketId(String ticketId);
}
