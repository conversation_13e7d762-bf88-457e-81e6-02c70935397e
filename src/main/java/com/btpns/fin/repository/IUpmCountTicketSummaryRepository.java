package com.btpns.fin.repository;

import com.btpns.fin.model.UpmCountTicketSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IUpmCountTicketSummaryRepository extends JpaRepository<UpmCountTicketSummary, String> {
    @Query(value = "select ticketId, Status, currentStateDT, tanggalEfektif from " +
            "( " +
            "SELECT " +
                "case " +
                    "when (tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > GETDATE()) then 'pending' " +
                    "ELSE tfa.CurrentState " +
                "END as status, " +
                "tfa.CurrentStateDT as CurrentStateDT, " +
                "tfa.TicketId, " +
                "tfr.TanggalEfektif " +
            "FROM TrxFuidRequest tfr with(nolock) " +
            "INNER JOIN tema.dbo.TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId " +
            "WHERE tfa.CurrentStateDT BETWEEN :startDate AND :endDate  " +
            "OR tfr.TanggalEfektif BETWEEN :startDateEfektif AND :endDateEfektif " +
            "union " +
            "SELECT " +
                "case " +
                    "when (tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > GETDATE()) then 'pending' " +
                    "ELSE tspa.CurrentState " +
                "END as status, " +
                "tspa.CurrentStateDT as CurrentStateDT, " +
                "tspa.TicketId, " +
                "tspr.TanggalEfektif " +
            "FROM TrxSetupParamRequest tspr with(nolock) " +
            "INNER JOIN TrxSetupParamApproval tspa with(nolock) on tspr.ticketId = tspa.ticketId " +
            "WHERE tspa.CurrentStateDT BETWEEN :startDate AND :endDate " +
            "OR tspr.TanggalEfektif BETWEEN :startDateEfektif AND :endDateEfektif " +
            ") as datas", nativeQuery = true)
    public List<UpmCountTicketSummary> getListTicketSummaryUPM(String startDate, String endDate, String startDateEfektif, String endDateEfektif);
}
