package com.btpns.fin.repository;

import com.btpns.fin.model.entity.ReportAplikasiPerTiket;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IReportRepository extends JpaRepository<ReportAplikasiPerTiket, String> {
    @Query(value = "SELECT * FROM ( " +
                "SELECT tfa.Id, tfr.TicketId, tfr.CreateDatetime, tfr.Aplikasi AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
                "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
                "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FU' AS Kategori, " +
                "CASE " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tfa.CurrentState " +
                "END AS CurrentState, " +
                "sd.ParamDetailDesc AS CurrentStateDesc, " +
                "CASE " +
                    "WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT " +
                    "ELSE null " +
                "END AS DoneDT, " +
                "CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
                "PUK1NIK, puk1.FullName AS PUK1Nama, puk1.Occupation AS PUK1Jabatan, PUK1Dt, " +
                "PUK2NIK, puk2.FullName AS PUk2Nama, puk2.Occupation AS PUK2Jabatan, PUK2Dt " +
                "FROM TrxFuidRequest tfr WITH (NOLOCK) " +
                "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tujuan.ParamDetailId = " +
                "CASE " +
                    "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                    "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                    "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                    "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                    "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                    "ELSE tfr.Tujuan " +
                "END " +
                "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
                "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                "CASE " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tfa.CurrentState " +
                "END " +
                "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
                "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
                "LEFT JOIN MsEmployee puk1 WITH (NOLOCK) on tfa.PUK1NIK = puk1.NIK " +
                "LEFT JOIN MsEmployee puk2 WITH (NOLOCK) on tfa.PUK2NIK = puk2.NIK " +
                "WHERE ((:tipeKewenanganLimit = '-1') OR (tfr.TipeKewenanganLimit = :tipeKewenanganLimit)) " +
                "UNION " +
                "SELECT tspa.Id, tspr.TicketId, tspr.CreateDatetime, tspr.Aplikasi AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
                "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
                "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
                "CASE " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tspa.CurrentState " +
                "END AS CurrentState, " +
                "sd.ParamDetailDesc AS CurrentStateDesc, " +
                "CASE " +
                    "WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT " +
                    "ELSE null " +
                "END AS DoneDT, " +
                "CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
                "PUK1NIK, puk1.FullName AS PUK1Nama, puk1.Occupation AS PUK1Jabatan, PUK1Dt, " +
                "PUK2NIK, puk2.FullName AS PUk2Nama, puk2.Occupation AS PUK2Jabatan, PUK2Dt " +
                "FROM TrxSetupParamRequest tspr WITH (NOLOCK) " +
                "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                "CASE " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tspa.CurrentState " +
                "END " +
                "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
                "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
                "LEFT JOIN MsEmployee puk1 WITH (NOLOCK) on tspa.PUK1NIK = puk1.NIK " +
                "LEFT JOIN MsEmployee puk2 WITH (NOLOCK) on tspa.PUK2NIK = puk2.NIK " +
                ") x " +
                "WHERE x.CurrentState IN (:status) " +
                "AND x.Kategori IN (:type) " +
                "AND (('approved' = :status AND x.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND x.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                "AND ((:upmInputNIK is null) OR (x.UPMInputNIK in (:upmInputNIK))) " +
                "AND ((:upmCheckerNIK is null) OR (x.UPMCheckerNIK in (:upmCheckerNIK))) " +
                "AND ( " +
                "(x.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                "(x.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                "(x.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                ") " +
                "ORDER BY x.CurrentStateDT DESC ",
           countQuery = "SELECT COUNT(*) FROM ( " +
                   "SELECT tfa.Id, tfr.TicketId, tfr.CreateDatetime, tfr.Aplikasi AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
                   "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
                   "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FU' AS Kategori, " +
                   "CASE " +
                       "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                       "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                       "ELSE tfa.CurrentState " +
                   "END AS CurrentState, " +
                   "sd.ParamDetailDesc AS CurrentStateDesc, " +
                   "CASE " +
                       "WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT ELSE null " +
                       "END AS DoneDT, " +
                   "CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
                   "PUK1NIK, puk1.FullName AS PUK1Nama, puk1.Occupation AS PUK1Jabatan, PUK1Dt, " +
                   "PUK2NIK, puk2.FullName AS PUK2Nama, puk2.Occupation AS PUK2Jabatan, PUK2Dt " +
                   "FROM TrxFuidRequest tfr WITH (NOLOCK) " +
                   "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                   "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tujuan.ParamDetailId = " +
                   "CASE " +
                       "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                       "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                       "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                       "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                       "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                       "ELSE tfr.Tujuan " +
                   "END " +
                   "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
                   "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                   "CASE " +
                       "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                       "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                       "ELSE tfa.CurrentState " +
                   "END " +
                   "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
                   "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
                   "LEFT JOIN MsEmployee puk1 WITH (NOLOCK) on tfa.PUK1NIK = puk1.NIK " +
                   "LEFT JOIN MsEmployee puk2 WITH (NOLOCK) on tfa.PUK2NIK = puk2.NIK " +
                   "WHERE ((:tipeKewenanganLimit = '-1') OR (tfr.TipeKewenanganLimit = :tipeKewenanganLimit)) " +
                   "UNION " +
                   "SELECT tspa.Id, tspr.TicketId, tspr.CreateDatetime, tspr.Aplikasi AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
                   "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
                   "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
                   "CASE " +
                       "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                       "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                       "ELSE tspa.CurrentState " +
                   "END AS CurrentState, " +
                   "sd.ParamDetailDesc AS CurrentStateDesc, " +
                   "CASE " +
                       "WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT " +
                       "ELSE null " +
                   "END AS DoneDT, " +
                   "CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
                   "PUK1NIK, puk1.FullName AS PUK1Nama, puk1.Occupation AS PUK1Jabatan, PUK1Dt, " +
                   "PUK2NIK, puk2.FullName AS PUK2Nama, puk2.Occupation AS PUK2Jabatan, PUK2Dt " +
                   "FROM TrxSetupParamRequest tspr WITH (NOLOCK) " +
                   "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                   "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                   "CASE " +
                       "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                       "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                       "ELSE tspa.CurrentState " +
                   "END " +
                   "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
                   "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
                   "LEFT JOIN MsEmployee puk1 WITH (NOLOCK) on tspa.PUK1NIK = puk1.NIK " +
                   "LEFT JOIN MsEmployee puk2 WITH (NOLOCK) on tspa.PUK2NIK = puk2.NIK " +
                   ") x " +
                   "WHERE x.CurrentState IN (:status) " +
                   "AND x.Kategori IN (:type) " +
                   "AND (('approved' = :status AND x.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND x.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                   "AND ((:upmInputNIK is null) OR (x.UPMInputNIK in (:upmInputNIK))) " +
                   "AND ((:upmCheckerNIK is null) OR (x.UPMCheckerNIK in (:upmCheckerNIK))) " +
                   "AND ( " +
                   "(x.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                   "(x.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                   "(x.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                   ") " ,
           nativeQuery = true)
    Page<ReportAplikasiPerTiket> getListReportPermohonanPerTiket(@Param("pageable") Pageable pageable,
                                                                 @Param("type") List<String> type,
                                                                 @Param("status") String status,
                                                                 @Param("dateNow") String dateNow,
                                                                 @Param("upmInputNIK") List<String> upmInputNIK,
                                                                 @Param("upmCheckerNIK") List<String> upmCheckerNIK,
                                                                 @Param("isPending") Integer isPending,
                                                                 @Param("effectiveDTStart") String effectiveDTStart,
                                                                 @Param("effectiveDTEnd") String effectiveDTEnd,
                                                                 @Param("createDTStart") String createDTStart,
                                                                 @Param("createDTEnd") String createDTEnd,
                                                                 @Param("currentStateDTStart") String currentStateDTStart,
                                                                 @Param("currentStateDTEnd") String currentStateDTEnd,
                                                                 @Param("tipeKewenanganLimit") String tipeKewenanganLimit);

    @Query(value = "SELECT * FROM ( " +
            "SELECT tfra.Id, tfr.TicketId, tfr.CreateDatetime, tfra.AplikasiName AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
            "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
            "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FU' AS Kategori, " +
            "CASE " +
                "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                "ELSE tfa.CurrentState " +
            "END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE " +
                "WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT " +
                "ELSE null " +
            "END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
            "PUK1NIK, puk1.FullName AS PUK1Nama, puk1.Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, puk2.FullName AS PUK2Nama, puk2.Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
            "INNER JOIN TrxFuidRequest tfr WITH (NOLOCK) on tfr.TicketId = tfra.TicketId " +
            "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tujuan.ParamDetailId =  " +
            "CASE " +
                "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                "ELSE tfr.Tujuan " +
            "END " +
            "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
                "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                "ELSE tfa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
            "LEFT JOIN MsEmployee puk1 WITH (NOLOCK) on tfa.PUK1NIK = puk1.NIK " +
            "LEFT JOIN MsEmployee puk2 WITH (NOLOCK) on tfa.PUK2NIK = puk2.NIK " +
            "WHERE ((:tipeKewenanganLimit = '-1') OR (tfr.TipeKewenanganLimit = :tipeKewenanganLimit)) " +
            "UNION " +
            "SELECT tspra.Id, tspr.TicketId, tspr.CreateDatetime, tspra.AplikasiName AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
            "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
            "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
            "CASE " +
                "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                "ELSE tspa.CurrentState " +
            "END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE " +
                "WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT " +
                "ELSE null "+
            "END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
            "PUK1NIK, puk1.FullName AS PUK1Nama, puk1.Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, puk2.FullName AS PUK2Nama, puk2.Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
            "INNER JOIN TrxSetupParamRequest tspr WITH (NOLOCK) on tspr.TicketId = tspra.TicketId " +
            "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
                "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                "ELSE tspa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
            "LEFT JOIN MsEmployee puk1 WITH (NOLOCK) on tspa.PUK1NIK = puk1.NIK " +
            "LEFT JOIN MsEmployee puk2 WITH (NOLOCK) on tspa.PUK2NIK = puk2.NIK " +
            ") x " +
            "WHERE x.CurrentState IN (:status) " +
            "AND x.Kategori IN (:type) " +
            "AND (('approved' = :status AND x.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND x.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ((:upmInputNIK is null) OR (x.UPMInputNIK in (:upmInputNIK))) " +
            "AND ((:upmCheckerNIK is null) OR (x.UPMCheckerNIK in (:upmCheckerNIK))) " +
            "AND ( " +
            "(x.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(x.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(x.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            "ORDER BY x.CurrentStateDT DESC ",
           countQuery = "SELECT COUNT(*) FROM ( " +
                   "SELECT tfra.Id, tfr.TicketId, tfr.CreateDatetime, tfra.AplikasiName AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
                   "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
                   "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FU' AS Kategori, " +
                   "CASE " +
                       "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                       "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                       "ELSE tfa.CurrentState " +
                   "END AS CurrentState, " +
                   "sd.ParamDetailDesc AS CurrentStateDesc, " +
                   "CASE " +
                        "WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT " +
                        "ELSE null "+
                   "END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
                   "PUK1NIK, puk1.FullName AS PUK1Nama, puk1.Occupation AS PUK1Jabatan, PUK1Dt, " +
                   "PUK2NIK, puk2.FullName AS PUK2Nama, puk2.Occupation AS PUK2Jabatan, PUK2Dt " +
                   "FROM TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
                   "INNER JOIN TrxFuidRequest tfr WITH (NOLOCK) on tfr.TicketId = tfra.TicketId " +
                   "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                   "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tujuan.ParamDetailId =  " +
                   "CASE " +
                       "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                       "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                       "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                       "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                       "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                       "ELSE tfr.Tujuan " +
                   "END " +
                   "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
                   "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                   "CASE " +
                       "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                       "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                       "ELSE tfa.CurrentState END " +
                   "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
                   "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
                   "LEFT JOIN MsEmployee puk1 WITH (NOLOCK) on tfa.PUK1NIK = puk1.NIK " +
                   "LEFT JOIN MsEmployee puk2 WITH (NOLOCK) on tfa.PUK2NIK = puk2.NIK " +
                   "WHERE ((:tipeKewenanganLimit = '-1') OR (tfr.TipeKewenanganLimit = :tipeKewenanganLimit)) " +
                   "UNION " +
                   "SELECT tspra.Id, tspr.TicketId, tspr.CreateDatetime, tspra.AplikasiName AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
                   "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
                   "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
                   "CASE " +
                       "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                       "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                       "ELSE tspa.CurrentState END AS CurrentState, " +
                   "sd.ParamDetailDesc AS CurrentStateDesc, " +
                   "CASE " +
                        "WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT " +
                        "ELSE null "+
                   "END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
                   "PUK1NIK, puk1.FullName AS PUK1Nama, puk1.Occupation AS PUK1Jabatan, PUK1Dt, " +
                   "PUK2NIK, puk2.FullName AS PUK2Nama, puk2.Occupation AS PUK2Jabatan, PUK2Dt " +
                   "FROM TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
                   "INNER JOIN TrxSetupParamRequest tspr WITH (NOLOCK) on tspr.TicketId = tspra.TicketId " +
                   "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                   "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                   "CASE " +
                       "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                       "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                       "ELSE tspa.CurrentState END " +
                   "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
                   "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
                   "LEFT JOIN MsEmployee puk1 WITH (NOLOCK) on tspa.PUK1NIK = puk1.NIK " +
                   "LEFT JOIN MsEmployee puk2 WITH (NOLOCK) on tspa.PUK2NIK = puk2.NIK " +
                   ") x " +
                   "WHERE x.CurrentState IN (:status) " +
                   "AND x.Kategori IN (:type) " +
                   "AND (('approved' = :status AND x.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND x.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                   "AND ((:upmInputNIK is null) OR (x.UPMInputNIK in (:upmInputNIK))) " +
                   "AND ((:upmCheckerNIK is null) OR (x.UPMCheckerNIK in (:upmCheckerNIK))) " +
                   "AND ( " +
                   "(x.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                   "(x.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                   "(x.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                   ") " ,
           nativeQuery = true)
    Page<ReportAplikasiPerTiket> getListReportPermohonanPerAplikasi(@Param("pageable") Pageable pageable,
                                                                    @Param("type") List<String> type,
                                                                    @Param("status") String status,
                                                                    @Param("dateNow") String dateNow,
                                                                    @Param("upmInputNIK") List<String> upmInputNIK,
                                                                    @Param("upmCheckerNIK") List<String> upmCheckerNIK,
                                                                    @Param("isPending") Integer isPending,
                                                                    @Param("effectiveDTStart") String effectiveDTStart,
                                                                    @Param("effectiveDTEnd") String effectiveDTEnd,
                                                                    @Param("createDTStart") String createDTStart,
                                                                    @Param("createDTEnd") String createDTEnd,
                                                                    @Param("currentStateDTStart") String currentStateDTStart,
                                                                    @Param("currentStateDTEnd") String currentStateDTEnd,
                                                                    @Param("tipeKewenanganLimit") String tipeKewenanganLimit);
}
