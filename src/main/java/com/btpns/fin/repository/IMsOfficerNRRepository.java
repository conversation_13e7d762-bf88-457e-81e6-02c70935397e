package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsOfficerNR;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface IMsOfficerNRRepository extends JpaRepository<MsOfficerNR, BigInteger> {
    @Query(value = "SELECT * FROM MsOfficerNR mon WITH (NOLOCK) WHERE mon.SrcSystem = 'EGLS' AND ((:accountStatus = -1) OR (mon.AccountEnabled = :accountStatus))", nativeQuery = true)
    Page<MsOfficerNR> findEGLSByStatusUser(String accountStatus, Pageable pageable);
    @Query(value = "SELECT * FROM MsOfficerNR mon WITH (NOLOCK) WHERE mon.SrcSystem = 'EGLS' AND (mon.OfficerName LIKE '%' + :searchData + '%') AND ((:accountStatus = -1) OR (mon.AccountEnabled = :accountStatus))", nativeQuery = true)
    Page<MsOfficerNR> findEGLSByUsername(String searchData, String accountStatus, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mon WITH (NOLOCK) WHERE mon.SrcSystem = 'EGLS' AND (mon.FirstName LIKE '%' + :searchData + '%') AND ((:accountStatus = -1) OR (mon.AccountEnabled = :accountStatus))", nativeQuery = true)
    Page<MsOfficerNR> findEGLSByFirstName(String searchData, String accountStatus, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mon WITH (NOLOCK) WHERE mon.SrcSystem = 'EGLS' AND (mon.LastName LIKE '%' + :searchData + '%') AND ((:accountStatus = -1) OR (mon.AccountEnabled = :accountStatus))", nativeQuery = true)
    Page<MsOfficerNR> findEGLSByLastName(String searchData, String accountStatus, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mon WITH (NOLOCK) WHERE mon.SrcSystem = :srcSystem AND ((:accountStatus = -1) OR (mon.AccountEnabled = :accountStatus))", nativeQuery = true)
    List<MsOfficerNR> findAllEGLSBySrcSystem(String srcSystem, String accountStatus);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) " +
            "Where mo.SrcSystem = 'PROSPERA' " +
            "AND ( " +
                    "(:filterStatus = '-1') or " +
                    "(:filterStatus = '1' and mo.OfficerStatusDesc = 'Active' and mo.RoleId is not null) or " +
                    "(:filterStatus = '2' and ((mo.OfficerStatusDesc = 'Inactive') or (mo.OfficerStatusDesc = 'Active' and mo.RoleId is null))) " +
                ") " +
            "AND (" +
                    "(:filterRole = '-1') or " +
                    "(mo.RoleID = :filterRole) " +
                ") " +
            "AND ( " +
                    "(:filterOffice = '-1') OR " +
                    "(:filterOffice LIKE 'W%') AND (mo.MMSCode = :filterOffice) OR " +
                    "(:isKFO = 1 AND mo.KFOCode = :filterOffice) OR " +
                    "(:isKFO <> 1 AND mo.KCSCode = :filterOffice) " +
                ") ", nativeQuery = true)
    Page<MsOfficerNR> findAllProsperaByStatusByRoleByOffice(String filterStatus, String filterRole, String filterOffice, boolean isKFO, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) " +
            "Where mo.NIK LIKE %:searchData% and mo.SrcSystem = 'PROSPERA' " +
            "AND ( " +
                    "(:filterStatus = '-1') or " +
                    "(:filterStatus = '1' and mo.OfficerStatusDesc = 'Active' and mo.RoleId is not null) or " +
                    "(:filterStatus = '2' and ((mo.OfficerStatusDesc = 'Inactive') or (mo.OfficerStatusDesc = 'Active' and mo.RoleId is null))) " +
                ") " +
            "AND (" +
                    "(:filterRole = '-1') or " +
                    "(mo.RoleID = :filterRole) " +
                ") " +
            "AND ( " +
                    "(:filterOffice = '-1') OR " +
                    "(:filterOffice LIKE 'W%') AND (mo.MMSCode = :filterOffice) OR " +
                    "(:isKFO = 1 AND mo.KFOCode = :filterOffice) OR " +
                    "(:isKFO <> 1 AND mo.KCSCode = :filterOffice) " +
                ") ", nativeQuery = true)
    Page<MsOfficerNR> findProsperaByNikByStatusByRoleByOffice(String searchData, String filterStatus, String filterRole, String filterOffice, boolean isKFO, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) " +
            "Where mo.OfficerName LIKE %:searchData% and mo.SrcSystem = 'PROSPERA' " +
            "AND ( " +
                    "(:filterStatus = '-1') or " +
                    "(:filterStatus = '1' and mo.OfficerStatusDesc = 'Active' and mo.RoleId is not null) or " +
                    "(:filterStatus = '2' and ((mo.OfficerStatusDesc = 'Inactive') or (mo.OfficerStatusDesc = 'Active' and mo.RoleId is null))) " +
                ") " +
            "AND (" +
                    "(:filterRole = '-1') or " +
                    "(mo.RoleID = :filterRole) " +
                ") " +
            "AND ( " +
                    "(:filterOffice = '-1') OR " +
                    "(:filterOffice LIKE 'W%') AND (mo.MMSCode = :filterOffice) OR " +
                    "(:isKFO = 1 AND mo.KFOCode = :filterOffice) OR " +
                    "(:isKFO <> 1 AND mo.KCSCode = :filterOffice) " +
                ") ", nativeQuery = true)
    Page<MsOfficerNR> findProsperaByOfficerNameByStatusByRoleByOffice(String searchData, String filterStatus, String filterRole, String filterOffice, boolean isKFO, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) " +
            "Where mo.LoginName LIKE %:searchData% and mo.SrcSystem = 'PROSPERA' " +
            "AND ( " +
                    "(:filterStatus = '-1') or " +
                    "(:filterStatus = '1' and mo.OfficerStatusDesc = 'Active' and mo.RoleId is not null) or " +
                    "(:filterStatus = '2' and ((mo.OfficerStatusDesc = 'Inactive') or (mo.OfficerStatusDesc = 'Active' and mo.RoleId is null))) " +
                ") " +
            "AND (" +
                    "(:filterRole = '-1') or " +
                    "(mo.RoleID = :filterRole) " +
                ") " +
            "AND ( " +
                    "(:filterOffice = '-1') OR " +
                    "(:filterOffice LIKE 'W%') AND (mo.MMSCode = :filterOffice) OR " +
                    "(:isKFO = 1 AND mo.KFOCode = :filterOffice) OR " +
                    "(:isKFO <> 1 AND mo.KCSCode = :filterOffice) " +
                ") ", nativeQuery = true)
    Page<MsOfficerNR> findProsperaByLoginNameByStatusByRoleByOffice(String searchData, String filterStatus, String filterRole, String filterOffice, boolean isKFO, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) Where mo.RoleID IS NOT NULL and mo.SrcSystem = 'PROSPERA' AND mo.OfficerStatusCode = :officerStatusActiveCode", nativeQuery = true)
    List<MsOfficerNR> findProsperaByRoleAndStatus(Integer officerStatusActiveCode);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) Where mo.RoleID IS NOT NULL and mo.SrcSystem = 'PROSPERA' AND mo.OfficerStatusCode = :officerStatusActiveCode and mo.NIK = :nik", nativeQuery = true)
    List<MsOfficerNR> findProsperaByRoleByStatusByNik(Integer officerStatusActiveCode, String nik);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) WHERE mo.DTEndProfile >= :localDate AND mo.SrcSystem = 'T24'", nativeQuery = true)
    Page<MsOfficerNR> findAllActiveT24(LocalDate localDate, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) WHERE mo.DTEndProfile >= :localDate AND mo.LoginName LIKE %:searchData% AND mo.SrcSystem = 'T24'", nativeQuery = true)
    Page<MsOfficerNR> findActiveT24ByLoginName(LocalDate localDate, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) WHERE mo.DTEndProfile >= :localDate AND mo.OfficerCode LIKE %:searchData% AND mo.SrcSystem = 'T24'", nativeQuery = true)
    Page<MsOfficerNR> findActiveT24ByOfficerCode(LocalDate localDate, String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsOfficerNR mo with(nolock) WHERE mo.DTEndProfile >= :localDate AND mo.OfficerName LIKE %:searchData% AND mo.SrcSystem = 'T24'", nativeQuery = true)
    Page<MsOfficerNR> findActiveT24ByOfficerName(LocalDate localDate, String searchData, Pageable pageable);
}
