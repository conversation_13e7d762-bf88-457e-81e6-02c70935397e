package com.btpns.fin.repository;

import com.btpns.fin.model.SOPNumberModel;
import com.btpns.fin.model.entity.MsSOPNumber;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

@Repository
public interface IMsSOPNumberRepository extends JpaRepository<MsSOPNumber, BigInteger> {
    @Query(value = "SELECT TOP 1 * FROM MsSOPNumber", nativeQuery = true)
    MsSOPNumber findAllMsSOPNumberData();
}
