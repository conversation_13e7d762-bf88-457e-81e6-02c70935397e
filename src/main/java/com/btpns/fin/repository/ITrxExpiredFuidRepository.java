package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxExpiredFuid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface ITrxExpiredFuidRepository extends JpaRepository<TrxExpiredFuid, String> {
    @Query(value = "select * from TrxExpiredFuid tefr with(nolock) where tefr.isProcessedByUpm = :isProcessedByUpm", nativeQuery = true)
    Page<TrxExpiredFuid> findExpiredFuidByState(Integer isProcessedByUpm, Pageable pageable);

    @Query(value = "SELECT count(1) FROM TrxExpiredFuid tef with(nolock) WHERE tef.CreatedDate = :date", nativeQuery = true)
    Integer countSavedExpiredFuidByCreatedDate(LocalDate date);

    Optional<TrxExpiredFuid> findFirstByOrderByCreatedDateDesc();

    @Query(value = "SELECT count(1) FROM TrxExpiredFuid tef with(nolock) WHERE tef.TicketId = :ticketId and tef.isProcessedByUpm = 0", nativeQuery = true)
    int findExpiredTicketFuidByTicketId(String ticketId);

    TrxExpiredFuid findByTicketId(String ticketId);
}
