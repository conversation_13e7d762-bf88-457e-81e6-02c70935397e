package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxPUKVendorBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.time.LocalDateTime;

@Repository
public interface ITrxPUKVendorBatchRepository extends JpaRepository<TrxPUKVendorBatch, BigInteger> {
    @Query(value = "SELECT * from TrxPUKVendorBatch tpvb with(nolock) WHERE tpvb.BatchId = :batchId", nativeQuery = true)
    public TrxPUKVendorBatch getTrxPUKVendorBatchByBatchId(@Param("batchId") String batchId);

    @Query(value = "SELECT count(*) FROM TrxPUKVendorBatch tpvb with(nolock) WHERE uploaderNIK = :uploaderNIK and createDateTime between :startDate and :endDate", nativeQuery = true)
    public Integer checkRequestInterval(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("uploaderNIK") String uploaderNIK);
}
