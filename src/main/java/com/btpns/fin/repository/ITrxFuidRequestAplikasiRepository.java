package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxFuidRequestAplikasi;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;

public interface ITrxFuidRequestAplikasiRepository extends JpaRepository<TrxFuidRequestAplikasi, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM TrxFuidRequestAplikasi WHERE ticketId = :ticketId", nativeQuery = true)
    public int deleteTrxFuidRequestAplikasiByTicketId(@Param("ticketId") String ticketId);

    @Modifying
    @Query(value = "update TrxFuidRequestAplikasi set periodDateDone = :periodDateDone, periodMonthDone = :periodMonthDone where TicketId = :ticketId", nativeQuery = true)
    public int updateTrxFuidRequestAplikasiByTicketId(@Param("ticketId") String ticketId, @Param("periodDateDone") String periodDateDone, @Param("periodMonthDone") String periodMonthDone);
}
