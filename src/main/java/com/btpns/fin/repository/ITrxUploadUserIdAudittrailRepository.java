package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxUploadUserIdAudittrail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

@Repository
public interface ITrxUploadUserIdAudittrailRepository extends JpaRepository<TrxUploadUserIdAudittrail, BigInteger> {
    @Query(value = "SELECT * FROM TrxUploadUserIdAudittrail tua WITH(NOLOCK) " +
                   "WHERE ((:aplikasi = 'all') OR (tua.paramDetailId = :aplikasi)) " +
                   "AND ((:startDate is null OR :endDate is null) OR (tua.UploadAt BETWEEN :startDate AND :endDate)) " +
                   "ORDER BY tua.UploadAt DESC", nativeQuery = true)
    Page<TrxUploadUserIdAudittrail> getMonitoringUploadUserId(@Param("aplikasi") String aplikasi,@Param("startDate") String startDate, @Param("endDate") String endDate, Pageable pageable);
}
