package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxUPMReminder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Optional;

@Repository
public interface ITrxUPMReminderRepository extends JpaRepository<TrxUPMReminder, BigInteger> {
    @Query(value = "SELECT * FROM TrxUPMReminder tur with(nolock) WHERE tur.NikUPM = :nik AND tur.Type = :type AND tur.Notified = 0 AND YEAR(tur.CreatedAt) = :year AND MONTH(tur.CreatedAt) = :month", nativeQuery = true)
    Optional<TrxUPMReminder> findUnnoticedReminderByMonthAndYear(String nik, String type, int month, int year);
}
