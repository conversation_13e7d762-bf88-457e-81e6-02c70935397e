package com.btpns.fin.repository;

import com.btpns.fin.model.entity.UpmTicket;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface IUpmTicketRepository extends JpaRepository<UpmTicket, String> {
    @Query(value = "SELECT * FROM (" +
            "SELECT tfr.TicketId, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, " +
            "tfr.Aplikasi, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'User ID' AS Keterangan, tfa.CurrentState, tfa.UPMInputNIK, tfa.UPMCheckerNIK, tfa.CurrentStateDT " +
            "FROM TrxFuidRequest tfr INNER JOIN TrxFuidApproval tfa on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan on tfr.Tujuan = tujuan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail alasan on tfr.Alasan = alasan.ParamDetailId " +
            "WHERE (tfa.CurrentState = :status OR 'all' = :status) " +
            "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND (('ticketId' = :searchFlag and ('' = :ticketId OR tfr.TicketId like '%' + :ticketId + '%')) OR ('nik' = :searchFlag and tfr.DataNIK IN :nik) OR ('name' = :searchFlag and tfr.DataNamaLengkap like %:name%)) " +
            "AND ('' = :upmProcess OR tfa.UPMInputNIK = :upmProcess) " +
            "UNION " +
            "SELECT tspr.TicketId, tspr.TanggalEfektif, tspr.DataNIK, tspr.DataNamaLengkap, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, " +
            "tspr.Aplikasi, tspr.KategoriParamName, tspr.AlasanPengajuan, 'Setup Parameter' AS Keterangan, tspa.CurrentState, tspa.UPMInputNIK, tspa.UPMCheckerNIK, tspa.CurrentStateDT " +
            "FROM TrxSetupParamRequest tspr INNER JOIN TrxSetupParamApproval tspa on tspr.ticketId = tspa.ticketId " +
            "WHERE (tspa.CurrentState = :status OR 'all' = :status) " +
            "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND (('ticketId' = :searchFlag and ('' = :ticketId OR tspr.TicketId like '%' + :ticketId + '%')) OR ('nik' = :searchFlag and tspr.DataNIK IN :nik) OR ('name' = :searchFlag and tspr.DataNamaLengkap like %:name%)) " +
            "AND ('' = :upmProcess OR tspa.UPMInputNIK = :upmProcess) " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC",
            countQuery = "SELECT COUNT(*) " +
                    "FROM (" +
                    "SELECT tfr.TicketId, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, " +
                    "tfr.Aplikasi, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'User ID' AS Keterangan, tfa.CurrentState, tfa.UPMInputNIK, tfa.UPMCheckerNIK, tfa.CurrentStateDT " +
                    "FROM TrxFuidRequest tfr INNER JOIN TrxFuidApproval tfa on tfr.ticketId = tfa.ticketId " +
                    "INNER JOIN MsSystemParamDetail tujuan on tfr.Tujuan = tujuan.ParamDetailId " +
                    "INNER JOIN MsSystemParamDetail alasan on tfr.Alasan = alasan.ParamDetailId " +
                    "WHERE (tfa.CurrentState = :status OR 'all' = :status) " +
                    "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND (('ticketId' = :searchFlag and ('' = :ticketId OR tfr.TicketId like '%' + :ticketId + '%')) OR ('nik' = :searchFlag and tfr.DataNIK IN :nik) OR ('name' = :searchFlag and tfr.DataNamaLengkap like %:name%)) " +
                    "AND ('' = :upmProcess OR tfa.UPMInputNIK = :upmProcess) " +
                    "UNION " +
                    "SELECT tspr.TicketId, tspr.TanggalEfektif, tspr.DataNIK, tspr.DataNamaLengkap, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, " +
                    "tspr.Aplikasi, tspr.KategoriParamName, tspr.AlasanPengajuan, 'Setup Parameter' AS Keterangan, tspa.CurrentState, tspa.UPMInputNIK, tspa.UPMCheckerNIK, tspa.CurrentStateDT " +
                    "FROM TrxSetupParamRequest tspr INNER JOIN TrxSetupParamApproval tspa on tspr.ticketId = tspa.ticketId " +
                    "WHERE (tspa.CurrentState = :status OR 'all' = :status) " +
                    "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND (('ticketId' = :searchFlag and ('' = :ticketId OR tspr.TicketId like '%' + :ticketId + '%')) OR ('nik' = :searchFlag and tspr.DataNIK IN :nik) OR ('name' = :searchFlag and tspr.DataNamaLengkap like %:name%)) " +
                    "AND ('' = :upmProcess OR tspa.UPMInputNIK = :upmProcess) " +
                    ") x ",
            nativeQuery = true)
    public Page<UpmTicket> getListTicketUPM(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                            @Param("ticketId") String ticketId, @Param("upmProcess") String upmProcess, @Param("isPending") Integer isPending,
                                            @Param("searchFlag") String searchFlag, @Param("nik") List<String> nik, @Param("name") String name);

    @Query(value = "SELECT * FROM (SELECT " +
            "tfr.TicketId, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, " +
            "tfr.Aplikasi, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'User ID' AS Keterangan, tfa.CurrentState, tfa.UPMInputNIK, tfa.UPMCheckerNIK, tfa.CurrentStateDT " +
            "FROM TrxFuidRequest tfr INNER JOIN TrxFuidApproval tfa on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan on tfr.Tujuan = tujuan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail alasan on tfr.Alasan = alasan.ParamDetailId " +
            "WHERE (tfa.CurrentState = :status OR 'all' = :status) " +
            "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND (('ticketId' = :searchFlag and ('' = :ticketId OR tfr.TicketId like '%' + :ticketId + '%')) OR ('nik' = :searchFlag and tfr.DataNIK IN :nik) OR ('name' = :searchFlag and tfr.DataNamaLengkap like %:name%)) " +
            "AND ('' = :upmProcess OR tfa.UPMInputNIK = :upmProcess) " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC",
            countQuery = "SELECT COUNT(*) " +
                    "FROM TrxFuidRequest tfr INNER JOIN TrxFuidApproval tfa on tfr.ticketId = tfa.ticketId " +
                    "INNER JOIN MsSystemParamDetail tujuan on tfr.Tujuan = tujuan.ParamDetailId " +
                    "INNER JOIN MsSystemParamDetail alasan on tfr.Alasan = alasan.ParamDetailId " +
                    "WHERE (tfa.CurrentState = :status OR 'all' = :status) " +
                    "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND (('ticketId' = :searchFlag and ('' = :ticketId OR tfr.TicketId like '%' + :ticketId + '%')) OR ('nik' = :searchFlag and tfr.DataNIK IN :nik) OR ('name' = :searchFlag and tfr.DataNamaLengkap like %:name%)) " +
                    "AND ('' = :upmProcess OR tfa.UPMInputNIK = :upmProcess) ",
            nativeQuery = true)
    public Page<UpmTicket> getListTicketFuidUPM(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                          @Param("ticketId") String ticketId, @Param("upmProcess") String upmProcess, @Param("isPending") Integer isPending,
                                          @Param("searchFlag") String searchFlag, @Param("nik") List<String> nik, @Param("name") String name);

    @Query(value = "SELECT * FROM (SELECT " +
            "tspr.TicketId, tspr.TanggalEfektif, tspr.DataNIK, tspr.DataNamaLengkap, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, " +
            "tspr.Aplikasi, tspr.KategoriParamName AS JenisPengajuan, tspr.AlasanPengajuan AS Deskripsi, 'Setup Parameter' AS Keterangan, tspa.CurrentState, tspa.UPMInputNIK, tspa.UPMCheckerNIK, tspa.CurrentStateDT " +
            "FROM TrxSetupParamRequest tspr INNER JOIN TrxSetupParamApproval tspa on tspr.ticketId = tspa.ticketId " +
            "WHERE (tspa.CurrentState = :status OR 'all' = :status) " +
            "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND (('ticketId' = :searchFlag and ('' = :ticketId OR tspr.TicketId like '%' + :ticketId + '%')) OR ('nik' = :searchFlag and tspr.DataNIK IN :nik) OR ('name' = :searchFlag and tspr.DataNamaLengkap like %:name%)) " +
            "AND ('' = :upmProcess OR tspa.UPMInputNIK = :upmProcess) " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC",
            countQuery = "SELECT COUNT(*) " +
                    "FROM TrxSetupParamRequest tspr INNER JOIN TrxSetupParamApproval tspa on tspr.ticketId = tspa.ticketId " +
                    "WHERE (tspa.CurrentState = :status OR 'all' = :status) " +
                    "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND (('ticketId' = :searchFlag and ('' = :ticketId OR tspr.TicketId like '%' + :ticketId + '%')) OR ('nik' = :searchFlag and tspr.DataNIK IN :nik) OR ('name' = :searchFlag and tspr.DataNamaLengkap like %:name%)) " +
                    "AND ('' = :upmProcess OR tspa.UPMInputNIK = :upmProcess) " ,
            nativeQuery = true)
    public Page<UpmTicket> getListTicketSetupParamUPM(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                                      @Param("ticketId") String ticketId, @Param("upmProcess") String upmProcess, @Param("isPending") Integer isPending,
                                                      @Param("searchFlag") String searchFlag, @Param("nik") List<String> nik, @Param("name") String name);
}
