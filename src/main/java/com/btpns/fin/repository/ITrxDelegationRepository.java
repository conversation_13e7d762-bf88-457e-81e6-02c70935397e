package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxDelegation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public interface ITrxDelegationRepository extends JpaRepository<TrxDelegation, String> {
    @Query(value = "SELECT td.delegationId FROM TrxDelegation td with(nolock) ORDER BY td.delegationId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    public String getLastDelegationId();

    @Query(value = "SELECT * FROM TrxDelegation td with(nolock) WHERE td.nikRequester = :nikRequester " +
            "AND :dateNow BETWEEN td.StartDate AND td.EndDate " +
            "AND td.Status = :status", nativeQuery = true)
    public TrxDelegation getTrxDelegationByNikRequester(@Param("nikRequester") String nikRequester, @Param("dateNow") Date dateNow, @Param("status") String status);

    @Query(value = "SELECT * FROM TrxDelegation td with(nolock) WHERE td.delegationId = :delegationId", nativeQuery = true)
    public TrxDelegation getTrxDelegationByDelegationId(@Param("delegationId") String delegationId);

    @Modifying
    @Query(value = "DELETE FROM TrxDelegation WHERE nikRequester = :nikRequester", nativeQuery = true)
    public int deleteTrxDelegationByNikRequester(@Param("nikRequester") String nikRequester);

    @Query(value = "SELECT * FROM TrxDelegation td with(nolock) WHERE td.nikRequester = :nikRequester", nativeQuery = true)
    public Page<TrxDelegation> getListTrxDelegationByNikRequester(@Param("nikRequester") String nikRequester, @Param("pageable") Pageable pageable);

    @Query(value = "SELECT * FROM TrxDelegation td with(nolock) WHERE " +
            "(StartDate BETWEEN :startDate and :endDate " +
            " or EndDate BETWEEN :startDate and :endDate " +
            " or :startDate BETWEEN StartDate and EndDate " +
            " or :endDate BETWEEN StartDate and EndDate) and status = :status " +
            "ORDER BY td.delegationId DESC ", nativeQuery = true)
    public Page<TrxDelegation> getListTrxDelegationByDateAndStatus(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("pageable") Pageable pageable, @Param("status") String status);

    @Query(value = "SELECT * FROM TrxDelegation td with(nolock) WHERE " +
            "(StartDate BETWEEN :startDate and :endDate " +
            " or EndDate BETWEEN :startDate and :endDate " +
            " or :startDate BETWEEN StartDate and EndDate " +
            " or :endDate BETWEEN StartDate and EndDate) and status = :status " +
            "ORDER BY td.delegationId DESC", nativeQuery = true)
    public List<TrxDelegation> getListTrxDelegationByDateAndStatus(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("status") String status);

    @Query(value = "SELECT * FROM TrxDelegation td with(nolock) WHERE td.nikRequester = :nikRequester " +
            "AND td.Status = :status", nativeQuery = true)
    public TrxDelegation getDelegationActiveByNikRequester(@Param("nikRequester") String nikRequester, @Param("status") String status);

    @Query(value = "SELECT count(*) FROM TrxDelegation tfr with(nolock) WHERE createDateTime between :startDate and :endDate and NIKRequester = :nikRequester", nativeQuery = true)
    public Integer checkRequestInterval(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("nikRequester") String nikRequester);
}
