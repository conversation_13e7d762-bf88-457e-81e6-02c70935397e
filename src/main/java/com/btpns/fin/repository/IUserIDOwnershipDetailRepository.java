package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsUserIDOwnershipDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface IUserIDOwnershipDetailRepository extends JpaRepository<MsUserIDOwnershipDetail, String> {

    @Query(value = "select me.NIK, me.FullName as <PERSON><PERSON><PERSON><PERSON>, me.Organization as <PERSON><PERSON><PERSON><PERSON>, me.OccupationDesc as Jaba<PERSON>, me.Location as UnitKerja, 'Email/LDAP' as Aplikasi " +
            "FROM MsEmployeeHierarchy me " +
            "WHERE me.NIK = :nik " +
            "UNION ALL " +
            "select tp.NIKVendor as NIK, tp.NameVendor as <PERSON>aUser, '' as Kewenangan, tp.OccupationDescVendor as <PERSON><PERSON><PERSON>, '' as Unit<PERSON><PERSON><PERSON>, 'Email/LDAP' as Aplikasi " +
            "FROM TrxPUKVendor tp " +
            "WHERE tp.NIKVendor = :nik " +
            "UNION ALL " +
            "SELECT rtgs.NIK, rtgs.<PERSON><PERSON>, rtgs.Kewenangan, rtgs.Jabatan, rtgs.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsDboRTGS rtgs  " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000001' = mui.ParamDetailId  " +
            "WHERE rtgs.NIK = :nik AND mui.Visible = 1 " +
            "UNION ALL " +
            "SELECT s4.NIK, s4.NamaUser, s4.Kewenangan, s4.Jabatan, s4.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsS4 s4  " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000002' = mui.ParamDetailId  " +
            "WHERE s4.NIK = :nik AND mui.Visible = 1 " +
            "UNION ALL " +
            "SELECT spk.NIK, spk.NamaUser, spk.Kewenangan, spk.Jabatan, spk.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsSPK spk " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000003' = mui.ParamDetailId  " +
            "WHERE spk.NIK = :nik AND mui.Visible = 1 " +
            "UNION ALL " +
            "SELECT slik.NIK, slik.NamaUser, slik.Kewenangan, slik.Jabatan, slik.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsSlik slik " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000004' = mui.ParamDetailId  " +
            "WHERE slik.NIK = :nik AND mui.Visible = 1 " +
            "UNION ALL " +
            "SELECT cms.NIK, cms.NamaUser, cms.Kewenangan, cms.Jabatan, cms.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsCMS cms  " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000005' = mui.ParamDetailId  " +
            "WHERE cms.NIK = :nik AND mui.Visible = 1 " +
            "UNION ALL " +
            "SELECT mtmi.NIK, mtmi.NamaUser, mtmi.Kewenangan, mtmi.Jabatan, mtmi.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsTepatMBankingIndividu mtmi  " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000006' = mui.ParamDetailId  " +
            "WHERE mtmi.NIK = :nik AND mui.Visible = 1 " +
            "UNION ALL " +
            "SELECT mtmc.NIK, mtmc.NamaUser, mtmc.Kewenangan, mtmc.Jabatan, mtmc.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsTepatMBankingCorporate mtmc  " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000007' = mui.ParamDetailId  " +
            "WHERE mtmc.NIK = :nik AND mui.Visible = 1 " +
            "UNION ALL " +
            "SELECT bicac.IdUser as NIK, bicac.NamaUser, bicac.GrupUser as Kewenangan, '' as Jabatan, bicac.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsBiCAC bicac  " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000009' = mui.ParamDetailId  " +
            "WHERE bicac.NIK = :nik AND mui.Visible = 1 " +
            "UNION ALL " +
            "SELECT digitus.NIK, digitus.NamaUser, digitus.Kewenangan, digitus.Jabatan, " +
            "CASE " +
                "WHEN EXISTS(SELECT * FROM MsEmployeeHierarchy meh where meh.NIK = :nik) THEN optima.Location " +
                "ELSE '' " +
            "END as UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsDigitus digitus  " +
            "INNER JOIN MsUserIDApplication mui on 'AU00000010' = mui.ParamDetailId  " +
            "LEFT JOIN MsEmployeeHierarchy optima on digitus.NIK = optima.NIK " +
            "WHERE digitus.NIK = :nik " +
            "UNION ALL " +
            "SELECT cu.NIK, cu.namaUser, cu.kewenangan, cu.jabatan, cu.UnitKerja, mui.ParamDetailDesc AS Aplikasi " +
            "FROM MsCustomUserID cu " +
            "INNER JOIN MsUserIDApplication mui ON cu.paramDetailId = mui.ParamDetailId " +
            "WHERE cu.NIK = :nik AND mui.Visible = 1", nativeQuery = true)
    List<MsUserIDOwnershipDetail> findAllOwnedActiveUserID(String nik);
}
