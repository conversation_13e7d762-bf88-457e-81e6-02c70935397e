package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsProsperaRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IMsProsperaRoleRepository extends JpaRepository<MsProsperaRole,String> {
    MsProsperaRole findAllByTemaRoleCode(String role);

    MsProsperaRole findAllByRoleDesc(String role);

    List<MsProsperaRole> findBySystemParamIdIn(List<String> paramIds);

    @Query(value = "SELECT * FROM MsProsperaRole with(nolock) WHERE SystemParamId IN(:paramIds) AND IsActive = 1", nativeQuery = true)
    List<MsProsperaRole> findActiveBySystemParamIdIn(List<String> paramIds);

    MsProsperaRole findByProsperaRoleCode(Integer prosperaRoleCode);

    @Query(value = "SELECT mpr.TemaRoleCode FROM MsProsperaRole mpr with(nolock) WHERE mpr.SystemParamId = :paramId ORDER BY mpr.TemaRoleCode DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastTemaRoleCodeByParamId(String paramId);
}
