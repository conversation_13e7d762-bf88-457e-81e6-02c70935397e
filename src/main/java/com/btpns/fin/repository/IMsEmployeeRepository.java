package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsEmployee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IMsEmployeeRepository extends JpaRepository<MsEmployee,String> {
    @Query(value = "SELECT * FROM MsEmployee me with(nolock) where me.nik = :nik", nativeQuery = true)
    public MsEmployee getMsEmployeeByNik(@Param("nik") String nik);

    @Query(value = "SELECT * FROM MsEmployee me with(nolock) where me.nik IN :nik", nativeQuery = true)
    public List<MsEmployee> getMsEmployee(@Param("nik") List<String> nik);

    @Query(value = "SELECT * FROM MsEmployee me with(nolock) where me.fullName LIKE %:name%", nativeQuery = true)
    public List<MsEmployee> getMsEmployeeByName(@Param("name") String name);

    @Query(value = "SELECT * FROM MsEmployee with(nolock) where DirectSupervisorNIK = :nik and StatusEmployeeDesc = 'Active Assignment'", nativeQuery = true)
    List<MsEmployee> getListEmployeesActiveByDirectSupervisorNIK(String nik);
}
