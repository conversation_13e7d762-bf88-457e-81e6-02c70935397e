package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxUARSummary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.math.BigInteger;

public interface ITrxUARSummaryRepository extends JpaRepository<TrxUARSummary, BigInteger> {
    @Query(value = "SELECT * FROM TrxUARSummary tus with(nolock) " +
            "WHERE ((:aplikasi = 'all') OR (tus.Aplikasi = :aplikasi)) " +
            "AND ((:periodYear = -1) OR (tus.PeriodYear = :periodYear)) " +
            "AND ((:periodQuarter = 'all') OR (tus.PeriodQuarter = :periodQuarter)) " +
            "AND ((:refNumber = 'all') OR (tus.RefNumber LIKE '%' + :refNumber + '%')) " +
            "ORDER BY tus.CreatedAt DESC", nativeQuery = true)
    Page<TrxUARSummary> findByRefNumberByTriwulanByTahunByAplikasi(String refNumber, String periodQuarter, Integer periodYear, String aplikasi, Pageable pageable);
}
