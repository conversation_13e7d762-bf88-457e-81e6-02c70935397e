package com.btpns.fin.repository;

import com.btpns.fin.model.entity.AnalyticCount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IAnalyticCountRepository extends JpaRepository<AnalyticCount,String> {
    @Query(value = "SELECT tur.NIK AS nik, Nama AS nama, " +
            "(ISNULL(countTicketInprogressSP,0) + ISNULL(countTicketInprogressFU,0)) AS countTicketInprogress, " +
            "(ISNULL(countTicketVerificationSP,0) + ISNULL(countTicketVerificationFU,0)) AS countTicketVerification, " +
            "(ISNULL(countTicketDoneSP,0) + ISNULL(countTicketDoneFU,0)) AS countTicketDone  " +
            "FROM TrxUpmRole tur  " +
            "LEFT JOIN ( " +
            "SELECT UPMInputNIK AS nik, COUNT(tspa.TicketId) AS countTicketInprogressSP  " +
            "FROM TrxSetupParamApproval tspa WITH (NOLOCK) " +
            "WHERE tspa.CurrentState = 'inprogress_upm' " +
            "AND tspa.CurrentStateDT BETWEEN :startDate AND :endDate " +
            "GROUP BY UPMInputNIK " +
            ") a ON a.nik = tur.NIK " +
            "LEFT JOIN ( " +
            "SELECT UPMInputNIK AS nik, COUNT(tfa.TicketId) AS countTicketInprogressFU " +
            "FROM TrxFuidApproval tfa WITH (NOLOCK) " +
            "WHERE tfa.CurrentState = 'inprogress_upm' " +
            "AND tfa.CurrentStateDT BETWEEN :startDate AND :endDate " +
            "GROUP BY UPMInputNIK " +
            ") b ON b.nik = tur.NIK " +
            "LEFT JOIN ( " +
            "SELECT UPMInputNIK AS nik, COUNT(tspa.TicketId) AS countTicketVerificationSP  " +
            "FROM TrxSetupParamApproval tspa WITH (NOLOCK) " +
            "WHERE tspa.CurrentState = 'verification_upm' " +
            "AND tspa.CurrentStateDT BETWEEN :startDate AND :endDate " +
            "GROUP BY UPMInputNIK " +
            ") c ON c.nik = tur.NIK " +
            "LEFT JOIN ( " +
            "SELECT UPMInputNIK AS nik, COUNT(tfa.TicketId) AS countTicketVerificationFU " +
            "FROM TrxFuidApproval tfa WITH (NOLOCK) " +
            "WHERE tfa.CurrentState = 'verification_upm' " +
            "AND tfa.CurrentStateDT BETWEEN :startDate AND :endDate " +
            "GROUP BY UPMInputNIK " +
            ") d ON d.nik = tur.NIK " +
            "LEFT JOIN ( " +
            "SELECT UPMInputNIK AS nik, COUNT(tspa.TicketId) AS countTicketDoneSP  " +
            "FROM TrxSetupParamApproval tspa WITH (NOLOCK) " +
            "WHERE tspa.CurrentState = 'done_upm' " +
            "AND tspa.CurrentStateDT BETWEEN :startDate AND :endDate " +
            "GROUP BY UPMInputNIK " +
            ") e ON e.nik = tur.NIK " +
            "LEFT JOIN ( " +
            "SELECT UPMInputNIK AS nik, COUNT(tfa.TicketId) AS countTicketDoneFU " +
            "FROM TrxFuidApproval tfa WITH (NOLOCK) " +
            "WHERE tfa.CurrentState = 'done_upm' " +
            "AND tfa.CurrentStateDT BETWEEN :startDate AND :endDate " +
            "GROUP BY UPMInputNIK " +
            ") f ON f.nik = tur.NIK " +
            "WHERE (('Checker' = :role and Role in ('Checker','Admin')) or Role = :role) " +
            "AND ('0' in ('0') OR tur.NIK in ('0'))", nativeQuery = true)
    public List<AnalyticCount> getCountAnalyticMaker(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("role") String role);
}
