package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsUserIDOwnership;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IUserIDOwnershipRepository extends JpaRepository<MsUserIDOwnership, String> {
    @Query(value = "Select * From ( " +
            "Select meh.NIK, meh.FullName as Nama, meh.OccupationDesc as <PERSON><PERSON><PERSON>, " +
            "REVERSE( " +
               "SUBSTRING( " +
                 "REVERSE( " +
                   "CONCAT( " +
                     "'Email/LDAP, ', " +
                     "IIF(meh.NIK = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011' and Visible = 1), NULL), " +
                     "IIF(meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                     "IIF(meh.NIK = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                   ") " +
                 ") " +
               ", 3, 9999) " +
            ")as UserID " +
            "FROM MsEmployeeHierarchy meh " +
            "LEFT JOIN MsOfficerNR prospera on meh.NIK = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
            "LEFT JOIN MsOfficerNR egls on meh.NIK = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
            "LEFT JOIN MsOfficerNR t24 on meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
            "LEFT JOIN MsDboRTGS dboRTGS on meh.NIK = dboRTGS.NIK " +
            "LEFT JOIN MsS4 s4 on meh.NIK = s4.NIK " +
            "LEFT JOIN MsSPK spk on meh.NIK = spk.NIK " +
            "LEFT JOIN MsSlik slik on meh.NIK = slik.NIK " +
            "LEFT JOIN MsCMS cms on meh.NIK = cms.NIK " +
            "LEFT JOIN MsTepatMBankingIndividu individu on meh.NIK = individu.NIK " +
            "LEFT JOIN MsTepatMBankingCorporate corporate on meh.NIK = corporate.NIK " +
            "LEFT JOIN MsBiCAC bicac on meh.NIK = bicac.NIK " +
            "LEFT JOIN MsDigitus digitus on meh.NIK = digitus.NIK " +
            "LEFT JOIN MsCustomUserID customId on meh.NIK = customId.NIK " +
            "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
            "WHERE meh.DTTermination > GETDATE() OR meh.DTTermination LIKE '' OR meh.DTTermination IS NULL " +
            "GROUP BY meh.NIK, meh.FullName, meh.OccupationDesc, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
            "UNION " +
            "Select tpv.NIKVendor, tpv.NameVendor as Nama, tpv.OccupationDescVendor as Jabatan, " +
            "REVERSE( " +
               "SUBSTRING( " +
                 "REVERSE( " +
                   "CONCAT( " +
                     "'Email/LDAP, ', " +
                     "IIF(tpv.NIKVendor = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                     "IIF(tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                   ") " +
                 ") " +
               ", 3, 9999) " +
            ")as UserID " +
            "FROM TrxPUKVendor tpv " +
            "LEFT JOIN MsOfficerNR prospera on tpv.NIKVendor = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
            "LEFT JOIN MsOfficerNR egls on tpv.NIKVendor = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
            "LEFT JOIN MsOfficerNR t24 on tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
            "LEFT JOIN MsDboRTGS dboRTGS on tpv.NIKVendor = dboRTGS.NIK " +
            "LEFT JOIN MsS4 s4 on tpv.NIKVendor = s4.NIK " +
            "LEFT JOIN MsSPK spk on tpv.NIKVendor = spk.NIK " +
            "LEFT JOIN MsSlik slik on tpv.NIKVendor = slik.NIK " +
            "LEFT JOIN MsCMS cms on tpv.NIKVendor = cms.NIK " +
            "LEFT JOIN MsTepatMBankingIndividu individu on tpv.NIKVendor = individu.NIK " +
            "LEFT JOIN MsTepatMBankingCorporate corporate on tpv.NIKVendor = corporate.NIK " +
            "LEFT JOIN MsBiCAC bicac on tpv.NIKVendor = bicac.NIK " +
            "LEFT JOIN MsDigitus digitus on tpv.NIKVendor = digitus.NIK " +
            "LEFT JOIN MsCustomUserID customId on tpv.NIKVendor = customId.NIK " +
            "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
            "GROUP BY tpv.NIKVendor, tpv.NameVendor, tpv.OccupationDescVendor, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
            ") x ",
            countQuery = "Select COUNT(*) From ( " +
                    "Select meh.NIK, meh.FullName as Nama, meh.OccupationDesc as Jabatan, " +
                    "REVERSE( " +
                       "SUBSTRING( " +
                         "REVERSE( " +
                           "CONCAT( " +
                             "'Email/LDAP, ', " +
                             "IIF(meh.NIK = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                             "IIF(meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                             "IIF(meh.NIK = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1),NULL), " +
                             "IIF(meh.NIK = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                           ") " +
                         ") " +
                       ", 3, 9999) " +
                    ")as UserID " +
                    "FROM MsEmployeeHierarchy meh " +
                    "LEFT JOIN MsOfficerNR prospera on meh.NIK = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
                    "LEFT JOIN MsOfficerNR egls on meh.NIK = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
                    "LEFT JOIN MsOfficerNR t24 on meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
                    "LEFT JOIN MsDboRTGS dboRTGS on meh.NIK = dboRTGS.NIK " +
                    "LEFT JOIN MsS4 s4 on meh.NIK = s4.NIK " +
                    "LEFT JOIN MsSPK spk on meh.NIK = spk.NIK " +
                    "LEFT JOIN MsSlik slik on meh.NIK = slik.NIK " +
                    "LEFT JOIN MsCMS cms on meh.NIK = cms.NIK " +
                    "LEFT JOIN MsTepatMBankingIndividu individu on meh.NIK = individu.NIK " +
                    "LEFT JOIN MsTepatMBankingCorporate corporate on meh.NIK = corporate.NIK " +
                    "LEFT JOIN MsBiCAC bicac on meh.NIK = bicac.NIK " +
                    "LEFT JOIN MsDigitus digitus on meh.NIK = digitus.NIK " +
                    "LEFT JOIN MsCustomUserID customId on meh.NIK = customId.NIK " +
                    "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
                    "WHERE meh.DTTermination > GETDATE() OR meh.DTTermination LIKE '' OR meh.DTTermination IS NULL " +
                    "GROUP BY meh.NIK, meh.FullName, meh.OccupationDesc, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
                    "UNION " +
                    "Select tpv.NIKVendor, tpv.NameVendor as Nama, tpv.OccupationDescVendor as Jabatan, " +
                    "REVERSE( " +
                       "SUBSTRING( " +
                         "REVERSE( " +
                           "CONCAT( " +
                             "'Email/LDAP, ', " +
                             "IIF(tpv.NIKVendor = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                             "IIF(tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                           ") " +
                         ") " +
                       ", 3, 9999) " +
                    ")as UserID " +
                    "FROM TrxPUKVendor tpv " +
                    "LEFT JOIN MsOfficerNR prospera on tpv.NIKVendor = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
                    "LEFT JOIN MsOfficerNR egls on tpv.NIKVendor = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
                    "LEFT JOIN MsOfficerNR t24 on tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
                    "LEFT JOIN MsDboRTGS dboRTGS on tpv.NIKVendor = dboRTGS.NIK " +
                    "LEFT JOIN MsS4 s4 on tpv.NIKVendor = s4.NIK " +
                    "LEFT JOIN MsSPK spk on tpv.NIKVendor = spk.NIK " +
                    "LEFT JOIN MsSlik slik on tpv.NIKVendor = slik.NIK " +
                    "LEFT JOIN MsCMS cms on tpv.NIKVendor = cms.NIK " +
                    "LEFT JOIN MsTepatMBankingIndividu individu on tpv.NIKVendor = individu.NIK " +
                    "LEFT JOIN MsTepatMBankingCorporate corporate on tpv.NIKVendor = corporate.NIK " +
                    "LEFT JOIN MsBiCAC bicac on tpv.NIKVendor = bicac.NIK " +
                    "LEFT JOIN MsDigitus digitus on tpv.NIKVendor = digitus.NIK " +
                    "LEFT JOIN MsCustomUserID customId on tpv.NIKVendor = customId.NIK " +
                    "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
                    "GROUP BY tpv.NIKVendor, tpv.NameVendor, tpv.OccupationDescVendor, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
                    ") x ",
            nativeQuery = true)
    Page<MsUserIDOwnership> findAllUserIDOwnership(Pageable pageable);

    @Query(value = "Select * From ( " +
            "Select meh.NIK, meh.FullName as Nama, meh.OccupationDesc as Jabatan, " +
            "REVERSE( " +
               "SUBSTRING( " +
                 "REVERSE( " +
                   "CONCAT( " +
                     "'Email/LDAP, ', " +
                     "IIF(meh.NIK = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                     "IIF(meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                     "IIF(meh.NIK = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1),NULL), " +
                     "IIF(meh.NIK = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                   ") " +
                 ") " +
               ", 3, 9999) " +
            ")as UserID " +
            "FROM MsEmployeeHierarchy meh " +
            "LEFT JOIN MsOfficerNR prospera on meh.NIK = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
            "LEFT JOIN MsOfficerNR egls on meh.NIK = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
            "LEFT JOIN MsOfficerNR t24 on meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
            "LEFT JOIN MsDboRTGS dboRTGS on meh.NIK = dboRTGS.NIK " +
            "LEFT JOIN MsS4 s4 on meh.NIK = s4.NIK " +
            "LEFT JOIN MsSPK spk on meh.NIK = spk.NIK " +
            "LEFT JOIN MsSlik slik on meh.NIK = slik.NIK " +
            "LEFT JOIN MsCMS cms on meh.NIK = cms.NIK " +
            "LEFT JOIN MsTepatMBankingIndividu individu on meh.NIK = individu.NIK " +
            "LEFT JOIN MsTepatMBankingCorporate corporate on meh.NIK = corporate.NIK " +
            "LEFT JOIN MsBiCAC bicac on meh.NIK = bicac.NIK " +
            "LEFT JOIN MsDigitus digitus on meh.NIK = digitus.NIK " +
            "LEFT JOIN MsCustomUserID customId on meh.NIK = customId.NIK " +
            "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
            "WHERE meh.DTTermination > GETDATE() OR meh.DTTermination LIKE '' OR meh.DTTermination IS NULL " +
            "GROUP BY meh.NIK, meh.FullName, meh.OccupationDesc, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
            "UNION " +
            "Select tpv.NIKVendor, tpv.NameVendor as Nama, tpv.OccupationDescVendor as Jabatan, " +
            "REVERSE( " +
               "SUBSTRING( " +
                 "REVERSE( " +
                   "CONCAT( " +
                     "'Email/LDAP, ', " +
                     "IIF(tpv.NIKVendor = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                     "IIF(tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                   ") " +
                 ") " +
               ", 3, 9999) " +
            ")as UserID " +
            "FROM TrxPUKVendor tpv " +
            "LEFT JOIN MsOfficerNR prospera on tpv.NIKVendor = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
            "LEFT JOIN MsOfficerNR egls on tpv.NIKVendor = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
            "LEFT JOIN MsOfficerNR t24 on tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
            "LEFT JOIN MsDboRTGS dboRTGS on tpv.NIKVendor = dboRTGS.NIK " +
            "LEFT JOIN MsS4 s4 on tpv.NIKVendor = s4.NIK " +
            "LEFT JOIN MsSPK spk on tpv.NIKVendor = spk.NIK " +
            "LEFT JOIN MsSlik slik on tpv.NIKVendor = slik.NIK " +
            "LEFT JOIN MsCMS cms on tpv.NIKVendor = cms.NIK " +
            "LEFT JOIN MsTepatMBankingIndividu individu on tpv.NIKVendor = individu.NIK " +
            "LEFT JOIN MsTepatMBankingCorporate corporate on tpv.NIKVendor = corporate.NIK " +
            "LEFT JOIN MsBiCAC bicac on tpv.NIKVendor = bicac.NIK " +
            "LEFT JOIN MsDigitus digitus on tpv.NIKVendor = digitus.NIK " +
            "LEFT JOIN MsCustomUserID customId on tpv.NIKVendor = customId.NIK " +
            "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
            "GROUP BY tpv.NIKVendor, tpv.NameVendor, tpv.OccupationDescVendor, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
            ") x WHERE x.NIK LIKE %:searchData% ",
            countQuery = "Select COUNT(*) From ( " +
                    "Select meh.NIK, meh.FullName as Nama, meh.OccupationDesc as Jabatan, " +
                    "REVERSE( " +
                       "SUBSTRING( " +
                         "REVERSE( " +
                           "CONCAT( " +
                             "'Email/LDAP, ', " +
                             "IIF(meh.NIK = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                             "IIF(meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                             "IIF(meh.NIK = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1),NULL), " +
                             "IIF(meh.NIK = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                           ") " +
                         ") " +
                       ", 3, 9999) " +
                    ")as UserID " +
                    "FROM MsEmployeeHierarchy meh " +
                    "LEFT JOIN MsOfficerNR prospera on meh.NIK = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
                    "LEFT JOIN MsOfficerNR egls on meh.NIK = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
                    "LEFT JOIN MsOfficerNR t24 on meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
                    "LEFT JOIN MsDboRTGS dboRTGS on meh.NIK = dboRTGS.NIK " +
                    "LEFT JOIN MsS4 s4 on meh.NIK = s4.NIK " +
                    "LEFT JOIN MsSPK spk on meh.NIK = spk.NIK " +
                    "LEFT JOIN MsSlik slik on meh.NIK = slik.NIK " +
                    "LEFT JOIN MsCMS cms on meh.NIK = cms.NIK " +
                    "LEFT JOIN MsTepatMBankingIndividu individu on meh.NIK = individu.NIK " +
                    "LEFT JOIN MsTepatMBankingCorporate corporate on meh.NIK = corporate.NIK " +
                    "LEFT JOIN MsBiCAC bicac on meh.NIK = bicac.NIK " +
                    "LEFT JOIN MsDigitus digitus on meh.NIK = digitus.NIK " +
                    "LEFT JOIN MsCustomUserID customId on meh.NIK = customId.NIK " +
                    "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
                    "WHERE meh.DTTermination > GETDATE() OR meh.DTTermination LIKE '' OR meh.DTTermination IS NULL " +
                    "GROUP BY meh.NIK, meh.FullName, meh.OccupationDesc, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
                    "UNION " +
                    "Select tpv.NIKVendor, tpv.NameVendor as Nama, tpv.OccupationDescVendor as Jabatan, " +
                    "REVERSE( " +
                       "SUBSTRING( " +
                         "REVERSE( " +
                           "CONCAT( " +
                             "'Email/LDAP, ', " +
                             "IIF(tpv.NIKVendor = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                             "IIF(tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                           ") " +
                         ") " +
                       ", 3, 9999) " +
                    ")as UserID " +
                    "FROM TrxPUKVendor tpv " +
                    "LEFT JOIN MsOfficerNR prospera on tpv.NIKVendor = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
                    "LEFT JOIN MsOfficerNR egls on tpv.NIKVendor = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
                    "LEFT JOIN MsOfficerNR t24 on tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
                    "LEFT JOIN MsDboRTGS dboRTGS on tpv.NIKVendor = dboRTGS.NIK " +
                    "LEFT JOIN MsS4 s4 on tpv.NIKVendor = s4.NIK " +
                    "LEFT JOIN MsSPK spk on tpv.NIKVendor = spk.NIK " +
                    "LEFT JOIN MsSlik slik on tpv.NIKVendor = slik.NIK " +
                    "LEFT JOIN MsCMS cms on tpv.NIKVendor = cms.NIK " +
                    "LEFT JOIN MsTepatMBankingIndividu individu on tpv.NIKVendor = individu.NIK " +
                    "LEFT JOIN MsTepatMBankingCorporate corporate on tpv.NIKVendor = corporate.NIK " +
                    "LEFT JOIN MsBiCAC bicac on tpv.NIKVendor = bicac.NIK " +
                    "LEFT JOIN MsDigitus digitus on tpv.NIKVendor = digitus.NIK " +
                    "LEFT JOIN MsCustomUserID customId on tpv.NIKVendor = customId.NIK " +
                    "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
                    "GROUP BY tpv.NIKVendor, tpv.NameVendor, tpv.OccupationDescVendor, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
                    ") x WHERE x.NIK LIKE %:searchData%",
            nativeQuery = true)
    Page<MsUserIDOwnership> findUserIDOwnershipByNIK(String searchData, Pageable pageable);

    @Query(value = "Select * From ( " +
            "Select meh.NIK, meh.FullName as Nama, meh.OccupationDesc as Jabatan, " +
            "REVERSE( " +
               "SUBSTRING( " +
                 "REVERSE( " +
                   "CONCAT( " +
                     "'Email/LDAP, ', " +
                     "IIF(meh.NIK = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                     "IIF(meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                     "IIF(meh.NIK = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                     "IIF(meh.NIK = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1),NULL), " +
                     "IIF(meh.NIK = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                   ") " +
                 ") " +
               ", 3, 9999) " +
            ")as UserID " +
            "FROM MsEmployeeHierarchy meh " +
            "LEFT JOIN MsOfficerNR prospera on meh.NIK = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
            "LEFT JOIN MsOfficerNR egls on meh.NIK = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
            "LEFT JOIN MsOfficerNR t24 on meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
            "LEFT JOIN MsDboRTGS dboRTGS on meh.NIK = dboRTGS.NIK " +
            "LEFT JOIN MsS4 s4 on meh.NIK = s4.NIK " +
            "LEFT JOIN MsSPK spk on meh.NIK = spk.NIK " +
            "LEFT JOIN MsSlik slik on meh.NIK = slik.NIK " +
            "LEFT JOIN MsCMS cms on meh.NIK = cms.NIK " +
            "LEFT JOIN MsTepatMBankingIndividu individu on meh.NIK = individu.NIK " +
            "LEFT JOIN MsTepatMBankingCorporate corporate on meh.NIK = corporate.NIK " +
            "LEFT JOIN MsBiCAC bicac on meh.NIK = bicac.NIK " +
            "LEFT JOIN MsDigitus digitus on meh.NIK = digitus.NIK " +
            "LEFT JOIN MsCustomUserID customId on meh.NIK = customId.NIK " +
            "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
            "WHERE meh.DTTermination > GETDATE() OR meh.DTTermination LIKE '' OR meh.DTTermination IS NULL " +
            "GROUP BY meh.NIK, meh.FullName, meh.OccupationDesc, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
            "UNION " +
            "Select tpv.NIKVendor, tpv.NameVendor as Nama, tpv.OccupationDescVendor as Jabatan, " +
            "REVERSE( " +
               "SUBSTRING( " +
                 "REVERSE( " +
                   "CONCAT( " +
                     "'Email/LDAP, ', " +
                     "IIF(tpv.NIKVendor = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                     "IIF(tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1), NULL), " +
                     "IIF(tpv.NIKVendor = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                   ") " +
                 ") " +
               ", 3, 9999) " +
            ")as UserID " +
            "FROM TrxPUKVendor tpv " +
            "LEFT JOIN MsOfficerNR prospera on tpv.NIKVendor = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
            "LEFT JOIN MsOfficerNR egls on tpv.NIKVendor = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
            "LEFT JOIN MsOfficerNR t24 on tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
            "LEFT JOIN MsDboRTGS dboRTGS on tpv.NIKVendor = dboRTGS.NIK " +
            "LEFT JOIN MsS4 s4 on tpv.NIKVendor = s4.NIK " +
            "LEFT JOIN MsSPK spk on tpv.NIKVendor = spk.NIK " +
            "LEFT JOIN MsSlik slik on tpv.NIKVendor = slik.NIK " +
            "LEFT JOIN MsCMS cms on tpv.NIKVendor = cms.NIK " +
            "LEFT JOIN MsTepatMBankingIndividu individu on tpv.NIKVendor = individu.NIK " +
            "LEFT JOIN MsTepatMBankingCorporate corporate on tpv.NIKVendor = corporate.NIK " +
            "LEFT JOIN MsBiCAC bicac on tpv.NIKVendor = bicac.NIK " +
            "LEFT JOIN MsDigitus digitus on tpv.NIKVendor = digitus.NIK " +
            "LEFT JOIN MsCustomUserID customId on tpv.NIKVendor = customId.NIK " +
            "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
            "GROUP BY tpv.NIKVendor, tpv.NameVendor, tpv.OccupationDescVendor, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
            ") x WHERE x.Nama LIKE %:searchData% ",
            countQuery = "Select COUNT(*) From ( " +
                    "Select meh.NIK, meh.FullName as Nama, meh.OccupationDesc as Jabatan, " +
                    "REVERSE( " +
                       "SUBSTRING( " +
                         "REVERSE( " +
                           "CONCAT( " +
                             "'Email/LDAP, ', " +
                             "IIF(meh.NIK = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                             "IIF(meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                             "IIF(meh.NIK = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                             "IIF(meh.NIK = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1),NULL), " +
                             "IIF(meh.NIK = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                           ") " +
                         ") " +
                       ", 3, 9999) " +
                    ")as UserID " +
                    "FROM MsEmployeeHierarchy meh " +
                    "LEFT JOIN MsOfficerNR prospera on meh.NIK = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
                    "LEFT JOIN MsOfficerNR egls on meh.NIK = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
                    "LEFT JOIN MsOfficerNR t24 on meh.NIK LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
                    "LEFT JOIN MsDboRTGS dboRTGS on meh.NIK = dboRT GS.NIK " +
                    "LEFT JOIN MsS4 s4 on meh.NIK = s4.NIK " +
                    "LEFT JOIN MsSPK spk on meh.NIK = spk.NIK " +
                    "LEFT JOIN MsSlik slik on meh.NIK = slik.NIK " +
                    "LEFT JOIN MsCMS cms on meh.NIK = cms.NIK " +
                    "LEFT JOIN MsTepatMBankingIndividu individu on meh.NIK = individu.NIK " +
                    "LEFT JOIN MsTepatMBankingCorporate corporate on meh.NIK = corporate.NIK " +
                    "LEFT JOIN MsBiCAC bicac on meh.NIK = bicac.NIK " +
                    "LEFT JOIN MsDigitus digitus on meh.NIK = digitus.NIK " +
                    "LEFT JOIN MsCustomUserID customId on meh.NIK = customId.NIK " +
                    "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
                    "WHERE meh.DTTermination > GETDATE() OR meh.DTTermination LIKE '' OR meh.DTTermination IS NULL " +
                    "GROUP BY meh.NIK, meh.FullName, meh.OccupationDesc, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
                    "UNION " +
                    "Select tpv.NIKVendor, tpv.NameVendor as Nama, tpv.OccupationDescVendor as Jabatan, " +
                    "REVERSE( " +
                       "SUBSTRING( " +
                         "REVERSE( " +
                           "CONCAT( " +
                             "'Email/LDAP, ', " +
                             "IIF(tpv.NIKVendor = prospera.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = egls.OfficerName, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                             "IIF(tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%'), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = dboRTGS.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = s4.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = spk.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = slik.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = cms.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = individu.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = corporate.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = bicac.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = digitus.NIK, (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1), NULL), " +
                             "IIF(tpv.NIKVendor = customId.NIK, STRING_AGG(mui.ParamDetailDesc+ ', ', ''), NULL) " +
                           ") " +
                         ") " +
                       ", 3, 9999) " +
                    ")as UserID " +
                    "FROM TrxPUKVendor tpv " +
                    "LEFT JOIN MsOfficerNR prospera on tpv.NIKVendor = prospera.NIK and prospera.SrcSystem = 'PROSPERA' and prospera.RoleID IS NOT NULL and prospera.OfficerStatusCode = 1 " +
                    "LEFT JOIN MsOfficerNR egls on tpv.NIKVendor = egls.OfficerName and egls.SrcSystem = 'EGLS' and egls.AccountEnabled = 1 " +
                    "LEFT JOIN MsOfficerNR t24 on tpv.NIKVendor LIKE IIF(RIGHT(t24.LoginName, 1) LIKE '[A-Za-z]','%'+LEFT(t24.LoginName, LEN(t24.LoginName) - 1)+'%','%'+t24.LoginName+'%') and t24.SrcSystem = 'T24' and t24.DTEndProfile >= GETDATE() " +
                    "LEFT JOIN MsDboRTGS dboRTGS on tpv.NIKVendor = dboRTGS.NIK " +
                    "LEFT JOIN MsS4 s4 on tpv.NIKVendor = s4.NIK " +
                    "LEFT JOIN MsSPK spk on tpv.NIKVendor = spk.NIK " +
                    "LEFT JOIN MsSlik slik on tpv.NIKVendor = slik.NIK " +
                    "LEFT JOIN MsCMS cms on tpv.NIKVendor = cms.NIK " +
                    "LEFT JOIN MsTepatMBankingIndividu individu on tpv.NIKVendor = individu.NIK " +
                    "LEFT JOIN MsTepatMBankingCorporate corporate on tpv.NIKVendor = corporate.NIK " +
                    "LEFT JOIN MsBiCAC bicac on tpv.NIKVendor = bicac.NIK " +
                    "LEFT JOIN MsDigitus digitus on tpv.NIKVendor = digitus.NIK " +
                    "LEFT JOIN MsCustomUserID customId on tpv.NIKVendor = customId.NIK " +
                    "LEFT JOIN MsUserIDApplication mui on mui.ParamDetailId = customId.ParamDetailId and Visible = 1 " +
                    "GROUP BY tpv.NIKVendor, tpv.NameVendor, tpv.OccupationDescVendor, prospera.NIK, egls.OfficerName, t24.LoginName, dboRTGS.NIK, s4.NIK, spk.NIK, slik.NIK, cms.NIK, individu.NIK, corporate.NIK, bicac.NIK, digitus.NIK, customId.NIK " +
                    ") x WHERE x.Nama LIKE %:searchData%",
            nativeQuery = true)
    Page<MsUserIDOwnership> findUserIDOwnershipByName(String searchData, Pageable pageable);

    @Query(value = "Select " +
            "REVERSE( " +
               "SUBSTRING( " +
                 "REVERSE( " +
                   "CONCAT( " +
                     "'Email/LDAP, '," +
                     "IIF(EXISTS(Select 1 FROM MsOfficerNR where SrcSystem = 'PROSPERA' and RoleID IS NOT NULL and OfficerStatusCode = 1 and NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000008' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsOfficerNR where SrcSystem = 'EGLS' and AccountEnabled = 1 and OfficerName = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000011'), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsOfficerNR where SrcSystem = 'T24' and DTEndProfile >= GETDATE() and LoginName LIKE IIF(RIGHT(:nik, 1) LIKE '[A-Za-z]','%'+LEFT(:nik, LEN(:nik) - 1)+'%','%'+:nik+'%')), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000012' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsDBORTGS where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000001' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsS4 where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000002' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsSPK where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000003' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsSlik where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000004'and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsCMS where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000005' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsTepatMbankingIndividu where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000006' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsTepatMbankingCorporate where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000007' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsBiCAC where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000009' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsDigitus where NIK = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication WHERE ParamDetailId = 'AU00000010' and Visible = 1), NULL), " +
                     "IIF(EXISTS(Select 1 FROM MsCustomUserID where nik = :nik), (Select STRING_AGG(ParamDetailDesc+ ', ', '') from MsUserIDApplication mui " +
                     "LEFT JOIN MsCustomUserID customId on mui.ParamDetailId = customId.ParamDetailId " +
                     "where mui.Type = 'custom' and mui.Visible = 1 and customId.NIK = :nik), NULL) " +
                   ") " +
                 ") " +
               ", 3, 9999) " +
            ")as UserID "
            , nativeQuery = true)
    String findOwnedActiveUserIDByNIK(String nik);
}
