package com.btpns.fin.repository;

import com.btpns.fin.model.DetailUserProspera;
import com.btpns.fin.model.MessageValueProspera;
import com.btpns.fin.model.UserProspera;
import com.btpns.fin.model.request.ReqPersonnelProspera;
import com.btpns.fin.model.response.*;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.List;
import java.util.Map;

public interface IProsperaRepository {
    @GET("personnel")
    public Call<MessageValueProspera<List<UserProspera>>> getPersonnelProsperaByNik(@HeaderMap Map<String, String> headerMap, @QueryMap Map<String, String> params);

    @GET("personnel/details/{globalPersonnelNum}")
    public Call<MessageValueProspera<DetailUserProspera>> getPersonnelDetailProspera(@HeaderMap Map<String, String> headerMap, @Path("globalPersonnelNum") String globalPersonnelNum);

    @GET("personnel/newCenterCodeOfficer")
    public Call<MessageValueProspera<ResNewCenterCodeOfficer>> getNewCenterCodeOfficer(@HeaderMap Map<String, String> headerMap, @QueryMap Map<String, Integer> params);

    @POST("personnel")
    public Call<MessageValueProspera<ResRegisterNewPersonnelProspera>> registerNewPersonnelProspera(@HeaderMap Map<String, String> headerMap, @Body ReqPersonnelProspera requestBody);

    @PUT("personnel/{id}")
    public Call<MessageValueProspera<ResUpdatePersonnelProspera>> updatePersonnelProspera(@Path("id") Integer id, @HeaderMap Map<String, String> headerMap, @Body ReqPersonnelProspera requestBody);

    @GET("office/{officeCode}")
    public Call<MessageValueProspera<ResOfficeDetailProspera>> getOfficeDetailProspera(@HeaderMap Map<String, String> headerMap, @Path("officeCode") String officeCode);

    @GET("list/office")
    public Call<MessageValueProspera<List<ResListOfficeProspera>>> getListOfficeProspera(@HeaderMap Map<String, String> headerMap, @QueryMap Map<String, Object> params);

    @GET("roles")
    public Call<MessageValueProspera<List<ResRolesProspera>>> getListRoleProspera(@HeaderMap Map<String, String> headerMap);
}
