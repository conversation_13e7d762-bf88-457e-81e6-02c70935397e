package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxEmailAudittrail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;
import java.util.Optional;

@Repository
public interface ITrxEmailAudittrailRepository extends JpaRepository<TrxEmailAudittrail, BigInteger> {
    @Query(value = "Select * from TrxEmailAudittrail with(nolock) where requestId = :requestId", nativeQuery = true)
    Optional<TrxEmailAudittrail> findByRequestId(String requestId);

    @Query(value = "Select * from TrxEmailAudittrail with(nolock) where status = :status", nativeQuery = true)
    List<TrxEmailAudittrail> findByStatus(String status);

    @Query(value = "SELECT tea.requestId FROM TrxEmailAudittrail tea with(nolock) ORDER BY tea.requestId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastRequestId();
}
