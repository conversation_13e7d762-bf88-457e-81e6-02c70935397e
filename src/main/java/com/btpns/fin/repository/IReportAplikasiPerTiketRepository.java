package com.btpns.fin.repository;

import com.btpns.fin.model.entity.ReportAplikasiPerTiket;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IReportAplikasiPerTiketRepository extends JpaRepository<ReportAplikasiPerTiket, String> {
    @Query(value = "SELECT * FROM (" +
            "SELECT tfra.Id, tfr.TicketId, tfr.CreateDatetime, tfra.AplikasiName AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
            "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
            "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FUID' AS Kategori, " +
            "CASE " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tfa.CurrentState END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
            "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
            "INNER JOIN TrxFuidRequest tfr WITH (NOLOCK) on tfr.TicketId = tfra.TicketId " +
            "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tujuan.ParamDetailId =  " +
            "CASE " +
                "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                "ELSE tfr.Tujuan " +
            "END " +
            "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tfa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
            "WHERE tfa.CurrentState = :status " +
            "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ('0' in (:upmInputNIK) OR tfa.UPMInputNIK in (:upmInputNIK)) " +
            "AND ('0' in (:upmCheckerNIK) OR tfa.UPMCheckerNIK in (:upmCheckerNIK)) " +
            "AND (" +
            "(tfr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(tfr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(tfa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            "UNION " +
            "SELECT tspra.Id, tspr.TicketId, tspr.CreateDatetime, tspra.AplikasiName AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
            "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
            "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
            "CASE " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tspa.CurrentState END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
            "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
            "INNER JOIN TrxSetupParamRequest tspr WITH (NOLOCK) on tspr.TicketId = tspra.TicketId " +
            "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tspa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
            "WHERE tspa.CurrentState = :status " +
            "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ('0' in (:upmInputNIK) OR tspa.UPMInputNIK in (:upmInputNIK)) " +
            "AND ('0' in (:upmCheckerNIK) OR tspa.UPMCheckerNIK in (:upmCheckerNIK)) " +
            "AND (" +
            "(tspr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(tspr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(tspa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC ",
            countQuery = "SELECT COUNT(*) " +
                    "FROM (" +
                    "SELECT tfra.Id, tfr.TicketId, tfr.CreateDatetime, tfra.AplikasiName AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
                    "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
                    "tfr.AlasanPengajuan, 'FUID' AS Kategori, tfa.CurrentState AS CurrentState, sd.ParamDetailDesc AS CurrentStateDesc, " +
                    "CASE WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
                    "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
                    "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
                    "FROM TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
                    "INNER JOIN TrxFuidRequest tfr WITH (NOLOCK) on tfr.TicketId = tfra.TicketId " +
                    "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                    "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                    "CASE " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tfa.CurrentState END " +
                    "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
                    "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
                    "WHERE tfa.CurrentState = :status " +
                    "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND ('0' in (:upmInputNIK) OR tfa.UPMInputNIK in (:upmInputNIK)) " +
                    "AND ('0' in (:upmCheckerNIK) OR tfa.UPMCheckerNIK in (:upmCheckerNIK)) " +
                    "AND (" +
                    "(tfr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                    "(tfr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                    "(tfa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                    ") " +
                    "UNION " +
                    "SELECT tspra.Id, tspr.TicketId, tspr.CreateDatetime, tspra.AplikasiName AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
                    "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
                    "tspr.ParameterBaru as AlasanPengajuan, 'SP' AS Kategori, tspa.CurrentState AS CurrentState, sd.ParamDetailDesc AS CurrentStateDesc, " +
                    "CASE WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
                    "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
                    "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
                    "FROM TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
                    "INNER JOIN TrxSetupParamRequest tspr WITH (NOLOCK) on tspr.TicketId = tspra.TicketId " +
                    "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                    "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                    "CASE " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tspa.CurrentState END " +
                    "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
                    "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
                    "WHERE tspa.CurrentState = :status " +
                    "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND ('0' in (:upmInputNIK) OR tspa.UPMInputNIK in (:upmInputNIK)) " +
                    "AND ('0' in (:upmCheckerNIK) OR tspa.UPMCheckerNIK in (:upmCheckerNIK)) " +
                    "AND (" +
                    "(tspr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                    "(tspr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                    "(tspa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                    ") " +
                    ") x ",
            nativeQuery = true)
    public Page<ReportAplikasiPerTiket> getListReportPerAplikasi(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                                                 @Param("upmInputNIK") List<String> upmInputNIK, @Param("upmCheckerNIK") List<String> upmCheckerNIK, @Param("isPending") Integer isPending,
                                                                 @Param("effectiveDTStart") String effectiveDTStart, @Param("effectiveDTEnd") String effectiveDTEnd,
                                                                 @Param("createDTStart") String createDTStart, @Param("createDTEnd") String createDTEnd,
                                                                 @Param("currentStateDTStart") String currentStateDTStart, @Param("currentStateDTEnd") String currentStateDTEnd);

    @Query(value = "SELECT * FROM (" +
            "SELECT tfra.Id, tfr.TicketId, tfr.CreateDatetime, tfra.AplikasiName AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
            "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
            "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FUID' AS Kategori, " +
            "CASE " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tfa.CurrentState END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
            "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
            "INNER JOIN TrxFuidRequest tfr WITH (NOLOCK) on tfr.TicketId = tfra.TicketId " +
            "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tujuan.ParamDetailId = " +
            "CASE " +
                "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                "ELSE tfr.Tujuan " +
            "END " +
            "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tfa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
            "WHERE tfa.CurrentState = :status " +
            "AND ((:tipeKewenanganLimit = '-1') OR (tfr.TipeKewenanganLimit = :tipeKewenanganLimit))" +
            "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ('0' in (:upmInputNIK) OR tfa.UPMInputNIK in (:upmInputNIK)) " +
            "AND ('0' in (:upmCheckerNIK) OR tfa.UPMCheckerNIK in (:upmCheckerNIK)) " +
            "AND (" +
            "(tfr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(tfr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(tfa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC ",
            countQuery = "SELECT COUNT(*) " +
                    "FROM TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
                    "INNER JOIN TrxFuidRequest tfr WITH (NOLOCK) on tfr.TicketId = tfra.TicketId " +
                    "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                    "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                    "CASE " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tfa.CurrentState END " +
                    "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
                    "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
                    "WHERE tfa.CurrentState = :status " +
                    "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND ('0' in (:upmInputNIK) OR tfa.UPMInputNIK in (:upmInputNIK)) " +
                    "AND ('0' in (:upmCheckerNIK) OR tfa.UPMCheckerNIK in (:upmCheckerNIK)) " +
                    "AND (" +
                    "(tfr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                    "(tfr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                    "(tfa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                    ") ",
            nativeQuery = true)
    public Page<ReportAplikasiPerTiket> getListFuidReportPerAplikasi(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                                                     @Param("upmInputNIK") List<String> upmInputNIK, @Param("upmCheckerNIK") List<String> upmCheckerNIK, @Param("isPending") Integer isPending,
                                                                     @Param("effectiveDTStart") String effectiveDTStart, @Param("effectiveDTEnd") String effectiveDTEnd,
                                                                     @Param("createDTStart") String createDTStart, @Param("createDTEnd") String createDTEnd,
                                                                     @Param("currentStateDTStart") String currentStateDTStart, @Param("currentStateDTEnd") String currentStateDTEnd,
                                                                     @Param("tipeKewenanganLimit") String tipeKewenanganLimit);

    @Query(value = "SELECT * FROM (" +
            "SELECT tspra.Id, tspr.TicketId, tspr.CreateDatetime, tspra.AplikasiName AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
            "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
            "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
            "CASE " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tspa.CurrentState END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
            "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
            "INNER JOIN TrxSetupParamRequest tspr WITH (NOLOCK) on tspr.TicketId = tspra.TicketId " +
            "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tspa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
            "WHERE tspa.CurrentState = :status " +
            "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ('0' in (:upmInputNIK) OR tspa.UPMInputNIK in (:upmInputNIK)) " +
            "AND ('0' in (:upmCheckerNIK) OR tspa.UPMCheckerNIK in (:upmCheckerNIK)) " +
            "AND (" +
            "(tspr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(tspr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(tspa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC ",
            countQuery = "SELECT COUNT(*) " +
                    "FROM TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
                    "INNER JOIN TrxSetupParamRequest tspr WITH (NOLOCK) on tspr.TicketId = tspra.TicketId " +
                    "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                    "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                    "CASE " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tspa.CurrentState END " +
                    "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
                    "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
                    "WHERE tspa.CurrentState = :status " +
                    "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND ('0' in (:upmInputNIK) OR tspa.UPMInputNIK in (:upmInputNIK)) " +
                    "AND ('0' in (:upmCheckerNIK) OR tspa.UPMCheckerNIK in (:upmCheckerNIK)) " +
                    "AND (" +
                    "(tspr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                    "(tspr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                    "(tspa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                    ") ",
            nativeQuery = true)
    public Page<ReportAplikasiPerTiket> getListSetupParamReportPerAplikasi(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                                                           @Param("upmInputNIK") List<String> upmInputNIK, @Param("upmCheckerNIK") List<String> upmCheckerNIK, @Param("isPending") Integer isPending,
                                                                           @Param("effectiveDTStart") String effectiveDTStart, @Param("effectiveDTEnd") String effectiveDTEnd,
                                                                           @Param("createDTStart") String createDTStart, @Param("createDTEnd") String createDTEnd,
                                                                           @Param("currentStateDTStart") String currentStateDTStart, @Param("currentStateDTEnd") String currentStateDTEnd);

    @Query(value = "SELECT * FROM (" +
            "SELECT tfa.Id, tfr.TicketId, tfr.CreateDatetime, tfr.Aplikasi AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
            "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
            "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FUID' AS Kategori, " +
            "CASE " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tfa.CurrentState END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
            "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxFuidRequest tfr WITH (NOLOCK) " +
            "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tujuan.ParamDetailId = " +
            "CASE " +
                "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                "ELSE tfr.Tujuan " +
            "END " +
            "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tfa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
            "WHERE tfa.CurrentState = :status " +
            "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ('0' in (:upmInputNIK) OR tfa.UPMInputNIK in (:upmInputNIK)) " +
            "AND ('0' in (:upmCheckerNIK) OR tfa.UPMCheckerNIK in (:upmCheckerNIK)) " +
            "AND (" +
            "(tfr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(tfr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(tfa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            "UNION " +
            "SELECT tspa.Id, tspr.TicketId, tspr.CreateDatetime, tspr.Aplikasi AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
            "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
            "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
            "CASE " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tspa.CurrentState END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
            "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxSetupParamRequest tspr WITH (NOLOCK) " +
            "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tspa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
            "WHERE tspa.CurrentState = :status " +
            "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ('0' in (:upmInputNIK) OR tspa.UPMInputNIK in (:upmInputNIK)) " +
            "AND ('0' in (:upmCheckerNIK) OR tspa.UPMCheckerNIK in (:upmCheckerNIK)) " +
            "AND (" +
            "(tspr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(tspr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(tspa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC ",
            countQuery = "SELECT COUNT(*) " +
                    "FROM (" +
                    "SELECT tfa.Id, tfr.TicketId, tfr.CreateDatetime, tfr.Aplikasi AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
                    "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
                    "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FUID' AS Kategori, " +
                    "CASE " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tfa.CurrentState END AS CurrentState, " +
                    "sd.ParamDetailDesc AS CurrentStateDesc, " +
                    "CASE WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
                    "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
                    "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
                    "FROM TrxFuidRequest tfr WITH (NOLOCK) " +
                    "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                    "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tfr.Tujuan = tujuan.ParamDetailId " +
                    "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
                    "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                    "CASE " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tfa.CurrentState END " +
                    "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
                    "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
                    "WHERE tfa.CurrentState = :status " +
                    "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND ('0' in (:upmInputNIK) OR tfa.UPMInputNIK in (:upmInputNIK)) " +
                    "AND ('0' in (:upmCheckerNIK) OR tfa.UPMCheckerNIK in (:upmCheckerNIK)) " +
                    "AND (" +
                    "(tfr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                    "(tfr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                    "(tfa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                    ") " +
                    "UNION " +
                    "SELECT tspa.Id, tspr.TicketId, tspr.CreateDatetime, tspr.Aplikasi AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
                    "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
                    "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
                    "CASE " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tspa.CurrentState END AS CurrentState, " +
                    "sd.ParamDetailDesc AS CurrentStateDesc, " +
                    "CASE WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
                    "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
                    "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
                    "FROM TrxSetupParamRequest tspr WITH (NOLOCK) " +
                    "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                    "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                    "CASE " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tspa.CurrentState END " +
                    "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
                    "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
                    "WHERE tspa.CurrentState = :status " +
                    "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND ('0' in (:upmInputNIK) OR tspa.UPMInputNIK in (:upmInputNIK)) " +
                    "AND ('0' in (:upmCheckerNIK) OR tspa.UPMCheckerNIK in (:upmCheckerNIK)) " +
                    "AND (" +
                    "(tspr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                    "(tspr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                    "(tspa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                    ") " +
                    ") x ",
            nativeQuery = true)
    public Page<ReportAplikasiPerTiket> getListReportPerTiket(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                                              @Param("upmInputNIK") List<String> upmInputNIK, @Param("upmCheckerNIK") List<String> upmCheckerNIK, @Param("isPending") Integer isPending,
                                                              @Param("effectiveDTStart") String effectiveDTStart, @Param("effectiveDTEnd") String effectiveDTEnd,
                                                              @Param("createDTStart") String createDTStart, @Param("createDTEnd") String createDTEnd,
                                                              @Param("currentStateDTStart") String currentStateDTStart, @Param("currentStateDTEnd") String currentStateDTEnd);

    @Query(value = "SELECT * FROM(" +
            "SELECT tfa.Id, tfr.TicketId, tfr.CreateDatetime, tfr.Aplikasi AS Aplikasi, tfr.TanggalEfektif, tfr.Tingkatan AS TingkatanUser, tfr.Attachment, " +
            "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataUserId, tfr.DataKodeCabang, tfr.DataNamaCabang, tfr.DataEmail, tfr.DataTelepon, " +
            "tujuan.ParamDetailDesc AS JenisPengajuan, tfr.AlasanPengajuan, alasan.ParamDetailDesc AS Deskripsi, 'FUID' AS Kategori, " +
            "CASE " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tfa.CurrentState END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE WHEN tfa.CurrentState = 'done_upm' THEN tfa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tfr.InfoTambahan, tfr.MasaBerlakuSampai, " +
            "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxFuidRequest tfr WITH (NOLOCK) " +
            "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tujuan.ParamDetailId = " +
            "CASE " +
                "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                "ELSE tfr.Tujuan " +
            "END " +
            "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tfa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
            "WHERE tfa.CurrentState = :status " +
            "AND ((:tipeKewenanganLimit = '-1') OR (tfr.TipeKewenanganLimit = :tipeKewenanganLimit))" +
            "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ('0' in (:upmInputNIK) OR tfa.UPMInputNIK in (:upmInputNIK)) " +
            "AND ('0' in (:upmCheckerNIK) OR tfa.UPMCheckerNIK in (:upmCheckerNIK)) " +
            "AND (" +
            "(tfr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(tfr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(tfa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC ",
            countQuery = "SELECT COUNT(*) " +
                    "FROM TrxFuidRequest tfr WITH (NOLOCK) " +
                    "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                    "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                    "CASE " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tfa.CurrentState = 'approved' AND tfr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tfa.CurrentState END " +
                    "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tfa.UPMInputNIK = ui.NIK " +
                    "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tfa.UPMCheckerNIK = uc.NIK " +
                    "WHERE tfa.CurrentState = :status " +
                    "AND (('approved' = :status AND tfr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND ('0' in (:upmInputNIK) OR tfa.UPMInputNIK in (:upmInputNIK)) " +
                    "AND ('0' in (:upmCheckerNIK) OR tfa.UPMCheckerNIK in (:upmCheckerNIK)) " +
                    "AND (" +
                    "(tfr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                    "(tfr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                    "(tfa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                    ") ",
            nativeQuery = true)
    public Page<ReportAplikasiPerTiket> getListFuidReportPerTiket(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                                                  @Param("upmInputNIK") List<String> upmInputNIK, @Param("upmCheckerNIK") List<String> upmCheckerNIK, @Param("isPending") Integer isPending,
                                                                  @Param("effectiveDTStart") String effectiveDTStart, @Param("effectiveDTEnd") String effectiveDTEnd,
                                                                  @Param("createDTStart") String createDTStart, @Param("createDTEnd") String createDTEnd,
                                                                  @Param("currentStateDTStart") String currentStateDTStart, @Param("currentStateDTEnd") String currentStateDTEnd,
                                                                  @Param("tipeKewenanganLimit") String tipeKewenanganLimit);

    @Query(value = "SELECT * FROM(" +
            "SELECT tspa.Id, tspr.TicketId, tspr.CreateDatetime, tspr.Aplikasi AS Aplikasi, tspr.TanggalEfektif, '' AS TingkatanUser, tspr.Attachment, " +
            "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, '' AS DataUserId, tspr.DataKodeCabang, tspr.DataNamaCabang, tspr.DataEmail, tspr.DataTelepon, " +
            "tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterBaru as AlasanPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori, " +
            "CASE " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tspa.CurrentState END AS CurrentState, " +
            "sd.ParamDetailDesc AS CurrentStateDesc, " +
            "CASE WHEN tspa.CurrentState = 'done_upm' THEN tspa.CurrentStateDT ELSE null END AS DoneDT, CurrentStateDT, ui.Nama AS UPMInputNIK, uc.Nama AS UPMCheckerNIK, tspr.AlasanPengajuan AS InfoTambahan, null AS MasaBerlakuSampai, " +
            "PUK1NIK, PUK1Name AS PUK1Nama, PUK1Occupation AS PUK1Jabatan, PUK1Dt, " +
            "PUK2NIK, PUK2Name AS PUk2Nama, PUK2Occupation AS PUK2Jabatan, PUK2Dt " +
            "FROM TrxSetupParamRequest tspr WITH (NOLOCK) " +
            "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
            "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
            "CASE " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
            "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
            "ELSE tspa.CurrentState END " +
            "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
            "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
            "WHERE tspa.CurrentState = :status " +
            "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND ('0' in (:upmInputNIK) OR tspa.UPMInputNIK in (:upmInputNIK)) " +
            "AND ('0' in (:upmCheckerNIK) OR tspa.UPMCheckerNIK in (:upmCheckerNIK)) " +
            "AND (" +
            "(tspr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
            "(tspr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
            "(tspa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
            ") " +
            ") x " +
            "ORDER BY x.CurrentStateDT DESC ",
            countQuery = "SELECT COUNT(*) " +
                    "FROM TrxSetupParamRequest tspr WITH (NOLOCK) " +
                    "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                    "INNER JOIN MsSystemParamDetail sd WITH (NOLOCK) on sd.ParamDetailId = " +
                    "CASE " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif <= :dateNow THEN 'new_upm' " +
                    "WHEN tspa.CurrentState = 'approved' AND tspr.TanggalEfektif > :dateNow THEN 'pending_upm' " +
                    "ELSE tspa.CurrentState END " +
                    "LEFT JOIN TrxUpmRole ui WITH (NOLOCK) on tspa.UPMInputNIK = ui.NIK " +
                    "LEFT JOIN TrxUpmRole uc WITH (NOLOCK) on tspa.UPMCheckerNIK = uc.NIK " +
                    "WHERE tspa.CurrentState = :status " +
                    "AND (('approved' = :status AND tspr.TanggalEfektif > :dateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :dateNow AND 0 = :isPending) OR ('approved' != :status)) " +
                    "AND ('0' in (:upmInputNIK) OR tspa.UPMInputNIK in (:upmInputNIK)) " +
                    "AND ('0' in (:upmCheckerNIK) OR tspa.UPMCheckerNIK in (:upmCheckerNIK)) " +
                    "AND (" +
                    "(tspr.CreateDatetime BETWEEN :createDTStart AND :createDTEnd) OR " +
                    "(tspr.TanggalEfektif BETWEEN :effectiveDTStart AND :effectiveDTEnd) OR  " +
                    "(tspa.CurrentStateDT BETWEEN :currentStateDTStart AND :currentStateDTEnd) " +
                    ") ",
            nativeQuery = true)
    public Page<ReportAplikasiPerTiket> getListSetupParamReportPerTiket(@Param("pageable") Pageable pageable, @Param("status") String status, @Param("dateNow") String dateNow,
                                                                        @Param("upmInputNIK") List<String> upmInputNIK, @Param("upmCheckerNIK") List<String> upmCheckerNIK, @Param("isPending") Integer isPending,
                                                                        @Param("effectiveDTStart") String effectiveDTStart, @Param("effectiveDTEnd") String effectiveDTEnd,
                                                                        @Param("createDTStart") String createDTStart, @Param("createDTEnd") String createDTEnd,
                                                                        @Param("currentStateDTStart") String currentStateDTStart, @Param("currentStateDTEnd") String currentStateDTEnd);
}
