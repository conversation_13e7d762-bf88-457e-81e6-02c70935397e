package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxUARApproval;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.math.BigInteger;

public interface ITrxUARApprovalRepository extends JpaRepository<TrxUARApproval, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM TrxUARApproval WHERE ticketId = :ticketId", nativeQuery = true)
    int deleteTrxUARApprovalByTicketId(String ticketId);
}
