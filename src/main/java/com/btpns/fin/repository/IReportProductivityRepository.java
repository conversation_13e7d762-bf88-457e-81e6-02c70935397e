package com.btpns.fin.repository;

import com.btpns.fin.model.entity.ReportProductivity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IReportProductivityRepository extends JpaRepository<ReportProductivity, String> {
    @Query(value = "SELECT tur.NIK AS nik, Nama AS nama,  " +
            "(ISNULL(countPerTiketSP,0) + ISNULL(countPerTiketFUID,0)) AS countPerTiket,  " +
            "(ISNULL(countPerAplikasiSP,0) + ISNULL(countPerAplikasiFUID,0)) AS countPerAplikasi FROM TrxUpmRole tur  " +
            "LEFT JOIN ( " +
            "SELECT nik, COUNT(TicketId) AS countPerTiketSP FROM ( " +
            "SELECT DISTINCT UPMInputNIK AS nik, tspa.TicketId FROM TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
            "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspra.ticketId = tspa.ticketId " +
            "WHERE tspa.CurrentState = 'done_upm' " +
            "AND (tspra.periodDate BETWEEN :startDate AND :endDate OR tspra.periodMonth BETWEEN :startDate AND :endDate) " +
            ") a " +
            "GROUP BY nik " +
            ") a1 ON a1.nik = tur.NIK " +
            "LEFT JOIN ( " +
            "SELECT UPMInputNIK AS nik, COUNT(tspa.TicketId) AS countPerAplikasiSP FROM TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
            "INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspra.ticketId = tspa.ticketId " +
            "WHERE tspa.CurrentState = 'done_upm' " +
            "AND (tspra.periodDate BETWEEN :startDate AND :endDate OR tspra.periodMonth BETWEEN :startDate AND :endDate) " +
            "GROUP BY UPMInputNIK " +
            ") b ON b.nik = tur.NIK " +
            "LEFT JOIN ( " +
            "SELECT nik, COUNT(TicketId) AS countPerTiketFUID FROM ( " +
            "SELECT DISTINCT UPMInputNIK AS nik, tfra.TicketId FROM TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
            "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfra.ticketId = tfa.ticketId " +
            "WHERE tfa.CurrentState = 'done_upm' " +
            "AND (tfra.periodDate BETWEEN :startDate AND :endDate OR tfra.periodMonth BETWEEN :startDate AND :endDate) " +
            ") c " +
            "GROUP BY nik " +
            ") c1 ON c1.nik = tur.NIK " +
            "LEFT JOIN ( " +
            "SELECT UPMInputNIK AS nik, COUNT(tfra.TicketId) AS countPerAplikasiFUID FROM TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
            "INNER JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfra.ticketId = tfa.ticketId " +
            "WHERE tfa.CurrentState = 'done_upm' " +
            "AND (tfra.periodDate BETWEEN :startDate AND :endDate OR tfra.periodMonth BETWEEN :startDate AND :endDate) " +
            "GROUP BY UPMInputNIK " +
            ") d ON d.nik = tur.NIK " +
            "WHERE Role = 'Maker' " +
            "AND ('0' in (:upmInputNIK) OR tur.NIK in (:upmInputNIK))", nativeQuery = true)
    public List<ReportProductivity> getListReportProductivity(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("upmInputNIK") List<String> upmInputNIK);
}
