package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsMMS;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IMsMmsRepository extends JpaRepository<MsMMS,String> {
    @Query(value = "SELECT * FROM MsMMS mm with(nolock) where mm.mmsCode = :mmsCode", nativeQuery = true)
    MsMMS findAllByMMSCode(String mmsCode);
}
