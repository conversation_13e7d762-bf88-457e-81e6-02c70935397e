package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxAudittrail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.List;

public interface ITrxAudittrailRepository extends JpaRepository<TrxAudittrail, BigInteger> {
    @Query(value = "SELECT * FROM TrxAudittrail ta with(nolock) WHERE ta.ticketId = :ticketId ORDER BY id ASC", nativeQuery = true)
    public List<TrxAudittrail> getTrxAudittrailByTicketId(@Param("ticketId") String ticketId);

    @Modifying
    @Query(value = "DELETE FROM TrxAudittrail WHERE ticketId = :ticketId", nativeQuery = true)
    int deleteTrxFuidRequestByTicketId(@Param("ticketId") String ticketId);

    int deleteTrxSetupParamRequestByTicketId(@Param("ticketId") String ticketId);
}
