package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxUserIdBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.time.LocalDateTime;

@Repository
public interface ITrxUserIdBatchRepository extends JpaRepository<TrxUserIdBatch, BigInteger> {
    @Query(value = "SELECT * from TrxUserIdBatch tub with(nolock) WHERE tub.BatchId = :batchId", nativeQuery = true)
    public TrxUserIdBatch getTrxUserIdBatchByBatchId(@Param("batchId") String batchId);

    @Query(value = "SELECT count(*) FROM TrxUserIdBatch tub with(nolock) WHERE uploaderNIK = :uploaderNIK and type = :type and createDateTime between :startDate and :endDate", nativeQuery = true)
    public Integer checkRequestInterval(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("uploaderNIK") String uploaderNIK, @Param("type") String type);
}
