package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsSlik;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

@Repository
public interface ISlikUserIdRepository extends JpaRepository<MsSlik, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM MsSlik", nativeQuery = true)
    int deleteAllMsSlik();

    @Modifying
    @Query(value = "DELETE FROM MsSlik WHERE NIK = :nik", nativeQuery = true)
    int deleteMsSlikByNik(String nik);

    @Query(value = "SELECT * FROM MsSlik ms with(nolock) ORDER BY ms.Kewenangan ASC, ms.NamaUser ASC ", nativeQuery = true)
    Page<MsSlik> getListMsSlik(Pageable pageable);

    @Query(value = "SELECT * FROM MsSlik ms with(nolock) Where ms.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsSlik> findAllByNikUser(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsSlik ms with(nolock) Where ms.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsSlik> findAllByNameUser(String searchData, Pageable pageable);

    @Query(value = "SELECT ms.* FROM MsSlik ms with(nolock) WHERE ms.NIK = :nik", nativeQuery = true)
    MsSlik findByNikUser(String nik);
}
