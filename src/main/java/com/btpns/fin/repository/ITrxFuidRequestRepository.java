package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxFuidRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ITrxFuidRequestRepository extends JpaRepository<TrxFuidRequest, String> {
    @Query(value = "SELECT * FROM TrxFuidRequest tfr with(nolock) WHERE tfr.ticketId = :ticketId", nativeQuery = true)
    public Optional<TrxFuidRequest> getTrxFuidRequestByTicketId(@Param("ticketId") String ticketId);

    @Query(value = "SELECT * FROM TrxFuidRequest tfr with(nolock) INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId WHERE tfr.nikRequester = :nikRequester", nativeQuery = true)
    public Page<TrxFuidRequest> getTrxFuidReqJoinApproval(@Param("nikRequester") String nikRequester, @Param("pageable") Pageable pageable);

    @Query(value = "SELECT * FROM TrxFuidRequest tfr with(nolock) INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId WHERE " +
            "(tfa.puk1NIK = :nikRequester AND tfa.CurrentState = 'waiting_puk1') OR " +
            "(tfa.puk2NIK = :nikRequester AND tfa.CurrentState = 'waiting_puk2')", nativeQuery = true)
    public Page<TrxFuidRequest> getTrxFuidReqJoinApprovalWait(@Param("nikRequester") String nikRequester, @Param("pageable") Pageable pageable);

    @Query(value = "SELECT COUNT(1) FROM TrxFuidRequest tfr with(nolock) INNER JOIN tfr.trxFuidApproval tfa with(nolock) where tfr.nikRequester = :nikRequester", nativeQuery = true)
    public int countAllTrxFuidReqJoinApproval(@Param("nikRequester") String nikRequester);

    @Query(value = "SELECT tfr.ticketId FROM TrxFuidRequest tfr with(nolock) where tfr.inputType is null ORDER BY tfr.ticketId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    public String getLastTicketId();

    @Query(value = "SELECT tfr.ticketId FROM TrxFuidRequest tfr with(nolock) where tfr.inputType = :inputType ORDER BY tfr.ticketId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    public String getLastTicketIdByInputType(@Param("inputType") String inputType);

    @Query(value = "SELECT tfr.requestId FROM TrxFuidRequest tfr with(nolock) WHERE tfr.requestId = :requestId AND tfr.CreateDatetime > :dateNow", nativeQuery = true)
    public String checkDuplicateRequestId(String requestId, @Param("dateNow") LocalDateTime dateNow);

    @Query(value = "SELECT count(*) FROM TrxFuidRequest tfr with(nolock) WHERE createDateTime between :startDate and :endDate and NIKRequester = :nikRequester", nativeQuery = true)
    public Integer checkRequestInterval(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("nikRequester") String nikRequester);

    @Modifying
    @Query(value = "DELETE FROM TrxFuidRequest WHERE ticketId = :ticketId", nativeQuery = true)
    int deleteTrxFuidRequestByTicketId(@Param("ticketId") String ticketId);

    @Query(value = "SELECT * FROM TrxFuidRequest tfr with(nolock) INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId WHERE " +
            "tfa.puk1NIK = :puk1NIK AND tfa.CurrentState = 'waiting_puk1'", nativeQuery = true)
    public List<TrxFuidRequest> getTrxFuidReqJoinApprovalWaitPuk1(@Param("puk1NIK") String puk1NIK);

    @Query(value = "SELECT * FROM TrxFuidRequest tfr with(nolock) INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId WHERE " +
            "tfa.puk2NIK = :puk2NIK AND tfa.CurrentState = 'waiting_puk2'", nativeQuery = true)
    public List<TrxFuidRequest> getTrxFuidReqJoinApprovalWaitPuk2(@Param("puk2NIK") String puk2NIK);

    @Query(value = "SELECT * FROM TrxFuidRequest tfr with(nolock) INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId WHERE tfa.CurrentState = :currentState AND tfr.MasaBerlakuSampai IN (:dates)", nativeQuery = true)
    public List<TrxFuidRequest> findExpiredFuidByStateAndDates(String currentState, List<LocalDate> dates);
}