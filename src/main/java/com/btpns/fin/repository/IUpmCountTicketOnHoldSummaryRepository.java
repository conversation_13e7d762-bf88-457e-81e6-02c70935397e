package com.btpns.fin.repository;

import com.btpns.fin.model.UpmCountTicketOnHoldSummary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IUpmCountTicketOnHoldSummaryRepository extends JpaRepository<UpmCountTicketOnHoldSummary, String> {
    @Query(value = "SELECT status, sum(counter) as total from " +
            "( " +
            "SELECT 'pending' as status, COUNT(*) as counter " +
            "FROM TrxFuidRequest tfr with(nolock) " +
            "INNER JOIN tema.dbo.TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId " +
            "WHERE tfa.CurrentState = 'approved' " +
            "AND tfr.TanggalEfektif > GETDATE() " +
            "AND (tfr.TanggalEfektif BETWEEN :startDateEffectiveOnHold AND :endDateEffectiveOnHold) " +
            "union all " +
            "SELECT 'pending' as status, COUNT(*) as counter " +
            "FROM TrxSetupParamRequest tspr with(nolock) " +
            "INNER JOIN tema.dbo.TrxSetupParamApproval tspa with(nolock) on tspr.ticketId = tspa.ticketId " +
            "WHERE tspa.CurrentState = 'approved' " +
            "AND tspr.TanggalEfektif > GETDATE() " +
            "AND (tspr.TanggalEfektif BETWEEN :startDateEffectiveOnHold AND :endDateEffectiveOnHold) " +
            ")as datas group by status", nativeQuery = true)
    public List<UpmCountTicketOnHoldSummary> getOnHoldTicketUPM(String startDateEffectiveOnHold, String endDateEffectiveOnHold);
}
