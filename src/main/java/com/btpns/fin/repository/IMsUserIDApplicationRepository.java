package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsUserIDApplication;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface IMsUserIDApplicationRepository extends JpaRepository<MsUserIDApplication, String> {
    @Query(value = "SELECT * FROM MsUserIDApplication mui with(nolock) where mui.ParamDetailId IN (:paramDetailIds)", nativeQuery = true)
    List<MsUserIDApplication> findUserIDApplicationInParamDetailIds(List<String> paramDetailIds);

    @Query(value = "SELECT * FROM MsUserIDApplication mui with(nolock) where (:isUAR IS NULL OR mui.UAR = :isUAR) AND (:isVisible IS NULL OR mui.Visible = :isVisible)", nativeQuery = true)
    List<MsUserIDApplication> findUARUserIDApplications(Boolean isVisible, Boolean isUAR);

    @Query(value = "SELECT * FROM MsUserIDApplication mui with(nolock) where (:searchData = 'all' OR mui.ParamDetailId = :searchData)", nativeQuery = true)
    Page<MsUserIDApplication> findAllUserIDApplicationsByCode(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsUserIDApplication mui with(nolock) where ((:searchData = 'all') OR (mui.ParamDetailDesc LIKE '%' + :searchData + '%'))", nativeQuery = true)
    Page<MsUserIDApplication> findAllUserIDApplicationsByDesc(String searchData, Pageable pageable);

    @Query(value = "SELECT mua.ParamDetailId FROM MsUserIDApplication mua with(nolock) ORDER BY mua.ParamDetailId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastParamDetailId();
}
