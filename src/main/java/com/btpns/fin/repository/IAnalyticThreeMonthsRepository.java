package com.btpns.fin.repository;

import com.btpns.fin.model.AnalyticVolumeTrxUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IAnalyticThreeMonthsRepository extends JpaRepository<AnalyticVolumeTrxUser, Integer> {
    @Query(value = "select ROW_NUMBER() OVER (order by type) as Id, type, FORMAT (tanggalEfektif, 'yyyyMM') as periodMonth , total from " +
            "( " +
            "select 'User ID Maintenance' as type, tfr.tanggalEfektif, count(*) as total " +
            "from TrxFuidRequestAplikasi tfra " +
            "JOIN TrxFuidRequest tfr WITH (NOLOCK) on tfra.ticketId = tfr.ticketId " +
            "JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
            "where tfa.CurrentState = 'done_upm' AND " +
            "( " +
                "(tfr.TanggalEfektif BETWEEN :startEffectiveDTPeriod1 AND :endEffectiveDTPeriod1) OR " +
                "(tfr.TanggalEfektif BETWEEN :startEffectiveDTPeriod2 AND :endEffectiveDTPeriod2) OR " +
                "(tfr.TanggalEfektif BETWEEN :startEffectiveDTPeriod3 AND :endEffectiveDTPeriod3) " +
            ") " +
            "group by tfr.TanggalEfektif " +
            "union " +
            "select 'Parameter Maintenance' as type, tspr.tanggalEfektif, count(*) as total " +
            "from TrxSetupParamRequestAplikasi tspra " +
            "JOIN TrxSetupParamRequest tspr WITH (NOLOCK) on tspra.ticketId = tspr.ticketId " +
            "JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
            "where tspa.CurrentState = 'done_upm' AND " +
            "( " +
            "    (tspr.TanggalEfektif BETWEEN :startEffectiveDTPeriod1 AND :endEffectiveDTPeriod1) OR " +
            "    (tspr.TanggalEfektif BETWEEN :startEffectiveDTPeriod2 AND :endEffectiveDTPeriod2) OR " +
            "    (tspr.TanggalEfektif BETWEEN :startEffectiveDTPeriod3 AND :endEffectiveDTPeriod3) " +
            ") " +
            "group by tspr.tanggalEfektif " +
            ") as datas order by type, periodMonth  ", nativeQuery = true)
    public List<AnalyticVolumeTrxUser> getCountAnalyticVolumeTrxUsers(@Param("startEffectiveDTPeriod1") String startEffectiveDTPeriod1, @Param("endEffectiveDTPeriod1") String endEffectiveDTPeriod1,
                                                                      @Param("startEffectiveDTPeriod2") String startEffectiveDTPeriod2, @Param("endEffectiveDTPeriod2") String endEffectiveDTPeriod2,
                                                                      @Param("startEffectiveDTPeriod3") String startEffectiveDTPeriod3, @Param("endEffectiveDTPeriod3") String endEffectiveDTPeriod3);
}
