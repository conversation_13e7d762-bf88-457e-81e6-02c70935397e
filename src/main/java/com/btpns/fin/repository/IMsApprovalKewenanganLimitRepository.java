package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsApprovalKewenanganLimit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.math.BigInteger;
import java.util.List;

public interface IMsApprovalKewenanganLimitRepository extends JpaRepository<MsApprovalKewenanganLimit, String> {
    @Query(value = "SELECT * from MsApprovalKewenanganLimit with(nolock) WHERE " +
                   "CASE " +
                        "WHEN :officeLevel = 'kckfo' THEN isKcKFO " +
                        "ELSE isHO " +
                   "END = 1 ", nativeQuery = true)
    List<MsApprovalKewenanganLimit> findAllByOfficeLevel(String officeLevel);

    @Query(value = "SELECT * FROM MsApprovalKewenanganLimit WITH(NOLOCK) WHERE Id = :id", nativeQuery = true)
    MsApprovalKewenanganLimit findMsApprovalKewenanganLimitById(String id);

    @Query(value = "SELECT Id FROM MsApprovalKewenanganLimit WITH(NOLOCK) ORDER BY Id DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastId();

    @Modifying
    @Query(value = "DELETE FROM MsApprovalKewenanganLimit WHERE Id = :id", nativeQuery = true)
    int deleteMsApprovalKewenanganLimitById(String id);

    @Query(value = "SELECT ApprovalOccupation from MsApprovalKewenanganLimit with(nolock) WHERE " +
            "CASE " +
                "WHEN :isKcKFO = 1 THEN isKcKFO " +
                "ELSE isHO " +
            "END = 1 " +
            "AND :nominalTransaction BETWEEN MinTunai AND MaxTunai", nativeQuery = true)
    public List<String> findTunaiApprovalLevelByNominal(boolean isKcKFO, Double nominalTransaction);

    @Query(value = "SELECT ApprovalOccupation from MsApprovalKewenanganLimit with(nolock) WHERE " +
            "CASE " +
            "WHEN :isKcKFO = 1 THEN isKcKFO " +
            "ELSE isHO " +
            "END = 1 " +
            "AND :nominalTransaction BETWEEN MinNonTunai AND MaxNonTunai", nativeQuery = true)
    public List<String> findNonTunaiApprovalLevelByNominal(boolean isKcKFO, Double nominalTransaction);

    @Query(value =  "Select  " +
                    "CASE " +
                        "WHEN :isKcKFO = '1' THEN " +
                            "CASE " +
                                "WHEN :isTunai = '1' " +
                                    "THEN (Select ApprovalOccupation from MsApprovalKewenanganLimit with(nolock) WHERE isKcKFO = 1 AND (Select MaxTunai + 1 from MsApprovalKewenanganLimit with(nolock) WHERE ApprovalOccupation = :approvalOccupation) BETWEEN MinTunai AND MaxTunai) " +
                                "ELSE " +
                                    "(Select ApprovalOccupation from MsApprovalKewenanganLimit with(nolock) WHERE isKcKFO = 1 AND (Select MaxNonTunai + 1 from MsApprovalKewenanganLimit with(nolock) WHERE ApprovalOccupation = :approvalOccupation) BETWEEN MinNonTunai AND MaxNonTunai) " +
                            "END " +
                        "ELSE " +
                            "CASE " +
                                "WHEN :isTunai = '1' " +
                                    "THEN (Select STRING_AGG(ApprovalOccupation, ', ') from MsApprovalKewenanganLimit with(nolock) WHERE isHO = 1 AND (Select MaxTunai + 1 from MsApprovalKewenanganLimit with(nolock) WHERE ApprovalOccupation = :approvalOccupation) BETWEEN MinTunai AND MaxTunai) " +
                                "ELSE " +
                                    "(Select STRING_AGG(ApprovalOccupation, ', ') ApprovalOccupation from MsApprovalKewenanganLimit with(nolock) WHERE isHO = 1 AND (Select MaxNonTunai + 1 from MsApprovalKewenanganLimit with(nolock) WHERE ApprovalOccupation = :approvalOccupation) BETWEEN MinNonTunai AND MaxNonTunai) " +
                            "END " +
                    "END AS ApprovalOccupation", nativeQuery = true)
    List<String> getHigherLevelApprovals(String approvalOccupation, boolean isKcKFO, Boolean isTunai);
}
