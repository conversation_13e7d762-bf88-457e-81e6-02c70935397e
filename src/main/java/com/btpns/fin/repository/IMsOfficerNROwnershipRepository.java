package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsOfficerNROwnership;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.math.BigInteger;
import java.util.List;

public interface IMsOfficerNROwnershipRepository extends JpaRepository<MsOfficerNROwnership, BigInteger> {
    @Query(value = "SELECT mon.MsOfficerNRID as Id, " +
            "CASE " +
                "WHEN mon.SrcSystem = 'PROSPERA' THEN mon.NIK " +
                "WHEN mon.SrcSystem = 'T24' THEN mon.LoginName  " +
                "WHEN mon.SrcSystem = 'EGLS' THEN mon.OfficerName  " +
                "ELSE NULL " +
            "END AS NIK, " +
            "CASE " +
                "WHEN mon.SrcSystem = 'PROSPERA' THEN mon.OfficerName  " +
                "WHEN mon.SrcSystem = 'T24' THEN mon.OfficerName " +
                "WHEN mon.SrcSystem = 'EGLS' THEN CONCAT(mon.FirstName, ' ', mon.LastName) " +
                "ELSE NULL " +
            "END AS NamaUser, " +
            "CASE " +
                "WHEN mon.SrcSystem = 'PROSPERA' THEN mon.RoleName " +
                "WHEN mon.SrcSystem = 'T24' THEN mon.MenuID " +
                "WHEN mon.SrcSystem = 'EGLS' THEN mon.RoleName " +
                "ELSE NULL " +
            "END AS Kewenangan, " +
                "CASE " +
                "WHEN optima.OccupationDesc IS NOT NULL THEN optima.OccupationDesc " +
                "WHEN alihdaya.OccupationDescVendor IS NOT NULL THEN alihdaya.OccupationDescVendor " +
                "ELSE '' " +
            "END AS Jabatan, " +
            "CASE " +
                "WHEN mon.MMSCode IS NOT NULL THEN mon.MMSName " +
                "WHEN mon.KFOCode IS NOT NULL THEN mon.KFOName " +
                "ELSE KCSName " +
            "END AS UnitKerja, " +
            "CASE " +
                "WHEN mon.SrcSystem = 'PROSPERA' THEN 'Prospera Revamp' " +
                "WHEN mon.SrcSystem = 'T24' THEN 'T24' " +
                "WHEN mon.SrcSystem = 'EGLS' THEN 'EGLS' " +
            "ELSE NULL " +
            "END AS Aplikasi " +
            "FROM " +
            "MsOfficerNR mon " +
            "LEFT JOIN MsEmployeeHierarchy optima on optima.NIK = " +
            "CASE " +
                "WHEN mon.SrcSystem = 'T24' THEN mon.LoginName  " +
                "WHEN mon.SrcSystem = 'EGLS' THEN mon.OfficerName  " +
                "ELSE mon.NIK  " +
            "END " +
            "LEFT JOIN TrxPUKVendor alihdaya on alihdaya.NIKVendor = " +
            "CASE " +
                "WHEN mon.SrcSystem = 'T24' THEN mon.LoginName  " +
                "WHEN mon.SrcSystem = 'EGLS' THEN mon.OfficerName  " +
                "ELSE mon.NIK  " +
            "END " +
            "WHERE " +
            "CASE " +
                "WHEN mon.SrcSystem = 'PROSPERA' AND mon.NIK = :nik AND mon.RoleID IS NOT NULL AND mon.OfficerStatusCode = 1 THEN 1 " +
                "WHEN mon.SrcSystem = 'T24' AND mon.DTEndProfile >= GETDATE() AND mon.LoginName LIKE IIF(RIGHT(:nik, 1) LIKE '[A-Za-z]','%'+LEFT(:nik, LEN(:nik) - 1)+'%','%'+:nik+'%') THEN 1 " +
                "WHEN mon.SrcSystem = 'EGLS' AND mon.OfficerName = :nik AND mon.AccountEnabled = 1 THEN 1 " +
                "ELSE 0 " +
            "END = 1", nativeQuery = true)
    List<MsOfficerNROwnership> findOfficerNROwnershipByNIK(String nik);
}
