package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxSetupParamRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public interface ITrxSetupParamRequestRepository extends JpaRepository<TrxSetupParamRequest, String> {
    @Query(value = "SELECT * FROM TrxSetupParamRequest tspr with(nolock) INNER JOIN TrxSetupParamApproval tspa with(nolock) ON tspr.ticketId = tspa.ticketId WHERE tspr.nikRequester = :nikRequester", nativeQuery = true)
    public Page<TrxSetupParamRequest> getTrxSetupParamReqJoinApproval(@Param("nikRequester") String nikRequester, @Param("pageable") Pageable pageable);

    @Query(value = "SELECT * FROM TrxSetupParamRequest tspr with(nolock) INNER JOIN TrxSetupParamApproval tspa with(nolock) ON tspr.ticketId = tspa.ticketId WHERE " +
            "(tspa.puk1NIK = :nikRequester AND tspa.CurrentState = 'waiting_puk1') OR " +
            "(tspa.puk2NIK = :nikRequester AND tspa.CurrentState = 'waiting_puk2')", nativeQuery = true)
    public Page<TrxSetupParamRequest> getTrxSetupParamReqJoinApprovalWait(@Param("nikRequester") String nikRequester, @Param("pageable") Pageable pageable);

    @Query(value = "SELECT tspr.ticketId FROM TrxSetupParamRequest tspr with(nolock) where tspr.inputType is null ORDER BY tspr.ticketId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    public String getLastTicketId();

    @Query(value = "SELECT tspr.ticketId FROM TrxSetupParamRequest tspr with(nolock) where tspr.inputType = 'manual' ORDER BY tspr.ticketId DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY", nativeQuery = true)
    String getLastTicketIdManualUPM();

    @Query(value = "SELECT tspr.requestId FROM TrxSetupParamRequest tspr with(nolock) WHERE tspr.requestId = :requestId AND tspr.CreateDatetime > :dateNow", nativeQuery = true)
    public String checkDuplicateRequestId(String requestId, @Param("dateNow") LocalDateTime dateNow);

    @Query(value = "SELECT count(*) FROM TrxSetupParamRequest tfr with(nolock) WHERE createDateTime between :startDate and :endDate and NIKRequester = :nikRequester", nativeQuery = true)
    public Integer checkRequestInterval(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("nikRequester") String nikRequester);

    @Modifying
    @Query(value = "DELETE FROM TrxSetupParamRequest WHERE ticketId = :ticketId", nativeQuery = true)
    int deleteTrxSetupParamRequestByTicketId(@Param("ticketId") String ticketId);

    @Query(value = "SELECT * FROM TrxSetupParamRequest tspr with(nolock) INNER JOIN TrxSetupParamApproval tspa with(nolock) ON tspr.ticketId = tspa.ticketId WHERE " +
            "tspa.puk1NIK = :puk1NIK AND tspa.CurrentState = 'waiting_puk1'", nativeQuery = true)
    public List<TrxSetupParamRequest> getTrxSetupParamReqJoinApprovalPuk1(@Param("puk1NIK") String puk1NIK);

    @Query(value = "SELECT * FROM TrxSetupParamRequest tspr with(nolock) INNER JOIN TrxSetupParamApproval tspa with(nolock) ON tspr.ticketId = tspa.ticketId WHERE " +
            "tspa.puk2NIK = :puk2NIK AND tspa.CurrentState = 'waiting_puk2'", nativeQuery = true)
    public List<TrxSetupParamRequest> getTrxSetupParamReqJoinApprovalPuk2(@Param("puk2NIK") String puk2NIK);
}
