package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxUpmRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ITrxUpmRoleRepository extends JpaRepository<TrxUpmRole, String> {
    @Query(value = "SELECT * FROM TrxUpmRole tur with(nolock) WHERE tur.nik = :nik", nativeQuery = true)
    public TrxUpmRole getTrxUpmRole(@Param("nik") String nik);

    @Query(value = "SELECT tur.nik, tur.role, tur.nama, tur.CreateDateTime FROM TrxUpmRole tur with(nolock) WHERE tur.role = 'Maker'", nativeQuery = true)
    public List<TrxUpmRole> getTrxUpmRoleMakerAll();

    @Query(value = "SELECT tur.nik, tur.role, tur.nama, tur.CreateDateTime FROM TrxUpmRole tur with(nolock) WHERE tur.role = 'Checker'", nativeQuery = true)
    public List<TrxUpmRole> getTrxUpmRoleCheckerAll();

    @Query(value = "SELECT tur.nik, tur.role, tur.nama, tur.CreateDateTime FROM TrxUpmRole tur with(nolock) WHERE tur.role in ('Checker','Admin')", nativeQuery = true)
    public List<TrxUpmRole> getTrxUpmRoleCheckerAndAdminAll();

    @Modifying
    @Query(value = "DELETE FROM TrxUpmRole WHERE nik = :nik", nativeQuery = true)
    public int deleteTrxUpmRole(String nik);

    @Query(value = "SELECT * FROM TrxUpmRole tur with(nolock) " +
            "ORDER BY tur.role ASC, tur.Nama ASC ", nativeQuery = true)
    public Page<TrxUpmRole> getListTrxUpmRole(@Param("pageable") Pageable pageable);
}
