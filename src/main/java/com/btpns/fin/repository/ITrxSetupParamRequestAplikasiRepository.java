package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxSetupParamRequestAplikasi;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;

public interface ITrxSetupParamRequestAplikasiRepository extends JpaRepository<TrxSetupParamRequestAplikasi, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM TrxSetupParamRequestAplikasi WHERE ticketId = :ticketId", nativeQuery = true)
    public int deleteTrxSetupParamRequestAplikasiByTicketId(@Param("ticketId") String ticketId);

    @Modifying
    @Query(value = "update TrxSetupParamRequestAplikasi set periodDateDone = :periodDateDone, periodMonthDone = :periodMonthDone where TicketId = :ticketId", nativeQuery = true)
    public int updateTrxSetupParamRequestRequestAplikasiByTicketId(@Param("ticketId") String ticketId, @Param("periodDateDone") String periodDateDone, @Param("periodMonthDone") String periodMonthDone);
}
