package com.btpns.fin.repository;

import com.btpns.fin.model.entity.InquiryTicket;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface IInquiryTicketRepository extends JpaRepository<InquiryTicket, String> {
    @Query(value = "SELECT * FROM (" +
            "SELECT tfr.TicketId, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataKodeCabang, tfr.DataNamaCabang, " +
            "tfr.Aplikasi AS Aplikasi, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS DeskripsiAlasan, 'FUID' AS Kategori " +
            "FROM TrxFuidRequest tfr with(nolock) " +
            "INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId and tfa.CurrentState = 'done_upm' and tfr.TanggalEfektif BETWEEN :startDate AND :endDate " +
            "INNER JOIN MsSystemParamDetail tujuan with(nolock) on tujuan.ParamDetailId = " +
            "CASE " +
                "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                "ELSE tfr.Tujuan " +
            "END " +
            "INNER JOIN MsSystemParamDetail alasan with(nolock) on tfr.Alasan = alasan.ParamDetailId " +
            "UNION " +
            "SELECT tspr.TicketId, tspr.TanggalEfektif,tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, tspr.DataKodeCabang, tspr.DataNamaCabang, " +
            "tspr.Aplikasi AS Aplikasi, tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori " +
            "FROM TrxSetupParamRequest tspr with(nolock) " +
            "INNER JOIN TrxSetupParamApproval tspa with(nolock) on tspr.ticketId = tspa.ticketId and tspa.CurrentState = 'done_upm' and tspr.TanggalEfektif BETWEEN :startDate AND :endDate " +
            ") x ORDER BY x.TanggalEfektif DESC ",
            countQuery = "SELECT COUNT(1) FROM (" +
                    "SELECT tfr.TicketId, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataKodeCabang, tfr.DataNamaCabang, " +
                    "tfr.Aplikasi AS Aplikasi, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS DeskripsiAlasan, 'FUID' AS Kategori " +
                    "FROM TrxFuidRequest tfr with(nolock) " +
                    "INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId and tfa.CurrentState = 'done_upm' and tfr.TanggalEfektif BETWEEN :startDate AND :endDate " +
                    "INNER JOIN MsSystemParamDetail tujuan with(nolock) on tujuan.ParamDetailId = " +
                    "CASE " +
                    "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
                    "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
                    "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
                    "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
                    "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
                    "ELSE tfr.Tujuan " +
                    "END " +
                    "INNER JOIN MsSystemParamDetail alasan with(nolock) on tfr.Alasan = alasan.ParamDetailId " +
                    "WHERE tfr.TanggalEfektif BETWEEN :startDate AND :endDate " +
                    "UNION " +
                    "SELECT tspr.TicketId, tspr.TanggalEfektif,tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, tspr.DataKodeCabang, tspr.DataNamaCabang, " +
                    "tspr.Aplikasi AS Aplikasi, tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterLama AS Deskripsi, 'SP' AS Kategori " +
                    "FROM TrxSetupParamRequest tspr with(nolock) " +
                    "INNER JOIN TrxSetupParamApproval tspa with(nolock) on tspr.ticketId = tspa.ticketId and tspa.CurrentState = 'done_upm' and tspr.TanggalEfektif BETWEEN :startDate AND :endDate " +
                    ") x " ,
            nativeQuery = true)
    Page<InquiryTicket> findAllTicketUserInquiry(String startDate, String endDate, Pageable pageable);
}
