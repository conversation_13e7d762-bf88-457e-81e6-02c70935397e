package com.btpns.fin.repository;
import com.btpns.fin.model.entity.ApplicationMonthly;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IAnalyticApplicationRepository extends JpaRepository<ApplicationMonthly,String> {
    @Query(value = "select top 10 " +
            "aplikasi, (select ParamDetailDesc from MsTemaApplication mta where ParamDetailId = aplikasi) as aplikasiDesc, count(*) as total from " +
            "( " +
                "select " +
                "tfra.Aplikasi, " +
                "tfra.TicketId, " +
                "tfra.id " +
                "from TrxFuidRequestAplikasi tfra WITH (NOLOCK) " +
                "JOIN TrxFuidRequest tfr WITH (NOLOCK) on tfra.ticketId = tfr.ticketId " +
                "JOIN TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                "where tfa.CurrentState = 'done_upm' AND " +
                "tfr.TanggalEfektif BETWEEN :startEffectiveDTPeriod AND :endEffectiveDTPeriod " +
                "UNION " +
                "select " +
                "CASE tspra.Aplikasi " +
                    "when 'AP00000001' then 'AF00000002' -- Prospera \n"+
                    "when 'AP00000002' then 'AF00000003' -- SCBS T24  \n"+
                    "when 'AP00000003' then 'AF00000006' -- CMS (Card Management System) \n"+
                    "when 'AP00000004' then 'AF00000007' -- SKN Konverter \n"+
                    "when 'AP00000005' then 'AF00000008' -- DBO-RTGS \n"+
                    "when 'AP00000006' then 'AF00000009' -- EGLS \n"+
                    "when 'AP00000007' then 'AF00000010' -- Epayment \n"+
                    "when 'AP00000008' then 'AF00000011' -- ETP \n"+
                    "when 'AP00000009' then 'AF00000012' -- MBanking  \n"+
                    "when 'AP00000010' then 'AF00000014' -- RSD \n"+
                    "when 'AP00000011' then 'AF00000016' -- SPK \n"+
                    "when 'AP00000012' then 'AF00000017' -- SSSS (S4) \n"+
                    "when 'AP00000013' then 'AF00000018' -- Tepat Mbanking / Internet Banking \n"+
                    "when 'AP00000014' then 'AF00000020' -- Web Mprospera \n"+
                    "when 'AP00000015' then 'AF00000021' -- Xlink/Xcard \n"+
                    "when 'AP00000016' then 'AF00000025' -- Lainnya \n"+
                    "else tspra.aplikasi " +
                "END as aplikasi, " +
                "tspra.TicketId, " +
                "tspra.id " +
                "from TrxSetupParamRequestAplikasi tspra WITH (NOLOCK) " +
                "JOIN TrxSetupParamRequest tspr WITH (NOLOCK) on tspra.ticketId = tspr.ticketId " +
                "JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                "where tspa.CurrentState = 'done_upm' AND " +
                "tspr.TanggalEfektif BETWEEN :startEffectiveDTPeriod AND :endEffectiveDTPeriod " +
            ") as datas group by aplikasi ORDER by count(*) desc , aplikasi asc ", nativeQuery = true)
    public List<ApplicationMonthly> getTopAppMonthly(@Param("startEffectiveDTPeriod") String startEffectiveDTPeriod,
                                                     @Param("endEffectiveDTPeriod") String endEffectiveDTPeriod);
}
