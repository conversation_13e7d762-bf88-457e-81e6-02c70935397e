package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsEmployeeDirector;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface IMsEmployeeDirectorRepository extends JpaRepository<MsEmployeeDirector, String> {
    @Query(value = "SELECT * FROM MsEmployeeDirector med with(nolock) where med.NIKLdap = :nikLdap", nativeQuery = true)
    public MsEmployeeDirector getMsEmployeeDirectorByNIKLdap(@Param("nikLdap") String nikLdap);

    @Query(value = "SELECT * FROM MsEmployeeDirector med with(nolock) where med.NIKOptima = :nikOptima", nativeQuery = true)
    public MsEmployeeDirector getMsEmployeeDirectorByNIKOptima(@Param("nikOptima") String nikOptima);

    @Query(value = "SELECT * FROM MsEmployeeDirector med with(nolock)", nativeQuery = true)
    public Page<MsEmployeeDirector> getMsEmployeeDirectorList(Pageable pageable);

    @Modifying
    @Query(value = "DELETE FROM MsEmployeeDirector WHERE NIKOptima = :nikOptima", nativeQuery = true)
    int deleteMsEmployeeDirectorByNIKOptima(String nikOptima);

    @Query(value = "SELECT " +
                   "CASE " +
                       "WHEN EXISTS (SELECT 1 from MsEmployeeDirector where NIKLdap = :nik) " +
                            "THEN (SELECT NIKOptima from MsEmployeeDirector where NIKLdap = :nik) " +
                       "ELSE :nik " +
                   "END ", nativeQuery = true)
    String checkNikDirector(@Param("nik") String nik);
}