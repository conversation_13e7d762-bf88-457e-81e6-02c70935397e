package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MMS_UPM;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IMmsUPMRepository extends JpaRepository<MMS_UPM,String> {
    @Query(value = "SELECT * FROM MMS_UPM mms with(nolock) where mms.MMSCode = :mmsCode", nativeQuery = true)
    public List<MMS_UPM> getMsMmsById(@Param("mmsCode") String mmsCode);

    @Query(value = "SELECT * FROM MMS_UPM mms with(nolock) ORDER BY mms.MMSCode ASC", nativeQuery = true)
    Page<MMS_UPM> findAllMmsDataPageable(@Param("pageable") Pageable pageable);

    @Query(value = "SELECT * FROM MMS_UPM mms with(nolock) where mms.MMSCode LIKE %:searchData%", nativeQuery = true)
    Page<MMS_UPM> findAllByMmsCodePageable(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MMS_UPM mms with(nolock) where mms.MMSName LIKE %:searchData%", nativeQuery = true)
    Page<MMS_UPM> findAllByMmsNamePageable(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MMS_UPM mms with(nolock) where mms.MMSCode = :mmsCode", nativeQuery = true)
    MMS_UPM findByMmsCode(@Param("mmsCode") String mmsCode);

    @Modifying
    @Query(value = "DELETE FROM MMS_UPM WHERE MMSCode = :mmsCode", nativeQuery = true)
    int deleteMmsData(String mmsCode);
}
