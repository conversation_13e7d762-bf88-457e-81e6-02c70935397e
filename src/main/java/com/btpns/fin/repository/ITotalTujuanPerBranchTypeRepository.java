package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TotalTujuanPerBranchType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ITotalTujuanPerBranchTypeRepository extends JpaRepository<TotalTujuanPerBranchType, String> {
    @Query(value = "SELECT " +
            "mspd.ParamDetailDesc as JenisPengajuan, " +
            "SUM(CASE WHEN tfr.DataKodeCabang = '0001' THEN '1' ELSE 0 END) as HO, " +
            "SUM(CASE WHEN tfr.DataKodeCabang LIKE 'W%' THEN '1' ELSE 0 END) as MMS, " +
            "SUM(CASE WHEN tfr.DataKodeCabang NOT LIKE 'W%' AND tfr.DataKodeCabang <> '0001' THEN '1' ELSE 0 END) as KC<PERSON><PERSON><PERSON>, " +
            "COUNT(*) AS GrandTotal " +
            "FROM TrxFuidRequestAplikasi tfra with(nolock) " +
            "INNER JOIN TrxFuidRequest tfr with(nolock) on tfra.TicketId = tfr.TicketId " +
            "INNER JOIN TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail mspd with(nolock) ON mspd.ParamDetailId =  " +
            "CASE " +
            "WHEN tfr.Tujuan = 'pendaftaran_baru_prospera' THEN 'pendaftaran_baru' " +
            "WHEN tfr.Tujuan = 'perubahan_prospera' THEN 'perubahan' " +
            "WHEN tfr.Tujuan = 'penghapusan_prospera' THEN 'penghapusan' " +
            "WHEN tfr.Tujuan = 'reset_password_prospera' THEN 'reset_password' " +
            "WHEN tfr.Tujuan = 'alternate/delegasi_prospera' THEN 'alternate/delegasi' " +
            "ELSE tfr.Tujuan " +
            "END " +
            "WHERE tfa.CurrentState = 'done_upm' AND MONTH(tfr.TanggalEfektif) = :month AND YEAR(tfr.TanggalEfektif) = :year " +
            "GROUP BY mspd.ParamDetailDesc ORDER BY JenisPengajuan", nativeQuery = true)
    public List<TotalTujuanPerBranchType> getTotalTujuanFUIDPerBranchTypeByMonth(int month, int year);

    @Query(value = "SELECT " +
            "tspr.KategoriParamName as JenisPengajuan, " +
            "SUM(CASE WHEN tspr.DataKodeCabang = '0001' THEN '1' ELSE 0 END) as HO, " +
            "SUM(CASE WHEN tspr.DataKodeCabang LIKE 'W%' THEN '1' ELSE 0 END) as MMS, " +
            "SUM(CASE WHEN tspr.DataKodeCabang NOT LIKE 'W%' AND tspr.DataKodeCabang <> '0001' THEN '1' ELSE 0 END) as KCKFO, " +
            "COUNT(*) AS GrandTotal " +
            "FROM TrxSetupParamRequestAplikasi tspra with(nolock) " +
            "INNER JOIN TrxSetupParamRequest tspr with(nolock) on tspra.TicketId = tspr.TicketId " +
            "INNER JOIN TrxSetupParamApproval tspa with(nolock) ON tspr.TicketId = tspa.TicketId " +
            "WHERE tspa.CurrentState = 'done_upm' AND MONTH(tspr.TanggalEfektif) = :month AND YEAR(tspr.TanggalEfektif) = :year " +
            "GROUP BY tspr.KategoriParamName " +
            "ORDER BY JenisPengajuan", nativeQuery = true)
    public List<TotalTujuanPerBranchType> getTotalTujuanSPPerBranchTypeByMonth(int month, int year);
}
