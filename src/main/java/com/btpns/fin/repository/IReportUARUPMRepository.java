package com.btpns.fin.repository;

import com.btpns.fin.model.response.ResReportUARUPM;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface IReportUARUPMRepository extends JpaRepository<ResReportUARUPM, String> {
    @Query(value = "Select tur.TicketId, tur.NIK, tur.namaUser as nama, tur.kewenangan, tur.jabatan, tur.unitKerja, tua.UserConfirmation as status, tua.UserNIKNotes as keterangan " +
                   "FROM TrxUARRequest tur WITH (NOLOCK) INNER JOIN TrxUARApproval tua WITH (NOLOCK) on tur.ticketId = tua.ticketId " +
                   "where tur.PeriodYear = :tahun and tur.PeriodQuarter = :triwulan and tur.Aplikasi = :aplikasi and tua.CurrentState = :status " +
                   "order by tua.CurrentStateDT DESC",
            countQuery = "SELECT COUNT(*) " +
            "FROM TrxUARRequest tur WITH (NOLOCK) INNER JOIN TrxUARApproval tua WITH (NOLOCK) ON tur.ticketId = tua.ticketId " +
            "WHERE tur.PeriodYear = :tahun AND tur.PeriodQuarter = :triwulan AND tur.Aplikasi = :aplikasi AND tua.CurrentState = :status",
            nativeQuery = true)
    Page<ResReportUARUPM> getReportUARUPM(String triwulan, String tahun, String aplikasi, String status, Pageable pageable);
}
