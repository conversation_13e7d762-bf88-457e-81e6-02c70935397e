package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxPUKVendor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.List;

public interface ITrxPUKVendorRepository extends JpaRepository<TrxPUKVendor, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM TrxPUKVendor WHERE NIKVendor IN :nikList", nativeQuery = true)
    public int deleteTrxPUKVendor(@Param("nikList") List<String> nikList);

    @Query(value = "SELECT * FROM TrxPUKVendor tpv with(nolock) WHERE tpv.nikVendor = :nikVendor", nativeQuery = true)
    public TrxPUKVendor findByNikVendor(@Param("nikVendor") String nikVendor);

    @Query(value = "SELECT * FROM TrxPUKVendor tpv with(nolock) WHERE tpv.nikVendor IN :nik", nativeQuery = true)
    public List<TrxPUKVendor> getTrxPUKVendor(@Param("nik") List<String> nik);

    @Query(value = "SELECT * FROM TrxPUKVendor tpv with(nolock) " +
            "ORDER BY tpv.OccupationVendor ASC, tpv.NameVendor ASC ", nativeQuery = true)
    public Page<TrxPUKVendor> getListTrxPUKVendor(@Param("pageable") Pageable pageable);

    @Query(value = "SELECT * FROM TrxPUKVendor tpv with(nolock) Where tpv.nikVendor LIKE %:searchData%", nativeQuery = true)
    Page<TrxPUKVendor> findAllByNikVendor(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM TrxPUKVendor tpv with(nolock) Where tpv.NameVendor LIKE %:searchData%", nativeQuery = true)
    Page<TrxPUKVendor> findAllByNameVendor(String searchData, Pageable pageable);
}
