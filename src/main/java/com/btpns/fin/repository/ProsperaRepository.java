package com.btpns.fin.repository;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.model.DetailUserProspera;
import com.btpns.fin.model.MessageValueProspera;
import com.btpns.fin.model.UserProspera;
import com.btpns.fin.model.request.ReqPersonnelProspera;
import com.btpns.fin.model.response.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.btpns.fin.helper.Constants.*;

@Repository
public class ProsperaRepository {
    @Autowired
    IProsperaRepository prosperaRepository;

    private static final Logger log = LoggerFactory.getLogger(ProsperaRepository.class);

    public List<UserProspera> getPersonnelProsperaByNik(String nik, String authorization) {
        Map<String, String> headerMap = CommonHelper.buildHeader(authorization);
        Map<String, String> paramMap = buildParamMapGetPersonnelProsperaByNik(nik);

        Call<MessageValueProspera<List<UserProspera>>> call = prosperaRepository.getPersonnelProsperaByNik(headerMap, paramMap);
        try {
            Response<MessageValueProspera<List<UserProspera>>> response = call.execute();
            if (!response.isSuccessful()){
                log.error("FAIL getting response from get detail user prospera endpoint. Check okhttp log");
                return null;
            }

            if (response.body() != null && PROSPERA_SUCCESS_RESPONSE_CODE.equalsIgnoreCase(response.body().getResponseStatus().getResponseCode())){
                return response.body().getData();
            }
        } catch (IOException e) {
            log.error("Fail executing Retrofit to get detail user prospera endpoint. Error: ", e);
        }

        return null;
    }

    private Map<String, String> buildParamMapGetPersonnelProsperaByNik(String nik) {
        HashMap<String, String> paramMap = new HashMap<>() {{
            put("size", String.valueOf(30));
            put("page", String.valueOf(0));
            put("nik", nik);
        }};
        return paramMap;
    }

    public MessageValueProspera<ResRegisterNewPersonnelProspera> registerNewPersonnelProspera(ReqPersonnelProspera reqPersonnelProspera, String authorization) {
        Map<String, String> headerMap = CommonHelper.buildHeader(authorization);

        Call<MessageValueProspera<ResRegisterNewPersonnelProspera>> call = prosperaRepository.registerNewPersonnelProspera(headerMap, reqPersonnelProspera);
        try {
            Response<MessageValueProspera<ResRegisterNewPersonnelProspera>> response = call.execute();
            if (!response.isSuccessful()){
                log.error("FAIL sumbit new personnel prospera. Check okhttp log");
                return null;
            }

            if (response.body() != null){
                return response.body();
            }
        } catch (IOException e) {
            log.error("Fail executing Retrofit to sumbit new personnel prospera. Error: ", e);
        }

        return null;
    }

    public ResNewCenterCodeOfficer getNewCenterCodeOfficer(Integer mmsId, String authorization) {
        Map<String, String> headerMap = CommonHelper.buildHeader(authorization);
        Map<String, Integer> paramMap = buildParamMapGetNewCenterCodeOfficer(mmsId);

        Call<MessageValueProspera<ResNewCenterCodeOfficer>> call = prosperaRepository.getNewCenterCodeOfficer(headerMap, paramMap);
        try {
            Response<MessageValueProspera<ResNewCenterCodeOfficer>> response = call.execute();
            if (!response.isSuccessful()){
                log.error("FAIL getting response from get new center code officer endpoint. Check okhttp log");
                return null;
            }

            if (response.body() != null && PROSPERA_SUCCESS_RESPONSE_CODE.equalsIgnoreCase(response.body().getResponseStatus().getResponseCode())){
                return response.body().getData();
            }
        } catch (IOException e) {
            log.error("Fail executing Retrofit to get new center code officer. Error: ", e);
        }

        return null;
    }

    private Map<String, Integer> buildParamMapGetNewCenterCodeOfficer(Integer mmsId) {
        HashMap<String, Integer> paramMap = new HashMap<>() {{
            put("officeId", mmsId);
        }};
        return paramMap;
    }

    public DetailUserProspera getPersonnelDetailProspera(String globalPersonnelNum, String authorization) {
        Map<String, String> headerMap = CommonHelper.buildHeader(authorization);
        Call<MessageValueProspera<DetailUserProspera>> call = prosperaRepository.getPersonnelDetailProspera(headerMap, globalPersonnelNum);

        try {
            Response<MessageValueProspera<DetailUserProspera>> response = call.execute();
            if (!response.isSuccessful()){
                log.error("FAIL getting response from get personnel detail prospera endpoint. Check okhttp log");
                return null;
            }

            if (response.body() != null && PROSPERA_SUCCESS_RESPONSE_CODE.equalsIgnoreCase(response.body().getResponseStatus().getResponseCode())){
                return response.body().getData();
            }
        } catch (IOException e) {
            log.error("Fail executing Retrofit to get personnel detail prospera. Error: ", e);
        }

        return null;
    }

    public MessageValueProspera<ResUpdatePersonnelProspera> updatePersonnelProspera(ReqPersonnelProspera reqPersonnelProspera, Integer personnalId, String authorization) {
        Map<String, String> headerMap = CommonHelper.buildHeader(authorization);

        Call<MessageValueProspera<ResUpdatePersonnelProspera>> call = prosperaRepository.updatePersonnelProspera(personnalId, headerMap, reqPersonnelProspera);
        try {
            Response<MessageValueProspera<ResUpdatePersonnelProspera>> response = call.execute();
            if (!response.isSuccessful()){
                log.error("FAIL update status personnel prospera. Check okhttp   log");
                return null;
            }

            if (response.body() != null){
                return response.body();
            }
        } catch (IOException e) {
            log.error("Fail executing Retrofit to update status personnel prospera. Error: ", e);
        }

        return null;
    }

    public ResOfficeDetailProspera getOfficeProsperaByOfficeCode(String officeCode, String authorization) {
        Map<String, String> headerMap = CommonHelper.buildHeader(authorization);
        Call<MessageValueProspera<ResOfficeDetailProspera>> call = prosperaRepository.getOfficeDetailProspera(headerMap, officeCode);
        try {
            Response<MessageValueProspera<ResOfficeDetailProspera>> response = call.execute();
            if (!response.isSuccessful()){
                log.error("FAIL getting response from get detail office prospera endpoint. Check okhttp log");
                return null;
            }

            if (response.body() != null && PROSPERA_SUCCESS_RESPONSE_CODE.equalsIgnoreCase(response.body().getResponseStatus().getResponseCode())){
                return response.body().getData();
            }
        } catch (IOException e) {
            log.error("Fail executing Retrofit to get detail office prospera endpoint. Error: ", e);
        }

        return null;
    }

    public List<ResListOfficeProspera> getListOfficeProspera(String authorization, Map<String, Object> paramMap) {
        Map<String, String> headerMap = CommonHelper.buildHeader(authorization);

        Call<MessageValueProspera<List<ResListOfficeProspera>>> call = prosperaRepository.getListOfficeProspera(headerMap, paramMap);
        try {
            Response<MessageValueProspera<List<ResListOfficeProspera>>> response = call.execute();
            if (!response.isSuccessful()){
                log.error("FAIL getting response from get list office prospera endpoint. Check okhttp log");
                return null;
            }

            if (response.body() != null && PROSPERA_SUCCESS_RESPONSE_CODE.equalsIgnoreCase(response.body().getResponseStatus().getResponseCode())){
                return response.body().getData();
            }
        } catch (IOException e) {
            log.error("Fail executing Retrofit to get detail list prospera endpoint. Error: ", e);
        }

        return null;
    }

    public List<ResRolesProspera> getListRoleProspera(String authorization) {
        Map<String, String> headerMap = CommonHelper.buildHeader(authorization);

        Call<MessageValueProspera<List<ResRolesProspera>>> call = prosperaRepository.getListRoleProspera(headerMap);
        try {
            Response<MessageValueProspera<List<ResRolesProspera>>> response = call.execute();
            if (!response.isSuccessful()){
                log.error("FAIL getting response from get role prospera endpoint. Check okhttp log");
                return null;
            }

            if (response.body() != null && PROSPERA_SUCCESS_RESPONSE_CODE.equalsIgnoreCase(response.body().getResponseStatus().getResponseCode())){
                return response.body().getData();
            }
        } catch (IOException e) {
            log.error("Fail executing Retrofit to get role prospera endpoint. Error: ", e);
        }

        return null;
    }
}
