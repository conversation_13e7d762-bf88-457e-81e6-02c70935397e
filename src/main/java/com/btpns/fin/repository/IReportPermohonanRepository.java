package com.btpns.fin.repository;

import com.btpns.fin.model.entity.PermohonanDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IReportPermohonanRepository extends JpaRepository<PermohonanDetail, String> {
    @Query(value = "SELECT * FROM (" +
            "SELECT tfr.TicketId, tfr.CreateDatetime, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataKodeCabang, tfr.DataNamaCabang, " +
            "tfr.Aplikasi, tfr.MasaBerlakuSampai as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS Deskripsi, tfr.AlasanPengajuan AS Keterangan, tfa.UPMCheckerDt as tanggalSelesai " +
            "FROM TrxFuidRequest tfr WITH (NOLOCK) INNER JOIN tema.dbo.TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
            "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tfr.Tujuan = tujuan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
            "WHERE (tfa.CurrentState = :currentState AND 1 = :isUser AND tfr.nikRequester = :nikRequester AND tfr.CreateDatetime BETWEEN :startDate AND :endDate) " +
            "OR (tfa.CurrentState = :currentState AND 0 = :isUser AND (tfa.PUK1NIK = :nikRequester OR tfa.PUK2NIK = :nikRequester) AND tfr.CreateDatetime BETWEEN :startDate AND :endDate) " +
            "UNION " +
            "SELECT tspr.TicketId, tspr.CreateDatetime, tspr.TanggalEfektif, tspr.DataNIK, tspr.DataNamaLengkap, '' AS DataJabatan, tspr.DataKodeCabang, tspr.DataNamaCabang, " +
            "tspr.Aplikasi, '' as MasaBerlaku, tspr.KategoriParamName, '' AS Deskripsi, tspr.AlasanPengajuan AS Keterangan, tspa.UPMCheckerDt as tanggalSelesai " +
            "FROM TrxSetupParamRequest tspr WITH (NOLOCK) INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
            "WHERE (tspa.CurrentState = :currentState AND 1 = :isUser AND tspr.nikRequester = :nikRequester AND tspr.CreateDatetime BETWEEN :startDate AND :endDate) " +
            "OR (tspa.CurrentState = :currentState AND 0 = :isUser AND (tspa.PUK1NIK = :nikRequester OR tspa.PUK2NIK = :nikRequester) AND tspr.CreateDatetime BETWEEN :startDate AND :endDate) " +
            ") x " +
            "ORDER BY x.CreateDatetime DESC ",
            countQuery = "SELECT COUNT(*) " +
                    "FROM (" +
                    "SELECT tfr.TicketId, tfr.CreateDatetime, tfr.TanggalEfektif, tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataKodeCabang, tfr.DataNamaCabang, " +
                    "tfr.Aplikasi, tfr.MasaBerlakuSampai as MasaBerlaku, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS Deskripsi, tfr.AlasanPengajuan AS Keterangan, tfa.UPMCheckerDt as tanggalSelesai " +
                    "FROM TrxFuidRequest tfr WITH (NOLOCK) INNER JOIN tema.dbo.TrxFuidApproval tfa WITH (NOLOCK) on tfr.ticketId = tfa.ticketId " +
                    "INNER JOIN MsSystemParamDetail tujuan WITH (NOLOCK) on tfr.Tujuan = tujuan.ParamDetailId " +
                    "INNER JOIN MsSystemParamDetail alasan WITH (NOLOCK) on tfr.Alasan = alasan.ParamDetailId " +
                    "WHERE (tfa.CurrentState = :currentState AND 1 = :isUser AND tfr.nikRequester = :nikRequester AND tfr.CreateDatetime BETWEEN :startDate AND :endDate) " +
                    "OR (tfa.CurrentState = :currentState AND 0 = :isUser AND (tfa.PUK1NIK = :nikRequester OR tfa.PUK2NIK = :nikRequester) AND tfr.CreateDatetime BETWEEN :startDate AND :endDate) " +
                    "UNION " +
                    "SELECT tspr.TicketId, tspr.CreateDatetime, tspr.TanggalEfektif, tspr.DataNIK, tspr.DataNamaLengkap, '' AS DataJabatan, tspr.DataKodeCabang, tspr.DataNamaCabang, " +
                    "tspr.Aplikasi, '' as MasaBerlaku, tspr.KategoriParamName, '' AS Deskripsi, tspr.AlasanPengajuan AS Keterangan, tspa.UPMCheckerDt as tanggalSelesai " +
                    "FROM TrxSetupParamRequest tspr WITH (NOLOCK) INNER JOIN TrxSetupParamApproval tspa WITH (NOLOCK) on tspr.ticketId = tspa.ticketId " +
                    "WHERE (tspa.CurrentState = :currentState AND 1 = :isUser AND tspr.nikRequester = :nikRequester AND tspr.CreateDatetime BETWEEN :startDate AND :endDate) " +
                    "OR (tspa.CurrentState = :currentState AND 0 = :isUser AND (tspa.PUK1NIK = :nikRequester OR tspa.PUK2NIK = :nikRequester) AND tspr.CreateDatetime BETWEEN :startDate AND :endDate) " +
                    ") x ",
            nativeQuery = true)
    Page<PermohonanDetail> getReportPermohonan(@Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("nikRequester") String nikRequester,
                                               @Param("currentState") String currentState,
                                               @Param("pageable") Pageable pageable,
                                               @Param("isUser") int isUser);
}
