package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsHolidayList;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface IMsHolidayListRepository extends JpaRepository<MsHolidayList,String> {

    @Query(value = "SELECT * FROM MsHolidayList hl with(nolock) where hl.HolidayYear = :period", nativeQuery = true)
    public List<MsHolidayList> getMsHolidayList(@Param("period") String period);

    @Query(value = "SELECT * FROM MsHolidayList hl with(nolock) where hl.HolidayYear = :period OR '' = :period", nativeQuery = true)
    public Page<MsHolidayList> getMsHolidayListAll(@Param("period") String period, Pageable pageable);

    @Query(value = "SELECT * FROM MsHolidayList hl with(nolock) where hl.Id = :id", nativeQuery = true)
    public MsHolidayList getMsHolidayById(@Param("id") String id);

    @Modifying
    @Query(value = "DELETE FROM MsHolidayList WHERE Id = :id", nativeQuery = true)
    int deleteMsHolidayById(String id);

    @Query(value = "SELECT * FROM MsHolidayList hl with(nolock) where hl.HolidayDate = :date", nativeQuery = true)
    Optional<MsHolidayList> getMsHolidayByDate(@Param("date") LocalDate date);
}
