package com.btpns.fin.repository;

import com.btpns.fin.model.entity.PUKApprovalReminder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

@Repository
public interface IPUKApprovalReminderRepository extends JpaRepository<PUKApprovalReminder, BigInteger> {
    @Query(value = "Select * from ( " +
            "Select tfa.Id, tfr.ticketId, tfr.createDateTime, tfa.CurrentState, mspd.ParamDetailDesc as CurrentStateDesc, tfa.CurrentStateDT, DATEDIFF(minute, tfa.CurrentStateDT, GETDATE()) AS ApprovalInterval, " +
            "tfr.DataNIK, tfr.DataNamaLengkap, tfr.DataJabatan, tfr.DataKodeCabang, tfr.DataNamaCabang, " +
            "tfr.<PERSON>, tujuan.ParamDetailDesc AS JenisPengajuan, alasan.ParamDetailDesc AS Deskripsi, tfr.AlasanPengajuan, tfr.InfoTambahan, " +
            "tfa.PUK1ApprovalReminder, tfa.PUK2ApprovalReminder " +
            "from TrxFuidRequest tfr with(nolock)  " +
            "INNER JOIN TrxFuidApproval tfa with(nolock) on tfa.TicketId = tfr.TicketId  " +
            "INNER JOIN MsSystemParamDetail tujuan with(nolock) on tujuan.ParamDetailId = tfr.tujuan " +
            "INNER JOIN MsSystemParamDetail alasan with(nolock) on tfr.Alasan = alasan.ParamDetailId " +
            "INNER JOIN MsSystemParamDetail mspd with(nolock) on mspd.ParamDetailId = tfa.CurrentState " +
            "UNION  " +
            "SELECT tspa.Id, tspr.ticketId, tspr.createDateTime, tspa.CurrentState, mspd.ParamDetailDesc as CurrentStateDesc, tspa.CurrentStateDT, DATEDIFF(minute, tspa.CurrentStateDT, GETDATE()) AS ApprovalInterval, " +
            "tspr.DataNIK, tspr.DataNamaLengkap, tspr.DataJabatan, tspr.DataKodeCabang, tspr.DataNamaCabang, " +
            "tspr.Aplikasi, tspr.KategoriParamName AS JenisPengajuan, tspr.ParameterLama AS Deskripsi, tspr.ParameterBaru as AlasanPengajuan, tspr.AlasanPengajuan as InfoTambahan, " +
            "tspa.PUK1ApprovalReminder, tspa.PUK2ApprovalReminder " +
            "from TrxSetupParamRequest tspr with(nolock)  " +
            "INNER JOIN TrxSetupParamApproval tspa with(nolock) on tspa.TicketId = tspr.TicketId  " +
            "INNER JOIN MsSystemParamDetail mspd with(nolock) on mspd.ParamDetailId = tspa.CurrentState " +
            ") X WHERE  " +
            "ApprovalInterval > 180  " +
            "AND ((CurrentState = 'waiting_puk1' AND (PUK1ApprovalReminder IS NULL OR PUK1ApprovalReminder < 5)) OR (CurrentState = 'waiting_puk2' and (PUK2ApprovalReminder IS NULL OR PUK2ApprovalReminder < 5))) " +
            "AND CurrentStateDT BETWEEN CAST(CONVERT(VARCHAR, GETDATE()-5, 112) + ' 00:00:00' AS DATETIME) AND CAST(CONVERT(VARCHAR, GETDATE(), 112) + ' 23:59:59' AS DATETIME) ",  nativeQuery = true)
    List<PUKApprovalReminder> getListReminderApprovalPUK();
}
