package com.btpns.fin.repository;

import com.btpns.fin.model.ReportAlihDayaUser;
import com.btpns.fin.model.entity.ReportAplikasiPerTiket;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IReportUserAlihDayaRepository extends JpaRepository<ReportAlihDayaUser, String> {
    @Query(value = "Select NIKVendor, NameVendor, OccupationVendor, NIKPUK, NamePUK, MasaBerlakuSampai From TrxPUKVendor with(nolock) ORDER BY OccupationVendor ASC, NameVendor ASC", nativeQuery = true)
    List<ReportAlihDayaUser> getListReportUserAlihDaya();
}
