package com.btpns.fin.repository;

import com.btpns.fin.model.entity.MsCMS;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Optional;

@Repository
public interface ICMSUserIdRepository extends JpaRepository<MsCMS, BigInteger> {
    @Modifying
    @Query(value = "DELETE FROM MsCMS", nativeQuery = true)
    int deleteAllMsCMS();

    @Modifying
    @Query(value = "DELETE FROM MsCMS WHERE NIK = :nik", nativeQuery = true)
    public int deleteMsCMSUser(@Param("nik") String nik);

    Optional<MsCMS> findByNik(String nik);

    @Query(value = "SELECT * FROM MsCMS mc with(nolock) Where mc.NIK LIKE %:searchData%", nativeQuery = true)
    Page<MsCMS> findAllByNik(String searchData, Pageable pageable);

    @Query(value = "SELECT * FROM MsCMS mc with(nolock) Where mc.NamaUser LIKE %:searchData%", nativeQuery = true)
    Page<MsCMS> findAllByNamaUser(String searchData, Pageable pageable);
}
