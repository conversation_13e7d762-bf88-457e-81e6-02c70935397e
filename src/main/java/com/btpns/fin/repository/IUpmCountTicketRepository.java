package com.btpns.fin.repository;

import com.btpns.fin.model.entity.UpmTicket;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.List;

public interface IUpmCountTicketRepository extends JpaRepository<UpmTicket, String> {
    @Query(value = "SELECT COUNT(tfa.ticketId) FROM TrxFuidRequest tfr with(nolock) " +
            "INNER JOIN tema.dbo.TrxFuidApproval tfa with(nolock) on tfr.ticketId = tfa.ticketId " +
            "WHERE tfa.CurrentState = :status " +
            "AND (('approved' = :status AND tfr.TanggalEfektif > :sDateNow AND 1 = :isPending) OR ('approved' = :status AND tfr.TanggalEfektif <= :sDateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND (tfa.CurrentStateDT BETWEEN :startDate AND :endDate OR tfr.TanggalEfektif BETWEEN :startDateEfektif AND :endDateEfektif)", nativeQuery = true)
    public BigInteger getCountTicketFuid(@Param("status") String status, @Param("sDateNow") String sDateNow, @Param("isPending") Integer isPending,
                                         @Param("startDate") String startDate, @Param("endDate") String endDate,
                                         @Param("startDateEfektif") String startDateEfektif, @Param("endDateEfektif") String endDateEfektif);

    @Query(value = "SELECT COUNT(tspa.ticketId) FROM TrxSetupParamRequest tspr with(nolock) " +
            "INNER JOIN TrxSetupParamApproval tspa with(nolock) on tspr.ticketId = tspa.ticketId " +
            "WHERE tspa.CurrentState = :status " +
            "AND (('approved' = :status AND tspr.TanggalEfektif > :sDateNow AND 1 = :isPending) OR ('approved' = :status AND tspr.TanggalEfektif <= :sDateNow AND 0 = :isPending) OR ('approved' != :status)) " +
            "AND (tspa.CurrentStateDT BETWEEN :startDate AND :endDate OR tspr.TanggalEfektif BETWEEN :startDateEfektif AND :endDateEfektif)", nativeQuery = true)
    public BigInteger getCountTicketSetupParam(@Param("status") String status, @Param("sDateNow") String sDateNow, @Param("isPending") Integer isPending,
                                               @Param("startDate") String startDate, @Param("endDate") String endDate,
                                               @Param("startDateEfektif") String startDateEfektif, @Param("endDateEfektif") String endDateEfektif);
}
