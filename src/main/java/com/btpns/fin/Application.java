package com.btpns.fin;

import com.btpns.platform.BtpnsApplication;
import org.joda.time.DateTimeZone;
import org.springframework.boot.Banner;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

@BtpnsApplication
@EnableScheduling
@EnableAsync
public class Application {
    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Jakarta"));
        DateTimeZone.setDefault(DateTimeZone.forID("Asia/Jakarta"));
        new SpringApplicationBuilder(Application.class)
                .bannerMode(Banner.Mode.OFF)
                .build()
                .run(args);
    }
}
