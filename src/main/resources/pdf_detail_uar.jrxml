<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.0.final using JasperReports Library version 6.20.0-2bc7ab61c56f459e8176eb05c7705e145cd400ad  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="detailTiketUAR" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="40" bottomMargin="20" uuid="d56b67e8-27b0-4ce6-9e88-c7cee6d3af0a">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
    <parameter name="timelines" class="java.lang.String"/>
    <parameter name="logoBTPNS" class="java.lang.Object"/>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="ticketId" class="java.lang.String"/>
    <field name="aplikasi" class="java.lang.String"/>
    <field name="periode" class="java.lang.String"/>
    <field name="nik" class="java.lang.String"/>
    <field name="nama" class="java.lang.String"/>
    <field name="kewenangan" class="java.lang.String"/>
    <field name="jabatan" class="java.lang.String"/>
    <field name="unitKerja" class="java.lang.String"/>
    <field name="statusKonfirmasi" class="java.lang.String"/>
    <field name="keterangan" class="java.lang.String"/>
    <field name="nikPUK" class="java.lang.String"/>
    <field name="namaPUK" class="java.lang.String"/>
    <field name="jabatanPUK" class="java.lang.String"/>
    <field name="catatan" class="java.lang.String"/>
    <field name="statusTiket" class="java.lang.String"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <title>
        <band height="75" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="168" y="13" width="195" height="30" uuid="e4e6366f-63b5-4ed7-b8fb-0ae0f820a10a"/>
                <textElement textAlignment="Center">
                    <font size="14"/>
                </textElement>
                <text><![CDATA[User Access Review]]></text>
            </staticText>
            <image>
                <reportElement mode="Opaque" x="450" y="0" width="170" height="50" uuid="1adf2401-5363-4d10-9739-4b6501d8427e"/>
                <imageExpression><![CDATA[$P{logoBTPNS}]]></imageExpression>
            </image>
        </band>
    </title>
    <detail>
        <band height="220" splitType="Stretch">
            <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" x="21" y="8" width="458" height="20" uuid="9072363a-8619-432b-86d5-eefce91adf4c"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="0c96b257-1be3-4a1a-97ea-efb521545fc9"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Nomor Tiket]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="3a8d71b9-96c7-4455-970d-01ffe15ad095"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="52c0f239-291c-47ae-8710-788f517ef5a9">
                        <property name="com.jaspersoft.studio.unit.height" value="pixel"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{ticketId}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="28" width="458" height="20" uuid="aeb32f06-1aae-41d5-9732-5ab750140d96"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="4953a382-15d3-4c8d-86c3-3c009184a418"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Aplikasi]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="9fdc5a10-6fe8-4662-a640-caa77342196b"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="c9a14d4e-36c3-458e-82fb-1fb44ac96b38">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{aplikasi}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="48" width="458" height="20" uuid="8788c293-41c3-4130-b58b-1527420355e6"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="53f1121d-2b2a-47e2-b9d3-6bea90b582e7"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Periode]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="3c7169cb-14e5-42fe-9479-717a05c38261"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="c96cb3e5-f115-40cd-8b6b-1195d5b2d6ee">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{periode}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="68" width="458" height="20" uuid="8721e6de-f394-4482-ad76-57d0142075c9"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="99cebf63-327c-47f7-90f4-e221b1ba559d"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[NIK]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="14de025f-977a-4ba7-8faf-2df1e98b4fc9"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="dc1fb114-f7d7-439f-af9d-a6ebf30ba050">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nik}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="88" width="458" height="20" uuid="8a7f2cac-81e4-4c8c-bde5-10ba9d0cb8ce"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="1b12830a-ab54-4153-a6f3-1e62730b9c92"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Nama]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="96d99721-3c7a-481c-a0ac-75748b2952d9"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="8ba8e9da-**************-65597d37d32a">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nama}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="108" width="458" height="20" uuid="496b1163-a495-4f12-bcba-11b1620b67e9"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="51e1101a-6c5c-46d2-a6ce-cba84e685d01"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Kewenangan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="b9a6d848-4c7a-4f37-8346-9542e17fc623"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="743683ba-cec1-4040-a2db-c15ec34308d6">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{kewenangan}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="128" width="458" height="20" uuid="c9efe38e-cecd-43d4-a928-4a2b8009c681"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="55849566-dca2-4854-9762-212ef8c7d331"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Jabatan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="81d1d7bf-003a-410d-8508-33088cece846"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="07412b6e-8422-46f3-bc80-0c0b01ee71f4">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{jabatan}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="148" width="458" height="20" uuid="532b1f6a-e201-42f6-86ac-0897389d4b6b"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="edbb59ac-c27a-4e9b-b9a8-acbc4952caf6"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Unit Kerja]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="538f8787-992e-496b-9e7d-91a404ea746e"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="934bc138-5336-46d0-8e26-575dbf272681">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{unitKerja}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="168" width="458" height="20" uuid="a328bb71-7b54-4058-bdc1-39e7a42d5e12"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="94fe2128-e43e-4622-af6d-548085e67385"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Status Konfirmasi]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="2f0ff568-af63-47ef-8e94-67d68528586a"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="4e414078-a1a5-4101-9785-a2fb92d3f52d">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{statusKonfirmasi}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="188" width="458" height="20" uuid="794a473e-0dba-4003-9b8b-0080967f098d"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="cc079b7e-c35f-432f-977c-cb101508849b"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Keterangan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="f63f035f-b7d0-4351-90c3-58d545fbf8a6"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="61410fac-643d-45da-97ef-24404c00926a">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                        <printWhenExpression><![CDATA[$F{keterangan} != null && !$F{keterangan}.isEmpty()]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{keterangan}]]></textFieldExpression>
                </textField>
            </frame>
        </band>
        <band height="210">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" isPrintRepeatedValues="false" x="21" y="25" width="458" height="94" isRemoveLineWhenBlank="true" uuid="55fdba03-f3f2-42f2-bc23-2b53bbbd3cd2">
                    <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="200" height="20" uuid="4cda618d-6649-4788-ab6c-2e72863fef07">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Informasi Atasan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="0" y="16" width="200" height="15" uuid="e30bf2bf-e626-4302-aea8-6701499b1a51">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="9" isBold="false"/>
                    </textElement>
                    <text><![CDATA[Permohonan ini telah disetujui oleh]]></text>
                </staticText>
                <staticText>
                    <reportElement x="0" y="34" width="100" height="20" uuid="a16a755a-2563-4c03-9efc-2c1760ac386b">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[NIK]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="34" width="19" height="20" uuid="17432d4b-7c46-4db1-93e5-1479a04e9187">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="34" width="300" height="20" uuid="fc122199-98b3-497e-be2a-0cacff942e1b">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nikPUK}]]></textFieldExpression>
                </textField>
                <staticText>
                    <reportElement x="0" y="54" width="100" height="20" uuid="3bf5c008-ce47-4006-b9a1-e9ae8187ebd9">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[Nama Lengkap]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="54" width="19" height="20" uuid="5b407ca9-99fa-41cd-90fc-ab3c1fad5f86">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="54" width="300" height="20" uuid="81da9fdd-9b12-4ac4-aefe-9f24a6e80952">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{namaPUK}]]></textFieldExpression>
                </textField>
                <staticText>
                    <reportElement x="0" y="74" width="100" height="20" uuid="c4118e86-4e99-4330-9fef-2f5bcf539222">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[Jabatan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="74" width="19" height="20" uuid="0c5683da-35ea-460b-96e8-e9bfea1e7fb9">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="74" width="300" height="20" uuid="3c0c0e4b-3ef5-471b-8070-7a7d55f894c3">
                        <printWhenExpression><![CDATA[$F{nikPUK} != null && $F{namaPUK} != null && $F{jabatanPUK} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{jabatanPUK}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="130" width="458" height="69" uuid="43d1145d-a0de-4284-b55a-c975cb583b1b"/>
                <staticText>
                    <reportElement positionType="Float" x="0" y="0" width="200" height="20" uuid="083dfde4-e4d8-4cb4-b9b7-8f483f81e986"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Informasi UPM]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" x="0" y="23" width="100" height="20" uuid="51eaa872-9a23-44d2-954e-4f90aea872dd"/>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[Catatan]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" x="144" y="23" width="19" height="20" uuid="570a0a85-cd77-421b-9e7d-c47c2726d293"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement positionType="Float" x="158" y="23" width="300" height="20" uuid="e762f35d-7676-4b6a-ba2d-73f61b5ece19">
                        <printWhenExpression><![CDATA[!$F{catatan}.isEmpty() && $F{catatan} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{catatan}]]></textFieldExpression>
                </textField>
                <frame>
                    <reportElement positionType="Float" x="0" y="49" width="458" height="20" uuid="ff01d985-9e19-4eec-80f3-a7ac43d650f4"/>
                    <staticText>
                        <reportElement positionType="Float" x="0" y="0" width="100" height="20" uuid="0b94b6f6-58f3-4f56-ad09-904f6a683f18"/>
                        <textElement verticalAlignment="Middle">
                            <font/>
                        </textElement>
                        <text><![CDATA[Status Tiket]]></text>
                    </staticText>
                    <staticText>
                        <reportElement positionType="Float" x="144" y="0" width="19" height="20" uuid="d0bd7068-ee0e-4709-963f-bf63e9988e48"/>
                        <textElement textAlignment="Center" verticalAlignment="Middle">
                            <font size="11"/>
                        </textElement>
                        <text><![CDATA[:]]></text>
                    </staticText>
                    <textField>
                        <reportElement positionType="Float" x="158" y="0" width="300" height="20" uuid="bdf6acca-a310-4167-884d-07f3f55bef7b" />
                        <textElement verticalAlignment="Middle">
                            <font/>
                        </textElement>
                        <textFieldExpression><![CDATA[$F{statusTiket}]]></textFieldExpression>
                    </textField>
                </frame>
            </frame>
        </band>
        <band height="630">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" x="21" y="3" width="450" height="620" uuid="c9c5a0f1-3c98-4808-81eb-548b7acda6a1">
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="350" height="20" uuid="cca5aac7-0a95-413f-90f6-7427642e829e">
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Timeline Permohonan]]></text>
                </staticText>
                <frame>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="20" width="450" height="600" isRemoveLineWhenBlank="true" uuid="b2ee9219-e5be-42f4-b7ad-573dfa1ec6f4">
                        <printWhenExpression><![CDATA[!$P{timelines}.isEmpty() && $P{timelines} != null]]></printWhenExpression>
                    </reportElement>
                    <textField isStretchWithOverflow="true">
                        <reportElement isPrintWhenDetailOverflows="true" stretchType="ContainerBottom" x="0" y="2" width="450" height="575" uuid="fdee1150-1d11-4108-980d-4e3faba24f84">
                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                            <printWhenExpression><![CDATA[!$P{timelines}.isEmpty() && $P{timelines} != null]]></printWhenExpression>
                        </reportElement>
                        <textElement verticalAlignment="Top">
                            <font size="8"/>
                        </textElement>
                        <textFieldExpression><![CDATA[$P{timelines}]]></textFieldExpression>
                    </textField>
                </frame>
            </frame>
        </band>
    </detail>
    <pageFooter>
        <band height="50" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="130" y="15" width="270" height="20" uuid="e3c13d40-5e32-43c4-bafe-610b416ccba7"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="11" isBold="false"/>
                </textElement>
                <text><![CDATA["Detail Tiket ini di generate oleh system TEMA"]]></text>
            </staticText>
        </band>
    </pageFooter>
</jasperReport>
