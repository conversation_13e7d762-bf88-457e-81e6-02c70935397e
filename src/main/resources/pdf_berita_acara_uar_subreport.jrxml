<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.0.final using JasperReports Library version 6.20.0-2bc7ab61c56f459e8176eb05c7705e145cd400ad  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="beritaAcaraUAR" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="40" bottomMargin="20" uuid="d56b67e8-27b0-4ce6-9e88-c7cee6d3af0a">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
    <parameter name="aplikasi" class="java.lang.String"/>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="nik" class="java.lang.String"/>
    <field name="namaUser" class="java.lang.String"/>
    <field name="kewenangan" class="java.lang.String"/>
    <field name="jabatan" class="java.lang.String"/>
    <field name="unitKerja" class="java.lang.String"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <title>
        <band height="20" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="0" width="450" height="20" uuid="c9c5a0f1-3c98-4808-81eb-548b7acda6a1">
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textField>
                    <reportElement x="8" y="0" width="350" height="20" uuid="cca5aac7-0a95-413f-90f6-7427642e829e">
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10" isBold="true"/>
                    </textElement>
                    <textFieldExpression><![CDATA["List User " + $P{aplikasi} + " Tidak Aktif"]]></textFieldExpression>
                </textField>
            </frame>
        </band>
    </title>
    <columnHeader>
        <band height="30">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement x="8" y="0" width="541" height="30" uuid="3a2204c4-257c-47ba-bff1-a20faccd39ba"/>
                <staticText>
                    <reportElement positionType="Float" mode="Opaque" x="0" y="0" width="21" height="30" backcolor="#808080" uuid="de7998fe-**************-7267d7414341">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="8" isBold="true"/>
                    </textElement>
                    <text><![CDATA[NO]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" mode="Opaque" x="21" y="0" width="60" height="30" backcolor="#808080" uuid="de7998fe-**************-7267d7414341">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="8" isBold="true"/>
                    </textElement>
                    <text><![CDATA[NIK]]></text>
                </staticText>
                <staticText>
                    <reportElement mode="Opaque" x="81" y="0" width="100" height="30" backcolor="#808080" uuid="0334ac42-d8be-40b8-b15c-306b23dd2e29">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="8" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Nama]]></text>
                </staticText>
                <staticText>
                    <reportElement mode="Opaque" x="181" y="0" width="110" height="30" backcolor="#808080" uuid="d1197ceb-5b6f-4de6-97aa-336fd139043a">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="8" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Kewenangan]]></text>
                </staticText>
                <staticText>
                    <reportElement mode="Opaque" x="291" y="0" width="110" height="30" backcolor="#808080" uuid="b069dbc2-689c-4cf1-946b-4cd41186566d">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="8" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Jabatan]]></text>
                </staticText>
                <staticText>
                    <reportElement mode="Opaque" x="401" y="0" width="140" height="30" backcolor="#808080" uuid="df372015-b809-4f67-aec8-28d2f4d9e864">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="8" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Unit Kerja]]></text>
                </staticText>
            </frame>
        </band>
    </columnHeader>
    <detail>
        <band height="22" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" x="8" y="0" width="541" height="22" uuid="cd5e7826-f5b4-455f-8f7e-da66650ebeda"/>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="21" height="22" uuid="202ef399-51bf-486f-a1df-7e7f59bf8884">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="21" y="0" width="60" height="22" uuid="202ef399-51bf-486f-a1df-7e7f59bf8884">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nik}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="81" y="0" width="100" height="22" uuid="6ad4d479-1f90-4c14-95f5-508ff28b3160">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{namaUser}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="181" y="0" width="110" height="22" uuid="825e13ab-817d-41eb-ba41-c6ed3e36cc7c">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{kewenangan}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="291" y="0" width="110" height="22" uuid="463b9502-f0b7-4209-b9c8-1ad7ba17f382">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{jabatan}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="401" y="0" width="140" height="22" uuid="40267a6d-a6f6-40c7-b50f-36f60800fe76">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{unitKerja}]]></textFieldExpression>
                </textField>
            </frame>
        </band>
    </detail>
    <pageFooter>
        <band height="50" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="142" y="20" width="270" height="20" uuid="e3c13d40-5e32-43c4-bafe-610b416ccba7"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="10" isBold="false"/>
                </textElement>
                <text><![CDATA["Berita Acara ini di generate oleh system TEMA"]]></text>
            </staticText>
        </band>
    </pageFooter>
</jasperReport>
