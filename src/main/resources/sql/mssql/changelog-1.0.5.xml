<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1659694300701" author="Ryan">
        <createTable tableName="MsEmployeeHierarchy">
            <column name="NIK" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_MsEmployeeHierarchy_NIK"/>
            </column>
            <column name="FullName" type="varchar(200)"/>
            <column name="DTJoin" type="datetime"/>
            <column name="DTPermanent" type="varchar(8)"/>
            <column name="OccupationDesc" type="varchar(100)"/>
            <column name="Organization" type="varchar(100)"/>
            <column name="Location" type="varchar(100)"/>
            <column name="DirectSupervisorNIK" type="varchar(15)"/>
            <column name="DirectSupervisorName" type="varchar(255)"/>
            <column name="DirectSupervisorOccupation" type="varchar(100)"/>
            <column name="DirectSupervisorOrganization" type="varchar(100)"/>
            <column name="DirectSupervisor2NIK" type="varchar(15)"/>
            <column name="DirectSupervisor2Name" type="varchar(200)"/>
            <column name="DirectSupervisor2Occupation" type="varchar(100)"/>
            <column name="DirectSupervisor2Organization" type="varchar(100)"/>
            <column name="DTTermination" type="datetime"/>
            <column name="DTPopulateSource" type="datetime"/>
            <column name="FieldChecksum" type="varchar(100)"/>
            <column name="DTPopulate" type="datetime"/>
            <column name="SrcSystem" type="varchar(50)"/>
            <column name="SysPopulate" type="varchar(50)"/>
        </createTable>
        <addColumn tableName="TrxFuidApproval" >
            <column name="UPMInputAttachment" type="varchar(max)"/>
        </addColumn>
    </changeSet>
    <changeSet id="1659695135818" author="Ryan">
        <update tableName="TrxFuidApproval">
            <column name="UPMInputAttachment" value="[]"/>
        </update>
    </changeSet>
    <changeSet id="1661930878900" author="Ryan">
        <createTable tableName="TrxComment">
            <column name="CommentId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_TrxComment_CommentId"/>
            </column>
            <column name="TicketId" type="varchar(25)"/>
            <column name="NIKComment" type="varchar(40)"/>
            <column name="NamaComment" type="varchar(100)"/>
            <column name="CreateDateTime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="Status" type="varchar(20)"/>
            <column name="ReadDateTime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="Content" type="varchar(max)"/>
            <column name="PicUpmMakerNik" type="varchar(40)"/>
            <column name="PicUpmMakerNama" type="varchar(100)"/>
        </createTable>
    </changeSet>
    <changeSet id="1662546349348" author="ryan">
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000026"/>
            <column  name="ParamDetailDesc"  value="Update Group Email"/>
        </insert>
    </changeSet>
</databaseChangeLog>