<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1672839397178" author="Dian">
        <addColumn tableName="TrxFuidRequest">
            <column name="Role" type="varchar(50)"/>
        </addColumn>
    </changeSet>

    <changeSet id="1672839402696" author="Dian">
        <insert tableName="MsSystemParam">
            <column  name="ParamId" value="R000000001"/>
            <column  name="ParamDesc" value="role-mms"/>
        </insert>
        <insert tableName="MsSystemParam">
            <column  name="ParamId" value="R000000002"/>
            <column  name="ParamDesc" value="role-cabang"/>
        </insert>
        <insert tableName="MsSystemParam">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDesc" value="role-ho"/>
        </insert>
    </changeSet>

    <changeSet id="1672839408486" author="Dian">
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000001"/>
            <column  name="ParamDetailId" value="RM00000001"/>
            <column  name="ParamDetailDesc" value="CO"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000001"/>
            <column  name="ParamDetailId" value="RM00000002"/>
            <column  name="ParamDetailDesc" value="BM"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000001"/>
            <column  name="ParamDetailId" value="RM00000003"/>
            <column  name="ParamDetailDesc" value="Alternate BM"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000001"/>
            <column  name="ParamDetailId" value="RM00000004"/>
            <column  name="ParamDetailDesc" value="MTCO"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000002"/>
            <column  name="ParamDetailId" value="RC00000001"/>
            <column  name="ParamDetailDesc" value="CS KC/ KFO (BO - FD)"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000002"/>
            <column  name="ParamDetailId" value="RC00000002"/>
            <column  name="ParamDetailDesc" value="CS KC/ KFO (Teller)"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="R000000002"/>
            <column  name="ParamDetailId" value="RC00000003"/>
            <column  name="ParamDetailDesc" value="SCS/ BOM - FD"/>
            <column  name="Status"  value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000001"/>
            <column  name="ParamDetailDesc" value="User HO View"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000002"/>
            <column  name="ParamDetailDesc" value="B_M_Coord_"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000003"/>
            <column  name="ParamDetailDesc" value="BC"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000004"/>
            <column  name="ParamDetailDesc" value="SDH"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000005"/>
            <column  name="ParamDetailDesc" value="Helpdesk"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000006"/>
            <column  name="ParamDetailDesc" value="Payment &amp; Settlement Staff"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000007"/>
            <column  name="ParamDetailDesc" value="Payment &amp; Settlement SPV"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000008"/>
            <column  name="ParamDetailDesc" value="FA SPV"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000009"/>
            <column  name="ParamDetailDesc" value="IT DCO"/>
            <column  name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId" value="R000000003"/>
            <column  name="ParamDetailId" value="RH00000010"/>
            <column  name="ParamDetailDesc" value="IT Production Support"/>
            <column  name="Status" value="Active"/>
        </insert>
    </changeSet>
</databaseChangeLog>