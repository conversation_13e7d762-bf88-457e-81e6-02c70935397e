<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1686884129384" author="Dian">
        <sql>
            update MsProsperaRole set TemaRoleCode = 'RH00000011', SystemParamId = 'R000000003' where ProsperaRoleCode = '59'
            update MsProsperaRole set TemaRoleCode = 'RH00000012', SystemParamId = 'R000000003' where ProsperaRoleCode = '60'
            update MsProsperaRole set TemaRoleCode = 'RA00000012' where ProsperaRoleCode = '40'
            update MsProsperaRole set TemaRoleCode = 'RA00000013' where ProsperaRoleCode = '52'
            update MsProsperaRole set TemaRoleCode = 'RA00000014' where ProsperaRoleCode = '56'
            update MsProsperaRole set TemaRoleCode = 'RA00000015' where ProsperaRoleCode = '37'
            update MsProsperaRole set TemaRoleCode = 'RA00000016' where ProsperaRoleCode = '49'
            update MsProsperaRole set TemaRoleCode = 'RA00000017' where ProsperaRoleCode = '28'
            update MsProsperaRole set TemaRoleCode = 'RA00000018' where ProsperaRoleCode = '25'
            update MsProsperaRole set TemaRoleCode = 'RA00000019' where ProsperaRoleCode = '26'
            update MsProsperaRole set TemaRoleCode = 'RA00000020' where ProsperaRoleCode = '3'
        </sql>
    </changeSet>
</databaseChangeLog>