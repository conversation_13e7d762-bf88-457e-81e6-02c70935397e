<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1695091013095" author="Iqbal">
        <sql>
            ALTER TABLE dbo.MsOfficerNR
            ADD MenuID varchar(5)
            , DTStartProfile date
            , DTEndProfile date
            , DTValidityPassword date
            , KFOID int
            , KFOCode varchar(200)
            , KFOName varchar(200)
            , KCSID int
            , KCSCode varchar(1100)
            , KCSName varchar(255)
        </sql>
    </changeSet>
</databaseChangeLog>