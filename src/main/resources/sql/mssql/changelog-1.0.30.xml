<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1681176025891" author="Iqbal">
        <createTable tableName="MsS4">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsS4_Id"/>
            </column>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(40)"/>
            <column name="UnitKerja" type="varchar(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="1681271503194" author="Iqbal">
        <createTable tableName="MsSPK">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsSPK_Id"/>
            </column>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(40)"/>
            <column name="UnitKerja" type="varchar(80)"/>
        </createTable>
    </changeSet>

    <changeSet id="1681271514169" author="Iqbal">
        <createIndex indexName="IDX_MsS4_NIK" tableName="MsS4">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsS4_NamaUser" tableName="MsS4">
            <column name="NamaUser"/>
        </createIndex>

        <createIndex indexName="IDX_MsSPK_NIK" tableName="MsSPK">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsSPK_NamaUser" tableName="MsSPK">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>