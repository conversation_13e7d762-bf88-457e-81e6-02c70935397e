<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1696830653101" author="Iqbal">
        <createTable tableName="TrxUPMReminder">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxUPMReminder_Id"/>
            </column>
            <column name="Type" type="varchar(25)"/>
            <column name="CreatedAt" type="date"/>
            <column name="UpdatedAt" type="date"/>
            <column name="notified" type="boolean"/>
        </createTable>

        <createIndex indexName="IDX_TrxUPMReminder_CreatedAt" tableName="TrxUPMReminder">
            <column name="CreatedAt"/>
        </createIndex>

        <createIndex indexName="IDX_TrxUPMReminder_UpdatedAt" tableName="TrxUPMReminder">
            <column name="UpdatedAt"/>
        </createIndex>

    </changeSet>

    <changeSet id="1698029201582" author="Iqbal">
        <sql>
            ALTER TABLE dbo.MsOfficerNR
            Add DepartmentName varchar(100)
        </sql>
    </changeSet>
</databaseChangeLog>