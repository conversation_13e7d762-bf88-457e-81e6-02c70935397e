<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1694078504596" author="Iqbal">
        <addColumn tableName="MsUserIDApplication">
            <column name="Type" type="varchar(25)"/>
        </addColumn>

        <addColumn tableName="MsUserIDApplication">
            <column name="Visible" type="boolean"/>
        </addColumn>

        <addColumn tableName="MsUserIDApplication">
            <column name="UAR" type="boolean"/>
        </addColumn>
    </changeSet>

    <changeSet id="1694137505021" author="Iqbal">
        <insert tableName="MsSystemParam">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDesc" value="aplikasi-userid"/>
        </insert>
    </changeSet>

    <changeSet id="1694137521066" author="Iqbal">
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000008"/>
            <column name="ParamDetailDesc" value="Prospera Revamp"/>
            <column name="Type" value="existing"/>
            <column name="Visible" value="1"/>
            <column name="UAR" value="0"/>
        </insert>

        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000009"/>
            <column name="ParamDetailDesc" value="BI-CAC"/>
            <column name="Type" value="existing"/>
            <column name="Visible" value="1"/>
            <column name="UAR" value="0"/>
        </insert>

        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000010"/>
            <column name="ParamDetailDesc" value="DIGITUS"/>
            <column name="Type" value="existing"/>
            <column name="Visible" value="1"/>
            <column name="UAR" value="0"/>
        </insert>

        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000011"/>
            <column name="ParamDetailDesc" value="EGLS"/>
            <column name="Type" value="existing"/>
            <column name="Visible" value="1"/>
            <column name="UAR" value="0"/>
        </insert>
    </changeSet>

    <changeSet id="1694137536968" author="Iqbal">
        <update tableName="MsUserIDApplication">
            <column name="Type" value="existing"/>
            <where>ParamDetailId='AU00000001'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Type" value="existing"/>
            <where>ParamDetailId='AU00000002'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Type" value="existing"/>
            <where>ParamDetailId='AU00000003'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Type" value="existing"/>
            <where>ParamDetailId='AU00000004'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Type" value="existing"/>
            <where>ParamDetailId='AU00000005'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Type" value="existing"/>
            <where>ParamDetailId='AU00000006'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Type" value="existing"/>
            <where>ParamDetailId='AU00000007'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Visible" value="1"/>
            <where>ParamDetailId='AU00000001'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Visible" value="1"/>
            <where>ParamDetailId='AU00000002'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Visible" value="1"/>
            <where>ParamDetailId='AU00000003'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Visible" value="1"/>
            <where>ParamDetailId='AU00000004'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Visible" value="1"/>
            <where>ParamDetailId='AU00000005'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Visible" value="1"/>
            <where>ParamDetailId='AU00000006'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="Visible" value="1"/>
            <where>ParamDetailId='AU00000007'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="UAR" value="1"/>
            <where>ParamDetailId='AU00000001'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="UAR" value="1"/>
            <where>ParamDetailId='AU00000002'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="UAR" value="1"/>
            <where>ParamDetailId='AU00000003'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="UAR" value="1"/>
            <where>ParamDetailId='AU00000004'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="UAR" value="1"/>
            <where>ParamDetailId='AU00000005'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="UAR" value="1"/>
            <where>ParamDetailId='AU00000006'</where>
        </update>

        <update tableName="MsUserIDApplication">
            <column name="UAR" value="1"/>
            <where>ParamDetailId='AU00000007'</where>
        </update>
    </changeSet>

    <changeSet id="1694484373536" author="Iqbal">
        <addColumn tableName="TrxFuidApproval">
            <column name="PUK1Name" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxFuidApproval">
            <column name="PUK1Occupation" type="varchar(200)"/>
        </addColumn>

        <addColumn tableName="TrxFuidApproval">
            <column name="PUK2Name" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxFuidApproval">
            <column name="PUK2Occupation" type="varchar(200)"/>
        </addColumn>

        <addColumn tableName="TrxFuidRequest">
            <column name="PICEmailGroupName" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxFuidRequest">
            <column name="PICEmailGroupOccupation" type="varchar(200)"/>
        </addColumn>

        <addColumn tableName="TrxFuidRequest">
            <column name="AltPICEmailGroupName" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxFuidRequest">
            <column name="AltPICEmailGroupOccupation" type="varchar(200)"/>
        </addColumn>

        <addColumn tableName="TrxFuidRequest">
            <column name="PUKVendorNIK" type="varchar(25)"/>
        </addColumn>

        <addColumn tableName="TrxFuidRequest">
            <column name="PUKVendorName" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxFuidRequest">
            <column name="PUKVendorOccupation" type="varchar(200)"/>
        </addColumn>
    </changeSet>

    <changeSet id="1694489317981" author="Iqbal">
        <addColumn tableName="TrxSetupParamApproval">
            <column name="PUK1Name" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxSetupParamApproval">
            <column name="PUK1Occupation" type="varchar(200)"/>
        </addColumn>

        <addColumn tableName="TrxSetupParamApproval">
            <column name="PUK2Name" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxSetupParamApproval">
            <column name="PUK2Occupation" type="varchar(200)"/>
        </addColumn>

    </changeSet>

    <changeSet id="1694490341368" author="Iqbal">
        <addColumn tableName="TrxUARApproval">
            <column name="PUKName" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxUARApproval">
            <column name="PUKOccupation" type="varchar(200)"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>