<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1693362201826" author="Iqbal">
        <createTable tableName="TrxPUKVendorBatch">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxPUKVendorBatch_Id"/>
            </column>
            <column name="BatchId" type="varchar(50)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="BatchFileName" type="varchar(100)"/>
            <column name="TotalData" type="int"/>
            <column name="UploaderNIK" type="varchar(25)"/>
            <column name="UploaderName" type="varchar(100)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>