<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1691660211324" author="Iqbal">
        <addColumn tableName="MsDboRTGS">
            <column name="Jabatan" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="MsSlik">
            <column name="Jabatan" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="MsSPK">
            <column name="Jabatan" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="MsS4">
            <column name="Jabatan" type="varchar(100)"/>
        </addColumn>
    </changeSet>

    <changeSet id="1691988275693" author="Iqbal">
        <addColumn tableName="TrxUARRequest">
            <column name="IsManualConfirmation" type="boolean"/>
        </addColumn>

        <addColumn tableName="TrxUARRequest">
            <column name="Reminder" type="integer"/>
        </addColumn>

        <addColumn tableName="TrxUARApproval">
            <column name="UPMMakerAttachment" type="varchar(max)"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>