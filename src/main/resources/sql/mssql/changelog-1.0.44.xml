<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1701747252745" author="Iqbal">
        <createTable tableName="MsApprovalKewenanganLimit">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsApprovalKewenanganLimit_Id"/>
            </column>
            <column name="ApprovalLevel" type="varchar(100)"/>
            <column name="IsKcKFO" type="boolean"/>
            <column name="IsHO" type="boolean"/>
            <column name="MinTunai" type="numeric(18,2)"/>
            <column name="MaxTunai" type="numeric(18,2)"/>
            <column name="MinNonTunai" type="numeric(18,2)"/>
            <column name="MaxNonTunai" type="numeric(18,2)"/>
            <column name="UpdatedAt" type="timestamp" defaultValueComputed="current_timestamp"/>
        </createTable>

    </changeSet>

    <changeSet id="1701747547047" author="Iqbal">
        <insert tableName="MsApprovalKewenanganLimit">
            <column name="ApprovalLevel" value="TROPS &amp; TRANSFER SERVICES MANAGER"/>
            <column name="IsKcKFO" value="0"/>
            <column name="IsHO" value="1"/>
            <column name="MinTunai" value="0"/>
            <column name="MaxTunai" value="0"/>
            <column name="MinNonTunai" value="0"/>
            <column name="MaxNonTunai" value="150000000000"/>
        </insert>

        <insert tableName="MsApprovalKewenanganLimit">
            <column name="ApprovalLevel" value="TIME DEPOSIT SERVICES MANAGER"/>
            <column name="IsKcKFO" value="0"/>
            <column name="IsHO" value="1"/>
            <column name="MinTunai" value="0"/>
            <column name="MaxTunai" value="0"/>
            <column name="MinNonTunai" value="0"/>
            <column name="MaxNonTunai" value="150000000000"/>
        </insert>

        <insert tableName="MsApprovalKewenanganLimit">
            <column name="ApprovalLevel" value="PAYMENT SERVICES MANAGER"/>
            <column name="IsKcKFO" value="0"/>
            <column name="IsHO" value="1"/>
            <column name="MinTunai" value="0"/>
            <column name="MaxTunai" value="0"/>
            <column name="MinNonTunai" value="0"/>
            <column name="MaxNonTunai" value="150000000000"/>
        </insert>

        <insert tableName="MsApprovalKewenanganLimit">
            <column name="ApprovalLevel" value="CENTRALIZED TRANSACTION SERVICES HEAD"/>
            <column name="IsKcKFO" value="0"/>
            <column name="IsHO" value="1"/>
            <column name="MinTunai" value="1"/>
            <column name="MaxTunai" value="5000000000"/>
            <column name="MinNonTunai" value="150000000001"/>
            <column name="MaxNonTunai" value="250000000000"/>
        </insert>

        <insert tableName="MsApprovalKewenanganLimit">
            <column name="ApprovalLevel" value="OPERATION DISTRIBUTION HEAD"/>
            <column name="IsKcKFO" value="1"/>
            <column name="IsHO" value="0"/>
            <column name="MinTunai" value="0"/>
            <column name="MaxTunai" value="5000000000"/>
            <column name="MinNonTunai" value="0"/>
            <column name="MaxNonTunai" value="250000000000"/>
        </insert>

        <insert tableName="MsApprovalKewenanganLimit">
            <column name="ApprovalLevel" value="TRANSACTION SERVICES HEAD"/>
            <column name="IsKcKFO" value="1"/>
            <column name="IsHO" value="1"/>
            <column name="MinTunai" value="5000000001"/>
            <column name="MaxTunai" value="10000000000"/>
            <column name="MinNonTunai" value="250000000001"/>
            <column name="MaxNonTunai" value="350000000000"/>
        </insert>

        <insert tableName="MsApprovalKewenanganLimit">
            <column name="ApprovalLevel" value="DIRECTOR"/>
            <column name="IsKcKFO" value="1"/>
            <column name="IsHO" value="1"/>
            <column name="MinTunai" value="10000000001"/>
            <column name="MaxTunai" value="999999999999"/>
            <column name="MinNonTunai" value="350000000001"/>
            <column name="MaxNonTunai" value="999999999999"/>
        </insert>
    </changeSet>
    
    <changeSet id="1701943380149" author="Iqbal">
        <addColumn tableName="MsProsperaRole">
            <column name="IsActive" type="boolean"/>
        </addColumn>
    </changeSet>
    
    <changeSet id="1701943532893" author="Iqbal">
        <update tableName="MsProsperaRole">
            <column name="IsActive" value="1"/>
        </update>
    </changeSet>

</databaseChangeLog>