<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="ryan" id="1656659809838">
        <addColumn tableName="TrxFuidRequest" >
            <column name="requestId" type="varchar(40)"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamRequest" >
            <column name="requestId" type="varchar(40)"/>
        </addColumn>
        <createIndex indexName="IDX_TrxFuidRequest_requestId" tableName="TrxFuidRequest">
            <column name="requestId"/>
            <column name="CreateDatetime"/>
        </createIndex>
        <createIndex indexName="IDX_TrxSetupParamRequest_requestId" tableName="TrxSetupParamRequest">
            <column name="requestId"/>
            <column name="CreateDatetime"/>
        </createIndex>
    </changeSet>
    <changeSet author="ryan" id="1656991885216">
        <addColumn tableName="TrxPUKVendor" >
            <column name="NameVendor" type="varchar(200)"/>
            <column name="OccupationVendor" type="varchar(100)"/>
            <column name="OccupationDescVendor" type="varchar(100)"/>
            <column name="NamePUK" type="varchar(200)"/>
        </addColumn>
        <loadData file="TrxPUKVendor.csv"
                  relativeToChangelogFile="true"
                  tableName="TrxPUKVendor">
            <column header="NIK"
                    name="NIKVendor"/>
            <column header="NAMA"
                    name="NameVendor"/>
            <column header="POSISI"
                    name="OccupationVendor"/>
            <column header="POSISI DESKRIPSI"
                    name="OccupationDescVendor"/>
            <column header="NIK ATASAN"
                    name="NIKPUK"/>
            <column header="NAMA ATASAN"
                    name="NamePUK"/>
            <column header="MASA BERLAKU"
                    name="MasaBerlakuSampai"/>
        </loadData>
    </changeSet>
    <changeSet id="1657790367102" author="ryan">
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000025"/>
            <column  name="ParamDetailDesc"  value="Lainnya"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000016"/>
            <column  name="ParamDetailDesc"  value="Lainnya"/>
        </insert>
    </changeSet>
</databaseChangeLog>