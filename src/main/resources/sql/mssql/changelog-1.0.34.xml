<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1686812249168" author="Iqbal">
        <createTable tableName="TrxUARRequest">
            <column name="TicketId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_TrxUARRequest_TicketId"/>
            </column>
            <column name="CreatedAt" type="timestamp" defaultValueComputed="current_timestamp"/>
            <column name="Aplikasi" type="varchar(max)"/>
            <column name="PeriodYear" type="integer"/>
            <column name="PeriodQuarter" type="varchar(5)"/>
            <column name="NIK" type="varchar(25)"/>
            <column name="namaUser" type="varchar(100)"/>
            <column name="kewenangan" type="varchar(100)"/>
            <column name="jabatan" type="varchar(100)"/>
            <column name="unitKerja" type="varchar(200)"/>
            <column name="email" type="varchar(100)"/>
        </createTable>

        <createIndex indexName="IDX_TrxUARRequest_NIK" tableName="TrxUARRequest">
            <column name="NIK"/>
        </createIndex>
    </changeSet>

    <changeSet id="1686896985366" author="Iqbal">
        <createTable tableName="TrxUARApproval">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxUARApproval_Id"/>
            </column>
            <column name="TicketId" type="varchar(25)">
                <constraints nullable="false" foreignKeyName="fk_TrxUARApproval_TicketId" references="TrxUARRequest(TicketId)"/>
            </column>
            <column name="CurrentState" type="varchar(100)"/>
            <column name="CurrentStateDT" type="datetime"/>
            <column name="UserNIK" type="varchar(25)"/>
            <column name="UserNIKStatus" type="varchar(25)"/>
            <column name="UserResponseDT" type="datetime"/>
            <column name="UserConfirmation" type="tinyint(1)"/>
            <column name="UserNIKNotes" type="varchar(max)"/>
            <column name="PUKNIK" type="varchar(25)"/>
            <column name="PUKStatus" type="varchar(25)"/>
            <column name="PUKApprovalDT" type="datetime"/>
            <column name="PUKNotes" type="varchar(max)"/>
            <column name="PUKDelegationId" type="varchar(25)"/>
            <column name="UPMMakerNIK" type="varchar(25)"/>
            <column name="UPMMakerStatus" type="varchar(25)"/>
            <column name="UPMMakerProcessDT" type="datetime"/>
            <column name="UPMMakerNotes" type="varchar(max)"/>
            <column name="UPMCheckerNIK" type="varchar(25)"/>
            <column name="UPMCheckerStatus" type="varchar(25)"/>
            <column name="UPMCheckerApprovalDT" type="datetime"/>
            <column name="UPMCheckerNotes" type="varchar(max)"/>
        </createTable>

        <createIndex indexName="IDX_TrxUARApproval_PUKNIK" tableName="TrxUARApproval">
            <column name="PUKNIK"/>
        </createIndex>
    </changeSet>

    <changeSet id="1686898764782" author="Iqbal">
        <createTable tableName="TrxUARAudittrail">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxUARAudittrail_Id"/>
            </column>
            <column name="NIK" type="varchar(40)"/>
            <column name="Action" type="varchar(40)"/>
            <column name="CreatedAt" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="TicketId" type="varchar(25)"/>
            <column name="AdditionalInfo" type="varchar(max)"/>
        </createTable>

        <createIndex indexName="IDX_TrxUARAudittrail_TicketId" tableName="TrxUARAudittrail">
            <column name="TicketId"/>
        </createIndex>
    </changeSet>

    <changeSet id="1687156944965" author="Iqbal">
        <createTable tableName="MsUserIDApplication">
            <column name="ParamId" type="varchar(25)"/>
            <column name="ParamDetailId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_MsUserIDApplication_ParamDetailId"/>
            </column>
            <column name="ParamDetailDesc" type="varchar(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="1687157300595" author="Iqbal">
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000001"/>
            <column name="ParamDetailDesc" value="DBO-RTGS"/>
        </insert>
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000002"/>
            <column name="ParamDetailDesc" value="S4"/>
        </insert>
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000003"/>
            <column name="ParamDetailDesc" value="SPK"/>
        </insert>
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000004"/>
            <column name="ParamDetailDesc" value="SLIK"/>
        </insert>
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000005"/>
            <column name="ParamDetailDesc" value="CMS"/>
        </insert>
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000006"/>
            <column name="ParamDetailDesc" value="Tepat MBanking Individu"/>
        </insert>
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000007"/>
            <column name="ParamDetailDesc" value="Tepat MBanking Corporate"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column name="ParamId" value="STS0000000"/>
            <column name="ParamDetailId" value="pending_user"/>
            <column name="ParamDetailDesc" value="Menunggu Konfirmasi User"/>
            <column name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column name="ParamId" value="STS0000000"/>
            <column name="ParamDetailId" value="pending_puk"/>
            <column name="ParamDetailDesc" value="Menunggu Persetujuan PUK"/>
            <column name="Status" value="Active"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column name="ParamId" value="STS0000000"/>
            <column name="ParamDetailId" value="confirmed"/>
            <column name="ParamDetailDesc" value="Dikonfirmasi"/>
            <column name="Status" value="Active"/>
        </insert>
    </changeSet>
</databaseChangeLog>