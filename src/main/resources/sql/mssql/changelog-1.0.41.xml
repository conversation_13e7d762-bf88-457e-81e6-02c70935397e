<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1695285605307" author="Iqbal">
        <createTable tableName="MsCustomUserID">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsCustomUserID_Id"/>
            </column>
            <column name="ParamDetailId" type="varchar(25)"/>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(100)"/>
            <column name="Jabatan" type="varchar(100)"/>
            <column name="UnitKerja" type="varchar(100)"/>
        </createTable>

        <createIndex indexName="IDX_MsCustomUserID_ParamDetailId" tableName="MsCustomUserID">
            <column name="ParamDetailId"/>
        </createIndex>

        <createIndex indexName="IDX_MsCustomUserID_NIK" tableName="MsCustomUserID">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsCustomUserID_NamaUser" tableName="MsCustomUserID">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>