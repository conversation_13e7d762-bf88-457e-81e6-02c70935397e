<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1693368765787" author="Dian">
        <sql>
            UPDATE MsProsperaRole SET ProsperaRoleCode = '55' WHERE TemaRoleCode = 'RM00000004'
        </sql>
    </changeSet>

    <changeSet id="1693368772213" author="Dian">
        <createTable tableName="TrxEmailAudittrail">
            <column name="RequestId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_TrxEmailAudittrail_RequestId"/>
            </column>
            <column name="CreatedAt" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="UpdatedAt" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="EmailSubject" type="varchar(max)"/>
            <column name="EmailDestination" type="varchar(100)"/>
            <column name="EmailCc" type="varchar(100)"/>
            <column name="EmailBcc" type="varchar(100)"/>
            <column name="EmailMessage" type="varchar(max)"/>
            <column name="EmailAttachments" type="varchar(max)"/>
            <column name="Status" type="varchar(20)"/>
        </createTable>

        <createIndex indexName="IDX_TrxEmailAudittrail_RequestId" tableName="TrxEmailAudittrail">
            <column name="RequestId"/>
        </createIndex>
    </changeSet>

    <changeSet id="1693368785807" author="Dian">
        <createTable tableName="Scheduler">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_Scheduler_Id"/>
            </column>
            <column name="SchedulerKey" type="varchar(50)"/>
            <column name="ExecuteDateTime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="Status" type="varchar(50)"/>
        </createTable>

        <addUniqueConstraint
                constraintName="UNIQUE_SCHEDULER_EXECUTEDATETIME_SCHEDULERKEY"
                tableName="Scheduler"
                columnNames="ExecuteDateTime,SchedulerKey">
        </addUniqueConstraint>
    </changeSet>
</databaseChangeLog>