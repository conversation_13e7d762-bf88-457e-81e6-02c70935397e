<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet  author="Wawan"  id="1648025434443">
        <createTable tableName="MsSystemParam">
            <column name="ParamId" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsSystemParam_ParamId"/>
            </column>
            <column name="ParamDesc" type="varchar(max)"/>
        </createTable>
        <createTable tableName="MsSystemParamDetail">
            <column name="ParamId" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsSystemParamDetail_ParamId"/>
            </column>
            <column name="ParamDetailId" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsSystemParamDetail_ParamId"/>
            </column>
            <column name="ParamDetailDesc" type="varchar(max)"/>
        </createTable>
        <createTable tableName="MsCabang">
            <column name="CabangId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_MsCabang_CabangId"/>
            </column>
            <column name="CabangDesc" type="varchar(max)"/>
        </createTable>
        <createTable tableName="TrxFuidRequest">
            <column name="TicketId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_TrxFuidRequest_TicketId"/>
            </column>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="Tujuan" type="varchar(40)"/>
            <column name="Alasan" type="varchar(40)"/>
            <column name="TanggalEfektif" type="date"/>
            <column name="DataNIK" type="varchar(40)"/>
            <column name="DataNamaLengkap" type="varchar(100)"/>
            <column name="DataJabatan" type="varchar(40)"/>
            <column name="DataKodeCabang" type="varchar(10)"/>
            <column name="DataNamaCabang" type="varchar(200)"/>
            <column name="DataTelepon" type="varchar(25)"/>
            <column name="DataNamaVendor" type="varchar(40)"/>
            <column name="NIKRequester" type="varchar(40)"/>
            <column name="Aplikasi" type="varchar(max)"/>
            <column name="Tingkatan" type="varchar(max)"/>
            <column name="StatusMasaBerlaku" type="varchar(10)"/>
            <column name="MasaBerlakuSampai" type="date"/>
            <column name="AlasanPengajuan" type="varchar(max)"/>
            <column name="InfoTambahan" type="varchar(max)"/>
            <column name="Attachment" type="varbinary(max)"/>
        </createTable>
        <createTable tableName="TrxFuidApproval">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxFuidApproval_Id"/>
            </column>
            <column name="TicketId" type="varchar(25)">
                <constraints nullable="false" foreignKeyName="fk_TrxFuidApproval_TicketId" references="TrxFuidRequest(TicketId)"/>
            </column>
            <column name="CurrentState" type="varchar(20)"/>
            <column name="CurrentStateDT" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="PUK1NIK" type="varchar(10)"/>
            <column name="PUK1Status" type="varchar(20)"/>
            <column name="PUK1Dt" type="date"/>
            <column name="PUK1Notes" type="varchar(80)"/>

            <column name="PUK2NIK" type="varchar(10)"/>
            <column name="PUK2Status" type="varchar(20)"/>
            <column name="PUK2Dt" type="date"/>
            <column name="PUK2Notes" type="varchar(80)"/>

            <column name="UPMInputNIK" type="varchar(10)"/>
            <column name="UPMInputStatus" type="varchar(20)"/>
            <column name="UPMInputDt" type="date"/>
            <column name="UPMInputNotes" type="varchar(80)"/>

            <column name="UPMCheckerNIK" type="varchar(10)"/>
            <column name="UPMCheckerStatus" type="varchar(20)"/>
            <column name="UPMCheckerDt" type="date"/>
            <column name="UPMCheckerNotes" type="varchar(80)"/>
        </createTable>
        <createTable tableName="TrxSetupParamRequest">
            <column name="TicketId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_TrxSetupParamRequest_TicketId"/>
            </column>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="TanggalEfektif" type="date"/>
            <column name="DataNIK" type="varchar(40)"/>
            <column name="DataNamaLengkap" type="varchar(100)"/>
            <column name="DataJabatan" type="varchar(40)"/>
            <column name="DataKodeCabang" type="varchar(10)"/>
            <column name="DataNamaCabang" type="varchar(200)"/>
            <column name="DataTelepon" type="varchar(25)"/>
            <column name="NIKRequester" type="varchar(40)"/>
            <column name="Aplikasi" type="varchar(max)"/>
            <column name="ParameterLama" type="varchar(max)"/>
            <column name="ParameterBaru" type="varchar(max)"/>
            <column name="Attachment" type="varbinary(max)"/>
        </createTable>
        <createTable tableName="TrxSetupParamApproval">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxSetupParamApproval_Id"/>
            </column>
            <column name="TicketId" type="varchar(25)">
                <constraints nullable="false" foreignKeyName="fk_TrxSetupParamApproval_TicketId" references="TrxSetupParamRequest(TicketId)"/>
            </column>
            <column name="CurrentState" type="varchar(20)"/>
            <column name="CurrentStateDT" type="datetime" defaultValueComputed="current_timestamp"/>

            <column name="PUK1NIK" type="varchar(10)"/>
            <column name="PUK1Status" type="varchar(20)"/>
            <column name="PUK1Dt" type="date"/>
            <column name="PUK1Notes" type="varchar(80)"/>

            <column name="PUK2NIK" type="varchar(10)"/>
            <column name="PUK2Status" type="varchar(20)"/>
            <column name="PUK2Dt" type="date"/>
            <column name="PUK2Notes" type="varchar(80)"/>

            <column name="UPMInputNIK" type="varchar(10)"/>
            <column name="UPMInputStatus" type="varchar(20)"/>
            <column name="UPMInputDt" type="date"/>
            <column name="UPMInputNotes" type="varchar(80)"/>

            <column name="UPMCheckerNIK" type="varchar(10)"/>
            <column name="UPMCheckerStatus" type="varchar(20)"/>
            <column name="UPMCheckerDt" type="date"/>
            <column name="UPMCheckerNotes" type="varchar(80)"/>
        </createTable>
        <createTable tableName="TrxAudittrail">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxAudittrail_Id"/>
            </column>
            <column name="NIK" type="varchar(40)"/>
            <column name="Action" type="varchar(40)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="TicketId" type="varchar(25)"/>
        </createTable>
        <insert tableName="MsSystemParam">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDesc"  value="aplikasi-fuid"/>
        </insert>
        <insert tableName="MsSystemParam">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDesc"  value="aplikasi-setupparameter"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000001"/>
            <column  name="ParamDetailDesc"  value="email"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000002"/>
            <column  name="ParamDetailDesc"  value="Prospera / Terra / Agendaku"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000003"/>
            <column  name="ParamDetailDesc"  value="SCBS T24"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000004"/>
            <column  name="ParamDetailDesc"  value="Digitus"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000005"/>
            <column  name="ParamDetailDesc"  value="BI CAC"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000006"/>
            <column  name="ParamDetailDesc"  value="CMS (Card Management System)"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000007"/>
            <column  name="ParamDetailDesc"  value="SKN Konverter"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000008"/>
            <column  name="ParamDetailDesc"  value="DBO-RTGS"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000009"/>
            <column  name="ParamDetailDesc"  value="EGLS"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000010"/>
            <column  name="ParamDetailDesc"  value="Epayment"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000011"/>
            <column  name="ParamDetailDesc"  value="ETP"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000012"/>
            <column  name="ParamDetailDesc"  value="MBanking"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000013"/>
            <column  name="ParamDetailDesc"  value="ORMS"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000014"/>
            <column  name="ParamDetailDesc"  value="RSD"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000015"/>
            <column  name="ParamDetailDesc"  value="Silk"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000016"/>
            <column  name="ParamDetailDesc"  value="SPK"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000017"/>
            <column  name="ParamDetailDesc"  value="SSSS (S4)"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000018"/>
            <column  name="ParamDetailDesc"  value="Tepat Mbanking / Internet Banking"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000019"/>
            <column  name="ParamDetailDesc"  value="Voice Logger"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000020"/>
            <column  name="ParamDetailDesc"  value="Web Mprospera"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000021"/>
            <column  name="ParamDetailDesc"  value="Xlink/Xcard"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000022"/>
            <column  name="ParamDetailDesc"  value="Email Group"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000023"/>
            <column  name="ParamDetailDesc"  value="Email Keluar"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000001"/>
            <column  name="ParamDetailDesc"  value="Prospera"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000002"/>
            <column  name="ParamDetailDesc"  value="SCBS T24"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000003"/>
            <column  name="ParamDetailDesc"  value="CMS (Card Management System)"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000004"/>
            <column  name="ParamDetailDesc"  value="SKN Konverter"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000005"/>
            <column  name="ParamDetailDesc"  value="DBO-RTGS"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000006"/>
            <column  name="ParamDetailDesc"  value="EGLS"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000007"/>
            <column  name="ParamDetailDesc"  value="Epayment"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000008"/>
            <column  name="ParamDetailDesc"  value="ETP"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000009"/>
            <column  name="ParamDetailDesc"  value="MBanking"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000010"/>
            <column  name="ParamDetailDesc"  value="RSD"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000011"/>
            <column  name="ParamDetailDesc"  value="SPK"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000012"/>
            <column  name="ParamDetailDesc"  value="SSSS (S4)"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000013"/>
            <column  name="ParamDetailDesc"  value="Tepat Mbanking / Internet Banking"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000014"/>
            <column  name="ParamDetailDesc"  value="Web Mprospera"/>
        </insert>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000002"/>
            <column  name="ParamDetailId"  value="AP00000015"/>
            <column  name="ParamDetailDesc"  value="Email Group"/>
        </insert>

        <createTable tableName="MsOfficer">
            <column name="OfficerID" type="int">
                <constraints primaryKey="true" primaryKeyName="PK_MsOfficer_OfficerID"/>
            </column>
            <column name="MprosperaOfficerID" type="decimal(19,0)"/>
            <column name="MMSID" type="int"/>
            <column name="RoleID" type="int"/>
            <column name="MMSCode" type="varchar(5)"/>
            <column name="OfficerCode" type="varchar(6)"/>
            <column name="DTPopulate" type="datetime"/>
            <column name="SysPopulate" type="varchar(50)"/>
            <column name="OfficerName" type="varchar(200)"/>
            <column name="NIK" type="varchar(200)"/>
            <column name="LoginName" type="varchar(200)"/>
            <column name="EmailName" type="varchar(255)"/>
            <column name="RoleName" type="varchar(150)"/>
            <column name="OfficerStatusCode" type="int"/>
            <column name="OfficerStatusDesc" type="varchar(100)"/>
            <column name="AmtApprovalLimit" type="decimal(22,4)"/>
            <column name="DTKafka" type="datetime"/>
        </createTable>
        <createIndex indexName="IDX_MsOfficer_OfficerCode" tableName="MsOfficer">
            <column name="OfficerCode"/>
        </createIndex>
        <createIndex indexName="IDX_MsOfficer_MMSCode" tableName="MsOfficer">
            <column name="MMSCode"/>
        </createIndex>
        <createIndex indexName="IDX_MsOfficer_RoleName" tableName="MsOfficer">
            <column name="RoleName"/>
        </createIndex>
        <createIndex indexName="IDX_MsOfficer_NIK" tableName="MsOfficer">
            <column name="NIK"/>
        </createIndex>

        <createTable tableName="MsMMS">
            <column name="MMSID" type="int">
                <constraints primaryKey="true" primaryKeyName="PK__MsMMS__57559C4A605F4B98"/>
            </column>
            <column name="Address" type="varchar(400)"/>
            <column name="CostCenter" type="varchar(20)"/>
            <column name="DistrictDesc" type="varchar(100)"/>
            <column name="KFOCode" type="varchar(50)"/>
            <column name="KFOName" type="varchar(50)"/>
            <column name="MMSCode" type="varchar(10)"/>
            <column name="MMSName" type="varchar(200)"/>
            <column name="MMSStatusCode" type="varchar(300)"/>
            <column name="MMSStatusDesc" type="varchar(100)"/>
            <column name="ProvinceDesc" type="varchar(100)"/>
            <column name="RTRW" type="varchar(200)"/>
            <column name="StateDesc" type="varchar(100)"/>
            <column name="SubDistrictDesc" type="varchar(200)"/>
            <column name="ZipCode" type="varchar(20)"/>
        </createTable>
    </changeSet>
    <changeSet  author="Ryan"  id="1649297453000">
        <createTable tableName="MsEmployee">
            <column name="NIK" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK__MsEmployee__ID"/>
            </column>
            <column name="IdentityNum" type="varchar(25)"/>
            <column name="FullName" type="varchar(200)"/>
            <column name="Occupation" type="varchar(100)"/>
            <column name="OccupationDesc" type="varchar(100)"/>
            <column name="StatusEmployeeDesc" type="varchar(100)"/>
            <column name="BranchCode" type="varchar(10)"/>
            <column name="CostCenterCode" type="varchar(10)"/>
            <column name="DirectSupervisorNIK" type="varchar(15)"/>
            <column name="DirectSupervisorName" type="varchar(15)"/>
            <column name="FieldChecksum" type="varchar(100)"/>
            <column name="SrcSystem" type="varchar(50)"/>
            <column name="DTPopulate" type="datetime"/>
            <column name="SysPopulate" type="varchar(50)"/>
        </createTable>
    </changeSet>
    <changeSet  author="Ryan"  id="1649338750000">
        <addColumn tableName="TrxFuidRequest" >
            <column name="DataUserId" type="varchar(50)"/>
            <column name="DataEmail" type="varchar(max)"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamRequest" >
            <column name="KategoriParamId" type="varchar(50)"/>
            <column name="KategoriParamName" type="varchar(max)"/>
            <column name="AlasanPengajuan" type="varchar(max)"/>
        </addColumn>
    </changeSet>
    <changeSet  author="Ryan"  id="1649409385431">
        <addColumn tableName="TrxSetupParamRequest" >
            <column name="DataEmail" type="varchar(max)"/>
        </addColumn>
    </changeSet>
    <changeSet  author="Ryan"  id="1650424376403">
        <createTable tableName="TrxUpmRole">
            <column name="NIK" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK__TrxUpmRole__ID"/>
            </column>
            <column name="Nama" type="varchar(max)"/>
            <column name="Role" type="varchar(25)"/>
        </createTable>
    </changeSet>
    <changeSet  author="Ryan"  id="1650604435761">
        <createIndex indexName="IDX_TrxFuidRequest_TanggalEfektif" tableName="TrxFuidRequest">
            <column name="TanggalEfektif"/>
        </createIndex>
        <createIndex indexName="IDX_TrxSetupParamRequest_TanggalEfektif" tableName="TrxSetupParamRequest">
            <column name="TanggalEfektif"/>
        </createIndex>
        <createIndex indexName="IDX_TrxFuidApproval_CurrentState" tableName="TrxFuidApproval">
            <column name="CurrentState"/>
        </createIndex>
        <createIndex indexName="IDX_TrxSetupParamApproval_CurrentState" tableName="TrxSetupParamApproval">
            <column name="CurrentState"/>
        </createIndex>
    </changeSet>
    <changeSet  author="Ryan"  id="1652101084247">
        <modifyDataType  columnName="PUK1NIK" newDataType="varchar(40)" tableName="TrxFuidApproval"/>
        <modifyDataType  columnName="PUK2NIK" newDataType="varchar(40)" tableName="TrxFuidApproval"/>
        <modifyDataType  columnName="UPMInputNIK" newDataType="varchar(40)" tableName="TrxFuidApproval"/>
        <modifyDataType  columnName="UPMCheckerNIK" newDataType="varchar(40)" tableName="TrxFuidApproval"/>
        <modifyDataType  columnName="PUK1NIK" newDataType="varchar(40)" tableName="TrxSetupParamApproval"/>
        <modifyDataType  columnName="PUK2NIK" newDataType="varchar(40)" tableName="TrxSetupParamApproval"/>
        <modifyDataType  columnName="UPMInputNIK" newDataType="varchar(40)" tableName="TrxSetupParamApproval"/>
        <modifyDataType  columnName="UPMCheckerNIK" newDataType="varchar(40)" tableName="TrxSetupParamApproval"/>
    </changeSet>
    <changeSet id="Ryan" author="1652320658239">
        <addColumn tableName="TrxAudittrail" >
            <column name="AdditionalInfo" type="varchar(max)"/>
        </addColumn>
    </changeSet>
    <changeSet id="Ryan" author="1652342195721">
        <modifyDataType  columnName="PUK1Notes" newDataType="varchar(max)" tableName="TrxFuidApproval"/>
        <modifyDataType  columnName="PUK2Notes" newDataType="varchar(max)" tableName="TrxFuidApproval"/>
        <modifyDataType  columnName="UPMInputNotes" newDataType="varchar(max)" tableName="TrxFuidApproval"/>
        <modifyDataType  columnName="UPMCheckerNotes" newDataType="varchar(max)" tableName="TrxFuidApproval"/>
        <modifyDataType  columnName="PUK1Notes" newDataType="varchar(max)" tableName="TrxSetupParamApproval"/>
        <modifyDataType  columnName="PUK2Notes" newDataType="varchar(max)" tableName="TrxSetupParamApproval"/>
        <modifyDataType  columnName="UPMInputNotes" newDataType="varchar(max)" tableName="TrxSetupParamApproval"/>
        <modifyDataType  columnName="UPMCheckerNotes" newDataType="varchar(max)" tableName="TrxSetupParamApproval"/>
    </changeSet>
    <changeSet id="Ryan" author="1652406916699">
        <createTable tableName="MMS_UPM">
            <column name="MMSCode" type="varchar(10)">
                <constraints primaryKey="true" primaryKeyName="PK__MMS_UPM__ID"/>
            </column>
            <column name="MMSName" type="varchar(200)"/>
            <column name="Address" type="varchar(400)"/>
            <column name="KFOCode" type="varchar(50)"/>
            <column name="KFOName" type="varchar(100)"/>
            <column name="KCSCode" type="varchar(50)"/>
            <column name="KCSName" type="varchar(100)"/>
        </createTable>
    </changeSet>
    <changeSet id="Ryan" author="1653050187279">
        <modifyDataType  columnName="Attachment" newDataType="varchar(max)" tableName="TrxFuidRequest"/>
        <modifyDataType  columnName="Attachment" newDataType="varchar(max)" tableName="TrxSetupParamRequest"/>
        <addColumn tableName="TrxFuidRequest" >
            <column name="FileName" type="varchar(max)"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamRequest" >
            <column name="FileName" type="varchar(max)"/>
        </addColumn>
    </changeSet>
    <changeSet id="Ryan" author="1653366660725">
        <addColumn tableName="TrxFuidRequest" >
            <column name="TipeLimitTransaksi" type="varchar(20)"/>
            <column name="NominalTransaksi" type="numeric(18,2)"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamRequest" >
            <column name="TipeLimitTransaksi" type="varchar(20)"/>
            <column name="NominalTransaksi" type="numeric(18,2)"/>
        </addColumn>
    </changeSet>
    <changeSet id="Ryan" author="1653373020260">
        <addColumn tableName="TrxFuidRequest" >
            <column name="UnitKerjaLama" type="varchar(10)"/>
            <column name="UnitKerjaBaru" type="varchar(10)"/>
        </addColumn>
    </changeSet>
    <changeSet id="Ryan" author="1653378845243">
        <addColumn tableName="TrxFuidRequest" >
            <column name="TipeKewenanganLimit" type="varchar(20)"/>
        </addColumn>
    </changeSet>
    <changeSet id="1654164133846" author="Ryan">
        <createTable tableName="TrxFuidRequestAplikasi">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxFuidRequestAplikasi_Id"/>
            </column>
            <column name="TicketId" type="varchar(25)"/>
            <column name="Aplikasi" type="varchar(50)"/>
            <column name="periodDate" type="varchar(10)"/>
            <column name="periodMonth" type="varchar(10)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
        </createTable>
        <createTable tableName="TrxSetupParamRequestAplikasi">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxSetupParamRequestAplikasi_Id"/>
            </column>
            <column name="TicketId" type="varchar(25)"/>
            <column name="Aplikasi" type="varchar(50)"/>
            <column name="periodDate" type="varchar(10)"/>
            <column name="periodMonth" type="varchar(10)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
        </createTable>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="A000000001"/>
            <column  name="ParamDetailId"  value="AF00000024"/>
            <column  name="ParamDetailDesc"  value="Upload Spesimen"/>
        </insert>
    </changeSet>
    <changeSet id="1654229664997" author="Ryan">
        <addColumn tableName="TrxFuidRequestAplikasi" >
            <column name="AplikasiName" type="varchar(100)"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamRequestAplikasi" >
            <column name="AplikasiName" type="varchar(100)"/>
        </addColumn>
    </changeSet>
    <changeSet id="1654482163506" author="Ryan">
        <createTable tableName="TrxDelegation">
            <column name="DelegationId" type="varchar(25)"/>
            <column name="NIKRequester" type="varchar(40)"/>
            <column name="NamaRequester" type="varchar(max)"/>
            <column name="JabatanRequester" type="varchar(40)"/>
            <column name="StartDate" type="date"/>
            <column name="EndDate" type="date"/>
            <column name="Info" type="varchar(max)"/>
            <column name="NIKDelegation" type="varchar(40)"/>
            <column name="NamaDelegation" type="varchar(max)"/>
            <column name="JabatanDelegation" type="varchar(40)"/>
        </createTable>
    </changeSet>
    <changeSet id="1654580399142" author="Ryan">
        <addColumn tableName="TrxFuidApproval" >
            <column name="PUK1DelegationId" type="varchar(25)"/>
            <column name="PUK2DelegationId" type="varchar(25)"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamApproval" >
            <column name="PUK1DelegationId" type="varchar(25)"/>
            <column name="PUK2DelegationId" type="varchar(25)"/>
        </addColumn>
    </changeSet>
    <changeSet id="1654657757033" author="Ryan">
        <addColumn tableName="TrxFuidRequest" >
            <column name="TipeKaryawanBaru" type="varchar(20)"/>
        </addColumn>
        <createTable tableName="TrxPUKVendor">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxPUKVendor_Id"/>
            </column>
            <column name="NIKVendor" type="varchar(40)"/>
            <column name="NIKPUK" type="varchar(40)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="UpdateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="MasaBerlakuSampai" type="date"/>
        </createTable>
    </changeSet>
    <changeSet id="1654755594161" author="Ryan">
        <addColumn tableName="TrxSetupParamApproval" >
            <column name="UPMInputAttachment" type="varchar(max)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>