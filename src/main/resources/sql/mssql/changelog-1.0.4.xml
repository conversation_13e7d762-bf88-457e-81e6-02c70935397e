<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1658890081454" author="ryan">
        <sql>
            delete from MsSystemParamDetail where ParamDetailId = 'rangkap_jabatan';
        </sql>
        <insert tableName="MsSystemParamDetail">
            <column  name="ParamId"  value="ALS0000000"/>
            <column  name="ParamDetailId"  value="rangkap_jabatan"/>
            <column  name="ParamDetailDesc"  value="Rangkap Jabatan"/>
        </insert>
        <modifyDataType  columnName="DataJabatan" newDataType="varchar(100)" tableName="TrxFuidRequest"/>
        <modifyDataType  columnName="DataJabatan" newDataType="varchar(100)" tableName="TrxSetupParamRequest"/>
        <modifyDataType  columnName="JabatanRequester" newDataType="varchar(100)" tableName="TrxDelegation"/>
        <modifyDataType  columnName="JabatanDelegation" newDataType="varchar(100)" tableName="TrxDelegation"/>
    </changeSet>
</databaseChangeLog>