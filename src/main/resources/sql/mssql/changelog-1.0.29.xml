<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1681094770170" author="Dian">
        <createTable tableName="TrxUserIdBatch">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxUserIdBatch_Id"/>
            </column>
            <column name="BatchId" type="varchar(50)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="Type" type="varchar(40)"/>
            <column name="BatchFileName" type="varchar(100)"/>
            <column name="TotalData" type="int"/>
            <column name="UploaderNIK" type="varchar(25)"/>
            <column name="UploaderName" type="varchar(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="1681094775928" author="Dian">
        <createTable tableName="MsDboRTGS">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsDboRTGS_Id"/>
            </column>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(100)"/>
            <column name="UnitKerja" type="varchar(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="1681196850988" author="Dian">
        <createIndex indexName="IDX_TrxUserIdBatch_UploaderNIK_Type" tableName="TrxUserIdBatch">
            <column name="UploaderNIK"/>
            <column name="Type"/>
        </createIndex>

        <createIndex indexName="IDX_MsDboRTGS_NIK" tableName="MsDboRTGS">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsDboRTGS_NamaUser" tableName="MsDboRTGS">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>

    <changeSet id="1681198627470" author="Dian">
        <createTable tableName="MsSlik">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsSlik_Id"/>
            </column>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(100)"/>
            <column name="UnitKerja" type="varchar(100)"/>
        </createTable>

        <createIndex indexName="IDX_MsSlik_NIK" tableName="MsSlik">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsSlik_NamaUser" tableName="MsSlik">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>

    <changeSet id="1681440574535" author="Dian">
        <createTable tableName="MsCMS">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsCMS_Id"/>
            </column>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(100)"/>
            <column name="Jabatan" type="varchar(100)"/>
            <column name="UnitKerja" type="varchar(100)"/>
        </createTable>

        <createIndex indexName="IDX_MsCMS_NIK" tableName="MsCMS">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsCMS_NamaUser" tableName="MsCMS">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>

    <changeSet id="*************" author="Dian">
        <createTable tableName="MsTepatMBankingCorporate">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsTepatMBankingCorporate_Id"/>
            </column>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(100)"/>
            <column name="Jabatan" type="varchar(100)"/>
            <column name="UnitKerja" type="varchar(100)"/>
        </createTable>

        <createIndex indexName="IDX_MsTepatMBankingCorporate_NIK" tableName="MsTepatMBankingCorporate">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsTepatMBankingCorporate_NamaUser" tableName="MsTepatMBankingCorporate">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>

    <changeSet id="*************" author="Dian">
        <createTable tableName="MsDigitus">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsDigitus_Id"/>
            </column>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(100)"/>
            <column name="Jabatan" type="varchar(100)"/>
            <column name="LastLogin" type="date"/>
        </createTable>

        <createIndex indexName="IDX_MsDigitus_NIK" tableName="MsDigitus">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsDigitus_NamaUser" tableName="MsDigitus">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>