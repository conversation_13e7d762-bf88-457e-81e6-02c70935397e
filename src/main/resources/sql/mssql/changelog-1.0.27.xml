<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1680257957421" author="Iqbal">
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000001"/>
            <column name="ProsperaRoleCode" value="1"/>
            <column name="RoleDesc" value="Admin"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000002"/>
            <column name="ProsperaRoleCode" value="51"/>
            <column name="RoleDesc" value="AgendaKu CM"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000003"/>
            <column name="ProsperaRoleCode" value="53"/>
            <column name="RoleDesc" value="APPROVE RESTRUKTUR CONDITION"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000004"/>
            <column name="ProsperaRoleCode" value="21"/>
            <column name="RoleDesc" value="Auditor"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000005"/>
            <column name="ProsperaRoleCode" value="57"/>
            <column name="RoleDesc" value="Auditor_External"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000006"/>
            <column name="ProsperaRoleCode" value="48"/>
            <column name="RoleDesc" value="Auto Approval NOS"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000007"/>
            <column name="ProsperaRoleCode" value="54"/>
            <column name="RoleDesc" value="Auto ID Terra Transaksi"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000008"/>
            <column name="ProsperaRoleCode" value="36"/>
            <column name="RoleDesc" value="BO-TF"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000009"/>
            <column name="ProsperaRoleCode" value="58"/>
            <column name="RoleDesc" value="Call Center"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000010"/>
            <column name="ProsperaRoleCode" value="41"/>
            <column name="RoleDesc" value="CPC AUTO HP"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000011"/>
            <column name="ProsperaRoleCode" value="30"/>
            <column name="RoleDesc" value="CS KC/ KFO (BO)"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000012"/>
            <column name="ProsperaRoleCode" value="59"/>
            <column name="RoleDesc" value="DBO Inputer"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000013"/>
            <column name="ProsperaRoleCode" value="60"/>
            <column name="RoleDesc" value="DBO Otorisator"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000014"/>
            <column name="ProsperaRoleCode" value="40"/>
            <column name="RoleDesc" value="INPUT AUTO HP"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000015"/>
            <column name="ProsperaRoleCode" value="52"/>
            <column name="RoleDesc" value="INPUT RESTRUKTUR RECONDITION"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000016"/>
            <column name="ProsperaRoleCode" value="56"/>
            <column name="RoleDesc" value="MTASL"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000017"/>
            <column name="ProsperaRoleCode" value="37"/>
            <column name="RoleDesc" value="OS-TF"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000018"/>
            <column name="ProsperaRoleCode" value="49"/>
            <column name="RoleDesc" value="S_C.O HUB"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000019"/>
            <column name="ProsperaRoleCode" value="28"/>
            <column name="RoleDesc" value="S.C_O Alt C.O."/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000020"/>
            <column name="ProsperaRoleCode" value="25"/>
            <column name="RoleDesc" value="SC.O"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000021"/>
            <column name="ProsperaRoleCode" value="26"/>
            <column name="RoleDesc" value="SCS/ BOM"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column name="TemaRoleCode" value="RA00000022"/>
            <column name="ProsperaRoleCode" value="3"/>
            <column name="RoleDesc" value="UPM"/>
        </insert>
    </changeSet>
</databaseChangeLog>