<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="ryan" id="1656393059466">
        <addColumn tableName="TrxDelegation" >
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="UpdateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
        </addColumn>
    </changeSet>
    <changeSet author="ryan" id="1656469675859">
        <sql>
            delete from MsSystemParamDetail where ParamId = 'CP00000000';
        </sql>
        <loadData file="MsSystemParamDetail-1.0.0.csv"
                  relativeToChangelogFile="true"
                  tableName="MsSystemParamDetail">
            <column header="ParamId"
                    name="ParamId"/>
            <column header="ParamDetailId"
                    name="ParamDetailId"/>
            <column header="ParamDetailDesc"
                    name="ParamDetailDesc"/>
        </loadData>
    </changeSet>
    <changeSet author="ryan" id="1656484981543">
        <sql>
            update MsSystemParamDetail set ParamDetailDesc = 'Email' where ParamDetailId = 'AF00000001';
            update MsSystemParamDetail set ParamDetailDesc = 'Xlink/Xcard' where ParamDetailId = 'AP00000015';
        </sql>
    </changeSet>
</databaseChangeLog>