<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1680582457395" author="Iqbal">
        <addColumn tableName="MsProsperaRole">
            <column name="SystemParamId" type="varchar(50)"/>
        </addColumn>
    </changeSet>

    <changeSet id="1680582876748" author="Iqbal">
        <insert tableName="MsSystemParam">
            <column name="ParamId" value="R000000004"/>
            <column name="ParamDesc" value="role-lainnya"/>
        </insert>
    </changeSet>
    
    <changeSet id="1680583668729" author="Iqbal">
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000001"/>
            <where>TemaRoleCode='RM00000001'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000001"/>
            <where>TemaRoleCode='RM00000002'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000001"/>
            <where>TemaRoleCode='RM00000003'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000001"/>
            <where>TemaRoleCode='RM00000004'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000002"/>
            <where>TemaRoleCode='RC00000001'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000002"/>
            <where>TemaRoleCode='RC00000002'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000002"/>
            <where>TemaRoleCode='RC00000003'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000001'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000002'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000003'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000004'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000005'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000006'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000007'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000008'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000009'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000003"/>
            <where>TemaRoleCode='RH00000010'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000001'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000002'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000003'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000004'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000005'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000006'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000007'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000008'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000009'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000010'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000011'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000012'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000013'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000014'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000015'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000016'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000017'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000018'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000019'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000020'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000021'</where>
        </update>
        <update tableName="MsProsperaRole">
            <column name="SystemParamId" value="R000000004"/>
            <where>TemaRoleCode='RA00000022'</where>
        </update>
    </changeSet>
    
    <changeSet id="1680585226752" author="Iqbal">
        <createIndex indexName="IDX_MsProsperaRole_SystemParamId" tableName="MsProsperaRole">
            <column name="SystemParamId"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>