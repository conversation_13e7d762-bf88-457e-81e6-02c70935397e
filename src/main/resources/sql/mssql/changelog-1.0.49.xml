<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1712024692432" author="Dian">
        <createTable tableName="MsFAQTema">
            <column name="FaqId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_MsFAQTema_FaqId"/>
            </column>
            <column name="ContentTitle" type="varchar(60)"/>
            <column name="ContentDesc" type="varchar(max)"/>
            <column name="ContentOrder" type="integer"/>
            <column name="Visible" type="boolean"/>
            <column name="CreatedAt" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="UpdatedAt" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="CreatedBy" type="varchar(40)"/>
        </createTable>
    </changeSet>

    <changeSet id="1713426690115" author="Dian">
        <sql>
            DROP TABLE IF EXISTS dbo.MsApprovalKewenanganLimit
        </sql>
    </changeSet>

    <changeSet id="1713427861786" author="Dian">
        <createTable tableName="MsApprovalKewenanganLimit">
            <column name="Id" type="varchar(25)" >
                <constraints primaryKey="true" primaryKeyName="PK_MsApprovalKewenanganLimit_Id"/>
            </column>
            <column name="ApprovalOccupation" type="varchar(100)"/>
            <column name="IsKcKFO" type="boolean"/>
            <column name="IsHO" type="boolean"/>
            <column name="MinTunai" type="numeric(18,2)"/>
            <column name="MaxTunai" type="numeric(18,2)"/>
            <column name="MinNonTunai" type="numeric(18,2)"/>
            <column name="MaxNonTunai" type="numeric(18,2)"/>
            <column name="UpdatedAt" type="timestamp" defaultValueComputed="current_timestamp"/>
        </createTable>
    </changeSet>

    <changeSet id="1713426712625" author="Dian">
        <sql>
            INSERT INTO MsApprovalKewenanganLimit VALUES ('AKL0000001', 'TROPS &amp; TRANSFER SERVICES MANAGER', 0, 1, 0.00, 0.00, 0.00, ************.00, GETDATE())
            INSERT INTO MsApprovalKewenanganLimit VALUES ('AKL0000002', 'TIME DEPOSIT SERVICES MANAGER', 0, 1, 0.00, 0.00, 0.00, ************.00, GETDATE())
            INSERT INTO MsApprovalKewenanganLimit VALUES ('AKL0000003', 'PAYMENT SERVICES MANAGER', 0, 1, 0.00, 0.00, 0.00, ************.00, GETDATE())
            INSERT INTO MsApprovalKewenanganLimit VALUES ('AKL0000004', 'DIGITAL BANKING SERVICES HEAD', 0, 1, 1.00, **********.00, 1**********1.00, 2**********0.00, GETDATE())
            INSERT INTO MsApprovalKewenanganLimit VALUES ('AKL0000005', 'OPERATION DISTRIBUTION HEAD', 1, 0, 0.00, **********.00, 0.00, 2**********0.00, GETDATE())
            INSERT INTO MsApprovalKewenanganLimit VALUES ('AKL0000006', 'TRANSACTION SERVICES HEAD', 1, 1, **********.00, ***********.00, 2**********1.00, 3**********0.00, GETDATE())
            INSERT INTO MsApprovalKewenanganLimit VALUES ('AKL0000007', 'DIRECTOR', 1, 1, ***********.00, ************.00, ************.00, ************.00, GETDATE())
        </sql>
    </changeSet>

</databaseChangeLog>