<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="*************" author="Iqbal">
        <addColumn tableName="TrxFuidRequest">
            <column name="PICEmailGroup" type="varchar(100)"/>
        </addColumn>

        <addColumn tableName="TrxFuidRequest">
            <column name="AltPICEmailGroup" type="varchar(100)"/>
        </addColumn>
    </changeSet>

    <changeSet id="*************" author="Iqbal">
        <createTable tableName="MsTepatMBankingIndividu">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsTepatMBankingIndividu_Id"/>
            </column>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="Kewenangan" type="varchar(100)"/>
            <column name="Jabatan" type="varchar(100)"/>
            <column name="UnitKerja" type="varchar(100)"/>
        </createTable>

        <createIndex indexName="IDX_MsTepatMBankingIndividu_NIK" tableName="MsTepatMBankingIndividu">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsTepatMBankingIndividu_NamaUser" tableName="MsTepatMBankingIndividu">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>

    <changeSet id="*************" author="Iqbal">
        <createTable tableName="MsBiCAC">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsBiCAC_Id"/>
            </column>
            <column name="IdUser" type="varchar(25)"/>
            <column name="NIK" type="varchar(25)"/>
            <column name="NamaUser" type="varchar(100)"/>
            <column name="email" type="varchar(100)"/>
            <column name="Institusi" type="varchar(100)"/>
            <column name="UnitKerja" type="varchar(200)"/>
            <column name="GrupUser" type="varchar(100)"/>
            <column name="TanggalAktif" type="date"/>
            <column name="TanggalNonAktif" type="date"/>
            <column name="StatusUser" type="varchar(100)"/>
            <column name="StatusLogin" type="varchar(100)"/>
        </createTable>

        <createIndex indexName="IDX_MsBiCAC_NIK" tableName="MsBiCAC">
            <column name="NIK"/>
        </createIndex>

        <createIndex indexName="IDX_MsBiCAC_NamaUser" tableName="MsBiCAC">
            <column name="NamaUser"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>