<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1676003050368" author="Ryan">
        <createTable tableName="MsEmployeeDirector">
            <column name="NIKOptima" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_MsEmployeeDirector_NIKOptima"/>
            </column>
            <column name="NIKLdap" type="varchar(25)"/>
            <column name="NamaLengkap" type="varchar(200)"/>
            <column name="Email" type="varchar(max)"/>
            <column name="Keterangan" type="varchar(100)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="UpdateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
        </createTable>
        <sql>
            <![CDATA[
            INSERT  INTO  MsEmployeeDirector  (NIKOptima, NIKLdap, NamaLengkap, Email, Keterangan)
            VALUES
            ('22034317','fachmy.achmad','Fachmy Achmad','<EMAIL>','DIRECTOR'),
            ('00000005','gatot.prasetyo','Gatot Adhi Prasetyo','<EMAIL>','DIRECTOR'),
            ('22033946','hadi.wibowo','Hadi Wibowo','<EMAIL>','PRESIDENT DIRECTOR'),
            ('21718100','arief.ismail','Arief Ismail','<EMAIL>','COMPLIANCE & RISK DIRECTOR');
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>