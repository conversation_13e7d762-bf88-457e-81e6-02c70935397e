<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1689306438607" author="Dian">
        <sql>
            CREATE TABLE [dbo].[MsOfficerNR](
            [MsOfficerNRID]  bigint not null primary key,
            [OfficerSource] [varchar](100) NULL,
            [OfficerID] [bigint] NULL,
            [OfficerCode] [varchar](1100) NULL,
            [SrcSystem] [varchar](100) NULL,
            [DTPopulate] [datetime] NULL,
            [SysPopulate] [varchar](50) NULL,
            [MprosperaOfficerID] [decimal](19, 0) NULL,
            [MMSID] [int] NULL,
            [RoleID] [int] NULL,
            [MMSCode] [varchar](1100) NULL,
            [FieldChecksum] [varchar](100) NULL,
            [OfficerName] [varchar](100) NULL,
            [NIK] [varchar](200) NULL,
            [LoginName] [varchar](200) NULL,
            [EmailName] [varchar](255) NULL,
            [RoleName] [varchar](255) NULL,
            [OfficerStatusCode] [int] NULL,
            [OfficerStatusDesc] [varchar](100) NULL,
            [AmtApprovalLimit] [decimal](22, 4) NULL,
            [DTKafka] [datetimeoffset](7) NULL,
            [MMSName] [varchar](255) NULL,
            DTCreated date,
            DepartmentCode [varchar](20) NULL,
            FirstName [varchar](100)NULL,
            LastName [varchar](100) NULL,
            DTLastLogon [datetime] NULL,
            AccountEnabled [int] NULL
            )
        </sql>
    </changeSet>
</databaseChangeLog>