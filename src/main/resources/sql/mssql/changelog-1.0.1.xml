<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="wawan" id="1654771419869">
        <sql>
        delete from MsCabang;
        delete from MsSystemParam where ParamId in ('CP00000000','STS0000000','T000000000','ALS0000000');
        delete from MsSystemParamDetail where ParamId in ('CP00000000','STS0000000','T000000000','ALS0000000');
        delete from TrxUpmRole;
        delete from MMS_UPM ;
        </sql>
        <loadData file="Cabang.csv"
                  relativeToChangelogFile="true"
                  tableName="MSCABANG">
            <column header="Kode Cabang"
                    name="CabangId"/>
            <column header="Nama Kantor"
                    name="CabangDesc"/>
        </loadData>
    </changeSet>
    <changeSet author="wawan" id="1654773463481">
        <delete tableName="MsSystemParam">
            <where>ParamId in ('CP00000000','STS0000000','T000000000','ALS0000000')</where>
        </delete>
        <loadData file="MsSystemParam.csv"
                  relativeToChangelogFile="true"
                  tableName="MsSystemParam">
            <column header="ParamId"
                    name="ParamId"/>
            <column header="ParamDesc"
                    name="ParamDesc"/>
        </loadData>
        <loadData file="MsSystemParamDetail.csv"
                  relativeToChangelogFile="true"
                  tableName="MsSystemParamDetail">
            <column header="ParamId"
                    name="ParamId"/>
            <column header="ParamDetailId"
                    name="ParamDetailId"/>
            <column header="ParamDetailDesc"
                    name="ParamDetailDesc"/>
        </loadData>
        <loadData file="TrxUpmRole.csv"
                  relativeToChangelogFile="true"
                  tableName="TrxUpmRole">
            <column header="nik"
                    name="NIK"/>
            <column header="nama"
                    name="Nama"/>
            <column header="Role"
                    name="Role"/>
        </loadData>
    </changeSet>
    <changeSet author="wawan" id="1654775005178">
        <loadData file="MMS_UPM.csv"
                  relativeToChangelogFile="true"
                  tableName="MMS_UPM" quotchar='"'>
            <column header="MMSCode"
                    name="MMSCode"/>
            <column header="MMSName"
                    name="MMSName"/>
            <column header="Address"
                    name="Address"/>
            <column header="KFOCode"
                    name="KFOCode"/>
            <column header="KFOName"
                    name="KFOName"/>
            <column header="KCSCode"
                    name="KCSCode"/>
            <column header="KCSName"
                    name="KCSName"/>
        </loadData>
    </changeSet>
    <changeSet author="ryan" id="1655461010470">
        <update tableName="MsSystemParamDetail">
            <column name="ParamDetailId" value="mutasi/rotasi/promosi"/>
            <where>ParamDetailId='mutasi/rotasi'</where>
        </update>
    </changeSet>
    <changeSet author="ryan" id="1655949941394">
        <addColumn tableName="TrxDelegation" >
            <column name="Status" type="varchar(20)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>