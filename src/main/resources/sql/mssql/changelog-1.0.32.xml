<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1685953197489" author="Iqbal">
        <createTable tableName="TrxExpiredFuid">
            <column name="TicketId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_TrxExpiredFuid_TicketId"/>
            </column>
            <column name="CreatedDate" type="date"/>
            <column name="TanggalEfektif" type="date"/>
            <column name="NIK" type="varchar(25)"/>
            <column name="Nama" type="varchar(100)"/>
            <column name="KodeCabang" type="varchar(10)"/>
            <column name="NamaCabang" type="varchar(200)"/>
            <column name="Aplikasi" type="varchar(200)"/>
            <column name="TanggalMasaBerlaku" type="date"/>
            <column name="JenisPengajuan" type="varchar(100)"/>
            <column name="AlasanPengajuan" type="varchar(100)"/>
            <column name="PICProcess" type="varchar(100)"/>
            <column name="PICApprove" type="varchar(100)"/>
            <column name="isProcessedByUpm" type="tinyint(1)"/>
        </createTable>

        <createIndex indexName="IDX_TrxExpiredFuid_TanggalMasaBerlaku" tableName="TrxExpiredFuid">
            <column name="TanggalMasaBerlaku"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>