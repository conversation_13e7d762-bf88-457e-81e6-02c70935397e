<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1664202533961" author="Wawan">
        <addColumn tableName="TrxUpmRole" >
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
        </addColumn>
    </changeSet>
    <changeSet author="wawan" id="1664208334494">
        <sql>
            UPDATE TrxUpmRole set CreateDateTime = GETDATE();
        </sql>
    </changeSet>
    <changeSet id="1664448935281" author="Wawan">
        <addColumn tableName="TrxFuidRequestAplikasi" >
            <column name="periodDateDone" type="varchar(10)"/>
            <column name="periodMonthDone" type="varchar(10)"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamRequestAplikasi" >
            <column name="periodDateDone" type="varchar(10)"/>
            <column name="periodMonthDone" type="varchar(10)"/>
        </addColumn>
        <sql>
            UPDATE
                TrxFuidRequestAplikasi
            SET
                TrxFuidRequestAplikasi.periodDateDone = convert(varchar, TrxFuidApproval.CurrentStateDT, 112),
                TrxFuidRequestAplikasi.periodMonthDone = left(convert(varchar, TrxFuidApproval.CurrentStateDT, 112), 6)
            FROM
                TrxFuidRequestAplikasi
            INNER JOIN
                TrxFuidApproval
            ON
                TrxFuidRequestAplikasi.TicketId = TrxFuidApproval.TicketId
            where TrxFuidApproval.CurrentState = 'done_upm' ;
            UPDATE
                TrxSetupParamRequestAplikasi
            SET
                TrxSetupParamRequestAplikasi.periodDateDone = convert(varchar, TrxSetupParamApproval.CurrentStateDT, 112),
                TrxSetupParamRequestAplikasi.periodMonthDone = left(convert(varchar, TrxSetupParamApproval.CurrentStateDT, 112), 6)
            FROM
                TrxSetupParamRequestAplikasi
            INNER JOIN
                TrxSetupParamApproval
            ON
                TrxSetupParamRequestAplikasi.TicketId = TrxSetupParamApproval.TicketId
            where TrxSetupParamApproval.CurrentState = 'done_upm' ;
        </sql>
    </changeSet>
    <changeSet id="1664567106192" author="wawan">
        <createTable tableName="MsHolidayList">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsHolidayList_Id"/>
            </column>
            <column name="HolidayDate" type="varchar(10)"/>
            <column name="HolidayYear" type="varchar(4)" />
            <column name="HolidayDesc" type="varchar(100)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
        </createTable>
        <insert tableName="MsHolidayList">
            <column  name="HolidayDate"  value="2022-10-08"/>
            <column  name="HolidayYear"  value="2022"/>
            <column  name="HolidayDesc"  value="Maulid Nabi Muhammad SAW"/>
        </insert>
        <insert tableName="MsHolidayList">
            <column  name="HolidayDate"  value="2022-12-25"/>
            <column  name="HolidayYear"  value="2022"/>
            <column  name="HolidayDesc"  value="Hari Natal"/>
        </insert>
        <addColumn tableName="TrxFuidApproval" >
            <column name="SlaValue" type="int" defaultValueComputed="0"/>
            <column name="SlaInfo" type="varchar(80)"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamApproval" >
            <column name="SlaValue" type="int" defaultValueComputed="0"/>
            <column name="SlaInfo" type="varchar(80)"/>
        </addColumn>
        <sql>
            update TrxFuidApproval set SlaValue  = 0;
            update TrxSetupParamApproval set SlaValue = 0;
        </sql>
    </changeSet>
</databaseChangeLog>