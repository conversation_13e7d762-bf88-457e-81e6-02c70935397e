<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1724035843007" author="Dian">
        <createTable tableName="MsSOPNumber">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsSOPNumber_Id"/>
            </column>
            <column name="SOPNumber" type="varchar(max)"/>
            <column name="UpdatedAt" type="datetime" defaultValueComputed="current_timestamp"/>
        </createTable>
    </changeSet>

    <changeSet id="1724035848742" author="Dian">
        <sql>
            IF NOT EXISTS (SELECT 1 FROM MsSOPNumber)
            BEGIN
            INSERT INTO MsSOPNumber
            VALUES
            ('SOP.005/DIR/OPD/VII/2024', GETDATE())
            END
        </sql>
    </changeSet>

    <changeSet id="1724215221498" author="Dian">
        <modifyDataType tableName="TrxUARSummary" columnName="ContentFileName" newDataType="varchar(100)"/>
    </changeSet>
</databaseChangeLog>