<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1677157357313" author="Dian">
        <sql>
            DROP TABLE IF EXISTS dbo.MsTemaApplication
        </sql>
    </changeSet>

    <changeSet id="1677157302196" author="Dian">
        <createTable tableName="MsTemaApplication">
            <column name="ParamId" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsTemaApplication_ParamId"/>
            </column>
            <column name="ParamDetailId" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsTemaApplication_ParamDetailId"/>
            </column>
            <column name="ParamDetailDesc" type="varchar(max)"/>
            <column name="HoApplicationStatus" type="varchar(10)"/>
            <column name="BranchApplicationStatus" type="varchar(10)"/>
            <column name="MmsApplicationStatus" type="varchar(10)"/>
        </createTable>
    </changeSet>

    <changeSet id="1676260686008" author="Dian">
        <loadData file="MsTemaApplication.csv" relativeToChangelogFile="true" tableName="MsTemaApplication">
            <column header="ParamId" name="ParamId"/>
            <column header="ParamDetailId" name="ParamDetailId"/>
            <column header="ParamDetailDesc" name="ParamDetailDesc"/>
            <column header="HoApplicationStatus" name="HoApplicationStatus"/>
            <column header="BranchApplicationStatus" name="BranchApplicationStatus"/>
            <column header="MmsApplicationStatus" name="MmsApplicationStatus"/>
        </loadData>
    </changeSet>
</databaseChangeLog>