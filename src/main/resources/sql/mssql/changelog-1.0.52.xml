<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1721207189009" author="Dian">
        <modifyDataType tableName="MsFAQTema" columnName="ContentTitle" newDataType="varchar(max)"/>
    </changeSet>

    <changeSet id="1721207222364" author="Dian">
        <sql>
            IF NOT EXISTS (SELECT 1 FROM MsFAQTema)
            BEGIN
            INSERT INTO MsFAQTema
            VALUES
            ('FAQ' + FORMAT(GetDate(), 'yyMMdd') + '0001', 'Tidak bisa login TEMA-Web Error “Invalid username or Password”', 'Pastikan login menggunakan NIK (tanpa @mail.btpnsyariah.com dan password Email/LDAP dengan benar', 1, '1', GETDATE(), GETDATE(), '13042923'),
            ('FAQ' + FORMAT(GetDate(), 'yyMMdd') + '0002', 'Tidak Mendapatkan Notifikasi Email', 'Jika tidak dapat notifikasi Email, bisa langsung melakukan pengecekan di TEMA-Web nya dan lihat detail tiket maka akan terlihat status tiket nya', 2, '1', GETDATE(), GETDATE(), '13042923')
            END
        </sql>
    </changeSet>
</databaseChangeLog>