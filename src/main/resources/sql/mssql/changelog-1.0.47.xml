<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1708418832679" author="Dian">
        <createTable tableName="MsTemaAccessRole">
            <column name="SystemParamId" type="varchar(50)"/>
            <column name="AccessRoleCode" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsTemaAccessRole_AccessRoleCode"/>
            </column>
            <column name="AccessRoleDesc" type="varchar(max)"/>
        </createTable>
    </changeSet>

    <changeSet id="1708418841611" author="Dian">
        <insert tableName="MsTemaAccessRole">
            <column name="SystemParamId" value="R000000004"/>
            <column name="AccessRoleCode" value="TAR0000001"/>
            <column name="AccessRoleDesc" value="Admin"/>
        </insert>

        <insert tableName="MsTemaAccessRole">
            <column name="SystemParamId" value="R000000004"/>
            <column name="AccessRoleCode" value="TAR0000002"/>
            <column name="AccessRoleDesc" value="Checker"/>
        </insert>

        <insert tableName="MsTemaAccessRole">
            <column name="SystemParamId" value="R000000004"/>
            <column name="AccessRoleCode" value="TAR0000003"/>
            <column name="AccessRoleDesc" value="Maker"/>
        </insert>

        <insert tableName="MsTemaAccessRole">
            <column name="SystemParamId" value="R000000004"/>
            <column name="AccessRoleCode" value="TAR0000004"/>
            <column name="AccessRoleDesc" value="Viewer"/>
        </insert>

        <insert tableName="MsTemaAccessRole">
            <column name="SystemParamId" value="R000000004"/>
            <column name="AccessRoleCode" value="TAR0000005"/>
            <column name="AccessRoleDesc" value="Inquiry"/>
        </insert>
    </changeSet>

    <changeSet id="1708917695495" author="Dian">
        <createIndex indexName="IDX_TrxFuidApproval_TicketId" tableName="TrxFuidApproval">
            <column name="TicketId"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>