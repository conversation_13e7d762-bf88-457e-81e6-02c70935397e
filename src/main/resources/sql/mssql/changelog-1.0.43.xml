<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1698131023156" author="Iqbal">
        <createTable tableName="TrxUARSummary">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxUARSummary_Id"/>
            </column>
            <column name="ContentFileName" type="varchar(25)"/>
            <column name="ContentUrl" type="varchar(max)"/>
            <column name="ContentPath" type="varchar(max)"/>
            <column name="ContentType" type="varchar(max)"/>
            <column name="Aplikasi" type="varchar(25)"/>
            <column name="PeriodYear" type="integer"/>
            <column name="PeriodQuarter" type="varchar(5)"/>
            <column name="RefNumber" type="varchar(100)"/>
            <column name="CreatedAt" type="timestamp" defaultValueComputed="current_timestamp"/>
        </createTable>

        <createIndex indexName="IDX_TrxUARSummary_RefNumber" tableName="TrxUARSummary">
            <column name="RefNumber"/>
        </createIndex>

        <createIndex indexName="IDX_TrxUARSummary_CreatedAt" tableName="TrxUARSummary">
            <column name="CreatedAt"/>
        </createIndex>

    </changeSet>

    <changeSet id="1698133909364" author="Iqbal">
        <insert tableName="MsUserIDApplication">
            <column name="ParamId" value="A000000003"/>
            <column name="ParamDetailId" value="AU00000012"/>
            <column name="ParamDetailDesc" value="T24"/>
            <column name="Type" value="existing"/>
            <column name="Visible" value="1"/>
            <column name="UAR" value="0"/>
        </insert>
    </changeSet>

    <changeSet id="1699501421512" author="Iqbal">
        <addColumn tableName="TrxUPMReminder">
            <column name="NikUPM" type="varchar(25)"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>