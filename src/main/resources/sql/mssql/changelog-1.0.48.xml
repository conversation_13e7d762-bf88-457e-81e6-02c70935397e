<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1710904990135" author="Dian">
        <addColumn tableName="TrxFuidApproval">
            <column name="PUK1ApprovalReminder" type="integer"/>
        </addColumn>
        <addColumn tableName="TrxFuidApproval">
            <column name="PUK2ApprovalReminder" type="integer"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamApproval">
            <column name="PUK1ApprovalReminder" type="integer"/>
        </addColumn>
        <addColumn tableName="TrxSetupParamApproval">
            <column name="PUK2ApprovalReminder" type="integer"/>
        </addColumn>
    </changeSet>

    <changeSet id="1710909944665" author="Dian">
        <createIndex indexName="IDX_TrxFuidApproval_CurrentStateDT" tableName="TrxFuidApproval">
            <column name="CurrentStateDT"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>