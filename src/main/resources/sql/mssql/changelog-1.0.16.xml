<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1676346434086" author="Dian">
        <sql>
            DROP TABLE MsProsperaRole
        </sql>
    </changeSet>

    <changeSet id="1676348068706" author="Dian">
        <sql>
            DROP TABLE TrxProsperaRequest
        </sql>
    </changeSet>

    <changeSet id="1676360616019" author="Dian">
        <sql>
            DROP TABLE IF EXISTS dbo.CenterCodeOfficer
        </sql>
    </changeSet>

    <changeSet id="1675048892681" author="Dian">
        <createTable tableName="MsProsperaRole">
            <column name="TemaRoleCode" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_ProsperaRole_TemaRoleCode"/>
            </column>
            <column name="ProsperaRoleCode" type="int"/>
            <column name="RoleDesc" type="varchar(max)"/>
        </createTable>
    </changeSet>

    <changeSet id="1675651942610" author="Dian">
        <createTable tableName="TrxProsperaRequest">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxProsperaRequest_Id"/>
            </column>
            <column name="TicketId" type="varchar(25)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="Tujuan" type="varchar(40)"/>
            <column name="Alasan" type="varchar(40)"/>
            <column name="PayloadRequest" type="varchar(max)"/>
            <column name="PayloadResponse" type="varchar(max)"/>
            <column name="Status" type="varchar(25)"/>
        </createTable>
    </changeSet>

    <changeSet id="1675651949205" author="Dian">
        <createTable tableName="MsCenterCodeOfficerProspera">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsCenterCodeOfficerProspera_Id"/>
            </column>
            <column name="CabangDesc" type="varchar(max)"/>
            <column name="CabangId" type="varchar(25)"/>
            <column name="GeneratedCenterCodeOfficer" type="varchar(25)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
        </createTable>
    </changeSet>

    <changeSet id="1675048903327" author="Dian">
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RM00000001"/>
            <column  name="ProsperaRoleCode" value="24"/>
            <column  name="RoleDesc" value="CO"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RM00000002"/>
            <column  name="ProsperaRoleCode" value="47"/>
            <column  name="RoleDesc" value="BM"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RM00000003"/>
            <column  name="ProsperaRoleCode" value="29"/>
            <column  name="RoleDesc" value="Alternate BM"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RM00000004"/>
            <column  name="ProsperaRoleCode" value="75"/>
            <column  name="RoleDesc" value="MTCO"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RC00000001"/>
            <column  name="ProsperaRoleCode" value="38"/>
            <column  name="RoleDesc" value="CS KC/ KFO (BO - FD)"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RC00000002"/>
            <column  name="ProsperaRoleCode" value="27"/>
            <column  name="RoleDesc" value="CS KC/ KFO (Teller)"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RC00000003"/>
            <column  name="ProsperaRoleCode" value="39"/>
            <column  name="RoleDesc" value="SCS/ BOM - FD"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000001"/>
            <column  name="ProsperaRoleCode" value="19"/>
            <column  name="RoleDesc" value="User HO View"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000002"/>
            <column  name="ProsperaRoleCode" value="42"/>
            <column  name="RoleDesc" value="B_M_Coord_"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000003"/>
            <column  name="ProsperaRoleCode" value="43"/>
            <column  name="RoleDesc" value="BC"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000004"/>
            <column  name="ProsperaRoleCode" value="46"/>
            <column  name="RoleDesc" value="SDH"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000005"/>
            <column  name="ProsperaRoleCode" value="50"/>
            <column  name="RoleDesc" value="Helpdesk"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000006"/>
            <column  name="ProsperaRoleCode" value="31"/>
            <column  name="RoleDesc" value="Payment &amp; Settlement Staff"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000007"/>
            <column  name="ProsperaRoleCode" value="32"/>
            <column  name="RoleDesc" value="Payment &amp; Settlement SPV"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000008"/>
            <column  name="ProsperaRoleCode" value="9"/>
            <column  name="RoleDesc" value="FA SPV"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000009"/>
            <column  name="ProsperaRoleCode" value="23"/>
            <column  name="RoleDesc" value="IT DCO"/>
        </insert>
        <insert tableName="MsProsperaRole">
            <column  name="TemaRoleCode" value="RH00000010"/>
            <column  name="ProsperaRoleCode" value="7"/>
            <column  name="RoleDesc" value="IT Production Support"/>
        </insert>
    </changeSet>
</databaseChangeLog>