<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1663337752967" author="Ryan">
        <addColumn tableName="TrxFuidRequest" >
            <column name="OccupationDesc" type="varchar(100)"/>
            <column name="Organization" type="varchar(100)"/>
            <column name="Location" type="varchar(100)"/>
        </addColumn>
    </changeSet>
    <changeSet id="1663677460728" author="Wawan">
        <addColumn tableName="TrxFuidRequest" >
            <column name="InputType" type="varchar(20)"/>
            <column name="BatchId" type="varchar(50)"/>
        </addColumn>
        <createTable tableName="TrxFuidBatch">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxFuidBatch_Id"/>
            </column>
            <column name="BatchId" type="varchar(50)"/>
            <column name="CreateDatetime" type="datetime" defaultValueComputed="current_timestamp"/>
            <column name="type" type="varchar(40)"/>
            <column name="BatchFileName" type="varchar(100)"/>
            <column name="TotalData" type="int"/>
            <column name="uploaderNIK" type="varchar(25)"/>
            <column name="uploaderName" type="varchar(100)"/>
        </createTable>
    </changeSet>
    <changeSet id="1663840580641" author="Wawan">
        <modifyDataType
                columnName="DTPermanent"
                newDataType="varchar(25)"
                tableName="MsEmployeeHierarchy"/>
        <modifyDataType
                columnName="DTTermination"
                newDataType="varchar(25)"
                tableName="MsEmployeeHierarchy"/>
    </changeSet>
</databaseChangeLog>