<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1715655559642" author="Dian">
        <createTable tableName="TrxUploadUserIdAudittrail">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxUploadUserIdAudittrail_Id"/>
            </column>
            <column name="ParamDetailId" type="varchar(25)"/>
            <column name="UploadAt" type="timestamp" defaultValueComputed="current_timestamp"/>
            <column name="DataUser" type="varchar(max)"/>
        </createTable>
    </changeSet>

    <changeSet id="1715843226328" author="Dian">
        <addColumn tableName="TrxUploadUserIdAudittrail">
            <column name="FileContent" type="varchar(max)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>