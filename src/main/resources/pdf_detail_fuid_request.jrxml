<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.0.final using JasperReports Library version 6.20.0-2bc7ab61c56f459e8176eb05c7705e145cd400ad  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="detailTiketFuid" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="40" bottomMargin="20" uuid="d56b67e8-27b0-4ce6-9e88-c7cee6d3af0a">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
    <parameter name="timelines" class="java.lang.String"/>
    <parameter name="logoBTPNS" class="java.lang.Object"/>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="ticketId" class="java.lang.String"/>
    <field name="tanggalEfektif" class="java.lang.String"/>
    <field name="NIK" class="java.lang.String"/>
    <field name="nama" class="java.lang.String"/>
    <field name="jabatan" class="java.lang.String"/>
    <field name="userId" class="java.lang.String"/>
    <field name="kodeDanNamaCabang" class="java.lang.String"/>
    <field name="email" class="java.lang.String"/>
    <field name="telepon" class="java.lang.String"/>
    <field name="aplikasi" class="java.lang.String"/>
    <field name="tingkatan" class="java.lang.String"/>
    <field name="statusMasaBerlaku" class="java.lang.String"/>
    <field name="tujuan" class="java.lang.String"/>
    <field name="alasan" class="java.lang.String"/>
    <field name="role" class="java.lang.String"/>
    <field name="limitTransaksi" class="java.lang.String"/>
    <field name="nominal" class="java.lang.String"/>
    <field name="keterangan" class="java.lang.String"/>
    <field name="userIDUnitKerjaLama" class="java.lang.String"/>
    <field name="infoTambahan" class="java.lang.String"/>
    <field name="picEmailGroup" class="java.lang.String"/>
    <field name="altPicEmailGroup" class="java.lang.String"/>
    <field name="nikPUK1" class="java.lang.String"/>
    <field name="namaPUK1" class="java.lang.String"/>
    <field name="jabatanPUK1" class="java.lang.String"/>
    <field name="nikPUK2" class="java.lang.String"/>
    <field name="namaPUK2" class="java.lang.String"/>
    <field name="jabatanPUK2" class="java.lang.String"/>
    <field name="catatan" class="java.lang.String"/>
    <field name="statusTiket" class="java.lang.String"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <title>
        <band height="75" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="168" y="13" width="195" height="30" uuid="e4e6366f-63b5-4ed7-b8fb-0ae0f820a10a"/>
                <textElement textAlignment="Center">
                    <font size="14"/>
                </textElement>
                <text><![CDATA[Form Permohonan User ID]]></text>
            </staticText>
            <image>
                <reportElement mode="Opaque" x="450" y="0" width="170" height="50" uuid="1adf2401-5363-4d10-9739-4b6501d8427e"/>
                <imageExpression><![CDATA[$P{logoBTPNS}]]></imageExpression>
            </image>
        </band>
    </title>
    <detail>
        <band height="428" splitType="Stretch">
            <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" x="21" y="8" width="458" height="20" uuid="9072363a-8619-432b-86d5-eefce91adf4c"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="0c96b257-1be3-4a1a-97ea-efb521545fc9"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Nomor Tiket]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="3a8d71b9-96c7-4455-970d-01ffe15ad095"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="52c0f239-291c-47ae-8710-788f517ef5a9">
                        <property name="com.jaspersoft.studio.unit.height" value="pixel"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{ticketId}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="28" width="458" height="20" uuid="aeb32f06-1aae-41d5-9732-5ab750140d96"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="4953a382-15d3-4c8d-86c3-3c009184a418"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Tanggal Efektive]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="9fdc5a10-6fe8-4662-a640-caa77342196b"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="c9a14d4e-36c3-458e-82fb-1fb44ac96b38">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{tanggalEfektif}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="48" width="458" height="20" uuid="8788c293-41c3-4130-b58b-1527420355e6"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="53f1121d-2b2a-47e2-b9d3-6bea90b582e7"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[NIK]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="3c7169cb-14e5-42fe-9479-717a05c38261"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="c96cb3e5-f115-40cd-8b6b-1195d5b2d6ee">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{NIK}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="68" width="458" height="20" uuid="8721e6de-f394-4482-ad76-57d0142075c9"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="99cebf63-327c-47f7-90f4-e221b1ba559d"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Nama]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="14de025f-977a-4ba7-8faf-2df1e98b4fc9"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="dc1fb114-f7d7-439f-af9d-a6ebf30ba050">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nama}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="88" width="458" height="20" uuid="8a7f2cac-81e4-4c8c-bde5-10ba9d0cb8ce"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="1b12830a-ab54-4153-a6f3-1e62730b9c92"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Jabatan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="96d99721-3c7a-481c-a0ac-75748b2952d9"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="8ba8e9da-**************-65597d37d32a">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{jabatan}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="108" width="458" height="20" uuid="496b1163-a495-4f12-bcba-11b1620b67e9"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="51e1101a-6c5c-46d2-a6ce-cba84e685d01"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[User ID]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="b9a6d848-4c7a-4f37-8346-9542e17fc623"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="743683ba-cec1-4040-a2db-c15ec34308d6">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{userId}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="128" width="458" height="20" uuid="c9efe38e-cecd-43d4-a928-4a2b8009c681"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="55849566-dca2-4854-9762-212ef8c7d331"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Kode & Nama Cabang]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="81d1d7bf-003a-410d-8508-33088cece846"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="07412b6e-8422-46f3-bc80-0c0b01ee71f4">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{kodeDanNamaCabang}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="148" width="458" height="20" uuid="532b1f6a-e201-42f6-86ac-0897389d4b6b"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="edbb59ac-c27a-4e9b-b9a8-acbc4952caf6"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Email]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="538f8787-992e-496b-9e7d-91a404ea746e"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="934bc138-5336-46d0-8e26-575dbf272681">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{email}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="168" width="458" height="20" uuid="a328bb71-7b54-4058-bdc1-39e7a42d5e12"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="94fe2128-e43e-4622-af6d-548085e67385"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Telepon]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="2f0ff568-af63-47ef-8e94-67d68528586a"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="4e414078-a1a5-4101-9785-a2fb92d3f52d">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{telepon}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="188" width="458" height="20" uuid="794a473e-0dba-4003-9b8b-0080967f098d"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="cc079b7e-c35f-432f-977c-cb101508849b"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Jenis Aplikasi Pengajuan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="f63f035f-b7d0-4351-90c3-58d545fbf8a6"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="61410fac-643d-45da-97ef-24404c00926a">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{aplikasi}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="208" width="458" height="20" uuid="4677ef2c-b372-46b4-9d51-87efa9e55208">
                    <printWhenExpression><![CDATA[!$F{tingkatan}.isEmpty() && $F{tingkatan} != null]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="117be881-7008-4830-9f37-f1b6b85ac4f3"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Jenis Tingkatan User]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="2cccfdea-4edc-47e1-8c61-e4c09d51217c"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="0907323e-77d4-4343-909e-a2af199c294d">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{tingkatan}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="208" width="458" height="20" uuid="4677ef2c-b372-46b4-9d51-87efa9e55208">
                    <printWhenExpression><![CDATA[!$F{role}.isEmpty()]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="117be881-7008-4830-9f37-f1b6b85ac4f3"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Peran User]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="2cccfdea-4edc-47e1-8c61-e4c09d51217c"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="0907323e-77d4-4343-909e-a2af199c294d">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{role}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="228" width="458" height="20" uuid="7ef01b23-99ce-448a-9cc5-15c35e28ea3c"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="5814a1f8-360d-45b0-9709-5d613ec5bdde"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Status Masa Berlaku]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="03d41356-4ed5-4f69-b28a-e40512178bb8"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="0" width="300" height="20" uuid="9d635a9a-15b0-4399-b77d-31fd0f3b1d61">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{statusMasaBerlaku}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="248" width="458" height="20" uuid="5db03793-66f4-48cd-a7bb-f7833490db15"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="d36fb73e-09a8-4603-a3f0-5b7197520f4a"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Jenis Pengajuan (Tujuan)]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="a0f1b9df-4675-4ae1-9f26-682046db34d9"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="758c1b7e-f60d-419b-9d3f-55cd7c68aebd">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{tujuan}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="268" width="458" height="20" uuid="8784ad5f-42a2-4404-b45f-34006c803b11"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="a0af6422-222d-4d7d-8859-d3f4f271b5d6"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Alasan Pengajuan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="3707cc8a-ad56-4740-b6cf-15a0dea4393f"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="0f38d262-ca65-4d9c-af71-a5a58a133c89">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{alasan}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement isRemoveLineWhenBlank="true" positionType="Float" x="21" y="288" width="458" height="20" uuid="d37dad3f-7368-4edc-9617-5f843174c8d0">
                    <printWhenExpression><![CDATA[!$F{userIDUnitKerjaLama}.isEmpty()]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="147" height="20" uuid="67953c80-e88c-4913-9932-2b2cdf9cb826"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[User ID Unit Kerja Lama]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="38cf0624-2d91-45e3-940f-7badfcf3d5ba">
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="4223aa4d-66bc-400f-a549-************">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{userIDUnitKerjaLama}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement isRemoveLineWhenBlank="true" positionType="Float" x="21" y="308" width="458" height="20" uuid="d37dad3f-7368-4edc-9617-5f843174c8d0">
                    <printWhenExpression><![CDATA[$F{limitTransaksi} != null]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="67953c80-e88c-4913-9932-2b2cdf9cb826">
                        <printWhenExpression><![CDATA[$F{limitTransaksi} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Limit Transaksi]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="38cf0624-2d91-45e3-940f-7badfcf3d5ba">
                        <printWhenExpression><![CDATA[$F{limitTransaksi} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="4223aa4d-66bc-400f-a549-************">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                        <printWhenExpression><![CDATA[$F{limitTransaksi} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{limitTransaksi}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement isRemoveLineWhenBlank="true" positionType="Float" x="21" y="328" width="458" height="20" uuid="d37dad3f-7368-4edc-9617-5f843174c8d0">
                    <printWhenExpression><![CDATA[$F{nominal} != null]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="ad9ce56b-a410-4f81-ae3c-eb6320583c11">
                        <printWhenExpression><![CDATA[$F{nominal} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Nominal]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="ac33d684-c6c1-4078-a2ea-9a8f8e79f156">
                        <printWhenExpression><![CDATA[$F{nominal} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="d8b9923a-5559-497f-ae7d-e86edca8749c">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                        <printWhenExpression><![CDATA[$F{nominal} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nominal}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="348" width="458" height="20" uuid="cc3e7310-e933-4e59-98dc-2d7acde42312"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="2ab8cb9c-0793-4131-8c37-fba68757db35"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Keterangan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="2a68d902-0c55-47b7-bfe6-2c744eabbecf"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="8fb91720-6f2b-4a45-aa7c-26d903e44eb9">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{keterangan}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="368" width="458" height="20" uuid="e7ce8d74-fd46-4952-b981-85e87f88df5d"/>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="d273d4f2-4546-464d-8823-104675143164"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Info Tambahan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="60db66fa-5068-4211-995a-f2c866b8e33f"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="eba4dccb-0c8e-4436-982f-6997f384411b">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{infoTambahan}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement isRemoveLineWhenBlank="true" positionType="Float" x="21" y="388" width="458" height="20" uuid="49dafd76-606e-4283-b85a-e46e833de4a8">
                    <printWhenExpression><![CDATA[$F{picEmailGroup} != null]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="745914ed-85c5-4d11-8c3d-89f8f4d2c1c0"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[PIC Email Group]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="8d247317-7c5d-4e7c-8007-0d5b1fa42b4b"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="7978210f-aef7-41ec-8b55-7fa985b50532">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{picEmailGroup}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement isRemoveLineWhenBlank="true" positionType="Float" x="21" y="408" width="458" height="20" uuid="06219cc5-ef1b-49b8-954a-afb9699696a4">
                    <printWhenExpression><![CDATA[$F{picEmailGroup} != null]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="120" height="20" uuid="1dc3f671-9105-4683-88c6-2b04637d6455"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[Alt PIC Email Group]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="0" width="19" height="20" uuid="11d5947a-174d-41d1-b56b-340cc5b98491"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement x="158" y="0" width="300" height="20" uuid="f4075e69-45cb-4fbc-80f3-c975851f0f15">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{altPicEmailGroup}]]></textFieldExpression>
                </textField>
            </frame>
        </band>
        <band height="333">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" isPrintRepeatedValues="false" x="21" y="25" width="458" height="94" isRemoveLineWhenBlank="true" uuid="55fdba03-f3f2-42f2-bc23-2b53bbbd3cd2">
                    <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="200" height="20" uuid="4cda618d-6649-4788-ab6c-2e72863fef07">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Informasi Atasan 1]]></text>
                </staticText>
                <staticText>
                    <reportElement x="0" y="16" width="200" height="15" uuid="e30bf2bf-e626-4302-aea8-6701499b1a51">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="9" isBold="false"/>
                    </textElement>
                    <text><![CDATA[Permohonan ini telah disetujui oleh]]></text>
                </staticText>
                <staticText>
                    <reportElement x="0" y="34" width="100" height="20" uuid="a16a755a-2563-4c03-9efc-2c1760ac386b">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[NIK]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="34" width="19" height="20" uuid="17432d4b-7c46-4db1-93e5-1479a04e9187">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="34" width="300" height="20" uuid="fc122199-98b3-497e-be2a-0cacff942e1b">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nikPUK1}]]></textFieldExpression>
                </textField>
                <staticText>
                    <reportElement x="0" y="54" width="100" height="20" uuid="3bf5c008-ce47-4006-b9a1-e9ae8187ebd9">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[Nama Lengkap]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="54" width="19" height="20" uuid="5b407ca9-99fa-41cd-90fc-ab3c1fad5f86">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="54" width="300" height="20" uuid="81da9fdd-9b12-4ac4-aefe-9f24a6e80952">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{namaPUK1}]]></textFieldExpression>
                </textField>
                <staticText>
                    <reportElement x="0" y="74" width="100" height="20" uuid="c4118e86-4e99-4330-9fef-2f5bcf539222">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[Jabatan]]></text>
                </staticText>
                <staticText>
                    <reportElement x="144" y="74" width="19" height="20" uuid="0c5683da-35ea-460b-96e8-e9bfea1e7fb9">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="158" y="74" width="300" height="20" uuid="3c0c0e4b-3ef5-471b-8070-7a7d55f894c3">
                        <printWhenExpression><![CDATA[$F{nikPUK1} != null && $F{namaPUK1} != null && $F{jabatanPUK1} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{jabatanPUK1}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" isPrintRepeatedValues="false" x="21" y="125" width="458" height="94" isRemoveLineWhenBlank="true" uuid="da202d2b-2587-4dfd-b1ab-390dc1bfa383">
                    <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                </reportElement>
                <staticText>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="0" width="200" height="20" isRemoveLineWhenBlank="true" uuid="f1ad1043-2085-4b6b-abfe-36c4baa1394e">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Informasi Atasan 2]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="16" width="200" height="15" isRemoveLineWhenBlank="true" uuid="8a9876b2-8a9a-4992-8870-049dc78ff67d">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="9" isBold="false"/>
                    </textElement>
                    <text><![CDATA[Permohonan ini telah disetujui oleh]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="34" width="100" height="20" isRemoveLineWhenBlank="true" uuid="5c98f9f8-1d64-4f95-af3f-acb8735f0280">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[NIK]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="144" y="34" width="19" height="20" isRemoveLineWhenBlank="true" uuid="2f65d7b0-73cf-44e9-885e-0b0e093c2bf9">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="158" y="34" width="300" height="20" isRemoveLineWhenBlank="true" uuid="3e7e21b4-3f64-4523-9f04-5461737c1907">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nikPUK2}]]></textFieldExpression>
                </textField>
                <staticText>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="54" width="100" height="20" isRemoveLineWhenBlank="true" uuid="964dd11f-5a4b-4444-ab7f-330beae4dfbb">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[Nama Lengkap]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="144" y="54" width="19" height="20" isRemoveLineWhenBlank="true" uuid="dbf5fab1-9ec7-4728-a898-11653a25db65">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="158" y="54" width="300" height="20" isRemoveLineWhenBlank="true" uuid="4ca36a8e-ff44-4784-99f1-f4257b71825f">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{namaPUK2}]]></textFieldExpression>
                </textField>
                <staticText>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="74" width="100" height="20" isRemoveLineWhenBlank="true" uuid="24352d42-4dad-41dd-bf74-68ad1182fe3d">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[Jabatan]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="144" y="74" width="19" height="20" isRemoveLineWhenBlank="true" uuid="8987298c-3ccf-41fe-85a6-2c06c463dbf4">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="158" y="74" width="300" height="20" isRemoveLineWhenBlank="true" uuid="fb4155ac-da40-4d75-90ce-82397e2b073b">
                        <printWhenExpression><![CDATA[$F{nikPUK2} != null && $F{namaPUK2} != null && $F{jabatanPUK2} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{jabatanPUK2}]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="21" y="225" width="458" height="69" uuid="43d1145d-a0de-4284-b55a-c975cb583b1b"/>
                <staticText>
                    <reportElement positionType="Float" x="0" y="0" width="200" height="20" uuid="083dfde4-e4d8-4cb4-b9b7-8f483f81e986"/>
                    <textElement verticalAlignment="Middle">
                        <font size="11" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Informasi UPM]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" x="0" y="23" width="100" height="20" uuid="51eaa872-9a23-44d2-954e-4f90aea872dd"/>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <text><![CDATA[Catatan]]></text>
                </staticText>
                <staticText>
                    <reportElement positionType="Float" x="144" y="23" width="19" height="20" uuid="570a0a85-cd77-421b-9e7d-c47c2726d293"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="11"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField isStretchWithOverflow="true">
                    <reportElement positionType="Float" x="158" y="23" width="300" height="20" uuid="e762f35d-7676-4b6a-ba2d-73f61b5ece19">
                        <printWhenExpression><![CDATA[!$F{catatan}.isEmpty() && $F{catatan} != null]]></printWhenExpression>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{catatan}]]></textFieldExpression>
                </textField>
                <frame>
                    <reportElement positionType="Float" x="0" y="49" width="458" height="20" uuid="ff01d985-9e19-4eec-80f3-a7ac43d650f4"/>
                    <staticText>
                        <reportElement positionType="Float" x="0" y="0" width="100" height="20" uuid="0b94b6f6-58f3-4f56-ad09-904f6a683f18"/>
                        <textElement verticalAlignment="Middle">
                            <font/>
                        </textElement>
                        <text><![CDATA[Status Tiket]]></text>
                    </staticText>
                    <staticText>
                        <reportElement positionType="Float" x="144" y="0" width="19" height="20" uuid="d0bd7068-ee0e-4709-963f-bf63e9988e48"/>
                        <textElement textAlignment="Center" verticalAlignment="Middle">
                            <font size="11"/>
                        </textElement>
                        <text><![CDATA[:]]></text>
                    </staticText>
                    <textField>
                        <reportElement positionType="Float" x="158" y="0" width="300" height="20" uuid="bdf6acca-a310-4167-884d-07f3f55bef7b" />
                        <textElement verticalAlignment="Middle">
                            <font/>
                        </textElement>
                        <textFieldExpression><![CDATA[$F{statusTiket}]]></textFieldExpression>
                    </textField>
                </frame>
            </frame>
        </band>
        <band height="630">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" x="21" y="3" width="450" height="620" uuid="c9c5a0f1-3c98-4808-81eb-548b7acda6a1">
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <staticText>
                    <reportElement x="0" y="0" width="350" height="20" uuid="cca5aac7-0a95-413f-90f6-7427642e829e">
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="11" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Timeline Permohonan]]></text>
                </staticText>
                <frame>
                    <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="20" width="450" height="600" isRemoveLineWhenBlank="true" uuid="b2ee9219-e5be-42f4-b7ad-573dfa1ec6f4">
                        <printWhenExpression><![CDATA[!$P{timelines}.isEmpty() && $P{timelines} != null]]></printWhenExpression>
                    </reportElement>
                    <textField isStretchWithOverflow="true">
                        <reportElement stretchType="ContainerBottom" x="0" y="2" width="450" height="575" uuid="fdee1150-1d11-4108-980d-4e3faba24f84">
                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                            <printWhenExpression><![CDATA[!$P{timelines}.isEmpty() && $P{timelines} != null]]></printWhenExpression>
                        </reportElement>
                        <textElement verticalAlignment="Top">
                            <font size="8"/>
                        </textElement>
                        <textFieldExpression><![CDATA[$P{timelines}]]></textFieldExpression>
                    </textField>
                </frame>
            </frame>
        </band>
    </detail>
    <pageFooter>
        <band height="50" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="130" y="15" width="270" height="20" uuid="e3c13d40-5e32-43c4-bafe-610b416ccba7"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="11" isBold="false"/>
                </textElement>
                <text><![CDATA["Detail Tiket ini di generate oleh system TEMA"]]></text>
            </staticText>
        </band>
    </pageFooter>
</jasperReport>
