<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.0.final using JasperReports Library version 6.20.0-2bc7ab61c56f459e8176eb05c7705e145cd400ad  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report_konfirmasi_userid" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6418be1f-74ca-470c-958e-b34a70436329">
    <parameter name="tanggalUpload" class="java.lang.String"/>
    <parameter name="aplikasi" class="java.lang.String"/>
    <parameter name="logoBTPNS" class="java.lang.Object"/>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="nik" class="java.lang.String"/>
    <field name="namaUser" class="java.lang.String"/>
    <field name="kewenangan" class="java.lang.String"/>
    <field name="jabatan" class="java.lang.String"/>
    <field name="unitKerja" class="java.lang.String"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <title>
        <band height="80" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="283" y="20" width="230" height="20" uuid="cbecf312-0d6c-4440-82c3-e8df28b61941"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="SansSerif" size="12" isBold="true"/>
                </textElement>
                <text><![CDATA[Laporan Upload Database User ID]]></text>
            </staticText>
            <image>
                <reportElement mode="Opaque" x="712" y="0" width="170" height="50" uuid="1adf2401-5363-4d10-9739-4b6501d8427e"/>
                <imageExpression><![CDATA[$P{logoBTPNS}]]></imageExpression>
            </image>
            <textField>
                <reportElement x="140" y="60" width="173" height="20" uuid="8a875e80-dc43-4bec-9e50-34c681e5d0e3">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="10"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{tanggalUpload}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="61" y="60" width="79" height="20" uuid="24408744-4e11-4b54-94f7-02f97824fa6a"/>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <text><![CDATA[Tanggal Upload :]]></text>
            </staticText>
            <textField>
                <reportElement x="528" y="60" width="218" height="20" uuid="5a58d3fa-01e9-426a-93a2-678f1d60e32f"/>
                <textElement textAlignment="Right" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[$P{aplikasi}]]></textFieldExpression>
            </textField>
        </band>
    </title>
    <columnHeader>
        <band height="35" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement mode="Opaque" x="61" y="5" width="60" height="30" backcolor="#808080" uuid="de7998fe-**************-7267d7414341">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="8" isBold="true"/>
                </textElement>
                <text><![CDATA[NIK]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="121" y="5" width="120" height="30" backcolor="#808080" uuid="0334ac42-d8be-40b8-b15c-306b23dd2e29">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="8" isBold="true"/>
                </textElement>
                <text><![CDATA[Nama]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="241" y="5" width="125" height="30" backcolor="#808080" uuid="d1197ceb-5b6f-4de6-97aa-336fd139043a"/>
                <box>
                    <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="8" isBold="true"/>
                </textElement>
                <text><![CDATA[Kewenangan]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="366" y="5" width="200" height="30" backcolor="#808080" uuid="b069dbc2-689c-4cf1-946b-4cd41186566d">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="8" isBold="true"/>
                </textElement>
                <text><![CDATA[Jabatan]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="566" y="5" width="180" height="30" backcolor="#808080" uuid="df372015-b809-4f67-aec8-28d2f4d9e864">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="8" isBold="true"/>
                </textElement>
                <text><![CDATA[Unit Kerja]]></text>
            </staticText>
        </band>
    </columnHeader>
    <detail>
        <band height="22" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" x="11" y="0" width="768" height="22" uuid="ce6e4b2c-4d48-4c42-9d92-338e823e77b2">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="50" y="0" width="60" height="22" uuid="202ef399-51bf-486f-a1df-7e7f59bf8884">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{nik}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="110" y="0" width="120" height="22" uuid="6ad4d479-1f90-4c14-95f5-508ff28b3160">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{namaUser}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="230" y="0" width="125" height="22" uuid="825e13ab-817d-41eb-ba41-c6ed3e36cc7c">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{kewenangan}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="355" y="0" width="200" height="22" uuid="463b9502-f0b7-4209-b9c8-1ad7ba17f382">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{jabatan}]]></textFieldExpression>
                </textField>
                <textField isStretchWithOverflow="true">
                    <reportElement stretchType="RelativeToTallestObject" x="555" y="0" width="180" height="22" uuid="40267a6d-a6f6-40c7-b50f-36f60800fe76">
                        <property name="com.jaspersoft.studio.unit.firstLineIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingAfter" value="px"/>
                        <property name="com.jaspersoft.studio.unit.spacingBefore" value="px"/>
                        <property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
                        <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    </reportElement>
                    <box padding="2">
                        <topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                        <rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
                        <font fontName="SansSerif" size="7"/>
                        <paragraph firstLineIndent="0" leftIndent="1" rightIndent="0" spacingBefore="0" spacingAfter="0"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{unitKerja}]]></textFieldExpression>
                </textField>
            </frame>
        </band>
    </detail>
    <columnFooter>
        <band height="45" splitType="Stretch"/>
    </columnFooter>
    <pageFooter>
        <band height="20" splitType="Stretch">
            <staticText>
                <reportElement x="308" y="1" width="180" height="19" uuid="7b5dd181-a9c3-41e7-9337-b31af62c7b1f"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="8"/>
                </textElement>
                <text><![CDATA[Laporan ini di generate by system TEMA]]></text>
            </staticText>
            <textField>
                <reportElement x="758" y="1" width="50" height="14" uuid="a21de67c-ac99-457d-aec3-795b5819de3c">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="730" y="1" width="25" height="14" uuid="c9bb87da-0418-473c-8b63-87025fd091e5"/>
                <textElement textAlignment="Right" verticalAlignment="Middle">
                    <font size="8"/>
                </textElement>
                <text><![CDATA[Page : ]]></text>
            </staticText>
        </band>
    </pageFooter>
    <summary>
        <band height="42" splitType="Stretch"/>
    </summary>
</jasperReport>
