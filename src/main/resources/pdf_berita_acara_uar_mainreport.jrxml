<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.0.final using JasperReports Library version 6.20.0-2bc7ab61c56f459e8176eb05c7705e145cd400ad  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="beritaAcaraUAR" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="40" bottomMargin="20" uuid="d56b67e8-27b0-4ce6-9e88-c7cee6d3af0a">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
    <parameter name="refNumber" class="java.lang.String"/>
    <parameter name="logoBTPNS" class="java.lang.Object"/>
    <parameter name="aplikasi" class="java.lang.String"/>
    <parameter name="period" class="java.lang.String"/>
    <parameter name="tahun" class="java.lang.String"/>
    <parameter name="rangePeriod" class="java.lang.String"/>
    <parameter name="totalUser" class="java.lang.String"/>
    <parameter name="totalActiveUser" class="java.lang.String"/>
    <parameter name="totalInactiveUser" class="java.lang.String"/>
    <parameter name="createdAt" class="java.lang.String"/>
    <parameter name="noSOP" class="java.lang.String"/>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <background>
        <band splitType="Stretch"/>
    </background>
    <title>
        <band height="110" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="21" y="40" width="500" height="20" uuid="e4e6366f-63b5-4ed7-b8fb-0ae0f820a10a"/>
                <textElement textAlignment="Center">
                    <font size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[BERITA ACARA]]></text>
            </staticText>
            <line>
                <reportElement x="0" y="100" width="555" height="1" uuid="f2536aef-2eab-4150-b878-8becb5c2ded4">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
            </line>
            <image>
                <reportElement mode="Opaque" x="450" y="0" width="170" height="50" uuid="1adf2401-5363-4d10-9739-4b6501d8427e"/>
                <imageExpression><![CDATA[$P{logoBTPNS}]]></imageExpression>
            </image>
            <textField isStretchWithOverflow="true">
                <reportElement stretchType="RelativeToBandHeight" x="-11" y="60" width="575" height="20" uuid="d4657f7a-241f-44cd-a085-1cc92454f519">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <textElement textAlignment="Center">
                    <font size="11" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA["USER ACCESS REVIEW APLIKASI " + $P{aplikasi} + " - PERIODE TRIWULAN " + $P{period} + " " + $P{tahun}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="21" y="80" width="500" height="20" uuid="6f3fbc3e-4d15-4890-a704-8656c7c4273c"/>
                <textElement textAlignment="Center">
                    <font size="11" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{refNumber}]]></textFieldExpression>
            </textField>
        </band>
    </title>
    <detail>
        <band height="250" splitType="Stretch">
            <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <frame>
                <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="10" width="555" height="30" uuid="9072363a-8619-432b-86d5-eefce91adf4c">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textField isStretchWithOverflow="true">
                    <reportElement x="0" y="0" width="555" height="30" uuid="0c96b257-1be3-4a1a-97ea-efb521545fc9">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10"/>
                    </textElement>
                    <textFieldExpression><![CDATA["Merujuk " + $P{noSOP} + " Pengelolaan User ID dan Password, berikut kami sampaikan Berita Acara User Access Review (UAR) User ID " + $P{aplikasi} + " sebanyak " + $P{totalUser} + " USER."]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" x="0" y="40" width="555" height="20" uuid="9072363a-8619-432b-86d5-eefce91adf4c"/>
                <textField isStretchWithOverflow="true">
                    <reportElement isPrintRepeatedValues="false" x="0" y="0" width="555" height="20" uuid="4953a382-15d3-4c8d-86c3-3c009184a418"/>
                    <textElement verticalAlignment="Middle">
                        <font size="10"/>
                    </textElement>
                    <textFieldExpression><![CDATA["Data UAR " + $P{aplikasi} + " yang digunakan adalah Periode " + $P{rangePeriod} + "."]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="71" width="555" height="40" uuid="aeb32f06-1aae-41d5-9732-5ab750140d96"/>
                <textField isStretchWithOverflow="true">
                    <reportElement x="0" y="0" width="555" height="20" uuid="c9a14d4e-36c3-550e-82fb-1fb44ac96b38">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10"/>
                    </textElement>
                    <textFieldExpression><![CDATA["Berikut ini adalah user-user yang terdaftar pada aplikasi " + $P{aplikasi} + " (sebelum UAR dilakukan) :"]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement x="40" y="20" width="300" height="20" uuid="6738286e-8927-449e-abf3-d63b3a21553a">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                        <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10" isBold="true"/>
                    </textElement>
                    <textFieldExpression><![CDATA["Total User : " + $P{totalUser} + " User"]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="117" width="555" height="60" uuid="8788c293-41c3-4130-b58b-1527420355e6"/>
                <textField>
                    <reportElement x="0" y="0" width="555" height="20" uuid="c96cb3e5-f115-40cd-8b6b-1195d5b2d6ee">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10"/>
                    </textElement>
                    <textFieldExpression><![CDATA["Setelah melakukan konfirmasi penggunaan User ID aplikasi " + $P{aplikasi} + " kepada masing-masing user pengguna (proses UAR), maka hasilnya adalah sebagai berikut:"]]></textFieldExpression>
                </textField>
                <staticText>
                    <reportElement x="40" y="20" width="140" height="20" uuid="99cebf63-327c-47f7-90f4-e221b1ba559d">
                        <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Total User Aktif]]></text>
                </staticText>
                <staticText>
                    <reportElement x="180" y="20" width="20" height="20" uuid="14de025f-977a-4ba7-8faf-2df1e98b4fc9"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="10" isBold="true"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="200" y="20" width="300" height="20" uuid="c96cb3e5-f115-40cd-8b6b-1195d5b2d6ee">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10" isBold="true"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$P{totalActiveUser} + " User"]]></textFieldExpression>
                </textField>
                <staticText>
                    <reportElement x="40" y="40" width="140" height="20" uuid="99cebf63-327c-47f7-90f4-e221b1ba559d">
                        <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10" isBold="true"/>
                    </textElement>
                    <text><![CDATA[Total User Tidak Aktif]]></text>
                </staticText>
                <staticText>
                    <reportElement x="180" y="40" width="20" height="20" uuid="14de025f-977a-4ba7-8faf-2df1e98b4fc9"/>
                    <textElement textAlignment="Center" verticalAlignment="Middle">
                        <font size="10" isBold="true"/>
                    </textElement>
                    <text><![CDATA[:]]></text>
                </staticText>
                <textField>
                    <reportElement x="200" y="40" width="300" height="20" uuid="c96cb3e5-f115-40cd-8b6b-1195d5b2d6ee">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10" isBold="true"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$P{totalInactiveUser} + " User"]]></textFieldExpression>
                </textField>
            </frame>
            <frame>
                <reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="183" width="555" height="40" uuid="8721e6de-f394-4482-ad76-57d0142075c9"/>
                <staticText>
                    <reportElement x="0" y="0" width="555" height="20" uuid="99cebf63-327c-47f7-90f4-e221b1ba559d"/>
                    <textElement verticalAlignment="Middle">
                        <font size="10"/>
                    </textElement>
                    <text><![CDATA[Demikian Berita Acara ini disampaikan, atas perhatian yang diberikan kami ucapkan terima kasih.]]></text>
                </staticText>
                <textField>
                    <reportElement x="0" y="20" width="300" height="20" uuid="dc1fb114-f7d7-439f-af9d-a6ebf30ba050">
                        <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    </reportElement>
                    <textElement verticalAlignment="Middle">
                        <font size="10"/>
                    </textElement>
                    <textFieldExpression><![CDATA["Jakarta, " + $P{createdAt}]]></textFieldExpression>
                </textField>
            </frame>
        </band>
    </detail>
    <pageFooter>
        <band height="50" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="142" y="15" width="270" height="20" uuid="e3c13d40-5e32-43c4-bafe-610b416ccba7"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="10" isBold="false"/>
                </textElement>
                <text><![CDATA["Berita Acara ini di generate oleh system TEMA"]]></text>
            </staticText>
        </band>
    </pageFooter>
</jasperReport>
